import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/photo_view/gallery_photo_view.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_info_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_list_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_footer.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_header.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_item.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_product.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_product_footer.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_service_header.dart';
import 'package:XyyBeanSproutsFlutter/utils/jump_page_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/section_group_handler/section_group_handler.dart';

class OrderDetailPage extends BasePage {
  // 订单编号
  final String? orderId;

  // 客户id
  final String? merchantId;
  //是否母单
  final int? isParent;

  OrderDetailPage({this.orderId, this.merchantId,this.isParent});

  @override
  BaseState initState() {
    return _OrderDetailPageState(this.orderId, this.merchantId,this.isParent);
  }
}

class _OrderDetailPageState extends BaseState {
  /// 数据源
  OrderDetailInfoData? detailData = OrderDetailInfoData();

  SectionGroupHandler? _sectionHander;

  // 订单编号
  final String? orderId;

  // 客户id
  final String? merchantId;
  //是否母单
  final int? isParent;

  bool? isRecord;

  // 展开状态
  Map<int, bool> cacheOpen = {};

  // // 刷新 老豆芽没有，暂时先去掉
  // var _refreshController = EasyRefreshController();

  String? mImageHost;

  _OrderDetailPageState(this.orderId, this.merchantId,this.isParent);

  PageStateWidget? pageStateWidget;

  @override
  void initState() {
    pageStateWidget = new PageStateWidget();
    this._requestOrderDetailData();
    super.initState();
  }

  void _requestOrderDetailData() async {
    if (mImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          setState(() {
            mImageHost = value['image_host'];
          });
        }
      });
    }
    EasyLoading.show(status: "加载中");
    NetworkV2<OrderDetailInfoData>(OrderDetailInfoData())
        .requestDataV2("order/basic",
            parameters: {"orderId": orderId, "merchantId": merchantId}, contentType: RequestContentType.FORM, method: RequestMethod.POST)
        .then((value) {
      EasyLoading.dismiss();
      if (mounted && value.isSuccess != null && value.isSuccess!) {
        setState(() {
          this.detailData = value.getData();
        });
        // 请求订单商品
        _requestOrderProductListData();
      } else {
        showToast(value.msg ?? "");
      }
    });
  }

  void refreshOrderDetailData() {
    _requestOrderDetailData();
  }

  void _requestOrderProductListData() async {
    NetworkV2<OrderDetailListData>(OrderDetailListData())
        .requestDataV2("order/basic/produckList",
            parameters: {"orderNo": this.detailData?.orderNo ?? ""}, contentType: RequestContentType.FORM, method: RequestMethod.GET)
        .then((value) {
      EasyLoading.dismiss();
      if (mounted && value.isSuccess != null && value.isSuccess!) {
        setState(() {
          this.detailData?.packageList = value.getListData();
          if(this.isParent == 1){
            this.detailData?.packageList?.sort((a, b) {
              return (a.orgCode ?? "").compareTo(b.orgCode ?? "");
            });
            // 标记每个 merchantId 的第一个商品
            String? currentOrgCode;
            for (var item in this.detailData?.packageList ?? []) {
              if (item.orgCode != currentOrgCode) {
                item.isFirstOfMerchant = 1;  // 使用 1 表示是第一个
                currentOrgCode = item.orgCode;
                // print('标记第一个商品: orgCode=${item.orgCode}, productName=${item.productName}, isFirstOfMerchant=${item.isFirstOfMerchant}');
              } else {
                item.isFirstOfMerchant = 0;  // 使用 0 表示不是第一个
                // print('标记非第一个商品: orgCode=${item.orgCode}, productName=${item.productName}, isFirstOfMerchant=${item.isFirstOfMerchant}');
              }
            }
          }
        });
      } else {
        showToast(value.msg ?? "");
      }
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    print('qiyu:isParent: ${isParent}');
    this._sectionHander = this.generateHandler();
    return Container(
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFF7F7F8),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: this._sectionHander!.allItemCount,
                itemBuilder: (BuildContext context, int index) {
                  return this._sectionHander!.cellAtIndex(index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget orderListWidget() {
    return Container();
  }

  SectionGroupHandler generateHandler() {
    return SectionGroupHandler(
      numberOfSections: 6,
      numberOfRowsInSection: (section) {
        /// 展开收起逻辑
        bool? isOpen = this.cacheOpen[section];
        if (isOpen != null && isOpen == false) {
          return section == 5 ? 1 : 0;
        }
        if (section == 0) {
          /// 订单信息
          return (this.detailData?.orderBaseList(isParent: this.isParent) ?? []).length;
        }
        if (section == 3) {
          return (this.detailData?.receiveModels() ?? []).length;
        }
        if (section == 4) {
          return (this.detailData?.invoiceModels() ?? []).length;
        }
        if (section == 5) {
          return (this.detailData?.packageList ?? []).length;
        }
        return 0;
      },
      headerForSection: (section) {
        if (section == 0) {
          return OrderDetailHeader(
            title: "订单信息",
            isShouldOpen: true,
            isOpen: (this.cacheOpen[section] ?? true),
            clickCallback: (isOpen) {
              this.cacheOpen[section] = isOpen ?? true;
              setState(() {});
            },
          );
        }
        if (section == 1 && this.isParent != 1) {
          return this.detailData?.imUrl != null
              ? OrderDetailServiceHeader(
                  title: "${this.detailData?.imButtonDisplay}",
                  clickAction: () {
                    String imUrl = Uri.encodeComponent("${this.detailData?.imUrl}");
                    String routerPath = "xyy://crm-app.ybm100.com/crm/web_view?url=$imUrl";
                    XYYContainer.open(routerPath);

                    /// 埋点代码
                    this.track('mc-orderdetail-contactbutton');
                  },
                )
              : Container();
        }
        if (section == 2) {
          return OrderDetailHeader(
            title: "物流信息",
            isShouldClick: true,
            clickCallback: (open) {
              Navigator.of(context).pushNamed('/Logistics', arguments: {"arguments": this.detailData?.orderNo ?? ""});
            },
          );
        }
        if (section == 3) {
          return OrderDetailHeader(
            title: "收货信息",
            isShouldOpen: true,
            isOpen: (this.cacheOpen[section] ?? true),
            clickCallback: (isOpen) {
              this.cacheOpen[section] = isOpen ?? true;
              setState(() {});
            },
          );
        }
        if (section == 4) {
          return OrderDetailHeader(
            title: "发票信息",
            isShouldOpen: true,
            isOpen: (this.cacheOpen[section] ?? true),
            clickCallback: (isOpen) {
              this.cacheOpen[section] = isOpen ?? true;
              setState(() {});
            },
          );
        }
        if (section == 5) {
          if ((this.detailData?.packageList ?? []).length <= 0) {
            return Container();
          }
          return OrderDetailHeader(
            title: "订单商品(${(this.detailData?.packageList ?? []).length})",
          );
        }
        return Container();
      },
      cellForRowAtIndexPath: (indexPath) {
        if (indexPath.section < 5) {
          OrderEntryData model = OrderEntryData();
          if (indexPath.section == 0) {
            model = this.detailData!.orderBaseList(isParent: this.isParent)[indexPath.row];
          }
          if (indexPath.section == 3) {
            model = this.detailData!.receiveModels()[indexPath.row];
          }
          if (indexPath.section == 4) {
            model = this.detailData!.invoiceModels()[indexPath.row];
          }
          if (model.isImageItem) {
            return OrderDetailImageItem(
              model: model,
              imageOnTap: (int index) {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (BuildContext context) => GalleryPhotoView(
                      images: model.imageList!, //传入图片list
                      index: index, //传入当前点击的图片的index
                    ),
                  ),
                );
              },
            );
          }
          return OrderDetailItem(
            model: model,
            selectAction: this.selectActionWith,
            refreshCallback: this.refreshOrderDetailData,
          );
        }
        if (indexPath.section == 5) {
          OrderDetailListData model = this.detailData!.packageList![indexPath.row];
          return OrderDetailProduct(
            isParent:this.isParent,
            mImageHost: this.mImageHost,
            isRefund: false,
            data: model,
          );
        }
        return Container();
      },
      footerForSection: (section) {
        if (section == 5 && (this.detailData?.detailList?.length ?? 0) > 1) {
          return OrderDetailProductFooter(
            isOpen: (this.cacheOpen[section] ?? true),
            callback: (isOpen) {
              this.cacheOpen[section] = isOpen ?? true;
              setState(() {});
            },
          );
        }
        if (section == 1) {
          return Container();
        }
        return OrderDetailFooter();
      },
    );
  }

  Widget? getEmptyWidget() {
    if (detailData == null) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "订单详情";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      onBackPress(context);
    });
  }

  void onBackPress(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.maybePop(context, isRecord);
    } else {
      if (Platform.isIOS) {
        XYYContainer.bridgeCall("app_back");
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }

  void selectActionWith(OrderEntryData model) {
    if (model.title == "卡单原因" && model.content != null && model.content != "--") {
      Navigator.of(context).pushNamed('/OrderDetailReasonPage', arguments: {'refuseReason': model.content});
    }
    if (model.title == "下单客户" && model.content != "--") {
      this.jumpMerchantInfo();
    }
  }

  void jumpMerchantInfo() async {
    jumpCustomerPageByMerchantId(this.detailData?.merchantId, canJumpPublic: false);
  }
}
