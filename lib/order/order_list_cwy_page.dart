import 'dart:async';
import 'dart:io';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/header_footer/header_footer_helper.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

import 'bean/order_list_cwy_data.dart';


class OrderListCwyPage extends BasePage {
  final String? sysUserId;
  OrderListCwyPage({this.sysUserId});

  @override
  BaseState initState() {
    return _OrderListCwyPageState(this.sysUserId);
  }
}

class _OrderListCwyPageState extends BaseState {
  bool? isRecord;
  var _controller = EasyRefreshController();
  int pageNum=1;
  // 模拟数据
   List<OrderListItem> orderList =[];
// oaid
  final String? sysUserId;
  _OrderListCwyPageState(this.sysUserId);
  @override
  void initState() {
    super.initState();
    // this.setOpenStatus();
    this._requestOrderDetailData();
  }

  void _requestOrderDetailData() async {
    // 目前接口为空，使用模拟数据
    setState(() {
      this.pageNum=1;
    });
    var result =
    await NetworkV2<OrderListCwyData>(OrderListCwyData())
        .requestDataV2(
          'worries/messageList',
          contentType: RequestContentType.FORM,
          method: RequestMethod.GET,
          showErrorToast:false,
          parameters:{
            "pageNum":this.pageNum,
            "pageSize":20,
          },
        );
    print(123);
      print(result.data.toString());


    setState(() {
      this.orderList = result.getData()!.list ?? [];
      _controller.finishRefresh();
      _controller.finishLoad(noMore:  !result.data.hasNextPage);
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child:  EasyRefresh.custom(
          controller: _controller,
          enableControlFinishLoad: true,
          enableControlFinishRefresh: true,
          header: HeaderFooterHelp().getHeader(),
          footer: HeaderFooterHelp().getFooter(),
          onRefresh: () async {
            requestRecordList(true);
          },
          onLoad: () async {
            requestRecordList(false);
          },
          slivers: [
            SliverList(
              delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
                //创建列表项
                return GestureDetector(
                  onTap: () async {
                    NetworkV2<OrderListCwyData>(OrderListCwyData())
                        .requestDataV2(
                      'worries/read',
                      contentType: RequestContentType.FORM,
                      method: RequestMethod.GET,
                      parameters: {
                        "id": orderList[index].id,
                      },
                    );
                    await Navigator.of(context).pushNamed('/order_detail_refund_cwy_page', arguments: {
                      'orderNo': orderList[index].superId.toString()
                    });
                    this._requestOrderDetailData();
                  },
                  child: Container(
                    margin: EdgeInsets.only(top: 12, left: 10, right: 10),
                    child: Stack(
                      children: [
                        Container(
                          padding: EdgeInsets.all(10),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(10)),
                            color: Colors.white,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                            margin: EdgeInsets.only(bottom: 12),
                                            child: Text(
                                              "超无忧售后",
                                              style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w500,
                                                color: Color(0xFF292933),
                                              ),
                                            )),
                                        Container(
                                          margin: EdgeInsets.only(bottom: 12),
                                          child: Text(
                                            "查看详情 >",
                                            style: TextStyle(
                                              fontSize: 14,
                                              color: Color(0xFF00B377),
                                            ),
                                          ),
                                        )
                                      ],
                                    ),
                                    SizedBox(height: 0),
                                    Text(
                                      orderList[index].sendMessage ?? "",
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Color(0xFF292933),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (orderList[index].isRead == 0)
                          Positioned(
                            right: 6,
                            bottom: 6,
                            child: Container(
                              width: 10,
                              height: 10,
                              decoration: BoxDecoration(
                                color: Colors.red,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              }, childCount: orderList?.length ?? 0),
            )
          ])
    );
  }
  requestRecordList(bool isRefresh) async{
    if(!isRefresh){
      setState(() {
        this.pageNum=this.pageNum+1;
      });
    }else{
      setState(() {
        this.pageNum=1;
      });
    }
    var result =
        await NetworkV2<OrderListCwyData>(OrderListCwyData())
        .requestDataV2(
      'worries/messageList',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters:{
        "pageNum":this.pageNum,
        "pageSize":20
      },
    );
    setState(() {
      if(this.pageNum==1){
        this.orderList = result.data.list ?? [];
      }else{
        this.orderList.addAll(result.data.list ?? []);
      }
      _controller.finishRefresh();
      _controller.finishLoad(noMore:  !result.data.hasNextPage);
    });
  }
  @override
  String getTitleName() {
    return "超无忧售后";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      onBackPress(context);
    });
  }

  void onBackPress(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.maybePop(context, isRecord);
    } else {
      if (Platform.isIOS) {
        XYYContainer.bridgeCall("app_back");
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }
}
