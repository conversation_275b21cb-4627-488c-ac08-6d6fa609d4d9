import 'dart:convert';

import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_filter_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_amount.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_area.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_operation_item.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_time_item.dart';
import 'package:flutter/material.dart';

class OrderFilterPage extends BasePage {
  final String? sourceJSON;
  final bool? isRefund;
  final Map<String, String?>? currentParams;

  OrderFilterPage({
    this.sourceJSON,
    this.isRefund = false,
    this.currentParams,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return OrderFilterState(
      sourceJSON: this.sourceJSON,
      isRefund: this.isRefund,
      filterParams: this.currentParams,
    );
  }
}

class OrderFilterState extends BaseState {
  List<OrderFilterConfigModel> dataSource = [];
  List<dynamic> branchCodeData = [];
  String? sourceJSON;

  bool? isRefund;

  /// 当前页面选中的参数
  Map<String, String?>? filterParams;

  OrderFilterState({
    this.sourceJSON,
    this.isRefund,
    this.filterParams,
  });

  @override
  void dismissLoadingDialog() {
    /// 重写此方法，防止页面返回时取消上一个页面的loading
  }

  @override
  void initState() {
    super.initState();
    // 防止外部传入异常数据
    if (this.filterParams == null) {
      this.filterParams = Map<String, String?>();
    }
    // 配置选项
    this.configSource();
  }

  void configSource() {
    /// 清空数据源
    this.dataSource = [];

    /// 数据转换
    Map<String, dynamic>? jsonData = json.decode(this.sourceJSON ?? "");

    /// 选择时间范围 间隔 （天）
    int intervalDay = int.tryParse(jsonData?['queryDay']?.toString() ?? "0") ?? 365;

    /// 范围选项
    OrderFilterConfigModel areaModel = OrderFilterConfigModel(
      title: this.isRefund! ? '退单范围' : '订单范围',
      itemKey: 'area',
      items: null,
      selectIds: [],
      allowsMultipleSelection: true,
      canClean: false,
    );
    this.dataSource.add(areaModel);

    /// 时间选项
    List<dynamic> timeItemData = jsonData?['timeTypes'] ?? [];
    // 退款状态的筛选项后端返回的是key 不是id
    if (isRefund == true) {
      timeItemData.forEach((element) {
        element['id'] = element['key'];
      });
    }
    String timeDefaultId = this.filterParams?.containsKey('refundPeriod') == true ? "" : "1";
    String timeSelectedData = this.filterParams?[isRefund! ? 'period' : 'sceneType'] ?? timeDefaultId;
    OrderFilterConfigModel sceneTypeModel = OrderFilterConfigModel(
      title: this.isRefund == false ? '下单时间' : '申请时间',
      itemKey: this.isRefund == false ? 'sceneType' : 'period',
      defaultId: this.filterParams?.containsKey('refundPeriod') == true ? -1 : 1,
      intervalDay: intervalDay,
      items: OrderFilterItemData.formJsonList(timeItemData),
      selectIds: timeSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
      allowsMultipleSelection: false,
      canClean: false,
    );
    this.dataSource.add(sceneTypeModel);

    if (isRefund == true) {
      /// 退款审核时间
      List<dynamic> refundTimeData = jsonData?['refundTimeTypes'] ?? [];
      // 退款状态的筛选项后端返回的是key 不是id
      if (isRefund == true) {
        refundTimeData.forEach((element) {
          element['id'] = element['key'];
        });
      }
      String refundTimeSelectedData = this.filterParams?['refundPeriod'] ?? "";
      OrderFilterConfigModel refundTimeModel = OrderFilterConfigModel(
        title: "审核时间",
        itemKey: "refundPeriod",
        defaultId: this.filterParams?.containsKey('period') == true ? -1 : 1,
        intervalDay: intervalDay,
        items: OrderFilterItemData.formJsonList(refundTimeData),
        selectIds: refundTimeSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
        allowsMultipleSelection: false,
        canClean: false,
      );
      this.dataSource.add(refundTimeModel);
    }
    /// 商品类型 优选、控销、甄选
    List<dynamic> productTypeData = jsonData?['productTypes'] ?? [];
    String productTypelSelectedData = this.filterParams!['productTypes'] ?? "-1";
    OrderFilterConfigModel productTypeModel = OrderFilterConfigModel(
      title: '商品类型',
      itemKey: 'productTypes',
      items: OrderFilterItemData.formJsonList(productTypeData),
      selectIds: productTypelSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
      allowsMultipleSelection: false,
      canClean: false,
    );
    this.dataSource.add(productTypeModel);
    
    ///订单来源
    branchCodeData = jsonData?['orderBranchCodes'] ?? [];
    int i = -1;
    branchCodeData.forEach((element) {
      element['id'] = i++;
    });
    String branchCodeSelectedData = this.filterParams!['orderBranchCode'] ?? "-1";

    List<int> branchCodeIds = [branchCodeData.indexWhere((element) => element["code"] == branchCodeSelectedData) - 1];
    OrderFilterConfigModel payTypeModel = OrderFilterConfigModel(
      title: '订单来源',
      itemKey: 'orderBranchCode',
      items: OrderFilterItemData.formJsonList(branchCodeData),
      selectIds: branchCodeIds,
      allowsMultipleSelection: false,
      canClean: false,
    );
    this.dataSource.add(payTypeModel);

    if (this.isRefund ?? true) {
      /// 退单筛选
      /// 退款发起方
      List<dynamic> refundData = jsonData?['refundChannel'] ?? [];
      String refundSelectedData = this.filterParams!['refundChannelStr'] ?? "-1";
      List<int> refundSelectedIds = refundSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList();
      OrderFilterConfigModel refundModel = OrderFilterConfigModel(
        title: '退款发起方',
        itemKey: 'refundChannelStr',
        items: OrderFilterItemData.formJsonList(refundData),
        selectIds: refundSelectedIds,
        allowsMultipleSelection: true,
        canClean: false,
      );
      this.dataSource.add(refundModel);

      /// 入库状态
      List<dynamic> storeStatusData = jsonData?['storeStatus'] ?? [];
      String storeStatusSelectedData = this.filterParams!['storeStatusStr'] ?? "-1";
      OrderFilterConfigModel storeStatusModel = OrderFilterConfigModel(
        title: '入库状态',
        itemKey: 'storeStatusStr',
        items: OrderFilterItemData.formJsonList(storeStatusData),
        selectIds: storeStatusSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
        allowsMultipleSelection: true,
        canClean: false,
      );
      this.dataSource.add(storeStatusModel);

      /// 退款状态
      List<dynamic> orderRefundStatusData = jsonData?['orderRefundStatus'] ?? [];
      String orderRefundStatusSelectedData = this.filterParams!['auditState'] ?? "-1";
      OrderFilterConfigModel orderRefundStatusModel = OrderFilterConfigModel(
        title: '退款状态',
        itemKey: 'auditState',
        items: OrderFilterItemData.formJsonList(orderRefundStatusData),
        selectIds: orderRefundStatusSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
        allowsMultipleSelection: false,
        canClean: false,
      );
      this.dataSource.add(orderRefundStatusModel);
    } else {
      /// 订单筛选
      /// 支付方式
      List<dynamic> payTypeData = jsonData?['orderPayTypes'] ?? [];
      String paySelectedData = this.filterParams!['payTypes'] ?? "-1";
      List<int> paySelectedIds = paySelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList();
      OrderFilterConfigModel payTypeModel = OrderFilterConfigModel(
        title: '支付方式',
        itemKey: 'payTypes',
        items: OrderFilterItemData.formJsonList(payTypeData),
        selectIds: paySelectedIds,
        allowsMultipleSelection: true,
        canClean: false,
      );
      this.dataSource.add(payTypeModel);

      /// 支付渠道
      List<dynamic> payChannelData = jsonData?['payChannels'] ?? [];
      String payChannelSelectedData = this.filterParams!['payChannels'] ?? "-1";
      OrderFilterConfigModel payChannelModel = OrderFilterConfigModel(
        title: '支付渠道',
        itemKey: 'payChannels',
        items: OrderFilterItemData.formJsonList(payChannelData),
        selectIds: payChannelSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
        allowsMultipleSelection: true,
        canClean: false,
      );
      this.dataSource.add(payChannelModel);

      /// 订单状态
      List<dynamic> orderStatusData = jsonData?['orderStatuses'] ?? [];
      String orderStatusSelectedData = this.filterParams?['statusStr'] ?? "-1";
      OrderFilterConfigModel orderStatusModel = OrderFilterConfigModel(
        title: '订单状态',
        itemKey: 'statusStr',
        items: OrderFilterItemData.formJsonList(orderStatusData),
        selectIds: orderStatusSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
        allowsMultipleSelection: true,
        canClean: false,
      );
      this.dataSource.add(orderStatusModel);

      

      /// 卡单类型
      List<dynamic> stuckData = jsonData?['exceptionTypes'] ?? [];
      String stuckSelectedData = this.filterParams?['exceptionTypes'] ?? "-2";
      OrderFilterConfigModel stuckModel = OrderFilterConfigModel(
        title: '卡单类型',
        itemKey: 'exceptionTypes',
        defaultId: -2,
        items: OrderFilterItemData.formJsonList(stuckData),
        selectIds: stuckSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
        allowsMultipleSelection: false,
        canClean: true,
      );
      this.dataSource.add(stuckModel);

      /// 订单排序
      List<dynamic> sortData = jsonData?['orderSortedList'] ?? [];
      String sortSelectedData = this.filterParams?['sorted'] ?? "1";
      OrderFilterConfigModel sortModel = OrderFilterConfigModel(
        title: '订单排序',
        itemKey: 'sorted',
        defaultId: 1,
        items: OrderFilterItemData.formJsonList(sortData),
        selectIds: sortSelectedData.split(',').map((e) => int.tryParse(e) ?? -1).toList(),
        allowsMultipleSelection: false,
        canClean: false,
      );
      this.dataSource.add(sortModel);

      /// 4.9.0版本需求去掉 订单金额/活动情况筛选
      // /// 订单金额
      // OrderFilterConfigModel amountModel = OrderFilterConfigModel(
      //   title: '订单金额',
      //   itemKey: 'amount',
      //   items: null,
      //   selectIds: [],
      //   allowsMultipleSelection: true,
      //   canClean: false,
      // );
      // this.dataSource.add(amountModel);

      // /// 活动情况
      // List<dynamic> activityData = jsonData['activities'] ?? [];
      // String activitySelectedData = this.filterParams!['yhType'] ?? "-1";
      // OrderFilterConfigModel activityModel = OrderFilterConfigModel(
      //   title: '活动情况',
      //   itemKey: 'yhType',
      //   items: OrderFilterItemData.formJsonList(activityData),
      //   selectIds:
      //       activitySelectedData.split(',').map((e) => int.parse(e)).toList(),
      //   allowsMultipleSelection: true,
      //   canClean: false,
      // );
      // this.dataSource.add(activityModel);
    }
  }

  @override
  Widget buildWidget(BuildContext context) {
    return SafeArea(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          child: Column(
            children: [
              Expanded(
                child: ListView.builder(
                  itemCount: this.dataSource.length,
                  itemBuilder: (context, index) {
                    return getIndexWidget(context, index);
                  },
                ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(15, 15, 15, 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xFFD3D3D3),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: TextButton(
                          onPressed: resetAllItem,
                          style: ButtonStyle(
                            shadowColor: MaterialStateProperty.all<Color>(Colors.transparent),
                          ),
                          child: Container(
                            child: Text(
                              '重置',
                              style: TextStyle(
                                color: Color(0xFF333333),
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xFF35C561),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child: TextButton(
                          onPressed: determineParams,
                          style: ButtonStyle(
                            shadowColor: MaterialStateProperty.all<Color>(Colors.transparent),
                          ),
                          child: Container(
                            child: Text(
                              '确定',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  // 列表中个Item
  Widget getIndexWidget(BuildContext context, int index) {
    OrderFilterConfigModel configModel = this.dataSource[index];
    switch (configModel.itemKey) {
      case 'area':
        return OrderFilterArea(
          title: configModel.title,
          name: this.filterParams!['name'] ?? "全部",
          areaChange: (id, name, isGroup) {
            if (isGroup) {
              this.filterParams!.remove('searchUserId');
              this.filterParams!['groupId'] = id ?? "";
            } else {
              this.filterParams!.remove('groupId');
              this.filterParams!['searchUserId'] = id ?? "";
            }
            this.filterParams!['name'] = name ?? "";
          },
        );
      case 'sceneType':
      case 'period':
        return OrderFilterTimeItem(
          configModel: configModel,
          startTime: this.filterParams?["startCreateTime"] ?? "",
          endTime: this.filterParams?["endCreateTime"] ?? "",
          callBack: (sceneParam) {
            this.filterParams?.addAll(sceneParam);
            if (isRefund == true) {
              this.filterParams?.removeWhere((key, value) => key == 'refundPeriod' || key == 'startRefundAuditTime' || key == 'endRefundAuditTime');
              this.configSource();
              setState(() {});
            }
          },
        );
      case 'refundPeriod':
        return OrderFilterTimeItem(
          configModel: configModel,
          startTime: this.filterParams?["startRefundAuditTime"] ?? "",
          endTime: this.filterParams?["endRefundAuditTime"] ?? "",
          callBack: (refundTimeParam) {
            /// 做参数转换
            Map<String, String?> refundParams = {};
            refundParams['refundPeriod'] = refundTimeParam['refundPeriod'];
            if (refundTimeParam.containsKey('startCreateTime') && refundTimeParam.containsKey('endCreateTime')) {
              refundParams['startRefundAuditTime'] = refundTimeParam['startCreateTime'];
              refundParams['endRefundAuditTime'] = refundTimeParam['endCreateTime'];
            }
            this.filterParams?.addAll(refundParams);
            this.filterParams?.removeWhere((key, value) => key == 'period' || key == 'startCreateTime' || key == 'endCreateTime');
            this.configSource();
            setState(() {});
          },
        );
      case 'amount':
        return OrderFilterAmount(
          startPrice: this.filterParams!["minAmount"] ?? "",
          endPrice: this.filterParams!["maxAmount"] ?? "",
          priceChange: (startPrice, endPrice) {
            if (startPrice?.isNotEmpty ?? false) {
              this.filterParams!["minAmount"] = startPrice ?? "";
            }
            if (endPrice?.isNotEmpty ?? false) {
              this.filterParams!["maxAmount"] = endPrice ?? "";
            }
          },
        );
      case 'orderBranchCode':
        return OrderFilterOperationItem(
          title: configModel.title,
          itemKey: configModel.itemKey,
          items: configModel.items,
          defaultId: configModel.defaultId,
          allowsMultipleSelection: configModel.allowsMultipleSelection,
          selectIds: configModel.selectIds,
          canClean: false,
          changeValue: (itemKey, selectedIds) {
            this.filterParams![itemKey ?? ""] = selectedIds?.map((e) {
                  return branchCodeData[++e]["code"];
                }).join(",") ??
                "";
          },
        );
      default:
        return OrderFilterOperationItem(
          title: configModel.title,
          itemKey: configModel.itemKey,
          items: configModel.items,
          defaultId: configModel.defaultId,
          allowsMultipleSelection: configModel.allowsMultipleSelection,
          selectIds: configModel.selectIds,
          canClean: false,
          changeValue: (itemKey, selectedIds) {
            this.filterParams![itemKey ?? ""] = selectedIds?.map((e) => e.toString()).join(",") ?? "";
          },
        );
    }
  }

  void resetAllItem() {
    setState(() {
      this.filterParams = {};
      this.configSource();
    });
  }

  void determineParams() {
    // 移除空值
    this.filterParams!.removeWhere((key, value) => value!.isEmpty);
    // 判断时间选项
    bool customTimePerfect = true;
    if (this.filterParams!.containsKey("startCreateTime") || this.filterParams!.containsKey("endCreateTime")) {
      if (this.filterParams!["startCreateTime"]?.isEmpty ?? true) {
        customTimePerfect = false;
      }
      if (this.filterParams!["endCreateTime"]?.isEmpty ?? true) {
        customTimePerfect = false;
      }
    }
    print(this.filterParams);
    print("-----------************------------");
    if (customTimePerfect) {
      // 回调
      Navigator.of(context).pop(this.filterParams);
    } else {
      showToast("开始时间与结束时间不能存在空值");
    }
  }

  @override
  String getTitleName() {
    return this.isRefund! ? "退款单筛选" : "订单筛选";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.none,
      rightButtons: [
        LeftButton(
            leftBtnType: LeftButtonType.close,
            onPressed: () {
              Navigator.of(context).pop({"isClose": "true"});
            }),
      ],
    );
  }
}

class OrderFilterConfigModel extends Object {
  /// 显示标题
  String? title;

  /// 标示
  String? itemKey;

  /// 数据源
  List<OrderFilterItemData>? items;

  /// 选中的ids
  List<int>? selectIds;

  /// 默认选中项
  int? defaultId;

  /// 是否支持多选
  bool? allowsMultipleSelection;

  /// 是否可清空
  bool? canClean;

  /// 时间筛选的范围
  int? intervalDay;

  OrderFilterConfigModel({
    this.title,
    this.itemKey,
    this.items,
    this.selectIds,
    this.allowsMultipleSelection,
    this.defaultId = -1,
    this.canClean,
    this.intervalDay,
  });
}
