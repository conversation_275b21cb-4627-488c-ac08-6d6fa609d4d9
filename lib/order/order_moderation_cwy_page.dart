import 'dart:io';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/photo_view/gallery_photo_view.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'bean/order_detail_list_data.dart';
import 'bean/order_detail_refund_cwy_data.dart';

class OrderModerationCwyPage extends BasePage {
  // 订单编号
  final String? orderNo;

  // oaid

  OrderModerationCwyPage({this.orderNo});

  @override
  BaseState initState() {
    return _OrderModerationCwyPageState(this.orderNo);
  }
}

class _OrderModerationCwyPageState extends BaseState {
  bool? isRecord;
  // 订单编号
  final String? orderNo;

  Map menuRefundType={
    '-6700396257248063771':'非破损非近效期',
    '-5460415865475481309':'非近效期破损',
    '-6697938055163327598':'近效期未破损',
    '9045680371352617972':'近效期且破损',
    '-1':'--'
  };

  String? mImageHost;
  List<dynamic > refundCommodities = [

  ];
  _OrderModerationCwyPageState(this.orderNo);
  OrderDetailRefundInfoCwyData? orderDetail = OrderDetailRefundInfoCwyData();
  @override
  void initState() {
    super.initState();
    // this.setOpenStatus();
    this._requestOrderDetailData();
  }

  void _requestOrderDetailData() async {
    if (mImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          setState(() {
            mImageHost = value['image_host'];
          });
        }
      });
    }
    var result =
    await Network<OrderDetailRefundInfoCwyData>(OrderDetailRefundInfoCwyData())
        .requestData(
      'worries/orderDetail',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {"id":orderNo},
    );


    if (mounted && (result.isSuccess ?? false)) {
      print("lwq");
      print(result.toJson().toString());
      setState(() {
        this.orderDetail = result;
        this.refundCommodities = this.orderDetail?.refundProductInfoDtos ?? [];
      });
    }else{
      showToast(result.msg ?? "");
    }


  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child:Column(
        children: [
          Expanded(
      child:  SingleChildScrollView(
      padding: EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // OA审核中部分
          Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10)),
            ),
            margin: EdgeInsets.only(top: 10),
            child: _buildOAAuditSection(),
          ),
          SizedBox(height: 16),
          // 退款商品部分
          Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              color:Color(0xFFFFFFFF),
            ),

            child: _buildRefundCommoditiesSection(context),
          ),
          SizedBox(height: 16),
          // 订单信息部分
          Container(
            padding: EdgeInsets.all(10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              color:Color(0xFFFFFFFF),
            ),

            child: _buildOrderInfoSection(context),
          ),

        ],
      ),
    ),
    )
        ],
      )
    );
  }

  Widget _buildOAAuditSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          orderDetail?.auditStatusName ??"",
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Text(
          '具体审核信息可在OA系统或者药帮手查看',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildRefundCommoditiesSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '退款商品(${refundCommodities.length})',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        ListView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: refundCommodities.length,
          itemBuilder: (context, index) {
            return _buildCommodityItem(refundCommodities[index],index);
          },
        ),
      ],
    );
  }

  Widget _buildCommodityItem(Map commodity,int index) {
    return Column(
      children: [
        index != 0 ? Divider() : SizedBox.shrink(),
        Row(
          children: [

            Image.network(
              (mImageHost ?? "") + (commodity["imageUrl"] ?? ""),
              width: 70,
              height: 70,
            ),
            SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [

                  Text(
                    commodity["productName"] ?? "",
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),
                  SizedBox(height: 4),
                  Text(
                    '实付金额：￥${commodity["refundFee"] ?? ""}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '退款金额：￥${commodity["productMoney"] ?? ""}',
                    style: TextStyle(
                      fontSize: 12,

                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '退款商品数量：${commodity["productNum"] ?? ""}',
                    style: TextStyle(
                      fontSize: 12,

                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: 4),

      ],
    );
  }

  Widget _buildOrderInfoSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '订单信息',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        _buildOrderInfoRow('订单编号',  this.orderDetail?.orderNo ?? "", true),
        _buildOrderInfoRow('流程编号', this.orderDetail?.processNo ?? "", true),
        _buildOrderInfoRow('申请时间', this.orderDetail?.applyTime?.toString() ?? "", false),
        _buildOrderInfoRow('是否已退回', this.orderDetail?.isRefunded ?? "", false),
        _buildOrderInfoRow('退款类型',  menuRefundType[this.orderDetail?.refundType??"-1"]??'', false),
        _buildOrderInfoRow('退款原因',  this.orderDetail?.refundReason ?? "", false),
        _buildOrderInfoRow('退款描述',  this.orderDetail?.refundExplain ?? "", false),

        Padding(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start, // This makes children align at the top
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Container(
                width: 80,
                child:  Text(
                  "上传附件",
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ),
              Expanded(
                child: SizedBox( // 限制高度
                  height: 120,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: (this.orderDetail?.attachmentUrl ??[]).length,
                    itemBuilder: (context, index) {
                      return Padding(
                        padding: EdgeInsets.all(8.0),
                          child: GestureDetector(
                            onTap: () {
                              Navigator.of(context).push(
                                MaterialPageRoute(
                                  builder: (BuildContext context) =>
                                      GalleryPhotoView(
                                        images: this.orderDetail?.attachmentUrl ?? [], //传入图片list
                                        index: index, //传入当前点击的图片的index
                                      ),
                                ),
                              );
                            },
                            child: Image.network(
                              (this.orderDetail?.attachmentUrl??[])[index],
                              width: 100,
                              height: 100,
                              fit: BoxFit.cover,
                            ),
                          ),
                      );
                    },
                  ) ,
                )
              )
            ],
          ),
        )
      ],
    );
  }

  Widget _buildOrderInfoRow(String label, String value, bool hasCopyButton) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
         Container(
           width: 80,
          child:  Text(
            label,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            ),
          ),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center, // 顶部对齐
              children: [
                Expanded( // 让文本部分可以换行
                  child: Text(
                    value,
                    style: TextStyle(fontSize: 14),
                    softWrap: true, // 允许换行
                    overflow: TextOverflow.visible, // 超出时显示
                  ),
                ),
                if (hasCopyButton)
                  Padding(
                    padding: EdgeInsets.only(left: 8), // 添加左边距
                    child: OutlinedButton(
                      onPressed: () {
                        Clipboard.setData(ClipboardData(text: value));
                        XYYContainer.toastChannel.toast("复制成功");
                      },
                      child: Text('复制', style: TextStyle(color: Colors.black)),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.grey),
                        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        minimumSize: Size(0, 0),
                        textStyle: TextStyle(fontSize: 10),
                        tapTargetSize: MaterialTapTargetSize.shrinkWrap, // 紧凑布局
                      ),
                    ),
                  )
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  String getTitleName() {
    return "超无忧售后详情";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      onBackPress(context);
    });
  }

  void onBackPress(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.maybePop(context, isRecord);
    } else {
      if (Platform.isIOS) {
        XYYContainer.bridgeCall("app_back");
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }
}
