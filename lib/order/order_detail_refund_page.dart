import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/section_group_handler/section_group_handler.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_info_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_list_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_merchant_check.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_footer.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_header.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_item.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_product.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_product_footer.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_refund_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/detail/order_detail_service_header.dart';
import 'package:XyyBeanSproutsFlutter/utils/jump_page_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OrderDetailRefundPage extends BasePage {
  // 订单编号
  final String? orderId;

  // 客户id
  final String? merchantId;

  OrderDetailRefundPage({this.orderId, this.merchantId});

  @override
  BaseState initState() {
    return _OrderDetailRefundPageState(this.orderId, this.merchantId);
  }
}

class _OrderDetailRefundPageState extends BaseState {
  /// 数据源
  OrderDetailRefundInfoData? detailData = OrderDetailRefundInfoData();

  SectionGroupHandler? _sectionHander;

  // 展开状态
  Map<int, bool> cacheOpen = {};

  // 订单编号
  final String? orderId;

  // 客户id
  final String? merchantId;

  bool? isRecord;

  // // 刷新 老豆芽没有，暂时先去掉
  // var _refreshController = EasyRefreshController();

  String? mImageHost;

  _OrderDetailRefundPageState(this.orderId, this.merchantId);

  @override
  void initState() {
    super.initState();
    // this.setOpenStatus();
    this._requestOrderDetailData();
  }

  void _requestOrderDetailData() async {
    if (mImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          setState(() {
            mImageHost = value['image_host'];
          });
        }
      });
    }
    var result =
        await Network<OrderDetailRefundInfoData>(OrderDetailRefundInfoData())
            .requestData(
      'refund/queryRefundBasic',
      contentType: RequestContentType.FORM,
      method: RequestMethod.POST,
      parameters: {"id": orderId},
    );
    if (mounted && (result.isSuccess ?? false)) {
      setState(() {
        this.detailData = result;
      });
      _requestOrderProductListData();
    } else {
      showToast(result.message ?? "");
    }
  }

  void _requestOrderProductListData() async {
    NetworkV2<OrderDetailListData>(OrderDetailListData())
        .requestDataV2("refund/queryRefundProductList",
            parameters: {"refundOrderNo": this.detailData?.refundOrderNo ?? ""},
            contentType: RequestContentType.FORM,
            method: RequestMethod.GET)
        .then((value) {
      if (mounted && value.isSuccess != null && value.isSuccess!) {
        setState(() {
          this.detailData?.packageList = value.getListData();
        });
      } else {
        showToast(value.msg ?? "");
      }
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    this._sectionHander = this.generateHandler();
    return Container(
      child: Container(
        color: Color(0xFFF7F7F8),
        child: Column(
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
              child: ListView.builder(
                itemCount: _sectionHander!.allItemCount,
                itemBuilder: (BuildContext context, int index) {
                  return _sectionHander!.cellAtIndex(index);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  SectionGroupHandler generateHandler() {
    return SectionGroupHandler(
      numberOfSections: 3,
      numberOfRowsInSection: (section) {
        /// 展开收起逻辑
        bool? isOpen = this.cacheOpen[section];
        if (isOpen != null && isOpen == false) {
          return section == 2 ? 1 : 0;
        }
        if (section == 0) {
          /// 退单信息
          return (this.detailData?.receiveModels() ?? []).length;
        }
        if (section == 2) {
          return (this.detailData?.packageList ?? []).length;
        }
        return 0;
      },
      headerForSection: (section) {
        if (section == 0) {
          return OrderDetailHeader(
            title: "退款单信息",
            isShouldOpen: true,
            isOpen: (this.cacheOpen[section] ?? true),
            clickCallback: (isOpen) {
              this.cacheOpen[section] = isOpen ?? true;
              setState(() {});
            },
          );
        }
        if (section == 1) {
          return this.detailData?.imUrl != null
              ? OrderDetailServiceHeader(
                  title: "${this.detailData?.imButtonDisplay ?? '联系客服'}",
                  clickAction: () {
                    String imUrl =
                        Uri.encodeComponent("${this.detailData?.imUrl}");
                    String routerPath =
                        "xyy://crm-app.ybm100.com/crm/web_view?url=$imUrl";
                    XYYContainer.open(routerPath);

                    /// 埋点代码
                    this.track('mc-orderdetail-contactbutton');
                  },
                )
              : Container();
        }
        if (section == 2) {
          if ((this.detailData?.packageList ?? []).length <= 0) {
            return Container();
          }
          return OrderDetailHeader(
            title: "订单商品(${(this.detailData?.packageList ?? []).length})",
          );
        }
        return Container();
      },
      cellForRowAtIndexPath: (indexPath) {
        if (indexPath.section < 2) {
          OrderEntryData model =
              this.detailData!.receiveModels()[indexPath.row];
          return OrderDetailItem(
            model: model,
            selectAction: this.selectAction,
          );
        }
        if (indexPath.section == 2) {
          OrderDetailListData model =
              this.detailData!.packageList![indexPath.row];
          return OrderDetailProduct(
            mImageHost: this.mImageHost,
            isRefund: true,
            data: model,
          );
        }
        return Container();
      },
      footerForSection: (section) {
        if (section == 2 && (this.detailData?.packageList?.length ?? 0) > 1) {
          return OrderDetailProductFooter(
            isOpen: (this.cacheOpen[section] ?? true),
            callback: (isOpen) {
              this.cacheOpen[section] = isOpen ?? true;
              setState(() {});
            },
          );
        }
        if (section == 1) {
          return Container();
        }
        return OrderDetailFooter();
      },
    );
  }

  @override
  String getTitleName() {
    return "退款订单";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      onBackPress(context);
    });
  }

  void onBackPress(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.maybePop(context, isRecord);
    } else {
      if (Platform.isIOS) {
        XYYContainer.bridgeCall("app_back");
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }

  void selectAction(OrderEntryData model) {
    if (model.title == "退单客户" && model.content != "--") {
      this.jumpMerchantInfo();
    }
  }

  void jumpMerchantInfo() async {
    jumpCustomerPageByMerchantId(this.detailData?.merchantId, canJumpPublic: false);
  }
}
