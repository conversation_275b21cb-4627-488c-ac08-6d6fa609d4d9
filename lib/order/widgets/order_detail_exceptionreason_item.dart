import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';

class OrderDetailReasonPage extends BasePage {
  final String? refuseReason;
  OrderDetailReasonPage({this.refuseReason});

  @override
  BaseState initState() {
    return _OrderDetailReasonPageState(this.refuseReason);
  }
}

class _OrderDetailReasonPageState extends BaseState {
  String? refuseReason;

  _OrderDetailReasonPageState(this.refuseReason);
  @override
  Widget buildWidget(BuildContext context) {
    return SafeArea(
      child: Container(
        color: Color(0xFFEFEFF4),
        width: double.maxFinite,
        height: double.infinity,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Container(
              margin: EdgeInsets.only(left: 10, right: 10, top: 10),
              padding:
                  EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: Colors.white,
                  border: Border.all(color: Color(0xFFE7E7E7), width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0D292933),
                      offset: Offset(0, 1),
                      blurRadius: 2,
                    )
                  ]),
              child: Text(
                refuseReason ?? "--",
                textAlign: TextAlign.left,
                style: TextStyle(
                    color: Color(0xFF292933),
                    fontSize: 14,
                    fontWeight: FontWeight.normal),
              ),
            ),
          ],
        ),
        // ),
      ),
    );
  }
  @override
  String getTitleName() {
    return "卡单原因";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      if (Navigator.canPop(context)) {
      Navigator.maybePop(context, true);
    }
    });
  }
}
