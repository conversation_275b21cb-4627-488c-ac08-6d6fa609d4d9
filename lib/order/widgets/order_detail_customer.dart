import 'package:flutter/material.dart';

class OrderDetailCustomerWidget extends StatefulWidget {
  final String? title;
  final GestureTapCallback? callBack;
  const OrderDetailCustomerWidget({this.title, this.callBack});

  @override
  _OrderDetailCustomerWidgetState createState() => _OrderDetailCustomerWidgetState();
}

class _OrderDetailCustomerWidgetState extends State<OrderDetailCustomerWidget> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.callBack!();
      },
      child: Container(
        height: 60,
        margin: const EdgeInsets.fromLTRB(15, 15, 15, 0),
        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: Colors.white,
            border: Border.all(color: Color(0xFFE7E7E7), width: 1),
            boxShadow: [
              BoxShadow(
                color: Color(0x0D292933),
                offset: Offset(0, 1.5),
                blurRadius: 2,
              )
            ]),
        child: Row(
          children: [
            //
            Image.asset("assets/images/order/order_detail_customer.png", width: 30, height: 30),
            SizedBox(
              width: 12,
            ),
            Expanded(
              child: Text(
                widget.title ?? "--",
                style: TextStyle(color: Color(0xFF292933), fontSize: 14),
              ),
            ),
            Image.asset("assets/images/order/c_arrow.png", width: 8, height: 12),
          ],
        ),
      ),
    );
  }
}
