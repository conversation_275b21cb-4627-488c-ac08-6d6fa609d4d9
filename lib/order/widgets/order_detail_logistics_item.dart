import 'package:flutter/material.dart';

class OrderDetailLogisticsItemWidget extends StatefulWidget {
  // 物流信息
  final String? logistics;
  // 物流时间
  final String? logisticsTime;
  final VoidCallback? callBack;
  OrderDetailLogisticsItemWidget({this.logistics, this.logisticsTime, this.callBack});

  @override
  _OrderDetailLogisticsItemWidgetState createState() => _OrderDetailLogisticsItemWidgetState();
}

class _OrderDetailLogisticsItemWidgetState extends State<OrderDetailLogisticsItemWidget> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        widget.callBack!();
      },
      child: Container(
        margin: const EdgeInsets.fromLTRB(15, 15, 15, 0),
        padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: Colors.white,
            border: Border.all(color: Color(0xFFE7E7E7), width: 1),
            boxShadow: [
              BoxShadow(
                color: Color(0x0D292933),
                offset: Offset(0, 1.5),
                blurRadius: 2,
              )
            ]),
        child: Column(
          children: [
            SizedBox(height: 15),
            Row(
              children: [
                Image.asset("assets/images/order/order_detail_logistics.png", width: 30, height: 30),
                SizedBox(
                  width: 12,
                ),
                Expanded(
                    child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.logistics ?? "--",
                      style: TextStyle(color: Color(0xFF292933), fontSize: 14),
                    ),
                    SizedBox(height: 2),
                    Text(
                      widget.logisticsTime ?? "--",
                      style: TextStyle(color: Color(0xFF292933), fontSize: 14),
                    ),
                  ],
                )),
                Image.asset("assets/images/order/c_arrow.png", width: 8, height: 12),
              ],
            ),
            SizedBox(
              height: 15,
            )
          ],
        ),
      ),
    );
  }
}
