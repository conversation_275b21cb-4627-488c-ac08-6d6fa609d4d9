import 'package:XYYContainer/XYYContainer.dart';
import 'package:flutter/material.dart';

typedef OrderFilterAreaValueChange = void Function(
  String? id,
  String? name,
  bool isGroup,
);

// ignore: must_be_immutable
class OrderFilterArea extends StatefulWidget {
  final String? title;
  final String? id;
  String? name;
  final bool? isGroup;
  final OrderFilterAreaValueChange? areaChange;

  OrderFilterArea({
    this.title,
    this.id,
    this.name,
    this.isGroup,
    this.areaChange,
  });

  @override
  State<StatefulWidget> createState() {
    return OrderFilterAreaState();
  }
}

class OrderFilterAreaState extends State<OrderFilterArea> {
  OrderFilterAreaState();

  @override
  Widget build(Object context) {
    return GestureDetector(
      onTap: () {
        XYYContainer.open(
            'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=false',
            callback: (params) {
          setState(() {
            widget.name = params!["name"];
          });
          widget.areaChange!(
              params!["id"], params["name"], params["isgroup"] == 'true');
        });
      },
      child: Container(
        padding: EdgeInsets.only(left: 15, right: 15),
        height: 50,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Row(
              children: [
                Text(
                  widget.title!,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF333333),
                  ),
                ),
                Expanded(
                  child: Text(
                    widget.name ?? "全部",
                    textAlign: TextAlign.end,
                    style: TextStyle(
                      fontSize: 13,
                      color: Color(0xFF666666),
                    ),
                  ),
                ),
                SizedBox(width: 8),
                Image.asset(
                  'assets/images/order/order_filter_arrow.png',
                  width: 7,
                  height: 12,
                ),
              ],
            ),
            Container(
              decoration: BoxDecoration(
                color: Color(0xFFE1E1E5),
              ),
              height: 1,
            ),
          ],
        ),
      ),
    );
  }
}
