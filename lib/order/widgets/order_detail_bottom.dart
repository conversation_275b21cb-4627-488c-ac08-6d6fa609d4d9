import 'package:flutter/material.dart';

class OrderDetailBottomWidget extends StatefulWidget {
  final int? status;
  // 目前用不到
  // final String time;
  final ValueChanged<int>? callBack;
  final int? isFollow;
  const OrderDetailBottomWidget({this.status, this.isFollow, this.callBack});

  @override
  _OrderDetailBottomWidgetState createState() =>
      _OrderDetailBottomWidgetState();
}

class _OrderDetailBottomWidgetState extends State<OrderDetailBottomWidget> {
  // 是否展示时间
  bool hiddenTime = false;
  // 是否展示已跟进
  bool hiddenFollow = false;
  // 是否展示重新下单
  bool hiddenAgain = false;
  // 是否展示联系客户
  bool hiddenLink = false;
  // 已跟进状态
  order_detail_bottom_button_type? isFollow;

  @override
  void initState() {
    super.initState();
  }

  void setShowStatus() {
    switch (widget.status) {
      case 1:
        this.hiddenTime = false;
        this.hiddenLink = false;
        this.hiddenAgain = true;
        this.hiddenFollow = true;
        break;
      case 10:
        this.hiddenTime = false;
        this.hiddenLink = false;
        this.hiddenAgain = true;
        this.hiddenFollow = true;
        break;
      case 2:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = true;
        this.hiddenFollow = true;
        break;
      case 3:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = true;
        this.hiddenFollow = true;
        break;
      case 7:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = true;
        this.hiddenFollow = true;
        break;
      case 91:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = true;
        this.hiddenFollow = true;
        break;
      case 4:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = false;
        this.hiddenFollow = false;
        break;
      case 5:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = false;
        this.hiddenFollow = false;
        break;
      case 6:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = false;
        this.hiddenFollow = false;
        break;
      default:
        this.hiddenTime = true;
        this.hiddenLink = false;
        this.hiddenAgain = true;
        this.hiddenFollow = true;
        break;
    }
    if (widget.isFollow == 1) {
      this.isFollow = order_detail_bottom_button_type.dim;
    } else {
      this.isFollow = order_detail_bottom_button_type.normal;
    }
  }

  @override
  Widget build(BuildContext context) {
    this.setShowStatus();
    return Container(
      height: 50,
      child: Column(
        children: [
          Container(
            height: 1,
            color: Color(0xFFF6F6F6),
          ),
          Expanded(
            child: Row(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                timeText("", !this.hiddenTime),
                buttonWidget("已跟进", this.isFollow, !this.hiddenFollow, () {
                  if (this.isFollow == order_detail_bottom_button_type.dim) {
                    return;
                  }
                  widget.callBack!(0);
                }),
                SizedBox(width: 10),
                buttonWidget("重新下单", order_detail_bottom_button_type.normal,
                    !this.hiddenAgain, () {
                  widget.callBack!(1);
                }),
                SizedBox(width: 10),
                buttonWidget("联系客户", order_detail_bottom_button_type.bright,
                    !this.hiddenLink, () {
                  widget.callBack!(2);
                }),
                SizedBox(width: 15),
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget timeText(String time, bool visible) {
    return Visibility(
      visible: visible,
      child: Expanded(
        child: Container(
          padding: const EdgeInsets.only(left: 10, right: 10),
          child: Text(
            time,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: Color(0xFFFA5741),
                fontWeight: FontWeight.w500,
                fontSize: 14),
          ),
        ),
      ),
    );
  }

  Widget buttonWidget(String title, order_detail_bottom_button_type? type,
      bool visible, VoidCallback callBcak) {
    late Color borderColor; //  边框颜色
    Color? textColor; // 文字颜色
    Color? backColor; // 背景颜色
    FontWeight? fontWight; // 字体
    switch (type) {
      case order_detail_bottom_button_type.bright:
        borderColor = Color(0x8000B377);
        textColor = Color(0xFF00B377);
        backColor = Color(0xFFFFFFFF);
        fontWight = FontWeight.w500;
        break;
      case order_detail_bottom_button_type.normal:
        borderColor = Color(0xFFCACACA);
        textColor = Color(0xFF292933);
        backColor = Color(0xFFFFFFFF);
        fontWight = FontWeight.normal;
        break;
      case order_detail_bottom_button_type.dim:
        borderColor = Color(0xFFCACACA);
        textColor = Color(0xFF9494A6);
        backColor = Color(0xFFF6F6F6);
        fontWight = FontWeight.normal;
        break;
      default:
        break;
    }
    return Visibility(
      visible: visible,
      child: GestureDetector(
        onTap: callBcak,
        child: Container(
          padding: const EdgeInsets.fromLTRB(10, 8, 10, 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: backColor,
            border: Border.all(color: borderColor, width: 0.5),
          ),
          child: Text(
            title,
            style: TextStyle(
                color: textColor, fontWeight: fontWight, fontSize: 13),
          ),
        ),
      ),
    );
  }
}

enum order_detail_bottom_button_type {
  normal,
  bright,
  dim,
}
