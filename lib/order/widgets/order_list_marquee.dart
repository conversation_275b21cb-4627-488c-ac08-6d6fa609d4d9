import 'package:flutter/material.dart';
import 'package:marquee/marquee.dart';

// ignore: must_be_immutable
class OrderListMarquee extends StatefulWidget {
  String? tips;

  OrderListMarquee({Key? key, this.tips}) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _OrderListMarqueeState();
  }
}

class _OrderListMarqueeState extends State<OrderListMarquee>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Marquee(
      text: (widget.tips != null && widget.tips!.length > 0)
          ? widget.tips!
          : '此页面订单数据不是业绩口径，请勿与业绩进行比对。业绩统计规则请咨询省区销运。',
      style: TextStyle(
        fontSize: 12,
        color: Color(0xFFFF2121),
        fontWeight: FontWeight.normal,
      ),
      scrollAxis: Axis.horizontal,
      crossAxisAlignment: CrossAxisAlignment.center,
      blankSpace: 50.0,
      velocity: 50.0,
      pauseAfterRound: Duration(seconds: 1),
      startPadding: 10.0,
      accelerationDuration: Duration(seconds: 1),
      accelerationCurve: Curves.linear,
      decelerationDuration: Duration(milliseconds: 500),
      decelerationCurve: Curves.easeOut,
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
