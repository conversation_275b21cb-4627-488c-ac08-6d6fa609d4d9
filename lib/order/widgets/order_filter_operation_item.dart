import 'package:XyyBeanSproutsFlutter/order/bean/order_filter_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_select_view.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class OrderFilterOperationItem extends StatelessWidget {
  /// 标题
  final String? title;

  /// 数据源
  final List<OrderFilterItemData>? items;

  /// item对应的Key值
  final String? itemKey;

  /// 选中的ids
  List<int>? selectIds;

  final int? defaultId;

  final OrderFilterSelectViewController? controller;

  /// 是否支持多选
  final bool? allowsMultipleSelection;

  /// 是否可清空
  final bool? canClean;

  /// 选择回调
  final OrderFilterViewChangeValue? changeValue;

  OrderFilterOperationItem({
    this.title,
    this.itemKey,
    this.items,
    this.selectIds,
    this.allowsMultipleSelection,
    this.canClean,
    this.changeValue,
    this.controller,
    this.defaultId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            this.title!,
            style: TextStyle(
              fontSize: 15,
              color: Color(0xFF333333),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(
            height: 10,
          ),
          OrderFilterSelectView(
            itemKey: this.itemKey,
            items: this.items,
            defaultId: this.defaultId,
            controller:
                OrderFilterSelectViewController.fromSelectIds(this.selectIds),
            allowsMultipleSelection: this.allowsMultipleSelection,
            canClean: this.canClean,
            changeValue: this.changeValue,
          ),
        ],
      ),
    );
  }
}
