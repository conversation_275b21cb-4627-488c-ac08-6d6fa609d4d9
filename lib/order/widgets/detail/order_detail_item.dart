import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/photo_view/gallery_photo_view.dart';
import 'package:XyyBeanSproutsFlutter/common/photo_view/photo_gallery_page.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_info_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/common_alert_dialog.dart';
import 'package:flutter/cupertino.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:intl/intl.dart';

class OrderDetailItem extends StatefulWidget {
  final OrderEntryData model;
  final ValueChanged<OrderEntryData> selectAction;
  final VoidCallback? refreshCallback;

  OrderDetailItem(
      {required this.model, required this.selectAction, this.refreshCallback});
  @override
  _OrderDetailItemState createState() => _OrderDetailItemState();
}

class _OrderDetailItemState extends State<OrderDetailItem> {
  String sex = '1';
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15),
      padding: EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
      color: Color(0xFFFFFFFF),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 75,
            child: Text(
              widget.model.title ?? "--",
              style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 15,
                height: 1.1,
              ),
            ),
          ),
          SizedBox(width: 20),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      widget.selectAction(this.widget.model);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: Container(
                      constraints: BoxConstraints(minHeight: 20),
                      child:widget.model.isImageItem?SizedBox( // 限制高度
                        height: 120, // 图片高度 100 + 上下 padding 8*2
                        child: ListView.builder(
                          scrollDirection: Axis.horizontal,
                          itemCount: widget.model.imageList?.length ?? 0,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: EdgeInsets.all(8.0),
                              child: GestureDetector(
                                onTap: () {
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (BuildContext context) =>
                                          GalleryPhotoView(
                                            images: widget.model.imageList!, //传入图片list
                                            index: index, //传入当前点击的图片的index
                                          ),
                                    ),
                                  );
                                },
                                child: Image.network(
                                  widget.model.imageList![index],
                                  width: 100,
                                  height: 100,
                                  fit: BoxFit.cover,
                                ),
                              ),
                            );
                          },
                        ),
                      ): RichText(
                        text: TextSpan(
                          text: widget.model.content ?? "--",
                          style: TextStyle(
                            color: Color(widget.model.textColor ?? 0xFF292933),
                            fontSize: 15,
                            height: 1.1,
                          ),
                          children: [
                            TextSpan(
                              text: widget.model.secondContent ?? "",
                              style: TextStyle(
                                color: Color(
                                    widget.model.secondColor ?? 0xFF292933),
                                fontSize: 15,
                                height: 1.1,
                              ),
                            ),
                          ],
                        ),
                        maxLines: (widget.model.title == "卡单原因") ? 2 : null,
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.model.canCopy,
                  child: Container(
                    child: Container(
                      margin: EdgeInsets.only(left: 20),
                      padding: EdgeInsets.fromLTRB(3, 1, 3, 1),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(1),
                        color: Colors.white,
                        border:
                        Border.all(color: Color(0xFF7fd9bb), width: 0.5),
                      ),
                      child: GestureDetector(
                        onTap: () {
                          Clipboard.setData(
                              ClipboardData(text: widget.model.content));
                          XYYContainer.toastChannel
                              .toast("${widget.model.title}复制成功");
                        },
                        child: Text(
                          "复制",
                          style: TextStyle(
                            color: Color(0xFF00B377),
                            fontSize: 11,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.model.isLong,
                  child: Container(
                    child: Container(
                      margin: EdgeInsets.only(left: 20),
                      padding: EdgeInsets.fromLTRB(3, 1, 3, 1),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(1),
                        color: Colors.white,
                        border:
                        Border.all(color: Color(0xFF7fd9bb), width: 0.5),
                      ),
                      child: GestureDetector(
                        onTap: () {
                          this.dialogOpen(widget.model.orderNo,widget.model.remainingNumber);
                        },
                        child: Text(
                          "延长",
                          style: TextStyle(
                            color: Color(0xFF00B377),
                            fontSize: 11,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Visibility(
                  visible: widget.model.canModifyPayType,
                  child: Container(
                    child: Container(
                      margin: EdgeInsets.only(left: 20),
                      padding: EdgeInsets.fromLTRB(3, 1, 3, 1),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(1),
                        color: Colors.white,
                        border:
                        Border.all(color: Color(0xFF7fd9bb), width: 0.5),
                      ),
                      child: GestureDetector(
                        onTap: () {
                          this.changePayDialog(widget.model.orderNo,widget.model.newPayType,widget.model.newPayChannel,widget.model.content,widget.model.payChannelName,widget.model.newPayTypeName,widget.model.newPayChannelName);
                        },
                        child: Text(
                          "修改",
                          style: TextStyle(
                            color: Color(0xFF00B377),
                            fontSize: 11,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String getNewDate(val, day) {
    String dateString = val;
    DateTime dateTime = DateTime.parse(dateString);
    DateTime newDateTime = dateTime.add(Duration(days: day));
    String newDateString = newDateTime.toIso8601String();
    // 打印结果
    // print("原始日期: $dateString");
    // print("新日期: $newDateString");
    var formatter = DateFormat("yyyy-MM-dd HH:mm:ss");
    String formattedNewDateTimeString = formatter.format(newDateTime);
    return formattedNewDateTimeString;
  }

  void dialogOpen(orderNo,num) {
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          return CupertinoAlertDialog(
            title: ConstrainedBox(
              constraints: BoxConstraints(),
              // 设置最大宽度
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text('延长支付时间',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontWeight: FontWeight.w500, fontSize: 20)),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      new Container(
                        child: Text('时间延长仅剩'+num.toString()+'次，请谨慎选择',
                            textAlign: TextAlign.left,
                            style: TextStyle(fontSize: 14)),
                        margin: const EdgeInsets.all(5.0),
                        padding: const EdgeInsets.all(5.0),
                      ),
                    ],
                  ),
                  // Text('2024-07-06 10：55：45',
                  //     textAlign: TextAlign.center,
                  //     style:
                  //         TextStyle(fontWeight: FontWeight.w900, fontSize: 16)),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Radio(
                        // 按钮的值
                        value: '1',
                        // 改变事件
                        onChanged: (value) {
                          state(() {
                            this.sex = value as String;
                          });
                        },

                        // 按钮组的值
                        groupValue: this.sex,
                        activeColor: Colors.grey,
                      ),

                      Text(
                        "24小时",
                        style: TextStyle(fontSize: 14),
                      ),
                      const SizedBox(width: 25), // 添加两组之间的间距
                      Radio(
                          value: '2',
                          onChanged: (value) {
                            state(() {
                              this.sex = value as String;
                            });
                          },
                          groupValue: this.sex,
                          activeColor: Colors.grey),
                      Text(
                        "48小时",
                        style: TextStyle(fontSize: 14),
                      )
                    ],
                  ),
                ],
              ),
            ),
            actions: [
              CupertinoDialogAction(
                child: Text("确定", style: TextStyle(color: Colors.green)),
                onPressed: () async {
                  var userInfo = await UserInfoUtil.getUserInfo();
                  var result =
                      await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2())
                          .requestDataV2(
                    '/order/delay/expire/date',
                    parameters: {"orderNo": orderNo, "delayLevel": this.sex},
                    method: RequestMethod.POST,
                    contentType: RequestContentType.FORM,
                  );
                  if (result.status != 'failure') {
                    XYYContainer.toastChannel.toast("支付过期时间已延长");
                    // setState(() {
                    //   widget.model.content =
                    //       getNewDate(widget.model.content, int.parse(this.sex));
                    //   widget.model.isLong = false;
                    // });
                    widget.refreshCallback!();
                    Navigator.of(context).pop();
                  } else {
                    // XYYContainer.toastChannel.toast(result.errorMsg ?? "错误");
                  }
                },
              ),
              CupertinoDialogAction(
                child: Text("取消", style: TextStyle(color: Colors.black)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
      },
    );
  }
  void changePayDialog(orderNo,payType,payChannel,orderPayType,orderPayChannel,payTypeName,payChannelName){
    showCupertinoDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          return CupertinoAlertDialog(
            title: ConstrainedBox(
              constraints: BoxConstraints(),
              // 设置最大宽度
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text('修改订单支付方式',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                              fontWeight: FontWeight.w400, fontSize: 18)),
                    ],
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 20),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 100,
                          child: Text(
                            " 订单编号:",
                            style: TextStyle(
                                fontWeight: FontWeight.w300, fontSize: 14),
                          ),
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerLeft, // 左对齐
                            child:Text(
                              orderNo,
                              style: TextStyle(
                                  fontWeight: FontWeight.w300, fontSize: 14),
                              softWrap: true,
                              textAlign: TextAlign.left,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 5),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 100,
                          child: Text(
                            "原支付渠道:",
                            style: TextStyle(
                                fontWeight: FontWeight.w300, fontSize: 14),
                          ),
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerLeft, // 左对齐
                            child:Text(
                              orderPayChannel != "--" ? (orderPayType+"-"+orderPayChannel):orderPayType,
                              style: TextStyle(
                                  fontWeight: FontWeight.w300, fontSize: 14),
                              softWrap: true,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 5),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 100,
                          child: Text(
                            "新支付渠道:",
                            style: TextStyle(
                                fontWeight: FontWeight.w300, fontSize: 14),
                          ),
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.centerLeft, // 左对齐
                            child:Text(
                              payChannelName != "--" ? (payTypeName+"-"+payChannelName):payTypeName,
                              style: TextStyle(
                                  fontWeight: FontWeight.w300, fontSize: 14),
                              softWrap: true,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(top: 20),
                    child: Row(
                      children: [
                        Expanded(
                          child: Text(
                            "一笔订单只能做一次调整，请谨慎操作",
                            style: TextStyle(
                                fontWeight: FontWeight.w300, fontSize: 14),
                            softWrap: true,
                          ),
                        ),
                      ],
                    ),
                  )
                  // Text('2024-07-06 10：55：45',
                  //     textAlign: TextAlign.center,
                  //     style:
                  //         TextStyle(fontWeight: FontWeight.w900, fontSize: 16)),

                ],
              ),
            ),
            actions: [
              CupertinoDialogAction(
                child: Text("保存", style: TextStyle(color: Colors.green)),
                onPressed: () async {
                  print(orderNo+"jtt");
                  var result =
                  await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2())
                      .requestDataV2(
                    '/order/modifyPayType',
                    parameters: {"orderNo": orderNo,"payType":payType,"payChannel":payChannel},
                    method: RequestMethod.GET,
                    contentType: RequestContentType.FORM,
                  );
                  if (result.status == 'success') {
                    Navigator.of(context).pop();
                    widget.refreshCallback!();
                    XYYContainer.toastChannel.toast("订单方式修改成功");
                  } else {
                    XYYContainer.toastChannel.toast(result.errorMsg ?? "错误");
                  }



                },
              ),
              CupertinoDialogAction(
                child: Text("取消", style: TextStyle(color: Colors.black)),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        });
      },
    );
  }
}

class OrderDetailImageItem extends StatelessWidget {
  final OrderEntryData model;

  final ValueChanged<int> imageOnTap;

  OrderDetailImageItem({required this.model, required this.imageOnTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(left: 15, right: 15),
      padding: EdgeInsets.only(left: 10, right: 10, top: 5, bottom: 5),
      color: Color(0xFFFFFFFF),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            child: Text(
              model.title ?? "--",
              style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 15,
                height: 1.1,
              ),
            ),
          ),
          SizedBox(height: 5),
          Row(
            children: this.getImageWidgets(context),
          ),
        ],
      ),
    );
  }

  List<Widget> getImageWidgets(BuildContext context) {
    if (model.imageList == null) {
      return [];
    }
    List<Widget> widgetList = [];
    for (int index = 0; index < 3; index++) {
      if (index < model.imageList!.length) {
        GalleryItem item =
            GalleryItem(id: "tag$index", imageUrl: model.imageList![index]);
        widgetList.add(
          Expanded(
            child: AspectRatio(
              aspectRatio: 1.0,
              child: GalleryItemThumbnail(
                onTap: () {
                  this.imageOnTap(index);
                },
                galleryItem: item,
              ),
            ),
          ),
        );
      } else {
        widgetList.add(Expanded(child: Container()));
      }
      if (index != 2) {
        widgetList.add(SizedBox(width: 10));
      }
    }
    return widgetList;
  }
}
