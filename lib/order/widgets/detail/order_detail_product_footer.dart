import 'package:flutter/material.dart';

class OrderDetailProductFooter extends StatelessWidget {
  final bool? isOpen;
  final ValueChanged<bool?>? callback;

  OrderDetailProductFooter({this.isOpen, this.callback});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        bool? currentOpen = true;
        if (isOpen != null) {
          currentOpen = !this.isOpen!;
        }
        if (this.callback != null) {
          this.callback!(currentOpen);
        }
      },
      child: Container(
        margin: EdgeInsets.only(left: 15, right: 15, bottom: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(2),
            bottomRight: Radius.circular(2),
          ),
          color: Color(0xFFFFFFFF),
        ),
        height: 40,
        child: Column(
          children: [
            Container(
              color: Color(0xFFF7F7F8),
              height: 1,
            ),
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    (this.isOpen ?? true) ? "点击收起部分商品" : "点击展开全部商品",
                    style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF00B377),
                    ),
                  ),
                  Image.asset(
                    (this.isOpen ?? true)
                        ? 'assets/images/order/order_up_green_arrow.png'
                        : 'assets/images/order/order_down_green_arrow.png',
                    width: 10,
                    height: 10,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
