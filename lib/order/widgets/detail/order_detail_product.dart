import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_list_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class OrderDetailProduct extends StatelessWidget {
  //是不是母单
  final int? isParent;
  final String? mImageHost;
  //是不是退款单
  final bool? isRefund;
  final OrderDetailListData? data;

  OrderDetailProduct({
    this.isParent,
    this.mImageHost,
    this.isRefund,
    this.data,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      margin: EdgeInsets.only(left: 15, right: 15),
      padding: EdgeInsets.only(right: 10),
      child: Column(
        children: [
          // 如果是同一个 merchantId 的第一个商品，显示商家信息
          if (data?.isFirstOfMerchant == 1 && this.isParent == 1)
            Container(
              padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    data?.companyName ?? '',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      color: Color(0xFF292933),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      if (data?.imUrl != null) {
                        String imUrl = Uri.encodeComponent("${data?.imUrl}");
                        String routerPath = "xyy://crm-app.ybm100.com/crm/web_view?url=$imUrl";
                        XYYContainer.open(routerPath);
                      }
                    },
                    child: Row(
                      children: [
                        Image.asset(
                          'assets/images/order/order_detail_service_icon.png',
                          width: 18,
                          height: 18,
                        ),
                        SizedBox(width: 4),
                        Text(
                          data?.imButtonDisplay ?? '联系商家',
                          style: TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          Row(
            mainAxisSize: MainAxisSize.max,
            children: [
              Stack(
                children: [
                  Container(
                    margin: const EdgeInsets.only(left: 10, top: 15, right: 4),
                    child: ImageWidget(
                      url: (mImageHost ?? "") + (data?.imageUrl ?? ""),
                      w: 90,
                      h: 90,
                      defImagePath: "assets/images/order/icon_load_failed.png",
                    ),
                  ),
                  // Visibility(
                  //   visible: "${data?.isHighGross}" == "3",//甄选品
                  //   child: Transform.scale(
                  //     scale: 1.5,
                  //     alignment: Alignment.topLeft,
                  //     child: Image.asset(
                  //       'assets/images/commodity/isHighGrossIcon.jpg',
                  //       width: 35,
                  //       height: 13,
                  //     ),
                  //   ),
                  // ),
                  Positioned(
                    left: 5,
                    top: 5,
                    child: Row(
                      children: [
                        Visibility(
                          visible: data?.isShowHighGross ?? false,
                          child: Transform.scale(
                            scale: 1.5,
                            alignment: Alignment.topLeft,
                            child: Image.asset(
                              'assets/images/order/order_detail_height_icon.png',
                              width: 35,
                              height: 13,
                            ),
                          ),
                        ),
                        Visibility(
                          visible: data?.isShowHighGross ?? false,
                          child: SizedBox(width: 12),
                        ),
                        Visibility(
                          visible: data?.isShowControl ?? false,
                          child: Transform.scale(
                            scale: 1.5,
                            alignment: Alignment.topLeft,
                            child: Image.asset(
                              'assets/images/order/order_detail_control_icon.png',
                              width: 35,
                              height: 13,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: "${data?.isHighGross}" == "3",//甄选品
                    child: Positioned(
                      top: 7,
                      left: 10,
                      child: Image.asset(
                        'assets/images/commodity/isHighGrossIcon.jpg',
                        width: 30,
                        height: 40,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(width: 4),
              Expanded(
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: 10,
                    ),
                    GestureDetector(
                      onTap: () {
                        Clipboard.setData(ClipboardData(text: data?.productName ?? "--"));
                        XYYContainer.toastChannel.toast('复制成功');
                      },
                      child: Text(
                        data?.productName ?? "--",
                        overflow: TextOverflow.ellipsis,
                        softWrap: true,
                        maxLines: 2,
                        style: TextStyle(color: Color(0xFF292933), fontSize: 14),
                      ),
                    ),
                    SizedBox(height: 3),
                    Text(
                      "¥${data?.productPrice ?? "0.00"} ",
                      style: TextStyle(color: Color(0xFF676773), fontSize: 15),
                    ),
                    SizedBox(height: 3),
                    Text(
                      "规格: ${data?.spec ?? ""}",
                      style: TextStyle(color: Color(0xFF9494A6), fontSize: 11),
                    ),
                    SizedBox(height: 3),
                    Text(
                      "厂家: ${data?.manufacturer ?? ""}",
                      style: TextStyle(color: Color(0xFF9494A6), fontSize: 11),
                    ),
                    SizedBox(height: 4),
                    Container(
                      child: RichText(
                        softWrap: true,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "小计:",
                              style: TextStyle(
                                color: Color(0xFF292933),
                                fontSize: 11,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                            TextSpan(
                              text: (isRefund ?? false) ? "￥${data?.refundFee.toString()}" : "￥${data?.subTotal.toString()}",
                              style: TextStyle(
                                color: Color(0xFF666666),
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 1),
                    Container(
                      child: RichText(
                        softWrap: true,
                        text: TextSpan(
                          children: [
                            TextSpan(
                              text: "实付价",
                              style: TextStyle(
                                color: Color(0xFF666666),
                                fontSize: 12,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                            TextSpan(
                              text: ":",
                              style: TextStyle(
                                color: Color(0xFF666666),
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                            TextSpan(
                              text: data?.purchasePrice == null ? "--" : "¥${data?.purchasePrice}",
                              style: TextStyle(
                                color: Color(0xFFFE3D3D),
                                fontSize: 17,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(right: 3),
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 40, left: 15),
                      child: Text(
                        (isRefund ?? false) ? "X${data?.refundProductAmount}" : "X${data?.productAmount}",
                        textAlign: TextAlign.right,
                        style: TextStyle(
                          color: Color(0xFF9494A6),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 25,
                    ),
                    Visibility(
                      visible: (isRefund ?? false),
                      child: Container(
                        child: Text(
                          data?.statusStr ?? "",
                          textAlign: TextAlign.right,
                          style: TextStyle(
                            color: Color(_getrefundStatusTextColor()),
                            fontSize: 14,
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 15),
          Divider(
            color: Color(0xFFF6F6F6),
            height: 0.5,
            indent: 104,
            endIndent: 0,
          )
        ],
      ),
    );
  }

  int _getrefundStatusTextColor() {
    switch (data?.statusStr) {
      case "退款审核中":
        return 0xFFFF7200;
      case "退款待审核":
        return 0xFFFF2121;
      default:
        return 0xFF9494A6;
    }
  }
}
