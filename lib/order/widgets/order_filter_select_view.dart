import 'package:XyyBeanSproutsFlutter/order/bean/order_filter_item_data.dart';
import 'package:flutter/material.dart';

typedef OrderFilterViewChangeValue = void Function(
  String? itemKey,
  List<int>? selectIds,
);

// ignore: must_be_immutable
class OrderFilterSelectView extends StatefulWidget {
  /// 数据源
  final List<OrderFilterItemData>? items;

  /// item对应的Key值
  final String? itemKey;

  /// 选中的ids
  List<int>? selectIds;

  /// 不这样写好像也可以更新布局。先这样吧 懒的改回去了
  OrderFilterSelectViewController? controller;

  /// 是否支持多选
  final bool? allowsMultipleSelection;

  /// 是否可清空
  final bool? canClean;

  /// 默认选中项
  final int? defaultId;

  /// 选择回调
  final OrderFilterViewChangeValue? changeValue;

  OrderFilterSelectView({
    this.itemKey,
    this.items,
    this.controller,
    this.allowsMultipleSelection = false,
    this.changeValue,
    this.canClean = false,
    this.defaultId = -1,
  });

  @override
  State<StatefulWidget> createState() {
    return OrderFilterSelectViewState(
      items: this.items,
      itemKey: this.itemKey,
      allowsMultipleSelection: this.allowsMultipleSelection,
      changeValue: this.changeValue,
      canClean: this.canClean,
      defaultId: this.defaultId,
    );
  }
}

class OrderFilterSelectViewState extends State<OrderFilterSelectView> {
  /// 数据源
  List<OrderFilterItemData>? items;

  /// item对应的Key值
  String? itemKey;

  List<int>? selectIds;

  /// 是否支持多选
  bool? allowsMultipleSelection;

  /// 是否可清空
  bool? canClean;

  /// 默认选中项
  int? defaultId;

  /// 选择回调
  OrderFilterViewChangeValue? changeValue;

  OrderFilterSelectViewState({
    this.itemKey,
    this.items,
    this.allowsMultipleSelection = false,
    this.changeValue,
    this.defaultId,
    this.canClean = false,
  });

  @override
  void initState() {
    super.initState();
    if (widget.controller == null) {
      widget.controller = OrderFilterSelectViewController.fromSelectIds([]);
    }
    this.selectIds = widget.controller!.value;
  }

  @override
  Widget build(BuildContext context) {
    /// 每次刷新时获取当前的选中id
    this.selectIds = widget.controller!.value;

    List<Widget> itemWidget = [];
    for (OrderFilterItemData item in items!) {
      itemWidget.add(ValueListenableBuilder(
        valueListenable: widget.controller!,
        builder: (BuildContext context, List<int>? selectIds, Widget? child) {
          return OrderFilterSelectItem(
            content: item.value,
            id: item.id,
            isSelected: selectIds!.contains(item.id),
            selectCall: selectedAction,
            unSelectCall: unSelectedAction,
          );
        },
      ));
    }
    return Container(
      child: Wrap(
        spacing: 10,
        runSpacing: 10,
        children: itemWidget,
      ),
    );
  }

  void selectedAction(int? id) {
    // upgrade_2.0 guanchong 这里的id可能为空，如果为空则不进行处理，理论上一般不会为空
    if (id == null) {
      return;
    }

    /// 允许多选 并且ID不为-1（即全部选项）
    if (this.allowsMultipleSelection! && id != -1) {
      /// 如果当前选项包括-1（全部）则移除
      if (this.selectIds!.contains(-1)) {
        this.selectIds!.remove(-1);
      }
      this.selectIds!.add(id);
    } else {
      this.selectIds = [id];
    }
    setState(() {});
    widget.controller!.changeSelected(selectIds);
    this.callChange();
  }

  void unSelectedAction(int? id) {
    // upgrade_2.0 guanchong 这里的id可能为空，如果为空则不进行处理，理论上一般不会为空
    if (id == null) {
      return;
    }
    if (this.canClean!) {
      if (this.selectIds!.contains(id)) {
        this.selectIds!.remove(id);
      }
    } else {
      /// 如果反选为全部选项 则不处理
      if (id == -1) {
        return;
      }
      if (this.selectIds!.contains(id)) {
        this.selectIds!.remove(id);
      }
      // 反选如果为空 则选择默认选项
      if (this.selectIds!.length <= 0) {
        this.selectIds = [this.defaultId ?? -1];
      }
    }
    widget.controller!.changeSelected(selectIds);
    this.callChange();
  }

  void callChange() {
    this.changeValue!(this.itemKey, this.selectIds);
  }
}

class OrderFilterSelectItem extends StatelessWidget {
  final String? content;
  final int? id;
  final bool isSelected;
  final ValueChanged<int?>? selectCall;
  final ValueChanged<int?>? unSelectCall;

  OrderFilterSelectItem({
    this.content,
    this.id,
    this.isSelected = false,
    this.selectCall,
    this.unSelectCall,
  });

  @override
  Widget build(Object context) {
    return GestureDetector(
      onTap: () {
        if (this.isSelected) {
          this.unSelectCall!(this.id);
        } else {
          this.selectCall!(this.id);
        }
      },
      child: IntrinsicWidth(
        child: Container(
          padding: EdgeInsets.fromLTRB(12, 5, 12, 5),
          decoration: BoxDecoration(
            color: this.isSelected ? Color(0xFFFFFFFF) : Color(0xFFF0F0F0),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: this.isSelected ? Color(0xFF35C561) : Color(0xFFF0F0F0),
              width: 1,
            ),
          ),
          constraints: BoxConstraints(
            minWidth: 63,
            minHeight: 30,
          ),
          child: Center(
            child: Text(
              this.content!,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 13,
                fontWeight: this.isSelected ? FontWeight.w500 : FontWeight.normal,
                color: this.isSelected ? Color(0xFF35C561) : Color(0xFF666666),
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class OrderFilterSelectViewController extends ValueNotifier<List<int>?> {
  OrderFilterSelectViewController(List<int> value) : super(value);

  OrderFilterSelectViewController.fromSelectIds(List<int>? selectIds)
      : super(selectIds ?? []);

  void changeSelected(List<int>? selectIds) {
    this.value = selectIds;
    notifyListeners();
  }
}
