import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'fetch_recorder_bean.g.dart';

@JsonSerializable()
class FetchReorderBean extends BaseModel<FetchReorderBean> {
  String? shopCode;

  FetchReorderBean({this.shopCode});

  @override
  FetchReorderBean fromJsonMap(Map<String, dynamic>? json) {
    return FetchReorderBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FetchReorderBeanToJson(this);
  }

  factory FetchReorderBean.fromJson(Map<String, dynamic> json) =>
      _$FetchReorderBeanFromJson(json);
}
