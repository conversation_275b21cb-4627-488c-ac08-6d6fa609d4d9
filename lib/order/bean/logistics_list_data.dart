import 'package:XyyBeanSproutsFlutter/Utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'logistics_list_data.g.dart';

@JsonSerializable()
class LogisticsListData extends BaseModel<LogisticsListData> {
  String? description;
  int? deliveryTime;

  LogisticsListData({this.description, this.deliveryTime});

  factory LogisticsListData.fromJson(Map<String, dynamic> json) =>
      _$LogisticsListDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return LogisticsListData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LogisticsListDataToJson(this);
  }
}
