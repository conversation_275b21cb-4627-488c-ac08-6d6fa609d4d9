import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'preferential_data.g.dart';

@JsonSerializable()
class PreferentialData extends BaseModelV2<PreferentialData> {
  String? title; // 标题
  String? preferential; // 优惠


  PreferentialData();


  factory PreferentialData.fromJson(Map<String, dynamic> json) =>
      _$PreferentialDataFromJson(json);

  @override
  PreferentialData fromJsonMap(Map<String, dynamic>? json) {
    return PreferentialData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PreferentialDataToJson(this);
  }
}
