// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderListData _$OrderListDataFromJson(Map<String, dynamic> json) {
  return OrderListData(
    totalAmount: json['totalAmount'] as String?,
    createTime: json['createTime'],
    delete: json['delete'] as bool?,
    exceptionReason: json['exceptionReason'] as String?,
    id: json['id'] as int?,
    imageUrl: json['imageUrl'] as String?,
    invoiceTitle: json['invoiceTitle'] as String?,
    merchantId: json['merchantId'] as int?,
    merchantName: json['merchantName'] as String?,
    money: (json['money'] as num?)?.toDouble(),
    orderNo: json['orderNo'] as String?,
    orderSource: json['orderSource'] as int?,
    payChannelName: json['payChannelName'] as String?,
    paymentTime: json['paymentTime'] as int?,
    productNum: json['productNum'] as int?,
    refundFee: json['refundFee'] as String?,
    refuseReason: json['refuseReason'] as String?,
    remark: json['remark'] as String?,
    salesId: json['salesId'] as int?,
    oederStatus: json['oederStatus'] as int?,
    statusName: json['statusName'] as String?,
    taxNumber: json['taxNumber'] as String?,
    useBalance: json['useBalance'] as bool?,
    varietyNum: json['varietyNum'] as int?,
    endTime: json['endTime'] as int?,
    expires: json['expires'] as String?,
    logistics: json['logistics'] as String?,
    logisticsTime: json['logisticsTime'] as int?,
    orderCreator: json['orderCreator'] as String?,
    orderResourceName: json['orderResourceName'] as String?,
    orderResourceType: json['orderResourceType'] as int?,
    payExpireTime: json['payExpireTime'] as int?,
    payTime: json['payTime'] as int?,
    isFollow: json['isFollow'] as int?,
    isRedPoint: json['isRedPoint'] as int?,
  )
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : OrderListData.fromJson(json['data'] as Map<String, dynamic>);
}

Map<String, dynamic> _$OrderListDataToJson(OrderListData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'totalAmount': instance.totalAmount,
      'createTime': instance.createTime,
      'delete': instance.delete,
      'exceptionReason': instance.exceptionReason,
      'id': instance.id,
      'imageUrl': instance.imageUrl,
      'invoiceTitle': instance.invoiceTitle,
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'money': instance.money,
      'orderNo': instance.orderNo,
      'orderSource': instance.orderSource,
      'payChannelName': instance.payChannelName,
      'paymentTime': instance.paymentTime,
      'productNum': instance.productNum,
      'refundFee': instance.refundFee,
      'refuseReason': instance.refuseReason,
      'remark': instance.remark,
      'salesId': instance.salesId,
      'oederStatus': instance.oederStatus,
      'statusName': instance.statusName,
      'taxNumber': instance.taxNumber,
      'useBalance': instance.useBalance,
      'varietyNum': instance.varietyNum,
      'endTime': instance.endTime,
      'expires': instance.expires,
      'logistics': instance.logistics,
      'logisticsTime': instance.logisticsTime,
      'orderCreator': instance.orderCreator,
      'orderResourceName': instance.orderResourceName,
      'orderResourceType': instance.orderResourceType,
      'payExpireTime': instance.payExpireTime,
      'payTime': instance.payTime,
      'isFollow': instance.isFollow,
      'isRedPoint': instance.isRedPoint,
    };
