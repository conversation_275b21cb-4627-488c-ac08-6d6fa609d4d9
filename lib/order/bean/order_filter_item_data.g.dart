// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_filter_item_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderFilterItemData _$OrderFilterItemDataFromJson(Map<String, dynamic> json) {
  return OrderFilterItemData(
    id: json['id'] as int?,
    value: json['value'] as String?,
  )
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'];
}

Map<String, dynamic> _$OrderFilterItemDataToJson(
        OrderFilterItemData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'id': instance.id,
      'value': instance.value,
    };
