import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_detail_goods_data.g.dart';

@JsonSerializable()
class OrderDetailGoodsData extends BaseModel<OrderDetailGoodsData> {
  String? approvalNumber;
  String? balanceAmount;
  dynamic balanceFlag;
  String? barcode;
  String? blackProductText;
  var createTime;
  String? creator;
  bool? delete;
  String? discountAmount;
  String? fob;
  String? id;
  String? imageUrl;
  var isSplit;
  String? manufacturer;
  var mediumPackageNum;
  String? orderNo;
  var productAmount;
  String? productName;
  String? productPrice;
  var productStatus;
  String? purchasePrice;
  String? realPayAmount;
  var refundProductAmount;
  var skuId;
  var spaceTimeQuantity;
  String? spec;
  var specMultiple;
  String? status;
  String? subTotal;
  var type;
  var updateTime;
  String? updator;
  double? useBalanceAmount;

  OrderDetailGoodsData(
      this.approvalNumber,
      this.balanceAmount,
      this.balanceFlag,
      this.barcode,
      this.blackProductText,
      this.createTime,
      this.creator,
      this.delete,
      this.discountAmount,
      this.fob,
      this.id,
      this.imageUrl,
      this.isSplit,
      this.manufacturer,
      this.mediumPackageNum,
      this.orderNo,
      this.productAmount,
      this.productName,
      this.productPrice,
      this.productStatus,
      this.purchasePrice,
      this.realPayAmount,
      this.refundProductAmount,
      this.skuId,
      this.spaceTimeQuantity,
      this.spec,
      this.specMultiple,
      this.status,
      this.subTotal,
      this.type,
      this.updateTime,
      this.updator,
      this.useBalanceAmount);

  @override
  OrderDetailGoodsData fromJsonMap(Map<String, dynamic>? json) {
    return OrderDetailGoodsData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderDetailGoodsDataToJson(this);
  }

  factory OrderDetailGoodsData.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailGoodsDataFromJson(json);
}
