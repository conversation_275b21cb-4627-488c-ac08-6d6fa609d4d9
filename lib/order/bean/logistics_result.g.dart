// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'logistics_result.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LogisticsResult _$LogisticsResultFromJson(Map<String, dynamic> json) {
  return LogisticsResult(
    orderDelivery: json['orderDelivery'] == null
        ? null
        : LogisticsData.fromJson(json['orderDelivery'] as Map<String, dynamic>),
  )
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : LogisticsResult.fromJson(json['data'] as Map<String, dynamic>)
    ..orderDeliveryMessageList =
        (json['orderDeliveryMessageList'] as List<dynamic>?)
            ?.map((e) => LogisticsData.fromJson(e as Map<String, dynamic>))
            .toList();
}

Map<String, dynamic> _$LogisticsResultToJson(LogisticsResult instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'orderDelivery': instance.orderDelivery,
      'orderDeliveryMessageList': instance.orderDeliveryMessageList,
    };
