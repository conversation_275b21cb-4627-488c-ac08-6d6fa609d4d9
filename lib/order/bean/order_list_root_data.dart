import 'package:XyyBeanSproutsFlutter/order/bean/order_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

import 'package:json_annotation/json_annotation.dart';

part 'order_list_root_data.g.dart';

@JsonSerializable()
class OrderListRootData extends BaseModel<OrderListRootData> {
  String? totalMoney;
  List<OrderListData>? rows;
  bool? lastPage;

  OrderListRootData();

  @override
  OrderListRootData fromJsonMap(Map<String, dynamic>? json) {
    return OrderListRootData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderListRootDataToJson(this);
  }

  factory OrderListRootData.fromJson(Map<String, dynamic> json) =>
      _$OrderListRootDataFromJson(json);
}
