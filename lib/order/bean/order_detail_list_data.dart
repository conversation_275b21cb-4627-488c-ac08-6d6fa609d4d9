import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_detail_list_data.g.dart';

@JsonSerializable()
class OrderDetailListData extends BaseModelV2<OrderDetailListData> {
  @JsonKey(defaultValue: 0)
  int? isFirstOfMerchant;//是否母单且是第一个
  String? merchantName;//店铺名称
  int? merchantId;//店铺ID
  String? orgCode;//机构编码
  String? companyName;//机构名称
  String? imButtonDisplay;//客服按钮名称
  String? imUrl;//联系客服路径
  String? imageUrl; // 图片商品
  String? manufacturer; // 生产商
  int? productAmount; // 产品数量
  dynamic refundProductAmount; // 产品数量新
  String? productName; // 商品名称
  dynamic productPrice; // 商品价格
  dynamic purchasePrice; // 实付金额
  // int? productstatus; // 商品状态
  // dynamic realPayAmount; // 小计金额
  // String? shopCode; // 店铺编码
  // String? shopName; // 店铺名称
  int? skuId; // 商品id
  String? spec; // 商品规格
  dynamic subTotal; // 小计
  dynamic refundFee; // 退单小计
  String? statusStr;

  /// 1-非优选 2-优选  3甄选品
  dynamic isHighGross;

  /// 1-普药 2-控销
  dynamic skuCollectType;

  bool get isShowHighGross {
    return "$isHighGross" == "2";
  }

  bool get isShowControl {
    return "$skuCollectType" == "2";
  }

  OrderDetailListData();

  factory OrderDetailListData.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailListDataFromJson(json);

  @override
  OrderDetailListData fromJsonMap(Map<String, dynamic>? json) {
    return OrderDetailListData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderDetailListDataToJson(this);
  }
}
