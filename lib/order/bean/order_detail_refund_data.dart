import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_list_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_info_data.dart';

part 'order_detail_refund_data.g.dart';

@JsonSerializable()
class OrderDetailRefundData extends BaseModel<OrderDetailRefundData> {
  // String appAuditStatusName; // 退款状态
  // int merchantId; // 药店编号
  // String merchantName; // 生产商
  // String  payType; // 支付类型
  // String refundAuditTime; // 审核时间
  // String refundChannelName; // 发起方
  // String refundCreateTime; // 申请时间
  // String refundExplain; // 退款说明
  // dynamic refundFee; // 退回金额
  // dynamic refundMoney; // 申请退款金额
  // String refundOrderNo; // 退款单号
  // String refundReason; // 退款原因
  // dynamic refundTime; // 退款时间
  // int  refundVarietyNum; // 退款件数
  // String remark3; // 入库状态
  // dynamic  totalAmont; // 退款总数
  OrderDetailRefundInfoData? detail;
  List<OrderDetailListData>? skus; // 商品列表

  OrderDetailRefundData();

  factory OrderDetailRefundData.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailRefundDataFromJson(json);

  @override
  OrderDetailRefundData fromJsonMap(Map<String, dynamic>? json) {
    return OrderDetailRefundData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderDetailRefundDataToJson(this);
  }
}

@JsonSerializable()
class OrderDetailRefundInfoData extends BaseModel<OrderDetailRefundInfoData> {
  String? appAuditStatusName; // 退款状态
  int? merchantId; // 药店编号
  String? merchantName; // 生产商
  // int? payType; // 支付类型
  dynamic refundAuditTime; // 审核时间
  String? refundChannelName; // 发起方
  dynamic refundCreateTime; // 申请时间
  String? refundExplain; // 退款说明
  dynamic refundFee; // refundFee
  String? refundOrderNo; // 退款单号
  String? refundReason; // 退款原因
  dynamic refundTime; // 退款时间
  int? refundVarietyNum; // 退货商品种数
  String? remark3; // 入库状态
  dynamic totalAmount; // 退款商品件数
  dynamic refundProductAmount; // 退款商品件数新字段
  String? saleName; // 销售名称
  String? branchName;
  dynamic additionalFee; // 额外赔付
  dynamic refundMode;  // 小额打款

  /// 客服页面链接
  dynamic? imUrl;

  /// 客服按钮提示文案
  dynamic? imButtonDisplay;

  List<OrderDetailListData>? packageList;

  // String? branchCode;
  // 退单信息模型
  List<OrderEntryData> receiveModels() {
    List<OrderEntryData> list = [];

    OrderEntryData refundOrderNo = OrderEntryData();
    refundOrderNo.title = "退款单号";
    refundOrderNo.canCopy = true;
    refundOrderNo.content = this.refundOrderNo ?? "--";
    list.add(refundOrderNo);

    OrderEntryData merchantName = OrderEntryData();
    merchantName.title = "退单客户";
    merchantName.canCopy = true;
    merchantName.textColor = 0xFF00B377;
    merchantName.content = this.merchantName ?? "--";
    list.add(merchantName);

    OrderEntryData salesName = OrderEntryData();
    salesName.title = "关联销售";
    salesName.content = this.saleName ?? "--";
    list.add(salesName);

    //下单时间
    OrderEntryData source = OrderEntryData();
    source.title = "订单来源";
    source.content = this.branchName ?? "--";
    list.add(source);

    OrderEntryData refundCreateTime = OrderEntryData();
    refundCreateTime.title = "申请日期";
    refundCreateTime.content = this.refundCreateTime ?? "--";
    list.add(refundCreateTime);

    OrderEntryData refundAuditTime = OrderEntryData();
    refundAuditTime.title = "审核时间";
    refundAuditTime.content = this.refundAuditTime ?? "--";
    list.add(refundAuditTime);

    OrderEntryData refundTime = OrderEntryData();
    refundTime.title = "退款时间";
    refundTime.content = this.refundTime ?? "--";
    list.add(refundTime);

    OrderEntryData refundChannelName = OrderEntryData();
    refundChannelName.title = "发起方";
    refundChannelName.content = this.refundChannelName ?? "--";
    list.add(refundChannelName);

    OrderEntryData appAuditStatusName = OrderEntryData();
    appAuditStatusName.title = "退款状态";
    appAuditStatusName.textColor = _getrefundStatusTextColor();
    appAuditStatusName.content = this.appAuditStatusName ?? "--";
    list.add(appAuditStatusName);

    OrderEntryData refundFee = OrderEntryData();
    refundFee.title = "退款金额";
    if (this.refundMode == 4) {
      refundFee.content = "--";
    }else {
      refundFee.content =
        this.refundFee != null ? "¥${this.refundFee.toString()}" : "--";
    }
    list.add(refundFee);

    OrderEntryData refundVarietyNum = OrderEntryData();
    refundVarietyNum.title = "退货商品种数";
    if (this.refundMode == 4) {
      refundVarietyNum.content = "--";
    }else {
    refundVarietyNum.content = this.refundVarietyNum?.toString() ?? "--";
    }
    list.add(refundVarietyNum);

    OrderEntryData totalAmont = OrderEntryData();
    totalAmont.title = "退款商品件数";
    if (this.refundMode == 4) {
      totalAmont.content = "--";
    }else {
    totalAmont.content = this.refundProductAmount?.toString() ?? "--";
    }
    list.add(totalAmont);

    OrderEntryData additionalFee = OrderEntryData();
    additionalFee.title = "额外赔偿";
    additionalFee.content = this.additionalFee != null ? "¥${this.additionalFee.toString()}" : "--";
    if (this.additionalFee != null) {
      list.add(additionalFee);
    }

    OrderEntryData refundMode = OrderEntryData();
    refundMode.title = "小额打款";
    refundMode.content = this.refundFee != null ? "¥${this.refundFee.toString()}" : "--";
    if (this.refundMode == 4) {
      list.add(refundMode);
    }

    OrderEntryData remark3 = OrderEntryData();
    remark3.title = "入库状态";
    remark3.content = this.remark3?.toString() ?? "--";
    list.add(remark3);

    OrderEntryData refundReason = OrderEntryData();
    refundReason.title = "退款原因";
    refundReason.content = this.refundReason?.toString() ?? "--";
    list.add(refundReason);

    OrderEntryData refundExplain = OrderEntryData();
    refundExplain.title = "退款说明";
    refundExplain.content = this.refundExplain?.toString() ?? "--";
    list.add(refundExplain);
    return list;
  }

  int _getrefundStatusTextColor() {
    switch (this.appAuditStatusName) {
      case "退款审核中":
        return 0xFFFF7200;
      case "退款待审核":
        return 0xFFFF2121;
      default:
        return 0xFF9494A6;
    }
  }

  OrderDetailRefundInfoData();

  factory OrderDetailRefundInfoData.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailRefundInfoDataFromJson(json);

  @override
  OrderDetailRefundInfoData fromJsonMap(Map<String, dynamic>? json) {
    return OrderDetailRefundInfoData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderDetailRefundInfoDataToJson(this);
  }
}
