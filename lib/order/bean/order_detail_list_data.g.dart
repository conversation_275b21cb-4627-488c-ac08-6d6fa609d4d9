// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_detail_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderDetailListData _$OrderDetailListDataFromJson(Map<String, dynamic> json) {
  return OrderDetailListData()
    ..isFirstOfMerchant = json['isFirstOfMerchant'] as int?
    ..imageUrl = json['imageUrl'] as String?
    ..manufacturer = json['manufacturer'] as String?
    ..productAmount = json['productAmount'] as int?
    ..refundProductAmount = json['refundProductAmount']
    ..productName = json['productName'] as String?
    ..productPrice = json['productPrice']
    ..purchasePrice = json['purchasePrice']
    ..skuId = json['skuId'] as int?
    ..spec = json['spec'] as String?
    ..subTotal = json['subTotal']
    ..refundFee = json['refundFee']
    ..statusStr = json['statusStr'] as String?
    ..isHighGross = json['isHighGross']
    ..skuCollectType = json['skuCollectType']
    ..merchantId = json['merchantId']
    ..orgCode = json['orgCode'] as String?
    ..companyName = json['companyName'] as String?
    ..merchantName = json['merchantName']
    ..imButtonDisplay = json['imButtonDisplay']
    ..imUrl = json['imUrl'];
}

Map<String, dynamic> _$OrderDetailListDataToJson(OrderDetailListData instance) =>
    <String, dynamic>{
      'isFirstOfMerchant': instance.isFirstOfMerchant,
      'imageUrl': instance.imageUrl,
      'manufacturer': instance.manufacturer,
      'productAmount': instance.productAmount,
      'refundProductAmount': instance.refundProductAmount,
      'productName': instance.productName,
      'productPrice': instance.productPrice,
      'purchasePrice': instance.purchasePrice,
      'skuId': instance.skuId,
      'spec': instance.spec,
      'subTotal': instance.subTotal,
      'refundFee': instance.refundFee,
      'statusStr': instance.statusStr,
      'isHighGross': instance.isHighGross,
      'skuCollectType': instance.skuCollectType,
      'merchantId': instance.merchantId,
      'orgCode': instance.orgCode,
      'companyName': instance.companyName,
      'merchantName': instance.merchantName,
      'imButtonDisplay': instance.imButtonDisplay,
      'imUrl': instance.imUrl,
    };
