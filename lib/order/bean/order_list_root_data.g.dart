// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_list_root_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderListRootData _$OrderListRootDataFromJson(Map<String, dynamic> json) {
  return OrderListRootData()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : OrderListRootData.fromJson(json['data'] as Map<String, dynamic>)
    ..totalMoney = json['totalMoney'] as String?
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => OrderListData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..lastPage = json['lastPage'] as bool?;
}

Map<String, dynamic> _$OrderListRootDataToJson(OrderListRootData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'totalMoney': instance.totalMoney,
      'rows': instance.rows,
      'lastPage': instance.lastPage,
    };
