import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_tag_data.g.dart';

@JsonSerializable()
class OrderTagData extends BaseModelV2<OrderTagData> {
  int? id;
  String? value;
  int? key;

  //订单来源专用
  String? code;

  // 这两个字段是省区项专用字段
  String? name;
  String? branchCode;

  bool? isSelect = false;

  OrderTagData();

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OrderTagData &&
          runtimeType == other.runtimeType &&
          id == other.id &&
          code == other.code &&
          value == other.value &&
          name == other.name &&
          branchCode == other.branchCode;

  @override
  int get hashCode =>
      id.hashCode ^ value.hashCode ^ name.hashCode ^ branchCode.hashCode;

  factory OrderTagData.fromJson(Map<String, dynamic> json) =>
      _$OrderTagDataFromJson(json);

  @override
  OrderTagData fromJsonMap(Map<String, dynamic>? json) {
    return OrderTagData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderTagDataToJson(this);
  }
}
