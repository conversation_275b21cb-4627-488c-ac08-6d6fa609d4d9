// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_detail_goods_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderDetailGoodsData _$OrderDetailGoodsDataFromJson(Map<String, dynamic> json) {
  return OrderDetailGoodsData(
    json['approvalNumber'] as String?,
    json['balanceAmount'] as String?,
    json['balanceFlag'],
    json['barcode'] as String?,
    json['blackProductText'] as String?,
    json['createTime'],
    json['creator'] as String?,
    json['delete'] as bool?,
    json['discountAmount'] as String?,
    json['fob'] as String?,
    json['id'] as String?,
    json['imageUrl'] as String?,
    json['isSplit'],
    json['manufacturer'] as String?,
    json['mediumPackageNum'],
    json['orderNo'] as String?,
    json['productAmount'],
    json['productName'] as String?,
    json['productPrice'] as String?,
    json['productStatus'],
    json['purchasePrice'] as String?,
    json['realPayAmount'] as String?,
    json['refundProductAmount'],
    json['skuId'],
    json['spaceTimeQuantity'],
    json['spec'] as String?,
    json['specMultiple'],
    json['status'] as String?,
    json['subTotal'] as String?,
    json['type'],
    json['updateTime'],
    json['updator'] as String?,
    (json['useBalanceAmount'] as num?)?.toDouble(),
  )
    ..isSuccess = json['isSuccess'] as bool?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : OrderDetailGoodsData.fromJson(json['data'] as Map<String, dynamic>);
}

Map<String, dynamic> _$OrderDetailGoodsDataToJson(
        OrderDetailGoodsData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'approvalNumber': instance.approvalNumber,
      'balanceAmount': instance.balanceAmount,
      'balanceFlag': instance.balanceFlag,
      'barcode': instance.barcode,
      'blackProductText': instance.blackProductText,
      'createTime': instance.createTime,
      'creator': instance.creator,
      'delete': instance.delete,
      'discountAmount': instance.discountAmount,
      'fob': instance.fob,
      'id': instance.id,
      'imageUrl': instance.imageUrl,
      'isSplit': instance.isSplit,
      'manufacturer': instance.manufacturer,
      'mediumPackageNum': instance.mediumPackageNum,
      'orderNo': instance.orderNo,
      'productAmount': instance.productAmount,
      'productName': instance.productName,
      'productPrice': instance.productPrice,
      'productStatus': instance.productStatus,
      'purchasePrice': instance.purchasePrice,
      'realPayAmount': instance.realPayAmount,
      'refundProductAmount': instance.refundProductAmount,
      'skuId': instance.skuId,
      'spaceTimeQuantity': instance.spaceTimeQuantity,
      'spec': instance.spec,
      'specMultiple': instance.specMultiple,
      'status': instance.status,
      'subTotal': instance.subTotal,
      'type': instance.type,
      'updateTime': instance.updateTime,
      'updator': instance.updator,
      'useBalanceAmount': instance.useBalanceAmount,
    };
