import 'package:XyyBeanSproutsFlutter/order/bean/order_tag_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_status_tag_data.g.dart';

@JsonSerializable()
class OrderStatusTagData extends BaseModelV2<OrderStatusTagData> {
  /// 订单筛选字段
  List<OrderTagData>? payChannels;
  List<OrderTagData>? activities;
  List<OrderTagData>? orderStatuses;
  List<OrderTagData>? sysGroups;
  List<OrderTagData>? effectiveOrderStatuses;
  List<OrderTagData>? orderPayTypes;
  List<OrderTagData>? orderSortedList;

  /// 订单卡单筛选
  List<OrderTagData>? exceptionTypes;

  /// 优选、控销 筛选
  List<OrderTagData>? productTypes;

  /// 共有字段
  List<OrderTagData>? timeTypes;
  List<OrderTagData>? orderBranchCodes;

  /// 退单筛选字段
  List<OrderTagData>? refundChannel;
  List<OrderTagData>? storeStatus;
  List<OrderTagData>? orderRefundStatus;
  List<OrderTagData>? refundTimeTypes;

  dynamic queryDay;

  OrderStatusTagData();

  factory OrderStatusTagData.fromJson(Map<String, dynamic> json) =>
      _$OrderStatusTagDataFromJson(json);

  @override
  OrderStatusTagData fromJsonMap(Map<String, dynamic>? json) {
    return OrderStatusTagData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderStatusTagDataToJson(this);
  }
}
