import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_detail_info_data.g.dart';

@JsonSerializable()
class OrderDetailInfoData extends BaseModelV2<OrderDetailInfoData> {
  var data;
  String? activityTypeNames; // 活动类型
  String? address; // 收货地址
  dynamic balanceAmount; // 返点金额
  dynamic balanceAmountSum; // 返点金额
  String? billInfo; // 发票信息
  String? contactor; // 收货人
  String? createTime; // 下单时间
  List<OrderDetailListData>? detailList; // 商品列表
  dynamic discount; // 订单优惠金额
  String? exceptionReason; // 卡单原因
  dynamic freight; // 运费
  dynamic freightAmount; // 运费 5.2.5版本增加
  int? id; // 商品编号
  String? invoiceTitle; // 发票抬头
  String? logistics; // 物流信息
  String? merchantName; // 药店名
  int? merchantId; // 药店名
  String? mobile; // 手机号
  dynamic money; // 订单支付金额
  String? orderNo; // 订单号
  List<OrderDetailListData>? packageList; // 套餐列表
  int? payChannel; // 支付渠道
  String? payChannelName; // 支付渠道名称
  String? payTime; // 支付时间
  String? paymentTime; // 到账时间
  dynamic payType; // 支付方式
  String? payTypeName; // 支付方式名称
  String? payExpireTime; //支付过期时间
  String? payExpireTimeOb;
  bool? canModifyPayExpireDate; //是否可修改支付过期时间
  bool? canModifyPayType;//是否可以修改支付方式
  int? newPayType;//支付方式
  String? newPayTypeName;//支付方式
  String? newPayChannelName;//新支付渠道
  int?  newPayChannel;//新支付渠道
  int? remainingNumber;//可修改次数
  dynamic evidenceVerifyStatusDesc; // 电汇审核状态描述
  List<dynamic>? evidenceImageList; // 电汇凭证
  String? statusName; // 订单状态名称
  dynamic promoDiscountAmount; // 满减
  String? remark; // 备注
  dynamic sourceBalanceUse; // sourceBalanceUse
  int? orderStatus; // 订单状态
  dynamic refundStatus; // 退款状态
  dynamic refundFee; // 退款金额 // 退款金额
  dynamic exceptionTypeQuery; //卡单类型
  String? taxNumber; // 税务编号
  dynamic totalAmount; // 订单总金额
  dynamic varietyNum; // 商品数量
  dynamic voucherDiscountAmount; // 优惠券
  String? saleName; // 关联销售
  String? branchName;
  String? branchCode;
  // //是否母单
  // int? isParent;

  /// 客服页面链接
  dynamic? imUrl;

  /// 客服按钮提示文案
  dynamic? imButtonDisplay;

  OrderDetailInfoData();

  // 订单状态模型
  OrderStatusData statusData() {
    OrderStatusData data = OrderStatusData();
    return data;
  }

  // 订单基础信息模型
  List<OrderEntryData> orderBaseList({int? isParent}) {
    List<OrderEntryData> list = [];
    // 订单状态
    OrderEntryData status = OrderEntryData();
    status.title = "订单状态";
    status.content = this.statusName ?? "--";
    list.add(status);
    // 订单编号
    OrderEntryData number = OrderEntryData();
    number.title = "订单编号";
    number.content = this.orderNo ?? "--";
    number.canCopy = true;
    list.add(number);

    //下单时间
    OrderEntryData time = OrderEntryData();
    time.title = "下单时间";
    time.content = this.createTime ?? "--";
    list.add(time);
    print('isParentqiyu:,${isParent}');
    // 订单来源（根据isParent控制显隐）
    if (isParent != 1) {
      OrderEntryData source = OrderEntryData();
      source.title = "订单来源";
      source.content = this.branchName ?? "--";
      source.canCopy = true;
      list.add(source);
    }

    // 下单客户
    OrderEntryData customer = OrderEntryData();
    customer.title = "下单客户";
    customer.canCopy = true;
    customer.content =
        (this.merchantName ?? "--") + "（药店编号：${this.merchantId ?? "--"}）";
    if (customer.content == "--") {
      customer.textColor = 0xFF292933;
    } else {
      customer.textColor = 0xFF00B377;
    }
    list.add(customer);

    // 关联销售
    OrderEntryData sales = OrderEntryData();
    sales.title = "关联销售";
    sales.content = this.saleName ?? "--";
    list.add(sales);

    // 商品金额
    OrderEntryData totalAmount = OrderEntryData();
    totalAmount.title = "商品金额";
    totalAmount.content = this.totalAmount != null
        ? "¥${this.totalAmount.toStringAsFixed(2)}"
        : "--";
    list.add(totalAmount);

    // 满减金额
    OrderEntryData promoDiscountAmount = OrderEntryData();
    promoDiscountAmount.title = "满减金额";
    promoDiscountAmount.content = this.promoDiscountAmount != null
        ? "¥${this.promoDiscountAmount.toStringAsFixed(2)}"
        : "--";
    list.add(promoDiscountAmount);

    // 优惠券金额
    OrderEntryData voucherDiscountAmount = OrderEntryData();
    voucherDiscountAmount.title = "优惠券金额";
    voucherDiscountAmount.content = this.voucherDiscountAmount != null
        ? "¥${this.voucherDiscountAmount.toStringAsFixed(2)}"
        : "--";
    list.add(voucherDiscountAmount);

    // 余额抵扣
    OrderEntryData sourceBalanceUse = OrderEntryData();
    sourceBalanceUse.title = "余额抵扣";
    sourceBalanceUse.content = this.sourceBalanceUse != null
        ? "¥${this.sourceBalanceUse.toStringAsFixed(2)}"
        : "--";
    list.add(sourceBalanceUse);

    // 返点金额
    OrderEntryData balanceAmount = OrderEntryData();
    balanceAmount.title = "返点金额";
    balanceAmount.content = this.balanceAmount != null
        ? "¥${this.balanceAmount.toStringAsFixed(2)}"
        : "--";

    list.add(balanceAmount);

    // 优惠活动
    OrderEntryData activityTypeNames = OrderEntryData();
    activityTypeNames.title = "优惠活动";
    activityTypeNames.content = this.activityTypeNames?.toString() ?? "--";
    list.add(activityTypeNames);

    // 支付方式
    OrderEntryData paytype = OrderEntryData();
    paytype.title = "支付方式";
    paytype.content = this.payTypeName ?? "--";
    paytype.canModifyPayType=this.canModifyPayType ?? false;
    paytype.newPayType=this.newPayType;
    paytype.newPayTypeName=this.newPayTypeName ?? "--";
    paytype.newPayChannel=this.newPayChannel;
    paytype.newPayChannelName=this.newPayChannelName ?? "--";
    paytype.orderNo = this.orderNo ?? "";
    paytype.payChannelName = this.payChannelName ?? "--";
    list.add(paytype);

    // 支付渠道
    OrderEntryData payChannel = OrderEntryData();
    payChannel.title = "支付渠道";
    payChannel.content = this.payChannelName ?? "--";
    list.add(payChannel);
    //支付过期时间

    print(this.canModifyPayExpireDate);
    if (this.statusName == '未支付') {
      OrderEntryData payExpireTimeOb = OrderEntryData();
      payExpireTimeOb.title = "支付过期时间";
      payExpireTimeOb.isLong = this.canModifyPayExpireDate ?? false;
      payExpireTimeOb.orderNo = this.orderNo ?? "";
      payExpireTimeOb.content = this.payExpireTime ?? "--";
      payExpireTimeOb.remainingNumber=this.remainingNumber ?? 0;
      list.add(payExpireTimeOb);
    }

    // 电汇审核状态
    OrderEntryData toAccountStatus = OrderEntryData();
    toAccountStatus.title = "电汇审核状态";
    toAccountStatus.content = '${this.evidenceVerifyStatusDesc ?? "--"}';
    list.add(toAccountStatus);

    // 电汇凭证
    OrderEntryData toAccountImages = OrderEntryData();
    toAccountImages.title = "电汇凭证";
    if (this.evidenceImageList != null && this.evidenceImageList!.length > 3) {
      toAccountImages.imageList = this.evidenceImageList!.sublist(0, 2);
    } else {
      toAccountImages.imageList = this.evidenceImageList;
    }

    toAccountImages.isImageItem = true;
    list.add(toAccountImages);

    // 到账时间
    OrderEntryData paymentTime = OrderEntryData();
    paymentTime.title = "到账时间";
    paymentTime.content = this.payTime ?? "--";

    list.add(paymentTime);

    // 退款状态
    OrderEntryData tuikuan = OrderEntryData();
    tuikuan.title = "退款状态";
    tuikuan.content = this.refundStatus?.toString() ?? "--";
    list.add(tuikuan);

    // 退款金额
    OrderEntryData tuikuanMount = OrderEntryData();
    tuikuanMount.title = "退款金额";
    tuikuanMount.content =
        this.refundFee != null ? "¥${this.refundFee.toStringAsFixed(2)}" : "--";
    list.add(tuikuanMount);

    // 卡单类型
    OrderEntryData exception = OrderEntryData();
    exception.title = "卡单类型";
    exception.content =
        this.exceptionTypeQuery != null ? this.exceptionTypeQuery : "--";
    list.add(exception);

    // 卡单原因
    OrderEntryData exceptionReason = OrderEntryData();
    exceptionReason.title = "卡单原因";
    exceptionReason.content =
        this.exceptionReason != null ? this.exceptionReason : "--";
    if (exceptionReason.content == "--") {
      exceptionReason.textColor = 0xFF292933;
    } else {
      exceptionReason.textColor = 0xFF00B377;
    }
    list.add(exceptionReason);

    // 实付金额
    OrderEntryData money = OrderEntryData();
    money.title = "实付金额";
    String realAmount =
        this.money != null ? "¥${this.money.toStringAsFixed(2)}" : "--";
    String freightMoney = this.freightAmount != null
        ? " (含运费¥${this.freightAmount.toStringAsFixed(2)})"
        : "";
    money.content = realAmount;
    money.secondContent = freightMoney;
    money.secondColor = 0xFF292933;
    list.add(money);
    return list;
  }

  // 收货模型
  List<OrderEntryData> receiveModels() {
    List<OrderEntryData> list = [];
    // 收货人
    OrderEntryData name = OrderEntryData();
    name.title = "收货人";
    name.content = this.contactor ?? "--";
    list.add(name);
    // 联系方式
    OrderEntryData mobile = OrderEntryData();
    mobile.title = "联系方式";
    mobile.content = this.mobile ?? "--";
    list.add(mobile);
    // 收货地址
    OrderEntryData address = OrderEntryData();
    address.title = "收货地址";
    address.content = this.address ?? "--";
    list.add(address);
    // 备注
    OrderEntryData mark = OrderEntryData();
    mark.title = "备注";
    mark.content = this.remark ?? "--";
    list.add(mark);
    return list;
  }

  // 发票信息
  List<OrderEntryData> invoiceModels() {
    List<OrderEntryData> list = [];
    // 发票类型
    OrderEntryData type = OrderEntryData();
    type.title = "发票类型";
    type.content = this.billInfo ?? "--";
    list.add(type);
    // 发票抬头
    OrderEntryData title = OrderEntryData();
    title.title = "发票抬头";
    title.content = this.invoiceTitle ?? "--";
    list.add(title);
    // 税务编号
    OrderEntryData number = OrderEntryData();
    number.title = "税务编号";
    number.content =
        this.taxNumber.toString() != "null" ? this.taxNumber.toString() : "--";
    list.add(number);
    return list;
  }

  // 状态转文字颜色
  String textColor(int status) {
    switch (status) {
      case 10:
        return "0xFFFF9500";
      case 2:
        return "0xFF00B377";
      default:
        return "0xFFA1A5B0";
    }
  }

  // 状态转背景图片
  String imageAssetPath(int status) {
    // return "assets/images/order/order_detail_headBack.png";
    //订单状态：1、待确认，10、待支付，5、已驳回，6、已超时，4、已取消，7、待配送，2、配送中，3、已完成，91、已退款
    switch (status) {
      case 1:
        return "assets/images/order/order_status_confirmIcon.png";
      case 2:
        return "assets/images/order/order_status_distriIcon.png";
      case 3:
        return "assets/images/order/order_status_finishedIcon.png";
      case 4:
        return "assets/images/order/order_status_cancelIcon.png";
      case 5:
        return "assets/images/order/order_status_rejectIcon.png";
      case 6:
        return "assets/images/order/order_status_outIcon.png";
      case 7:
        return "assets/images/order/order_status_waitIcon.png";
      case 10:
        return "assets/images/order/order_status_payIcon.png";
      case 91:
        return "assets/images/order/order_status_runfdIcon.png";
      default:
        return "assets/images/order/order_status_confirmIcon.png";
    }
  }

// 微秒转毫秒
  String millisecondsToMicroseconds(String milliseconds) {
    return milliseconds.substring(0, milliseconds.length - 4);
  }

  factory OrderDetailInfoData.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailInfoDataFromJson(json);

  @override
  OrderDetailInfoData fromJsonMap(Map<String, dynamic>? json) {
    return OrderDetailInfoData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderDetailInfoDataToJson(this);
  }
}

class OrderStatusData {
  String? statusName; // 状态名称
  int? status; // 订单状态 1、待确认，10、待支付，5、已驳回，6、已超时，4、已取消，7、待配送，2、配送中，3、已完成，91、已退款
  late String statusImageName; // 状态背景图
  String? textColor; // 文字颜色
  String? subContent; // 已驳回状态为驳回原因。   待确认、待支付状态为剩余时间
}

class OrderEntryData {
  String? title;
  String? content;
  String? secondContent;
  int? textColor;
  int? secondColor;
  List<dynamic>? imageList;
  bool canCopy = false; // 是否可复制
  bool isImageItem = false;
  bool isLong = false; //是否展示延长时间
  String orderNo = ""; //订单编号
  bool canModifyPayType = false;//是否可以修改支付方式
  int? newPayType = -1;//支付方式
  String? newPayTypeName = "";//支付方式
  String? newPayChannelName = "";//新支付渠道
  int?  newPayChannel=-1;//新支付渠道
  String? payChannelName = "";
  int? remainingNumber=-1;
}
