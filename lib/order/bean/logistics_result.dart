import 'package:XyyBeanSproutsFlutter/order/bean/logistics_data.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

part 'logistics_result.g.dart';

@JsonSerializable()
class LogisticsResult extends BaseModel<LogisticsResult> {
  // 订单信息
  LogisticsData? orderDelivery;

  // 物流信息
  List<LogisticsData>? orderDeliveryMessageList;

  LogisticsResult({this.orderDelivery});

  factory LogisticsResult.fromJson(Map<String, dynamic> json) =>
      _$LogisticsResultFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return LogisticsResult.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LogisticsResultToJson(this);
  }
}
