// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_manage_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderManageData _$OrderManageDataFromJson(Map<String, dynamic> json) {
  return OrderManageData()
    ..isLastPage = json['isLastPage'] as bool?
    ..lastPage = json['lastPage']
    ..totalMoney = (json['totalMoney'] as num?)?.toDouble()
    ..tips = json['tips'] as String?
    ..page = json['page'] == null
        ? null
        : OrderManagerPageData.fromJson(json['page'] as Map<String, dynamic>)
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => OrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => OrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderManageDataToJson(OrderManageData instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'lastPage': instance.lastPage,
      'totalMoney': instance.totalMoney,
      'tips': instance.tips,
      'page': instance.page,
      'list': instance.list,
      'rows': instance.rows,
    };

OrderManagerPageData _$OrderManagerPageDataFromJson(Map<String, dynamic> json) {
  return OrderManagerPageData()
    ..lastPage = json['lastPage']
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => OrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderManagerPageDataToJson(
        OrderManagerPageData instance) =>
    <String, dynamic>{
      'lastPage': instance.lastPage,
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };
