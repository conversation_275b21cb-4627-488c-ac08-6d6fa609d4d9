import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_filter_item_data.g.dart';

@JsonSerializable()
class OrderFilterItemData extends BaseModel {
  int? id;

  String? value;

  OrderFilterItemData({this.id, this.value});

  static List<OrderFilterItemData> formJsonList(
    List<dynamic> jsonList,
  ) {
    List<OrderFilterItemData> filterList =
        jsonList.map((e) => OrderFilterItemData.fromJson(e)).toList();
    return filterList;
  }

  factory OrderFilterItemData.fromJson(Map<String, dynamic> json) =>
      _$OrderFilterItemDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return OrderFilterItemData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderFilterItemDataToJson(this);
  }
}
