import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_detail_merchant_check.g.dart';

@JsonSerializable()
class OrderDetailMerchantCheckModel extends BaseModelV2 {
  /// 是否私海客户
  dynamic isPrivateCustomer;

  /// 是否可分配
  dynamic distributable;

  /// 客户ID
  dynamic merchantId;

  /// 用户ID
  dynamic sysUserId;

  /// 客户宽表id
  dynamic customerId;

  /// 是否已注册
  dynamic registerFlag;

  /// 提示信息
  dynamic msg;

  OrderDetailMerchantCheckModel();

  factory OrderDetailMerchantCheckModel.fromJson(Map<String, dynamic> json) =>
      _$OrderDetailMerchantCheckModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$OrderDetailMerchantCheckModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderDetailMerchantCheckModelToJson(this);
  }
}
