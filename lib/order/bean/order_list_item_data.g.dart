// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_list_item_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderListItemData _$OrderListItemDataFromJson(Map<String, dynamic> json) {
  return OrderListItemData()
    ..merchantName = json['merchantName'] as String?
    ..imageUrl = json['imageUrl'] as String?
    ..varietyNum = json['varietyNum'] as int?
    ..statusName = json['statusName'] as String?
    ..createTime = json['createTime'] as String?
    ..money = (json['money'] as num?)?.toDouble()
    ..status = json['status'] as int?
    ..merchantId = json['merchantId'] as int?
    ..totalAmount = (json['totalAmount'] as num?)?.toDouble()
    ..orderNo = json['orderNo'] as String?
    ..id = json['id'] as int?
    ..appAuditStatusName = json['appAuditStatusName'] as String?
    ..refundCreateTime = json['refundCreateTime'] as String?
    ..refundFee = (json['refundFee'] as num?)?.toDouble()
    ..refundOrderNo = json['refundOrderNo'] as String?
    ..productAmount = json['productAmount'] as int?
    ..refundProductAmount = json['refundProductAmount']
    ..auditState = json['auditState'] as int?
    ..auditProcessState = json['auditProcessState'] as int?
    ..remark3 = json['remark3'] as String?
    ..branchName = json['branchName'] as String?
    ..additionalFee = json['additionalFee']
    ..refundMode = json['refundMode']
    ..isParent = json['isParent'] as int?
    ..preferentialList = (json['preferentialList'] as List<dynamic>?)
        ?.map((e) => PreferentialData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderListItemDataToJson(OrderListItemData instance) =>
    <String, dynamic>{
      'merchantName': instance.merchantName,
      'imageUrl': instance.imageUrl,
      'varietyNum': instance.varietyNum,
      'statusName': instance.statusName,
      'createTime': instance.createTime,
      'money': instance.money,
      'status': instance.status,
      'merchantId': instance.merchantId,
      'totalAmount': instance.totalAmount,
      'orderNo': instance.orderNo,
      'id': instance.id,
      'appAuditStatusName': instance.appAuditStatusName,
      'refundCreateTime': instance.refundCreateTime,
      'refundFee': instance.refundFee,
      'refundOrderNo': instance.refundOrderNo,
      'productAmount': instance.productAmount,
      'refundProductAmount': instance.refundProductAmount,
      'auditState': instance.auditState,
      'auditProcessState': instance.auditProcessState,
      'remark3': instance.remark3,
      'branchName': instance.branchName,
      'additionalFee': instance.additionalFee,
      'refundMode': instance.refundMode,
      'preferentialList': instance.preferentialList,
      'isParent':instance.isParent,
    };
