// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'logistics_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LogisticsData _$LogisticsDataFromJson(Map<String, dynamic> json) {
  return LogisticsData(
    orderNo: json['orderNo'] as String?,
    deliveryTypeStr: json['deliveryTypeStr'] as String?,
    waybillNo: json['waybillNo'] as String?,
    isSign: json['isSign'] as int?,
  )
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : LogisticsData.fromJson(json['data'] as Map<String, dynamic>)
    ..orderDeliveryLogisticsDetailList =
        (json['orderDeliveryLogisticsDetailList'] as List<dynamic>?)
            ?.map((e) => LogisticsListData.fromJson(e as Map<String, dynamic>))
            .toList();
}

Map<String, dynamic> _$LogisticsDataToJson(LogisticsData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'orderNo': instance.orderNo,
      'deliveryTypeStr': instance.deliveryTypeStr,
      'waybillNo': instance.waybillNo,
      'isSign': instance.isSign,
      'orderDeliveryLogisticsDetailList':
          instance.orderDeliveryLogisticsDetailList,
    };
