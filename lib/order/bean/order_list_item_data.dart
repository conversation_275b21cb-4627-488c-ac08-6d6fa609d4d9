import 'package:XyyBeanSproutsFlutter/order/bean/preferential_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_list_item_data.g.dart';

@JsonSerializable()
class OrderListItemData extends BaseModelV2<OrderListItemData> {
  String? merchantName; // 药店名称
  // String? payTypeName; // 支付方式
  String? imageUrl; // 商品图
  int? varietyNum; // 商品种类数
  String? statusName; // 订单状态名称
  String? createTime; // 下单时间
  double? money; // 订单实付金额
  // int? payType;

  int? status; // 订单状态

  int? merchantId;

  double? totalAmount; // 订单金额
  String? orderNo;

  int? id;

  String? appAuditStatusName; //退款状态
  // String? refundChannelName; //退款渠道
  String? refundCreateTime; //申请时间
  double? refundFee;

  String? refundOrderNo; //退款单号
  // int? refundType;

  // String? refundTypeName;

  // int? refundChannel;

  int? productAmount;
  int? isParent; //是否母单
  dynamic refundProductAmount;

  int? auditState;

  int? auditProcessState;

  String? remark3;

  String? branchName;
  // 额外赔付
  dynamic additionalFee;
  // 小额打款
  dynamic refundMode;

  // String? branchCode;

  List<PreferentialData>? preferentialList; // 订单 优惠列表

  OrderListItemData();

  factory OrderListItemData.fromJson(Map<String, dynamic> json) =>
      _$OrderListItemDataFromJson(json);

  @override
  OrderListItemData fromJsonMap(Map<String, dynamic>? json) {
    return OrderListItemData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderListItemDataToJson(this);
  }
}
