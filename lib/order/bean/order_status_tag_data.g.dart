// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_status_tag_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderStatusTagData _$OrderStatusTagDataFromJson(Map<String, dynamic> json) {
  return OrderStatusTagData()
    ..payChannels = (json['payChannels'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..activities = (json['activities'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..orderStatuses = (json['orderStatuses'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..sysGroups = (json['sysGroups'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..effectiveOrderStatuses =
        (json['effectiveOrderStatuses'] as List<dynamic>?)
            ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
            .toList()
    ..orderPayTypes = (json['orderPayTypes'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..orderSortedList = (json['orderSortedList'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..exceptionTypes = (json['exceptionTypes'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..productTypes = (json['productTypes'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..timeTypes = (json['timeTypes'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..orderBranchCodes = (json['orderBranchCodes'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..refundChannel = (json['refundChannel'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..storeStatus = (json['storeStatus'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..orderRefundStatus = (json['orderRefundStatus'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..refundTimeTypes = (json['refundTimeTypes'] as List<dynamic>?)
        ?.map((e) => OrderTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..queryDay = json['queryDay'];
}

Map<String, dynamic> _$OrderStatusTagDataToJson(OrderStatusTagData instance) =>
    <String, dynamic>{
      'payChannels': instance.payChannels,
      'activities': instance.activities,
      'orderStatuses': instance.orderStatuses,
      'sysGroups': instance.sysGroups,
      'effectiveOrderStatuses': instance.effectiveOrderStatuses,
      'orderPayTypes': instance.orderPayTypes,
      'orderSortedList': instance.orderSortedList,
      'exceptionTypes': instance.exceptionTypes,
      'productTypes': instance.productTypes,
      'timeTypes': instance.timeTypes,
      'orderBranchCodes': instance.orderBranchCodes,
      'refundChannel': instance.refundChannel,
      'storeStatus': instance.storeStatus,
      'orderRefundStatus': instance.orderRefundStatus,
      'refundTimeTypes': instance.refundTimeTypes,
      'queryDay': instance.queryDay,
    };
