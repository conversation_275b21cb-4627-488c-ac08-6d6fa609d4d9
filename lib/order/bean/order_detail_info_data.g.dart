// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_detail_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

OrderDetailInfoData _$OrderDetailInfoDataFromJson(Map<String, dynamic> json) {
  return OrderDetailInfoData()
    ..data = json['data']
    ..activityTypeNames = json['activityTypeNames'] as String?
    ..address = json['address'] as String?
    ..balanceAmount = json['balanceAmount']
    ..balanceAmountSum = json['balanceAmountSum']
    ..billInfo = json['billInfo'] as String?
    ..contactor = json['contactor'] as String?
    ..createTime = json['createTime'] as String?
    ..detailList = (json['detailList'] as List<dynamic>?)
        ?.map((e) => OrderDetailListData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..discount = json['discount']
    ..exceptionReason = json['exceptionReason'] as String?
    ..freight = json['freight']
    ..freightAmount = json['freightAmount']
    ..id = json['id'] as int?
    ..invoiceTitle = json['invoiceTitle'] as String?
    ..logistics = json['logistics'] as String?
    ..merchantName = json['merchantName'] as String?
    ..merchantId = json['merchantId'] as int?
    ..mobile = json['mobile'] as String?
    ..money = json['money']
    ..orderNo = json['orderNo'] as String?
    ..packageList = (json['packageList'] as List<dynamic>?)
        ?.map((e) => OrderDetailListData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..payChannel = json['payChannel'] as int?
    ..payChannelName = json['payChannelName'] as String?
    ..payTime = json['payTime'] as String?
    ..paymentTime = json['paymentTime'] as String?
    ..payType = json['payType']
    ..payTypeName = json['payTypeName'] as String?
    ..payExpireTime = json['payExpireTime'] as String?
    ..payExpireTimeOb = json['payExpireTimeOb'] as String?
    ..canModifyPayExpireDate = json['canModifyPayExpireDate'] as bool?
    ..evidenceVerifyStatusDesc = json['evidenceVerifyStatusDesc']
    ..evidenceImageList = json['evidenceImageList'] as List<dynamic>?
    ..statusName = json['statusName'] as String?
    ..promoDiscountAmount = json['promoDiscountAmount']
    ..remark = json['remark'] as String?
    ..sourceBalanceUse = json['sourceBalanceUse']
    ..orderStatus = json['orderStatus'] as int?
    ..refundStatus = json['refundStatus']
    ..refundFee = json['refundFee']
    ..exceptionTypeQuery = json['exceptionTypeQuery']
    ..taxNumber = json['taxNumber'] as String?
    ..totalAmount = json['totalAmount']
    ..varietyNum = json['varietyNum']
    ..voucherDiscountAmount = json['voucherDiscountAmount']
    ..saleName = json['saleName'] as String?
    ..branchName = json['branchName'] as String?
    ..branchCode = json['branchCode'] as String?
    ..imUrl = json['imUrl']
    ..imButtonDisplay = json['imButtonDisplay']
    ..canModifyPayType = json['canModifyPayType']
    ..newPayType = json['newPayType'] as int?
    ..newPayTypeName = json['newPayTypeName']
    ..newPayChannel = json['newPayChannel'] as int?
    ..newPayChannelName = json['newPayChannelName']
    ..remainingNumber = json['remainingNumber'] as int?;
}

Map<String, dynamic> _$OrderDetailInfoDataToJson(
        OrderDetailInfoData instance) =>
    <String, dynamic>{
      'data': instance.data,
      'activityTypeNames': instance.activityTypeNames,
      'address': instance.address,
      'balanceAmount': instance.balanceAmount,
      'balanceAmountSum': instance.balanceAmountSum,
      'billInfo': instance.billInfo,
      'contactor': instance.contactor,
      'createTime': instance.createTime,
      'detailList': instance.detailList,
      'discount': instance.discount,
      'exceptionReason': instance.exceptionReason,
      'freight': instance.freight,
      'freightAmount': instance.freightAmount,
      'id': instance.id,
      'invoiceTitle': instance.invoiceTitle,
      'logistics': instance.logistics,
      'merchantName': instance.merchantName,
      'merchantId': instance.merchantId,
      'mobile': instance.mobile,
      'money': instance.money,
      'orderNo': instance.orderNo,
      'packageList': instance.packageList,
      'payChannel': instance.payChannel,
      'payChannelName': instance.payChannelName,
      'payTime': instance.payTime,
      'paymentTime': instance.paymentTime,
      'payType': instance.payType,
      'payTypeName': instance.payTypeName,
      'payExpireTime': instance.payExpireTime,
      'payExpireTimeOb': instance.payExpireTimeOb,
      'canModifyPayExpireDate': instance.canModifyPayExpireDate,
      'evidenceVerifyStatusDesc': instance.evidenceVerifyStatusDesc,
      'evidenceImageList': instance.evidenceImageList,
      'statusName': instance.statusName,
      'promoDiscountAmount': instance.promoDiscountAmount,
      'remark': instance.remark,
      'sourceBalanceUse': instance.sourceBalanceUse,
      'orderStatus': instance.orderStatus,
      'refundStatus': instance.refundStatus,
      'refundFee': instance.refundFee,
      'exceptionTypeQuery': instance.exceptionTypeQuery,
      'taxNumber': instance.taxNumber,
      'totalAmount': instance.totalAmount,
      'varietyNum': instance.varietyNum,
      'voucherDiscountAmount': instance.voucherDiscountAmount,
      'saleName': instance.saleName,
      'branchName': instance.branchName,
      'branchCode': instance.branchCode,
      'imUrl': instance.imUrl,
      'imButtonDisplay': instance.imButtonDisplay,
      'newPayType':instance.newPayType,
      'canModifyPayType':instance.imButtonDisplay,
      'newPayTypeName':instance.newPayTypeName,
      'newPayChannel':instance.newPayChannel,
      'newPayChannelName':instance.newPayChannelName,
      'remainingNumber':instance.remainingNumber,
    };
