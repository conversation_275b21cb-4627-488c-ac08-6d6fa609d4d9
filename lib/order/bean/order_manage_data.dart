import 'package:XyyBeanSproutsFlutter/order/bean/order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'order_manage_data.g.dart';

@JsonSerializable()
class OrderManageData extends BaseModelV2<OrderManageData> {
  bool? isLastPage = false;
  dynamic lastPage;
  double? totalMoney;
  String? tips;
  OrderManagerPageData? page;
  List<OrderListItemData>? list;
  List<OrderListItemData>? rows;

  OrderManageData();

  @override
  OrderManageData fromJsonMap(Map<String, dynamic>? json) {
    return OrderManageData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderManageDataToJson(this);
  }

  factory OrderManageData.fromJson(Map<String, dynamic> json) =>
      _$OrderManageDataFromJson(json);
}

@JsonSerializable()
class OrderManagerPageData extends BaseModelV2<OrderManagerPageData> {
  dynamic lastPage;
  dynamic isLastPage;
  List<OrderListItemData>? list;

  OrderManagerPageData();

  factory OrderManagerPageData.fromJson(Map<String, dynamic> json) =>
      _$OrderManagerPageDataFromJson(json);

  @override
  OrderManagerPageData fromJsonMap(Map<String, dynamic> json) {
    return _$OrderManagerPageDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderManagerPageDataToJson(this);
  }
}
