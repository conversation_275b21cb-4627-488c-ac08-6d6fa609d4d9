// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'order_detail_refund_cwy_data.dart';


OrderDetailRefundCwyData _$OrderDetailRefundDataFromJson(
    Map<String, dynamic> json) {
  return OrderDetailRefundCwyData()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : OrderDetailRefundCwyData.fromJson(json['data'] as Map<String, dynamic>)
    ..detail = json['detail'] == null
        ? null
        : OrderDetailRefundInfoCwyData.fromJson(
        json['detail'] as Map<String, dynamic>)
    ..skus = (json['skus'] as List<dynamic>?)
        ?.map((e) => OrderDetailListData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderDetailRefundDataCwyToJson(
    OrderDetailRefundCwyData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'detail': instance.detail,
      'skus': instance.skus,
    };

OrderDetailRefundInfoCwyData _$OrderDetailRefundInfoDataCwyFromJson(
    Map<String, dynamic> json) {
  return OrderDetailRefundInfoCwyData()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : OrderDetailRefundInfoCwyData.fromJson(
        json['data'] as Map<String, dynamic>)
    ..appAuditStatusName = json['appAuditStatusName'] as String?
    ..merchantId = json['merchantId'] as int?
    ..merchantName = json['merchantName'] as String?
    ..refundAuditTime = json['refundAuditTime']
    ..refundChannelName = json['refundChannelName'] as String?
    ..refundCreateTime = json['refundCreateTime']
    ..refundExplain = json['refundExplain'] as String?
    ..refundFee = json['refundFee']
    ..refundOrderNo = json['refundOrderNo'] as String?
    ..refundReason = json['refundReason'] as String?
    ..refundTime = json['refundTime']
    ..refundVarietyNum = json['refundVarietyNum'] as int?
    ..remark3 = json['remark3'] as String?
    ..totalAmount = json['totalAmount']
    ..refundProductAmount = json['refundProductAmount']
    ..saleName = json['saleName'] as String?
    ..branchName = json['branchName'] as String?
    ..additionalFee = json['additionalFee']
    ..refundMode = json['refundMode']
    ..imUrl = json['imUrl']
    ..auditState=json['auditState'] as dynamic?
    ..isRefunded=json['isRefunded'] as dynamic?
    ..orderNo=json['orderNo'] as dynamic?
    ..refundType=json['refundType'] as dynamic?
    ..imButtonDisplay = json['imButtonDisplay']
    ..refundProductInfoDtos=json['refundProductInfoDtos'] as List?
    ..attachmentUrl=json['attachmentUrl'] as List<dynamic>?
    ..applyTime=json['applyTime'] as String?
    ..processNo=json['processNo'] as String?
    ..applicent=json['applicent'] as dynamic?
    ..auditStatusName=json['auditStatusName'] as String?
    ..applicant=json['applicant'] as dynamic?
    ..companyName=json['companyName'] as dynamic?
    ..packageList = (json['packageList'] as List<dynamic>?)
        ?.map((e) => OrderDetailListData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderDetailRefundInfoCwyDataToJson(
    OrderDetailRefundInfoCwyData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'appAuditStatusName': instance.appAuditStatusName,
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'refundAuditTime': instance.refundAuditTime,
      'refundChannelName': instance.refundChannelName,
      'refundCreateTime': instance.refundCreateTime,
      'refundExplain': instance.refundExplain,
      'refundFee': instance.refundFee,
      'refundOrderNo': instance.refundOrderNo,
      'refundReason': instance.refundReason,
      'refundTime': instance.refundTime,
      'refundVarietyNum': instance.refundVarietyNum,
      'remark3': instance.remark3,
      'totalAmount': instance.totalAmount,
      'refundProductAmount': instance.refundProductAmount,
      'saleName': instance.saleName,
      'branchName': instance.branchName,
      'additionalFee': instance.additionalFee,
      'refundMode': instance.refundMode,
      'imUrl': instance.imUrl,
      'imButtonDisplay': instance.imButtonDisplay,
      'packageList': instance.packageList,
      'attachmentUrl':instance.attachmentUrl,
      'orderNo':instance.orderNo,
      'refundType':instance.refundType,
      'isRefunded':instance.isRefunded,
      'refundProductInfoDtos':instance.refundProductInfoDtos,
      'applyTime':instance.applyTime,
      'processNo':instance.processNo,
      'applicent':instance.applicent,
      'applicant':instance.applicant,
      'companyName':instance.companyName
    };
