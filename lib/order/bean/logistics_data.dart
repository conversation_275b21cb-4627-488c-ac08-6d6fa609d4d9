import 'package:json_annotation/json_annotation.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'logistics_list_data.dart';

part 'logistics_data.g.dart';

@JsonSerializable()
class LogisticsData extends BaseModel<LogisticsData> {
  // 订单编号
  String? orderNo;
  // 配送方式
  String? deliveryTypeStr;
  // 运单号
  String? waybillNo;
  // 是否签收
  int? isSign;
  // 物流信息
  List<LogisticsListData>? orderDeliveryLogisticsDetailList;

  LogisticsData(
      {this.orderNo, this.deliveryTypeStr, this.waybillNo, this.isSign});

  factory LogisticsData.fromJson(Map<String, dynamic> json) =>
      _$LogisticsDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return LogisticsData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LogisticsDataToJson(this);
  }
}
