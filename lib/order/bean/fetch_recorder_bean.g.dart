// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'fetch_recorder_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FetchReorderBean _$FetchReorderBeanFromJson(Map<String, dynamic> json) {
  return FetchReorderBean(
    shopCode: json['shopCode'] as String?,
  )
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : FetchReorderBean.fromJson(json['data'] as Map<String, dynamic>);
}

Map<String, dynamic> _$FetchReorderBeanToJson(FetchReorderBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'shopCode': instance.shopCode,
    };
