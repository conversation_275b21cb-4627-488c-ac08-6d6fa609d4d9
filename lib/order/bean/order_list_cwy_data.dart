import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';



@JsonSerializable()
class OrderListCwyResponse extends BaseModelV2<OrderListCwyResponse> {
  String? status;
  String? msg;
  String? errorMsg;
  String? errorCode;
  OrderListCwyData? data;

  OrderListCwyResponse();

  factory OrderListCwyResponse.fromJson(Map<String, dynamic> json) => _$OrderListCwyResponseFromJson(json);

  @override
  OrderListCwyResponse fromJsonMap(Map<String, dynamic>? json) {
    return OrderListCwyResponse.fromJson(json!);
  }

  Map<String, dynamic> toJson() => _$OrderListCwyResponseToJson(this);
}

@JsonSerializable()
class OrderListCwyData extends BaseModelV2<OrderListCwyData> {
  int? total;
  List<OrderListItem>? list;
  int? pageNum;
  int? pageSize;
  int? size;
  int? startRow;
  int? endRow;
  int? pages;
  int? prePage;
  int? nextPage;
  bool? isFirstPage;
  bool? isLastPage;
  bool? hasPreviousPage;
  bool? hasNextPage;
  int? navigatePages;
  List<int>? navigatepageNums;
  int? navigateFirstPage;
  int? navigateLastPage;

  OrderListCwyData();

  factory OrderListCwyData.fromJson(Map<String, dynamic> json) => _$OrderListCwyDataFromJson(json);

  @override
  OrderListCwyData fromJsonMap(Map<String, dynamic>? json) {
    return OrderListCwyData.fromJson(json!);
  }

  Map<String, dynamic> toJson() => _$OrderListCwyDataToJson(this);
}

@JsonSerializable()
class OrderListItem extends BaseModelV2<OrderListItem> {
  int? id;
  String? sendMessage;
  int? isRead;
  String? orderNo;
  String? refundOrderNo;
  String? oaId;
  int? createTime;
  int? superId;

  OrderListItem();

  factory OrderListItem.fromJson(Map<String, dynamic> json) => _$OrderListItemFromJson(json);

  @override
  OrderListItem fromJsonMap(Map<String, dynamic>? json) {
    return OrderListItem.fromJson(json!);
  }

  Map<String, dynamic> toJson() => _$OrderListItemToJson(this);
}

// GENERATED CODE - DO NOT MODIFY BY HAND



OrderListCwyResponse _$OrderListCwyResponseFromJson(Map<String, dynamic> json) {
  return OrderListCwyResponse()
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorMsg = json['errorMsg'] as String?
    ..errorCode = json['errorCode'] as String?
    ..data = json['data'] == null
        ? null
        : OrderListCwyData.fromJson(json['data'] as Map<String, dynamic>);
}

Map<String, dynamic> _$OrderListCwyResponseToJson(OrderListCwyResponse instance) =>
    <String, dynamic>{
      'status': instance.status,
      'msg': instance.msg,
      'errorMsg': instance.errorMsg,
      'errorCode': instance.errorCode,
      'data': instance.data?.toJson(),
    };

OrderListCwyData _$OrderListCwyDataFromJson(Map<String, dynamic> json) {
  return OrderListCwyData()
    ..total = json['total'] as int?
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => e == null ? null : OrderListItem.fromJson(e as Map<String, dynamic>))
        .whereType<OrderListItem>()
        .toList()
    ..pageNum = json['pageNum'] as int?
    ..pageSize = json['pageSize'] as int?
    ..size = json['size'] as int?
    ..startRow = json['startRow'] as int?
    ..endRow = json['endRow'] as int?
    ..pages = json['pages'] as int?
    ..prePage = json['prePage'] as int?
    ..nextPage = json['nextPage'] as int?
    ..isFirstPage = json['isFirstPage'] as bool?
    ..isLastPage = json['isLastPage'] as bool?
    ..hasPreviousPage = json['hasPreviousPage'] as bool?
    ..hasNextPage = json['hasNextPage'] as bool?
    ..navigatePages = json['navigatePages'] as int?
    ..navigatepageNums = (json['navigatepageNums'] as List<dynamic>?)?.map((e) => e as int).toList()
    ..navigateFirstPage = json['navigateFirstPage'] as int?
    ..navigateLastPage = json['navigateLastPage'] as int?;
}

Map<String, dynamic> _$OrderListCwyDataToJson(OrderListCwyData instance) =>
    <String, dynamic>{
      'total': instance.total,
      'list': instance.list?.map((e) => e.toJson()).toList(),
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'size': instance.size,
      'startRow': instance.startRow,
      'endRow': instance.endRow,
      'pages': instance.pages,
      'prePage': instance.prePage,
      'nextPage': instance.nextPage,
      'isFirstPage': instance.isFirstPage,
      'isLastPage': instance.isLastPage,
      'hasPreviousPage': instance.hasPreviousPage,
      'hasNextPage': instance.hasNextPage,
      'navigatePages': instance.navigatePages,
      'navigatepageNums': instance.navigatepageNums,
      'navigateFirstPage': instance.navigateFirstPage,
      'navigateLastPage': instance.navigateLastPage,
    };

OrderListItem _$OrderListItemFromJson(Map<String, dynamic> json) {
  return OrderListItem()
    ..id = json['id'] as int?
    ..sendMessage = json['sendMessage'] as String?
    ..isRead = json['isRead'] as int?
    ..orderNo = json['orderNo'] as String?
    ..refundOrderNo = json['refundOrderNo'] as String?
    ..oaId = json['oaId'] as String?
    ..superId=json['superId'] as int?
    ..createTime = json['createTime'] as int?;
}

Map<String, dynamic> _$OrderListItemToJson(OrderListItem instance) =>
    <String, dynamic>{
      'id': instance.id,
      'sendMessage': instance.sendMessage,
      'isRead': instance.isRead,
      'orderNo': instance.orderNo,
      'refundOrderNo': instance.refundOrderNo,
      'oaId': instance.oaId,
      'superId':instance.superId,
      'createTime': instance.createTime,
    };
