import 'dart:developer';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/logistics_result.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

import 'package:XyyBeanSproutsFlutter/common/section_group_handler/section_group_handler.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/logistics_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/logistics_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';

class LogisticsInfoPage extends BasePage {
  final String? arguments;

  LogisticsInfoPage({this.arguments = "YBM20201116183016108580"}) : super() {
    log('LogisticsInfoPage - $arguments');
  }

  @override
  BaseState initState() {
    return _LogisticsInfoState(this.arguments);
  }
}

class _LogisticsInfoState extends BaseState {
  String? orderNo;

  _LogisticsInfoState(this.orderNo);

  // 刷新控制器
  EasyRefreshController _refreshController = EasyRefreshController();

  LogisticsResult? data;

  /// 返回数据后，整个页面需要刷新，所以不使用ChangeNotifier
  // 数据源
  List<LogisticsData>? dataSource = [];

  /// 展开收起项
  List<_LogisticsPackageOpenModel> openOptions = [];

  @override
  void initState() {
    super.initState();
    this._requestLogisticsData();
  }

  // Request
  void _requestLogisticsData() async {
    var result = await Network<LogisticsResult>(LogisticsResult()).requestData(
      '/refund/queryLogistics',
      method: RequestMethod.GET,
      parameters: {"orderNo": this.orderNo},
    );
    // if (_refreshController.finishRefreshCallBack != null) {
    //   _refreshController.finishRefreshCallBack!();
    // }
    if (result.isSuccess != null && result.isSuccess == true && mounted) {
      setState(() {
        // this.data = result;
        this.dataSource = result.orderDeliveryMessageList ?? [];
        this.openOptions = [];

        /// 设置展开收起项
        if ((this.dataSource?.length ?? 0) > 1) {
          for (int section = 0; section < this.dataSource!.length; section++) {
            LogisticsData packageModel = this.dataSource![section];
            _LogisticsPackageOpenModel openModel = _LogisticsPackageOpenModel(
              section: section,
              isMore: packageModel.orderDeliveryLogisticsDetailList!.length > 2,
            );
            this.openOptions.add(openModel);
          }
        }
      });
    } else {
      XYYContainer.toastChannel.toast(result.msg ?? "网络请求异常，请稍后重试");
    }
  }

  // SubViews
  @override
  Widget buildWidget(BuildContext context) {
    var handler = this.buildListHandler();

    return SafeArea(
      child: Container(
        color: Color(0xFFF6F6F6),
        child: Column(
          children: [
            infoHeader(),
            Expanded(
              child: EasyRefresh(
                controller: _refreshController,
                onRefresh: () async {
                  _requestLogisticsData();
                },
                child: ListView.custom(
                  cacheExtent: 4,
                  childrenDelegate: _LogisticsCustomDelegate(
                    (BuildContext ctx, int index) {
                      return handler.cellAtIndex(index);
                    },
                    childCount: handler.allItemCount,
                  ),
                ),
                emptyWidget: getEmptyWidget(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (dataSource?.length != 0) {
      return null;
    }
    return PageStateWidget.pageEmpty(PageState.Empty);
  }

  // 头部数据
  Widget infoHeader() {
    List<_LogisticsHeaderModel> models = [];
    models.add(_LogisticsHeaderModel(
      title: '订单编号',
      content: this.orderNo,
    ));
    if (dataSource!.length >= 1) {
      models.add(_LogisticsHeaderModel(
        title: '配送方式',
        content: dataSource![0].deliveryTypeStr ?? "-",
      ));
    } else {
      models.add(_LogisticsHeaderModel(
        title: '配送方式',
        content: '-',
      ));
    }
    models.add(_LogisticsHeaderModel(
      title: '包裹数',
      content: '${dataSource!.length}',
    ));
    return _LogisticsInfoHeader(models: models);
  }

  // 分组处理器
  SectionGroupHandler buildListHandler() {
    if (dataSource != null && dataSource!.length > 1) {
      return SectionGroupHandler(
        numberOfSections: dataSource!.length,
        numberOfRowsInSection: (section) {
          return getRowsForSection(section);
        },
        headerForSection: (section) {
          return getLogisticsHeader(section);
        },
        cellForRowAtIndexPath: (IndexPath indexPath) {
          return getLogisticsItem(indexPath);
        },
        footerForSection: (section) {
          return getLogisticsFooter(section);
        },
      );
    } else {
      return SectionGroupHandler(
        numberOfSections: (dataSource ?? []).length > 0 ? 1 : 0,
        numberOfRowsInSection: (section) {
          return dataSource![section].orderDeliveryLogisticsDetailList!.length;
        },
        headerForSection: (section) {
          return getLogisticsHeader(section);
        },
        cellForRowAtIndexPath: (IndexPath indexPath) {
          return getLogisticsItem(indexPath);
        },
      );
    }
  }

  /// 获取section rows长度
  int getRowsForSection(int section) {
    _LogisticsPackageOpenModel openModel = openOptions[section];
    if (openModel.isMore) {
      return openModel.isOpen
          ? dataSource![section].orderDeliveryLogisticsDetailList!.length
          : 2;
    } else {
      return dataSource![section].orderDeliveryLogisticsDetailList!.length;
    }
  }

  /// 获取header
  Widget getLogisticsHeader(int section) {
    return _LogisticsSectionHeader(
      waybillNumber: dataSource![section].waybillNo ?? "-",
      isSign: dataSource![section].isSign == 1,
    );
  }

  /// 获取item
  Widget getLogisticsItem(IndexPath indexPath) {
    LogisticsListData listData = dataSource![indexPath.section]
        .orderDeliveryLogisticsDetailList![indexPath.row];
    String nodeTime = formatDate(
        DateTime.fromMillisecondsSinceEpoch(listData.deliveryTime!),
        [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ':', ss]);
    if (dataSource!.length > 1) {
      return _LogisticsItem(
        content: listData.description,
        time: nodeTime,
        isTop: indexPath.row == 0,
        isBottom: indexPath.row ==
            dataSource![indexPath.section]
                    .orderDeliveryLogisticsDetailList!
                    .length -
                1,
      );
    } else {
      BorderRadius radius = BorderRadius.zero;
      bool isTop = indexPath.row == 0;
      bool isBottom = indexPath.row ==
          dataSource![indexPath.section]
                  .orderDeliveryLogisticsDetailList!
                  .length -
              1;
      if (isTop && isBottom) {
        radius = BorderRadius.circular(4);
      } else if (isTop) {
        radius = BorderRadius.only(
          topLeft: Radius.circular(4),
          topRight: Radius.circular(4),
        );
      } else if (isBottom) {
        radius = BorderRadius.only(
          bottomLeft: Radius.circular(4),
          bottomRight: Radius.circular(4),
        );
      }
      return _LogisticsItem(
        content: listData.description,
        time: nodeTime,
        isTop: indexPath.row == 0,
        isBottom: indexPath.row ==
            dataSource![indexPath.section]
                    .orderDeliveryLogisticsDetailList!
                    .length -
                1,
        radius: radius,
      );
    }
  }

  /// 获取footer
  Widget getLogisticsFooter(int section) {
    return _LogisticsSectionFooter(
      tap: (isOpen) {
        setState(() {
          _LogisticsPackageOpenModel model = openOptions[section];
          model.isOpen = isOpen;
        });
      },
      isOpen: openOptions[section].isOpen,
    );
  }

  @override
  String getTitleName() {
    return '物流信息';
  }
}

class _LogisticsCustomDelegate extends SliverChildBuilderDelegate {
  _LogisticsCustomDelegate(builder, {int? childCount})
      : super(
          builder,
          childCount: childCount,
        );
}

class _LogisticsInfoHeader extends StatelessWidget {
  /// 数组长度必须为3
  final List<_LogisticsHeaderModel> models;

  _LogisticsInfoHeader({required this.models});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF6F6F6),
      child: Container(
        margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
        padding: EdgeInsets.only(top: 10, bottom: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: Colors.white,
        ),
        child: Column(
          children: [
            Container(
              height: 0.5,
              color: Color(0xFFE1E1E5),
            ),
            SizedBox(height: 10),
            _LogisticsInfoItem(
              title: models[0].title,
              content: models[0].content,
            ),
            SizedBox(height: 10),
            _LogisticsInfoItem(
              title: models[1].title,
              content: models[1].content,
            ),
            SizedBox(height: 10),
            _LogisticsInfoItem(
              title: models[2].title,
              content: models[2].content,
            ),
          ],
        ),
      ),
    );
  }
}

class _LogisticsHeaderModel {
  String? title;
  String? content;

  _LogisticsHeaderModel({this.title, this.content});
}

class _LogisticsInfoItem extends StatelessWidget {
  final String? title;
  final String? content;

  _LogisticsInfoItem({
    Key? key,
    required this.title,
    required this.content,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.baseline,
        textBaseline: TextBaseline.alphabetic,
        children: [
          Container(
            padding: EdgeInsets.only(left: 10),
            // width: 70,
            constraints: BoxConstraints(minWidth: 70),
            child: Text(
              this.title ?? "-",
              textAlign: TextAlign.left,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.normal,
                color: Color(0xFF9494A6),
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.only(left: 30, right: 10),
              child: Text(
                this.content ?? "-",
                maxLines: 2,
                textAlign: TextAlign.left,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 14,
                  color: Color(0xFF292933),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _LogisticsSectionHeader extends StatelessWidget {
  final String waybillNumber;
  final bool isSign;

  _LogisticsSectionHeader({
    Key? key,
    required this.waybillNumber,
    this.isSign = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF6F6F6),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4),
            topRight: Radius.circular(4),
          ),
          color: Color(0xFFFFFFFF),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Container(
              padding: EdgeInsets.only(left: 10, right: 10),
              constraints: BoxConstraints(minHeight: 45),
              decoration: BoxDecoration(
                color: Color(0xFFFAFAFA),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  GestureDetector(
                    child: Text(
                      "运单号：$waybillNumber",
                      style: TextStyle(
                        fontSize: 13,
                        color: Color(0xFF676773),
                      ),
                    ),
                    onTap: () {
                      Clipboard.setData(
                          ClipboardData(text: this.waybillNumber));
                      XYYContainer.toastChannel.toast('复制成功');
                    },
                  ),
                  Container(
                    child: Row(
                      children: [
                        Image.asset(
                          'assets/images/order/logistics_info_sign.png',
                          width: 18,
                          height: 17,
                        ),
                        SizedBox(
                          width: 6,
                        ),
                        Text(
                          this.isSign ? '已签收' : '未签收',
                          style: TextStyle(
                            fontSize: 14,
                            color: Color(0xFF666666),
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              color: Color(0xFFF6F6F6),
              height: 1,
            ),
          ],
        ),
      ),
    );
  }
}

class _LogisticsItem extends StatelessWidget {
  final bool isTop;
  final bool isBottom;
  final String? content;
  final String time;
  final BorderRadius radius;

  _LogisticsItem({
    Key? key,
    required this.content,
    required this.time,
    this.isTop = false,
    this.isBottom = false,
    this.radius = BorderRadius.zero,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF6F6F6),
      child: Container(
        margin: EdgeInsets.only(left: 0, right: 0),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: this.radius,
        ),
        child: CustomPaint(
          painter: _LogisticsLinePainter(
            isTop: this.isTop,
            isBottom: this.isBottom,
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.only(top: this.isTop ? 12 : 16),
                constraints: BoxConstraints(minWidth: 39),
                child: Container(
                  alignment: Alignment.center,
                  child: pointWidget(),
                ),
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(top: 11, bottom: 11, right: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        this.content!,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: 14,
                          color: this.isTop
                              ? Color(0xFFFF292933)
                              : Color(0xFF9494A6),
                        ),
                      ),
                      SizedBox(
                        height: 2,
                      ),
                      Text(
                        this.time,
                        textAlign: TextAlign.left,
                        style: TextStyle(
                          fontSize: 11,
                          color: Color(0xFF9494A6),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 左侧布局点
  Widget pointWidget() {
    if (this.isTop) {
      return Container(
        width: 19,
        height: 19,
        alignment: Alignment.center,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(9.5),
          color: Color(0xFFCCF8E6),
        ),
        child: Container(
          width: 11,
          height: 11,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(5.5),
            color: Color(0xFF35C561),
          ),
        ),
      );
    } else {
      return Container(
        decoration: BoxDecoration(
          border: Border.all(color: Color(0xFFE5E5EA), width: 1),
          borderRadius: BorderRadius.circular(5.5),
        ),
        child: Container(
          width: 9,
          height: 9,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.5),
            color: Color(0xFFB3B3C2),
            border: Border.all(color: Color(0xFFFFFFFF), width: 2),
          ),
        ),
      );
    }
  }
}

class _LogisticsLinePainter extends CustomPainter {
  final bool isTop;
  final bool isBottom;

  _LogisticsLinePainter({this.isTop = false, this.isBottom = false});

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.color = Color(0xFFE4E4E6);
    paint.strokeWidth = 1;

    canvas.drawLine(
      Offset(19, this.isTop ? 16 : 0),
      Offset(19, this.isBottom ? 16 : size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

typedef _LogisticsFooterTap = void Function(bool isOpen);

class _LogisticsSectionFooter extends StatelessWidget {
  final bool isOpen;
  final _LogisticsFooterTap? tap;

  _LogisticsSectionFooter({Key? key, required this.isOpen, this.tap})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF6F6F6),
      height: 47,
      child: GestureDetector(
        onTap: () {
          this.tap!(!isOpen);
        },
        child: Container(
          margin: EdgeInsets.fromLTRB(0, 0, 0, 10),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              bottomLeft: Radius.circular(4),
              bottomRight: Radius.circular(4),
            ),
            color: Color(0xFFFFFFFF),
          ),
          child: Column(
            children: [
              Container(
                height: 1,
                color: Color(0xFFF6F6F6),
              ),
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Text(
                      this.isOpen ? '收起物流信息' : '查看更多物流信息',
                      style: TextStyle(
                        fontSize: 14,
                        color: Color(0xFF333333),
                      ),
                    ),
                    Image.asset(
                      this.isOpen
                          ? 'assets/images/order/logistics_info_close.png'
                          : 'assets/images/order/logistics_info_open.png',
                      width: 12,
                      height: 12,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _LogisticsPackageOpenModel {
  int section;
  bool isMore = false;
  bool isOpen = false;

  _LogisticsPackageOpenModel(
      {required this.section, this.isMore = false, this.isOpen = false});
}
