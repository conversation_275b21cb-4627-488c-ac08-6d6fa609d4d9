import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/color_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/time/time_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/list/row_bean.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/list/visit_list_bean.dart';
import 'package:XyyBeanSproutsFlutter/visit/constant/visit_type_code.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/visit_status_tag_data.dart';

import 'visit_pop_dailog.dart';

class VisitManagePage extends BasePage {
  final String? userId;
  final String? userName;

  VisitManagePage({this.userId, this.userName});

  @override
  BaseState<StatefulWidget> initState() {
    return VisitListState();
  }
}

class VisitListState extends BaseState<VisitManagePage>
    with SingleTickerProviderStateMixin {
  late VisitDataModel _dataModel;
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();
  TextStyle styleBlack = TextStyle(
      fontSize: 16,
      fontWeight: FontWeight.bold,
      color: ColorUtils().color_292933,
      height: 1);
  TextStyle styleGray =
      TextStyle(fontSize: 14, color: ColorUtils().color_666666, height: 1);

  /// 是否是M级别账号ma
  bool isManagerLevel = false;

  @override
  void onCreate() {
    _dataModel = VisitDataModel(_refreshController);
    if (widget.userId != null) {
      _dataModel.filterParams["searchUserId"] = widget.userId;
      _dataModel.filterParams["name"] = widget.userName;
    }
    _dataModel.requestData(true);
    _dataModel.requestFilterData();
    this._requestRoleType();
    super.onCreate();
    if (Platform.isAndroid) {
      XYYContainer.bridgeCall("handle_call_record",
          parameters: {"source": "visit_manage"});
    }
  }

  void _requestRoleType() {
    UserInfoUtil.isBDMOrGJRBDM().then((value) {
      setState(() {
        this.isManagerLevel = value;
      });
    });
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<VisitDataModel>(create: (context) => _dataModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Consumer<VisitDataModel>(builder: (context, model, child) {
      return Stack(
        children: [
          Container(
            color: ColorUtils().color_EFEFF4,
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Divider(height: 1, color: ColorUtils().color_E1E1E5),
                buildHeadView(),
                Expanded(child: buildListView())
              ],
            ),
          ),
          Positioned(bottom: 50, right: 10, child: buildAddView())
        ],
      );
    });
  }

  @override
  String getTitleName() {
    return '拜访管理';
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        GestureDetector(
          onTap: () {
            jumpFilterPage(context);
          },
          child: buildFilterButton(context),
        )
      ],
    );
  }

  Widget buildFilterButton(BuildContext context) {
    return Container(
      height: 44,
      width: 44,
      alignment: Alignment.center,
      child: Image.asset(
        "assets/images/titlebar/icon_filter.png",
        width: 21,
        height: 21,
      ),
    );
  }

  ///跳转筛选
  void jumpFilterPage(context) async {
    if (_dataModel.tagData == null) {
      XYYContainer.toastChannel.toast('筛选数据未加载');
      return;
    }
    var result =
        await Navigator.pushNamed(context, "/visit_filter_page", arguments: {
      'sourceJSON': json.encode(_dataModel.tagData),
      'currentParams': _dataModel.filterParams,
    });
    if (result is Map) {
      Map asResult = result as Map<String, String?>;

      /// 如果不是关闭返回的 则处理参数
      if (!asResult.keys.contains("isClose")) {
        this._dataModel.filterParams = asResult as Map<String, String?>;
        _dataModel.requestData(true);
      }
    }
  }

  ///头部布局
  Widget buildHeadView() {
    int cnt = _dataModel.visitListBean?.statistics?.cnt ?? 0; //拜访总数
    int effectiveCnt =
        _dataModel.visitListBean?.statistics?.effectiveCnt ?? 0; //有效拜访总数
    int doorCnt = _dataModel.visitListBean?.statistics?.doorCnt ?? 0; //	上门拜访总数
    int effDoorCnt =
        _dataModel.visitListBean?.statistics?.effDoorCnt ?? 0; //有效上门拜访数
    int effPhoneCnt =
        _dataModel.visitListBean?.statistics?.effPhoneCnt ?? 0; //	有效电话拜访数
    int phoneCnt = _dataModel.visitListBean?.statistics?.phoneCnt ?? 0; //电话拜访总数
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(10),
          color: Colors.white,
          height: 78,
          child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(child: firstLineView("拜访总数", cnt.toString(), false)),
                Expanded(
                    child: firstLineView("有效拜访", effectiveCnt.toString(), true))
              ]),
        ),
        Divider(height: 1, color: ColorUtils().color_E1E1E5),
        Container(
          padding: EdgeInsets.all(10),
          color: Colors.white,
          height: 44,
          alignment: Alignment.centerLeft,
          child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Expanded(child: secondLineView("上门拜访总数", doorCnt.toString())),
                Expanded(child: secondLineView("有效上门拜访", effDoorCnt.toString()))
              ]),
        ),
        Divider(height: 1, color: ColorUtils().color_E1E1E5),
        Container(
            padding: EdgeInsets.all(10),
            color: Colors.white,
            height: 44,
            alignment: Alignment.centerLeft,
            child: Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                      child: secondLineView("电话拜访总数", phoneCnt.toString())),
                  Expanded(
                      child: secondLineView("有效电话拜访", effPhoneCnt.toString()))
                ]))
      ],
    );
  }

  ///头部第一行
  Widget firstLineView(String title, String content, bool showTip) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            if (showTip) {
              PopDialog.showPop(context: context, topP: 70);
            }
          },
          child: IntrinsicHeight(
            child: Row(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(title, style: styleGray),
                SizedBox(width: 5),
                Visibility(
                    child: Image.asset('assets/images/visit/icon_prompt.png',
                        width: 15, height: 15),
                    visible: showTip)
              ],
            ),
          ),
        ),
        SizedBox(height: 10),
        Text(content,
            style: TextStyle(
                color: ColorUtils().color_292933,
                fontSize: 24,
                fontWeight: FontWeight.bold)),
      ],
    );
  }

  ///头部第二行&第三行
  Widget secondLineView(String title, String content) {
    return Row(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(title, style: styleGray),
        SizedBox(width: 10),
        Text(content,
            style: TextStyle(
                color: ColorUtils().color_292933,
                fontSize: 15,
                height: 1,
                fontWeight: FontWeight.bold)),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
    if (!_dataModel.isDisposed) {
      _dataModel.dispose();
    }
  }

  ///拜访列表
  Widget buildListView() {
    return EasyRefresh.custom(
        controller: _refreshController,
        scrollController: _scrollController,
        enableControlFinishRefresh: true,
        enableControlFinishLoad: true,
        onRefresh: () async {
          _dataModel.requestData(true);
        },
        onLoad: !_dataModel.isLastPage
            ? () async {
                _dataModel.requestData(false);
              }
            : null,
        slivers: [
          SliverList(
            delegate:
                SliverChildBuilderDelegate((BuildContext context, int index) {
              //创建列表项
              bool showTitle = true;
              String time = "";
              List<RowBean>? rowsBean = _dataModel.rowBeans;
              time = TimeUtils().formatTime2(rowsBean?[index].createTime ?? 0);
              if (index == 0) {
                showTitle = true;
              } else {
                String lastTime = TimeUtils()
                    .formatTime2(rowsBean?[index - 1].createTime ?? 0);
                showTitle = time != lastTime;
              }
              return GestureDetector(
                onTap: () {
                  // if (rowsBean?[index].perfect == 1) {
                  //   return;
                  // }
                  var row = rowsBean?[index];
                  if(row?.perfect == 1 && row?.visitType == 2){
                    return;
                  }
                  var router =
                      '/visit_detail_page?scheduleId=${rowsBean?[index].id}';
                  XYYContainer.open(router);
                },
                child: visitListItem(
                    showTitle, time, rowsBean?[index] ?? RowBean()),
              );
            }, childCount: _dataModel.rowBeans?.length ?? 0),
          )
        ],
        emptyWidget: getEmptyWidget());
  }

  ///列表item
  Widget visitListItem(bool showTitle, String time, RowBean rowBean) {
    return Column(
      mainAxisSize: MainAxisSize.max,
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Visibility(
          child: Container(
            padding: EdgeInsets.only(top: 15, left: 10),
            child: Text(
              time,
              style: TextStyle(color: ColorUtils().color_292933, fontSize: 16),
            ),
          ),
          visible: showTitle,
        ),
        Container(
          constraints: BoxConstraints(minHeight: 50),
          margin: EdgeInsets.fromLTRB(10, 5, 10, 5),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(5), color: Colors.white),
          child: Row(
            children: [
              itemLeftView(rowBean),
              Container(
                color: VisitTypeCode.getDividerColorByType(rowBean.type ?? 0),
                height: 95,
                width: 2,
              ),
              Expanded(child: itemRightView(rowBean))
            ],
          ),
        )
      ],
    );
  }

  Widget itemLeftView(RowBean rowBean) {
    return Container(
      width: 86,
      padding: EdgeInsets.all(10),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Text(
            rowBean.visitTypeText ?? "",
            style: styleBlack,
          ),
          SizedBox(height: 10),
          Text(rowBean.creatorName ?? "",
              style: styleGray, maxLines: 1, overflow: TextOverflow.ellipsis),
          SizedBox(height: 10),
          Text(rowBean.timeFormat ?? "", style: styleGray),
        ],
      ),
    );
  }

  Widget itemRightView(RowBean rowBean) {
    print(rowBean);
    return Container(
      padding: EdgeInsets.all(10),
      child: Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Text(
                rowBean.merchantName ?? "",
                style: styleBlack,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              SizedBox(height: 10),
              Text('目的：${rowBean.visitReasonText ?? "—"}',
                  style: styleGray,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
              SizedBox(height: 10),
              Text('总结：${rowBean.visitDemo ?? "—"}',
                  style: styleGray,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis),
            ],
          ),
          Positioned(
            top: 0,
            right: 0,
            child: Visibility(
              child: Image.asset(
                  rowBean.effective == true ? 'assets/images/visit/icon_effective_schedule.png': 'assets/images/visit/icon_no_effective_schedule.png',
                  width: 38,
                  height: 38,),
              visible: rowBean.perfect != 1 && rowBean.effective != true,
            ),
          ),
          Positioned(
            right: 0,
            bottom: 0,
            child: Visibility(
              child: GestureDetector(
                onTap: () {
                  Map<String, dynamic> perfectMap = {};
                  perfectMap["visitId"] = rowBean.id.toString();
                  perfectMap["merchantName"] = rowBean.merchantName;
                  perfectMap["contactor"] = rowBean.contactor;
                  perfectMap["isEffective"] = rowBean.isEffective.toString();
                  perfectMap["merchantId"] = rowBean.merchantId.toString();
                  perfectMap['customerId'] = rowBean.customerId.toString();
                  perfectMap["mobile"] = rowBean.mobile.toString();
                  perfectMap["talkTime"] = rowBean.talkTime.toString();
                  perfectMap["isRegisterFlag"] =
                      rowBean.registerFlag.toString();
                  var jsonString = json.encode(perfectMap);
                  var router =
                      "/add_perfect_visit_page?perfectJSON=$jsonString";
                  router = Uri.encodeFull(router);
                  XYYContainer.open(router, callback: (result) {
                    if (result != null) {
                      if (result['success'] == 'true') {
                        _refreshController.callRefresh();
                      }
                    }
                  });
                },
                child: Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: ColorUtils().color_35c561),
                  padding: EdgeInsets.fromLTRB(7, 3, 7, 3),
                  child: Text(
                    '去完善',
                    style: TextStyle(color: Colors.white, fontSize: 13),
                  ),
                ),
              ),
              visible: rowBean.perfect == 1,
            ),
          ),
        ],
      ),
    );
  }

  ///列表异常状态设置
  Widget? getEmptyWidget() {
    if (_dataModel.isSuccess == null) {
      return null;
    }
    if (_dataModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _dataModel.requestData(true);
      });
    }

    if ((_dataModel.rowBeans?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  ///add悬浮按钮
  Widget buildAddView() {
    return GestureDetector(
      onTap: () async {
        String? roleJSON = await UserAuthManager.getRoleJSONString();

        /// M级账号跳转陪访
        if (this.isManagerLevel) {
          var router = '/add_accompany_visit_page?rolesJSON=$roleJSON';
          router = Uri.encodeFull(router);
          print('8888 - $router');
          XYYContainer.open(router);
        } else {
          /// 跳转拜访
          var router = '/add_visit_page?rolesJSON=$roleJSON';
          router = Uri.encodeFull(router);
          XYYContainer.open(router);
        }
      },
      child: Image.asset(
        "assets/images/visit/icon_add.png",
        width: 60,
        height: 60,
      ),
    );
  }
}

class VisitDataModel extends ChangeNotifier {
  int offset = 1;
  int? timeType = 1;
  int scheduleType = -1;
  int visitReason = -1;
  int ifEffective = -1;
  int perfect = -1;
  VisitListBean? visitListBean;
  List<RowBean>? rowBeans;
  bool isLastPage = false;
  bool? isSuccess;
  var _refreshController;
  VisitStatusTagData? tagData;
  Map<String, String?> filterParams = Map();
  bool isDisposed = false;

  VisitDataModel(this._refreshController);

  Map<String, String?> buildParamsMap() {
    var params = Map<String, String?>();
    //分页参数
    params["offset"] = offset.toString();
    params["limit"] = "10";
    params['scheduleType'] = scheduleType.toString();
    params['visitReason'] = visitReason.toString();
    params['ifEffective'] = ifEffective.toString();
    params['perfect'] = perfect.toString();

    params.addAll(filterParams);

    if (filterParams['timeType'] != "-100") {
      if (!filterParams.containsKey('timeType')) {
        params['timeType'] = this.timeType.toString();
      }
    } else {
      params.removeWhere((key, value) => key == 'timeType');
    }

    return params;
  }

  void requestData(bool refresh) {
    if (refresh) {
      offset = 1;
      rowBeans = null;
      EasyLoading.show(dismissOnTap: true);
    } else {
      offset++;
    }
    Network<VisitListBean>(VisitListBean())
        .requestData("visit/pageList",
            method: RequestMethod.GET, parameters: buildParamsMap())
        .then((value) {
      EasyLoading.dismiss();
      if (isDisposed) {
        return;
      }
      visitListBean = value;
      isLastPage = value.pageInfo?.lastPage ?? false;
      isSuccess = value.isSuccess;
      if (rowBeans == null) {
        rowBeans = value.pageInfo?.rows;
      } else {
        if (value.pageInfo != null && value.pageInfo!.rows != null) {
          rowBeans!.addAll(value.pageInfo!.rows!);
        }
      }
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: isLastPage);
      notifyListeners();
    });
  }

  @override
  void dispose() {
    super.dispose();
    isDisposed = true;
  }

  void requestFilterData() {
    NetworkV2<VisitStatusTagData>(VisitStatusTagData())
        .requestDataV2("schedule/v290/enumForSchedule",
            method: RequestMethod.GET)
        .then((value) {
      if (isDisposed) {
        return;
      }
      if (value.isSuccess == true) {
        tagData = value.getData();
      }
      notifyListeners();
    });
  }
}
