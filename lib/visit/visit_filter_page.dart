import 'dart:convert';

import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_filter_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/order_filter_page.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_area.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_operation_item.dart';
import 'package:XyyBeanSproutsFlutter/order/widgets/order_filter_time_item.dart';
import 'package:flutter/material.dart';

class VisitFilterPage extends BasePage {
  final String? sourceJSON;
  final Map<String, String?>? currentParams;

  VisitFilterPage({
    this.sourceJSON,
    this.currentParams,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return VisitFilterState(
      sourceJSON: this.sourceJSON,
      filterParams: this.currentParams,
    );
  }
}

class VisitFilterState extends BaseState {
  List<OrderFilterConfigModel> dataSource = [];
  List<dynamic> branchCodeData = [];
  String? sourceJSON;

  /// 当前页面选中的参数
  Map<String, String?>? filterParams;

  VisitFilterState({
    this.sourceJSON,
    this.filterParams,
  });

  @override
  void initState() {
    super.initState();
    // 防止外部传入异常数据
    if (this.filterParams == null) {
      this.filterParams = Map<String, String?>();
    }
    // 配置选项
    this.configSource();
  }

  void configSource() {
    /// 清空数据源
    this.dataSource = [];

    /// 数据转换
    Map<String, dynamic> jsonData = json.decode(this.sourceJSON!);

    /// 范围选项
    OrderFilterConfigModel areaModel = OrderFilterConfigModel(
      title: '拜访范围',
      itemKey: 'area',
      items: null,
      selectIds: [],
      allowsMultipleSelection: true,
      canClean: false,
    );
    this.dataSource.add(areaModel);

    /// 时间选项
    List<dynamic> timeItemData = jsonData['dataTimeList'];
    var timeType = this.filterParams?['timeType'];
    String timeSelectedData = ((timeType != "-100") ? timeType : "") ?? "1";
    int? defaultId = (timeType == "-100") ? null : 1;
    OrderFilterConfigModel sceneTypeModel = OrderFilterConfigModel(
      title: '拜访时间',
      itemKey: 'timeType',
      defaultId: defaultId,
      items: OrderFilterItemData.formJsonList(timeItemData),
      selectIds: timeSelectedData.length > 0
          ? timeSelectedData.split(',').map((e) => int.parse(e)).toList()
          : [],
      allowsMultipleSelection: false,
      canClean: false,
    );
    this.dataSource.add(sceneTypeModel);

    /// 拜访类型
    List<dynamic> scheduleTypeItemData = jsonData['scheduleTypeList'];
    String scheduleTypeSelectedData =
        this.filterParams?['scheduleType'] ?? "-1";
    //拜访类型的id返回的是String 这里做一下解析
    scheduleTypeItemData.forEach((element) {
      if (element['id'] is String) {
        element['id'] = int.parse(element['id']);
      }
    });
    OrderFilterConfigModel scheduleTypeModel = OrderFilterConfigModel(
      title: '拜访类型',
      itemKey: 'scheduleType',
      defaultId: -1,
      items: OrderFilterItemData.formJsonList(scheduleTypeItemData),
      selectIds:
          scheduleTypeSelectedData.split(',').map((e) => int.parse(e)).toList(),
      allowsMultipleSelection: true,
      canClean: false,
    );
    this.dataSource.add(scheduleTypeModel);

    /// 完善状态
    List<dynamic> perfectItemData = jsonData['perfectList'];
    String perfectSelectedData = this.filterParams?['perfect'] ?? "-1";
    OrderFilterConfigModel perfectModel = OrderFilterConfigModel(
      title: '完善状态',
      itemKey: 'perfect',
      defaultId: -1,
      items: OrderFilterItemData.formJsonList(perfectItemData),
      selectIds:
          perfectSelectedData.split(',').map((e) => int.parse(e)).toList(),
      allowsMultipleSelection: false,
      canClean: false,
    );
    this.dataSource.add(perfectModel);

    /// 拜访事由
    List<dynamic> visitReasonItemData = jsonData['visitReasonList'];
    String visitReasonSelectedData = this.filterParams?['visitReason'] ?? "-1";
    OrderFilterConfigModel visitReasonModel = OrderFilterConfigModel(
      title: '拜访事由',
      itemKey: 'visitReason',
      defaultId: -1,
      items: OrderFilterItemData.formJsonList(visitReasonItemData),
      selectIds:
          visitReasonSelectedData.split(',').map((e) => int.parse(e)).toList(),
      allowsMultipleSelection: true,
      canClean: false,
    );
    this.dataSource.add(visitReasonModel);

    /// 是否有效
    List<dynamic> ifEffectiveItemData = jsonData['ifEffectiveList'];
    String ifEffectiveSelectedData = this.filterParams?['ifEffective'] ?? "-1";
    OrderFilterConfigModel ifEffectiveModel = OrderFilterConfigModel(
      title: '是否有效',
      itemKey: 'ifEffective',
      defaultId: -1,
      items: OrderFilterItemData.formJsonList(ifEffectiveItemData),
      selectIds:
          ifEffectiveSelectedData.split(',').map((e) => int.parse(e)).toList(),
      allowsMultipleSelection: false,
      canClean: false,
    );
    this.dataSource.add(ifEffectiveModel);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return SafeArea(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          child: Column(
            children: [
              Expanded(
                child: ListView.builder(
                  itemCount: this.dataSource.length,
                  itemBuilder: (context, index) {
                    return getIndexWidget(context, index);
                  },
                ),
              ),
              Container(
                padding: EdgeInsets.fromLTRB(15, 15, 15, 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          border: Border.all(
                            color: Color(0xFFD3D3D3),
                            width: 1,
                          ),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child:TextButton(
                          onPressed: resetAllItem,
                          child: Container(
                            child: Text(
                              '重置',
                              style: TextStyle(
                                color: Color(0xFF333333),
                                fontSize: 16,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xFF35C561),
                          borderRadius: BorderRadius.circular(5),
                        ),
                        child:TextButton(
                          onPressed: determineParams,
                          child: Container(
                            child: Text(
                              '确定',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  // 列表中个Item
  Widget getIndexWidget(BuildContext context, int index) {
    OrderFilterConfigModel configModel = this.dataSource[index];
    switch (configModel.itemKey) {
      case 'area':
        return OrderFilterArea(
          title: configModel.title,
          name: this.filterParams?['name'] ?? "全部",
          areaChange: (id, name, isGroup) {
            if (isGroup) {
              this.filterParams?.remove('searchUserId');
              this.filterParams?['groupId'] = id ?? "";
            } else {
              this.filterParams?.remove('groupId');
              this.filterParams?['searchUserId'] = id ?? "";
            }
            this.filterParams?['name'] = name ?? "";
          },
        );
      case 'timeType':
        return OrderFilterTimeItem(
          configModel: configModel,
          startTime: this.filterParams?["startTime"] ?? "",
          endTime: this.filterParams?["endTime"] ?? "",
          callBack: (sceneParam) {
            if (sceneParam.containsKey("startCreateTime") &&
                sceneParam['startCreateTime']!.length > 0) {
              this.filterParams?['startTime'] = sceneParam['startCreateTime'];
              this.filterParams?['endTime'] = sceneParam['endCreateTime'];
              // 用作标识
              this.filterParams?['timeType'] = "-100";
            } else {
              this.filterParams?['timeType'] = sceneParam['timeType'];
              this.filterParams?.removeWhere(
                  (key, value) => key == 'startTime' || key == 'endTime');
            }
          },
        );
      default:
        return OrderFilterOperationItem(
          title: configModel.title,
          itemKey: configModel.itemKey,
          items: configModel.items,
          defaultId: configModel.defaultId,
          allowsMultipleSelection: configModel.allowsMultipleSelection,
          selectIds: configModel.selectIds,
          canClean: false,
          changeValue: (itemKey, selectedIds) {
            this.filterParams?[itemKey ?? ""] =
                selectedIds?.map((e) => e.toString()).join(",") ?? "";
          },
        );
    }
  }

  void resetAllItem() {
    setState(() {
      this.filterParams = {};
      this.configSource();
    });
  }

  void determineParams() {
    this.filterParams?.removeWhere((key, value) => value?.length == 0);
    Navigator.of(context).pop(this.filterParams);
  }

  @override
  String getTitleName() {
    return '';
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      leftType: LeftButtonType.none,
      rightButtons: [
        LeftButton(
            leftBtnType: LeftButtonType.close,
            onPressed: () {
              Navigator.of(context).pop({"isClose": "true"});
            }),
      ],
    );
  }
}
