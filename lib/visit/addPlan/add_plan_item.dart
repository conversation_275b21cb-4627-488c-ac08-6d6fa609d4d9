import 'package:XyyBeanSproutsFlutter/visit/bean/plan/plan_search_data.dart';
import 'package:flutter/material.dart';

class AddPlanItem extends StatelessWidget {
  final PlanSearchModel data;
  final Function(String) changeTag;//改变了标签
  final Map<String, String> filterParams;

  AddPlanItem({required this.data, required this.changeTag, required this.filterParams});

  @override
  Widget build(BuildContext context) {
    debugPrint('${data.customerName},${data.selected}');
    return Container(
      padding: EdgeInsets.only(top: 12, left: 12, bottom: 10, right: 10),
      margin: EdgeInsets.only(top: 10, left: 10, bottom: 0, right: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(width: 4),
          Image.asset(
            data.selected ? "assets/images/customer/customer_sku_collect_check_selected.png"
                : "assets/images/customer/customer_sku_collect_check_unselected.png",
            width: 18,
            height: 24,
          ),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(
                      child: Text(
                        data.customerName,
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Text(
                      data.merchantStatusText ?? '',
                      style: TextStyle(
                          fontSize: 14,
                          color: data.merchantStatus == 2
                              ? Color(0xffFF2021) // 红色
                              : data.merchantStatusText == 3
                              ? Color(0xffECA100) // 黄色
                              : Color(0xff00B377), // 默认绿色
                          // 1正常、2正常 未激活、3 冻结、4未注册
                      ),
                    ),
                  ],
                ), //第一排：名称
                SizedBox(height: 8),
                Row(
                  children: [
                    Image.asset(
                      'assets/images/customer/customer_map_location.png',
                      width: 8,
                      height: 11,
                    ),
                    SizedBox(width: 4),
                    Expanded(
                      child: RichText(
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1, // 限制文本最大行数
                        text: TextSpan(
                          text: "距您${data.distance}",
                          style: TextStyle(fontSize: 12,color: Colors.black),
                          children: <TextSpan>[
                            TextSpan(
                              text: "  |  ",//分割线
                              style: TextStyle(fontSize: 12, color: Colors.grey, fontWeight: FontWeight.w100),
                            ),
                            TextSpan(
                              text: data.address,
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),//第二排：位置、文案、…… 、按钮this.filterParams = result["filterParams"];
                SizedBox(height: 8),
                Row(
                  children: [
                    if (filterParams["licenseCode"] != null && filterParams["licenseCode"]!.isNotEmpty)
                      Container(
                        margin: EdgeInsets.only(left: 8),
                        padding: EdgeInsets.only(left: 5, right: 5, top: 1, bottom: 1),
                        decoration: BoxDecoration(
                          color: Color(0xFFF0FBF5), // 背景色
                          borderRadius: BorderRadius.circular(1.0),
                          border: Border.all(
                            color: Color(0xFF35C561), // 边框颜色
                            width: 0.5, // 边框宽度
                          ),
                        ),
                        child: Text(
                          filterParams["licenseCode"]!,
                          style: TextStyle(
                            color: Color(0xff00B377), // 文字颜色
                            fontSize: 10.0, // 文字大小
                          ),
                        ),
                      ),
                    if (filterParams["licenseCode"] != null && filterParams["licenseCode"]!.isNotEmpty)
                    Container(
                      margin: EdgeInsets.only(left: 8),
                      padding: EdgeInsets.only(left: 5,right: 5, top: 1, bottom: 1),
                      decoration: BoxDecoration(
                        color: Color(0xFFFAFBFA),// 背景色
                        borderRadius: BorderRadius.circular(1.0),
                        border: Border.all(
                          color: Color(0xFFDADADA), // 边框颜色
                          width: 0.5, // 边框宽度
                        ),
                      ),
                      child: Text(
                        filterParams["licenseCode"]!,
                        style: TextStyle(
                          color: Color(0xff676773), // 文字颜色
                          fontSize: 10.0, // 文字大小
                        ),
                      ),
                    ),
                    Container(
                      margin: EdgeInsets.only(left: 8),
                      padding: EdgeInsets.only(left: 5,right: 5, top: 1, bottom: 1),
                      decoration: BoxDecoration(
                        color: Color(0xFFFAFBFA),// 背景色
                        borderRadius: BorderRadius.circular(1.0),
                        border: Border.all(
                          color: Color(0xFFDADADA), // 边框颜色
                          width: 0.5, // 边框宽度
                        ),
                      ),
                      child: Text(
                        '本月未动销',
                        style: TextStyle(
                          color: Color(0xff676773), // 文字颜色
                          fontSize: 10.0, // 文字大小
                        ),
                      ),
                    ),
                  ],
                ),//第三排：3个标签
              ],
            ),
          ),
        ],
      ),
    );
  }
}