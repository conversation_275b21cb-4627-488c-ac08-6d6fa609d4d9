//添加计划 单个/app/crm/visit/plan/add，批量/app/crm/visit/plan/batchAdd

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/plan/plan_search_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/plan/visit_plan_data.dart';
import 'package:XyyBeanSproutsFlutter/visit/addPlan/add_plan_item.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/visit/addPlan/bottomSheet_listWidget.dart';
import 'package:XyyBeanSproutsFlutter/visit/addPlan/add_plan_filter.dart';

class AddPlanPage extends BasePage {

  // AddPlanPage({
  // });

  @override
  BaseState<StatefulWidget> initState() {
    return _AddPlanPageState();
  }
}

class _AddPlanPageState extends BaseState<AddPlanPage> {
  bool requestSuccess = true;
  List<PlanSearchModel> dataSource = [];
  bool isSelectedBtn1 = false;//公海未注册
  bool isSelectedBtn2 = false;//公海下单客户

  String? lat;
  String? lng;
  String? keyword = '';

  Map<String, dynamic> params = {};
  Map<String, String> filterParams = {};
  List<PlanSearchModel> selectData = [];

  @override
  void initState() {
    this.requestLocation();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (!FocusScope.of(context).hasPrimaryFocus &&
            FocusScope.of(context).focusedChild != null) {
          FocusManager.instance.primaryFocus?.unfocus();
        }
      },
      behavior: HitTestBehavior.translucent,
      child: Column(
        children: [
          SAppBarSearch(
            height: 44,
            hintText: "搜索对象",
            showLeading: false,
            hideCancel: true,
            autoFocus: false,
            onSearch: (value) {
              if (value.isEmpty) {
                showToast("请输入搜索内容");
                return;
              }
              if (value is String) {
                debugPrint("搜索内容");
                this.keyword = value;
                requestLocation();
              //   this._easyRefreshController.callRefresh();
              }
            },
          ),
          Divider(
            color: Color(0xffF5F5F5),
            height: 10.0,
            thickness: 0.5,
            indent: 0,
            endIndent: 0,
          ),//分割线
          Row(
            children: [
              SizedBox(width: 10,),
              // _buildUnregisteredOrderBtn('公海未注册', true),
              CupertinoButton(
                minSize: 30,
                padding: const EdgeInsets.only(left: 8, right: 8),
                color: isSelectedBtn1 ? Color(0xffE4F8F1) : Color(0xffF7F7F8),
                borderRadius: BorderRadius.circular(2.0),
                pressedOpacity: 1,
                child: Text(
                  "公海未注册",
                  style: TextStyle(
                    color: isSelectedBtn1 ? Color(0xff00B377) : Color(0xff676773),
                    fontSize: 12,
                    fontWeight: isSelectedBtn1 ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
                onPressed: () {
                  debugPrint("点击了公海未注册");
                  isSelectedBtn1 = !isSelectedBtn1;
                  if (isSelectedBtn1){
                    setState(() {
                      isSelectedBtn2 = false;
                    });
                  }
                  this.filterParams = {};
                  requestAddPlanListData();
                },
              ),
              SizedBox(width: 10,),
              // _buildUnregisteredOrderBtn('公海下单客户', false),
              CupertinoButton(
                minSize: 30,
                padding: const EdgeInsets.only(left: 8, right: 8),
                color: isSelectedBtn2 ? Color(0xffE4F8F1) : Color(0xffF7F7F8),
                borderRadius: BorderRadius.circular(2.0),
                pressedOpacity: 1,
                child: Text(
                  "公海下单客户",
                  style: TextStyle(
                    color: isSelectedBtn2 ? Color(0xff00B377) : Color(0xff676773),
                    fontSize: 12,
                    fontWeight: isSelectedBtn2 ? FontWeight.w600 : FontWeight.normal,
                  ),
                ),
                onPressed: () {
                  debugPrint("点击了公海下单客户");
                  isSelectedBtn2 = !isSelectedBtn2;
                  if (isSelectedBtn2){
                    setState(() {
                      isSelectedBtn1 = false;
                    });
                  }
                  this.filterParams = {};
                  requestAddPlanListData();
                },
              ),
              Spacer(),
              CupertinoButton(
                  //按钮被按下时的不透明度程度，0~1之间
                  minSize: 30,
                  pressedOpacity: 1,
                  child: Image.asset(
                    "assets/images/titlebar/icon_filter.png",
                    width: 21,
                    height: 21,
                  ),
                  onPressed: () {
                    debugPrint("点击了筛选");
                    jumpFilter();
                    // showModalBottomSheet(
                    //   context: context,
                    //   backgroundColor: Colors.transparent,//重点
                    //   builder: (BuildContext context) {
                    //     return Container(
                    //       decoration: BoxDecoration(
                    //         color: Colors.white,
                    //         borderRadius: BorderRadius.only(
                    //           topLeft: Radius.circular(8.0),
                    //           topRight: Radius.circular(8.0),
                    //         ),
                    //       ),
                    //       child: AddPlanFilterPage(
                    //         dataSource: this.dataSource,
                    //         onSelect: (PlanListModel model){
                    //           debugPrint("点击了---------");
                    //         },
                    //       ),
                    //     );
                    //   },
                    // );
                  }), //筛选按钮
            ],
          ),
          Expanded(
            child: Container(
              color: Color(0xFFF7F7F8),
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  debugPrint('选中状态：${this.dataSource[index].selected}');
                  return GestureDetector(
                    onTap: () {
                      debugPrint('点击了$index');
                      this.dataSource[index].selected = !this.dataSource[index].selected;
                      setState(() {
                        debugPrint('选中状态：${this.dataSource[index].selected}');
                      });
                    },
                    behavior: HitTestBehavior.opaque,
                    child: AddPlanItem(
                      data: this.dataSource[index],
                      filterParams:this.filterParams,
                      changeTag: (String str) {
                        debugPrint("删除回调：$str");
                      },
                    ),
                  );
                },
              ),
            ),
          ),
          _buildBottomView(),//bottom：生成路线图、添加计划按钮
        ],
      ),
    );
  }

  //bottom：选中对象、展开详情
  Widget _buildBottomView(){
    return Container(
      height: 49 + MediaQuery.of(context).padding.bottom, // 容器的高度，根据你的需求设置
      color: Colors.white, // 底部容器的背景颜色
      padding: EdgeInsets.only(left: 10, top: 6, right: 10), // 容器内边距
      child: Align(
        alignment: Alignment.topCenter, // 设置文本水平靠左、垂直居中
        child: Row(
          children: [
            Text(
              "选中对象：",
              style: TextStyle(
                  fontSize: 14,
                  color: Color(0xff9494A6)
              ),
            ),
            Text(
              '${this.selectData.length}/20',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF333333),
              ),
            ),
            SizedBox(width: 6),
            //展开详情 按钮
            _buildButton(),
            Spacer(),
            // 最右侧的文字按钮
            TextButton(
              onPressed: () {
                debugPrint("点击加入计划");
              },
              child: const Text(
                "加入计划",
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ButtonStyle(
                // 按钮大小
                minimumSize: MaterialStateProperty.all(const Size(109, 37)),
                // 水波纹颜色 不想使用 设置透明即可
                overlayColor: MaterialStateProperty.all(Colors.transparent),
                //背景颜色
                backgroundColor: MaterialStateProperty.all(Color(0xff00B377)),
                // 设置形状、圆角
                shape: MaterialStateProperty.all(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(5.0), // 设置圆角度数为5
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 展开详情 按钮
  Widget _buildButton(){
    return ElevatedButton(
      onPressed: () {
        // 处理按钮点击事件
        showModalBottomSheet(
          context: context,
          backgroundColor: Colors.transparent,//重点
          builder: (BuildContext context) {
            return Container(
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8.0),
                  topRight: Radius.circular(8.0),
                ),
              ),
              child: BottomSheetListWidget(
                dataSource: this.selectData,
                onSelect: (PlanSearchModel model){
                  debugPrint("点击了---------");
                },
              ),
            );
          },
        );
      },
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.zero,
        minimumSize: Size(47, 37),
        elevation: 0, // 禁用阴影
        primary: Colors.white, // 背景颜色
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "展开详情",
            style: TextStyle(
              color: Color(0xff00B377), // 文字颜色
              fontSize: 14,
            ),
          ),
          // SizedBox(width: 2), // 文字和图片之间的间距
          Image.asset(
            'assets/images/pop_data/pop_statistics_right.png',
            width: 12,
            height: 12,
          ),
        ],
      ),
    );
  }

  /// 空数据
  Widget? getEmptyWidget() {
    if (!this.requestSuccess && this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        this.requestLocation();
      });
    }
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  /// 请求定位信息
  void requestLocation() {
    XYYContainer.locationChannel.locate().then((value) {
      if (this.mounted && (value.isSuccess ?? false)) {
        this.lat = value.latitude;
        this.lng = value.longitude;
      }
      this.requestAddPlanListData();
    });
  }

  /// 当前页面是否获取经纬度
  bool get hasLocation {
    return this.lat?.isEmpty == false && this.lng?.isEmpty == false;
  }

  /// 请求网络
  Future<void> requestAddPlanListData() async {
    if (!this.hasLocation){
      showToast('未获取到当前位置信息，请检查设备');
    }
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }
    var userInfo = await UserInfoUtil.getUserInfo();

    var params = {
      'sysUserId': userInfo?.sysUserId,
      'lat': this.lat,
      'lng': this.lng,
      if (this.isSelectedBtn1 || this.isSelectedBtn2)
        'highSeas': this.isSelectedBtn1 ? '1' : '2',//1-公海未注册；2-公海下单客户
      if (this.keyword!.isNotEmpty)
        'keyword': this.keyword,
    };
    params.addAll(filterParams);

    var result =
    await NetworkV2<PlanSearchModel>(PlanSearchModel()).requestDataV2(
      'visit/plan/search',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      this.requestSuccess = result.isSuccess == true;
      if (result.isSuccess == true) {
        List<PlanSearchModel>? source = result.getListData();
        if (source != null) {
          this.dataSource = source;
          if (this.dataSource.length == 0){
            this.getEmptyWidget();
          }
        }
      }
      setState(() {});
    }
  }

  @override
  String getTitleName() {
    return "添加计划";
  }

  /// 跳转筛选页面
  void jumpFilter() async {
    /// 收起页面上的键盘
    FocusScope.of(context).requestFocus(FocusNode());

    dynamic result = await showCupertinoModalPopup(
      context: context,
      builder: (BuildContext context) {
        return ClipRRect(
          borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
          child: Container(
            height: MediaQuery.of(context).size.height - 88,
            child: AddPlanFilterPage(
              filterParams: this.filterParams,
              isMapFilter: true,
            ),
          ),
        );
      },
    );
    debugPrint('-------: $result');
    if (result != null) {
      if (result is Map<String, dynamic>) {
        if (result.containsKey("filterParams")) {
          this.filterParams = result["filterParams"];
          this.isSelectedBtn1 = false;
          this.isSelectedBtn2 = false;
          this.requestAddPlanListData();
        }
      }
    }
  }

  Map<String, dynamic> getPageParams() {
    Map<String, dynamic> pageParams = {};
    pageParams.addAll(this.params);
    pageParams.addAll(this.filterParams);
    return pageParams;
  }


}
