// AddPlanFilter
import 'dart:collection';
import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/filter/common_filter_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/widget/select_object_multiple_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/plan/add_plan_filter_data.dart';

class AddPlanFilterPage extends StatefulWidget {
  final Map<String, String> filterParams;
  final bool isMapFilter;

  AddPlanFilterPage({
    this.filterParams = const {},
    this.isMapFilter = false,
  });

  @override
  State<StatefulWidget> createState() {
    return AddPlanFilterPageState();
  }
}

class AddPlanFilterPageState extends State<AddPlanFilterPage> {
  /// 数据源
  List<SelectObjectFilterConfigModel> dataSource = [];

  /// 用来保存提示按钮
  GlobalKey _anchorKey = GlobalKey();

  /// 当前选择的参数
  Map<String, String> filterMap = {};

  /// 当前页面上展示用的缓存数据
  Map<String, dynamic> cacheMap = {};

  @override
  void initState() {
    this.filterMap = widget.filterParams;
    this.requestFilterData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding:
      EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
      color: Color(0xFFFFFFFF),
      child: Column(
        children: [
          this.backBar(),
          Expanded(
            child: ListView.builder(
              cacheExtent: 99999,
              padding: EdgeInsets.zero,
              itemCount: this.dataSource.length,
              itemBuilder: this.builderItem,
            ),
          ),
          this.bottomWidget(),
        ],
      ),
    );
  }

  Widget backBar() {
    return widget.isMapFilter
        ? Container(
      child: Row(
        children: [
          Spacer(),
          Container(
            child: IconButton(
              icon: ImageIcon(
                AssetImage('assets/images/titlebar/icon_close.png'),
                color: Colors.black,
                size: 22,
              ),
              onPressed: () {
                print("不响应事件");
                Navigator.of(context).pop();
              },
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
            ),
          )
        ],
      ),
    )
        : Container();
  }

  /// 构建item
  Widget builderItem(BuildContext context, int index) {
    SelectObjectFilterConfigModel config = this.dataSource[index];
    return Container(
      padding: EdgeInsets.all(10),
      child: SelectObjectFilterItem.single(
        title: config.itemTitle,
        contents: config.contents,
        defaultId: config.defaultId,
        selectedIds: this.getSelectIdsForKey(config.itemKey),
        isAllowMultipleSelection: config.isAllowMultipleSelection,
        isMutexByDefault: config.isMutexByDefault,
        isAllowClean: config.isAllowClean,
        valueChange: (itemKey, selectIds) {
          this.filterMap[itemKey] = selectIds.join(",");
        },
        itemKey: config.itemKey,
      ),
    );
  }

  /// 构建底部按钮
  Widget bottomWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 15, 15, 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Color(0xFFD3D3D3),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(5),
              ),
              child: TextButton(
                onPressed: resetAllItem,
                style: ButtonStyle(
                  overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                  MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    '重置',
                    style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xFF35C561),
                borderRadius: BorderRadius.circular(5),
              ),
              child: TextButton(
                onPressed: determineParams,
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                  MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  /// Events
  void resetAllItem() {
    this.cacheMap = {};
    this.filterMap = {};
    setState(() {});
  }

  void determineParams() {
    // trackOrderAndLevelEvent();
    //删除字典（Map）中值为空（null）的键值对
    this.filterMap.removeWhere((key, value) => value == 'null');
    Map<String, dynamic> result = {
      "filterParams": this.filterMap,
    };
    Navigator.of(context).pop(result);
  }

  // void trackOrderAndLevelEvent() {
  //   if (filterMap.containsKey(ObjectFilterKey.orderKeyName)) {
  //     switch (filterMap[ObjectFilterKey.orderKeyName]?.toString()) {
  //       case "1": // 本月未动销
  //         track(widget.isMapFilter ? "mc-map-unorder" : "mc-customer-unorder");
  //         break;
  //       case "2": // 本月已动销
  //         track(widget.isMapFilter ? "mc-map-haorder" :"mc-customer-haorder");
  //         break;
  //       case "3": // 从未下单
  //         track(widget.isMapFilter ? "mc-map-neverorder" :"mc-customer-neverorder");
  //         break;
  //     }
  //   }

    // if (filterMap.containsKey(ObjectFilterKey.levelKeyName)) {
    //   switch (filterMap[ObjectFilterKey.levelKeyName]?.toString()) {
    //     case "1":
    //       track("mc-map-gradeS"); // S
    //       break;
    //     case "5":
    //       track("mc-map-gradeA"); // A
    //       break;
    //     case "9":
    //       track("mc-map-gradeB"); // B
    //       break;
    //     case "13":
    //       track("mc-map-gradeC"); // C
    //       break;
    //     case "14":
    //       track("mc-map-gradeD"); // D
    //       break;
    //   }
    // }
  // }
  //
  // void track(String actionType, {Map<String, String>? extras}) {
  //   var hashMap = HashMap<String, String>();
  //   hashMap['action_type'] = actionType;
  //   if (extras != null && extras.isNotEmpty) {
  //     hashMap.addAll(extras);
  //   }
  //   XYYContainer.bridgeCall('event_track', parameters: hashMap);
  // }

  /// 生命周期提示提示按钮点击事件
  void _salesTipsAction(BuildContext context) {
    // XYYContainer.toastChannel.toast('点击了提示');
    dynamic renderBox = this._anchorKey.currentContext?.findRenderObject()!;
    Offset offset =
    renderBox.localToGlobal(Offset(10.0, renderBox.size.height));
    showGeneralDialog(
      context: context,
      barrierColor: Colors.transparent,
      // 遮罩颜色
      barrierLabel: "",
      barrierDismissible: true,
      // 点击遮罩是否关闭对话框
      transitionDuration: const Duration(milliseconds: 200),
      // 对话框打开/关闭的动画时长
      pageBuilder: (
          // 构建对话框内部UI
          BuildContext context,
          Animation animation,
          Animation secondaryAnimation,
          ) {
        return Stack(
          children: [
            Positioned(
              child: SelectObjectTipsPopover(),
              top: offset.dy,
              left: offset.dx - 77.5,
            )
          ],
        );
      },
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return FadeTransition(
          opacity: CurvedAnimation(
            parent: animation,
            curve: Curves.easeOut,
          ),
          child: child,
        );
      },
    );
  }

  /// Request
  void requestFilterData() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<AddPlanFilterModel>(
        AddPlanFilterModel())
        .requestDataV2('visit/plan/conditions');
    EasyLoading.dismiss();
    if (mounted && result.isSuccess == true) {
      if (result.data != null) {
        this.handlerConfigData(result.data);
      }
    }
  }

  void handlerConfigData(AddPlanFilterModel rootModel) {
    /// 资质状态
    if (rootModel.licenseStatusCondition != null && widget.isMapFilter) {
      String? defaultId = rootModel.licenseStatusCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
          orElse: () => AddPlanFilterItemModel())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.licenseKeyName,
        itemTitle: '资质状态',
        contents: (rootModel.licenseStatusCondition ?? [])
            .map((e) =>
            CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 距离范围
    if (rootModel.distanceCondition != null) {
      String? defaultId = rootModel.distanceCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
          orElse: () => AddPlanFilterItemModel())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.distanceKeyName,
        itemTitle: '距离范围',
        contents: (rootModel.distanceCondition ?? [])
            .map((e) =>
            CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 动销情况
    if (rootModel.placeOrderCondition != null) {
      String? defaultId = rootModel.placeOrderCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
          orElse: () => AddPlanFilterItemModel())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.salesKeyName,
        itemTitle: '动销情况',
        contents: (rootModel.placeOrderCondition ?? [])
            .map((e) =>
            CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 拜访情况
    if (rootModel.visitCondition != null) {
      String? defaultId = rootModel.visitCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
          orElse: () => AddPlanFilterItemModel())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.visitKeyName,
        itemTitle: '拜访情况',
        contents: (rootModel.visitCondition ?? [])
            .map((e) =>
            CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    /// 客户等级
    if (rootModel.levelCondition != null) {
      String? defaultId = rootModel.levelCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
          orElse: () => AddPlanFilterItemModel())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.levelKeyName,
        itemTitle: '客户等级',
        contents: (rootModel.levelCondition ?? [])
            .map((e) =>
            CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }

    setState(() {});
  }

  /// 获取当前选中的id
  List<String> getSelectIdsForKey(String itemKey) {
    return this.filterMap[itemKey]?.split(",") ?? [];
  }
}
