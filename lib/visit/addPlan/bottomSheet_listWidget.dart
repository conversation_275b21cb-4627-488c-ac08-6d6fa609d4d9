import 'package:XyyBeanSproutsFlutter/visit/bean/plan/plan_search_data.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class BottomSheetListWidget extends StatefulWidget {
  final List<PlanSearchModel> dataSource;
  final Function(PlanSearchModel) onSelect;

  BottomSheetListWidget({required this.dataSource, required this.onSelect});

  @override
  _BottomSheetListWidgetState createState() => _BottomSheetListWidgetState();
}

class _BottomSheetListWidgetState extends State<BottomSheetListWidget> {
  @override
  Widget build(BuildContext context) {
    debugPrint("展开了详情${widget.dataSource.length}");
    return Container(
      height: 250 + MediaQuery.of(context).padding.bottom,
      padding: EdgeInsets.only(left: 10, right: 10),
      // color: Colors.orange,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 最上面一排标题文字和关闭按钮
          Row(
            children: <Widget>[
              SizedBox(width: 50),
              Expanded(
                child: Center(
                  child: Text(
                    "已选对象",
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              IconButton(
                icon: Icon(Icons.close),
                onPressed: () {
                  Navigator.pop(context); // 关闭底部模态表单
                },
              ),
            ],
          ),
          SizedBox(height: 4),
          Expanded(
            child: ListView.builder(
              itemCount: widget.dataSource.length,
              itemBuilder: (context, index) {
                return _buildItem(index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItem(index) {
    PlanSearchModel model = widget.dataSource[index];
    return Container(
      height: 26,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
            width: 10,
          ),
          Expanded(
            child: Text(
              model.customerName,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
            ),
          ),
          TextButton(
            onPressed: () {
              debugPrint("点击了删除$index");
            },
            child: Image.asset(
              'assets/images/base/history_delete_icon.png',
              width: 18,
              height: 18,
            ),
            style: ButtonStyle(
              overlayColor: MaterialStateProperty.all<Color>(
                  Colors.transparent),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              minimumSize: MaterialStateProperty.all<Size>(Size(26, 26)),
            ),
          ),
        ],
      ),
    );
  }
}
