
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/photo_view/gallery_photo_view.dart';
import 'package:XyyBeanSproutsFlutter/common/photo_view/photo_gallery_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/color_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/time/time_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/format_utils.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/detail/visit_detail_bean.dart';
import 'package:XyyBeanSproutsFlutter/visit/constant/visit_type_code.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'bean/detail/visit_detail_bean_v2.dart';

class VisitDetailPage extends BasePage {
  final String? scheduleId;

  VisitDetailPage(this.scheduleId);

  @override
  BaseState<StatefulWidget> initState() {
    return VisitDetailState(this.scheduleId);
  }
}

class VisitDetailState extends BaseState<VisitDetailPage>
    with SingleTickerProviderStateMixin {
  VisitDetailModel? _detailModel;
  String? scheduleId;

  VisitDetailState(this.scheduleId);

  TextStyle styleBlack = TextStyle(
      color: ColorUtils().color_292933,
      fontSize: 17,
      fontWeight: FontWeight.bold);
  TextStyle styleGray =
  TextStyle(color: ColorUtils().color_8E8E93, fontSize: 12);

  @override
  void onCreate() {
    _detailModel = VisitDetailModel(scheduleId);
    _detailModel?.requestData();
    super.onCreate();
  }

  @override
  void dispose() {
    super.dispose();
    _detailModel?.dispose();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<VisitDetailModel?>(
          create: (context) => _detailModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Consumer<VisitDetailModel?>(builder: (context, model, child) {
      return SingleChildScrollView(
        child: Container(
          color: ColorUtils().color_EFEFF4,
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              Divider(height: 1, color: ColorUtils().color_E1E1E5),
              headerView(),
              visitInfo(),
              operatingState(),
            ],
          ),
        ),
      );
    });
  }

  @override
  String getTitleName() {
    return '拜访详情';
  }

  Widget headerView() {
    return Container(
      margin: EdgeInsets.fromLTRB(10, 10, 10, 5),
      constraints: BoxConstraints(minHeight: 100),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(7),
          border: Border.all(color: Colors.white, width: 0.5)),
      padding: EdgeInsets.fromLTRB(15, 15, 0, 15),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          // headerImage(),
          SizedBox(width: 10),
          Expanded(child: headerContent()),
          Container(
            padding: EdgeInsets.all(8),
            child: headerEffectiveView(),
            alignment: Alignment.bottomRight,
          ),
        ],
      ),
    );
  }

  ///头部拜访信息
  Widget headerContent() {
    String? type = _detailModel?.visitDetailBean?.personalSchedule?.visitTypeText;
    String scheduleTime = "";
    if (_detailModel?.visitDetailBean?.personalSchedule?.startTime != 0 &&
        _detailModel?.visitDetailBean?.personalSchedule?.endTime != 0) {
      scheduleTime = TimeUtils().formatTime1(
          _detailModel?.visitDetailBean?.personalSchedule?.createTime ?? 0);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.max,
      children: [
        Text(
            FormatUtils.formatText(
                _detailModel?.visitDetailBean?.merchantVisit?.merchantName),
            style: styleBlack),
        SizedBox(height: 5),
        Text(_detailModel?.visitDetailBean?.personalSchedule?.visitTypeText?? "—", style: styleGray),
        Visibility(
            child: SizedBox(height: 5),
            visible: VisitTypeCode.showAccompanyPeople(type)),
        Visibility(
          child: Text(
              "被陪访人:${FormatUtils.formatText(
                  _detailModel?.visitDetailBean?.merchantVisit
                      ?.accompanyName)}",
              style: styleGray),
          visible: VisitTypeCode.showAccompanyPeople(type),
        ),
        SizedBox(height: 5),
        Text(
            "${FormatUtils.formatText(
                _detailModel?.visitDetailBean?.personalSchedule?.userName)}",
            style: styleGray),
        SizedBox(height: 5),
        Text("${_detailModel?.visitDetailBean?.merchantVisit?.visitTime}", style: styleGray),
      ],
    );
  }

  ///左侧图标
  Widget headerImage() {
    return Image.asset(
        VisitTypeCode.getImageByType(
            _detailModel?.visitDetailBean?.merchantVisit?.visitType ?? 0),
        width: 32,
        height: 32);
  }

  ///右侧有效图标
  Widget headerEffectiveView() {
    bool effective =
        _detailModel?.visitDetailBean?.personalSchedule?.effective ?? false;
    bool showByType = VisitTypeCode.showEffective(
      _detailModel?.visitDetailBean?.merchantVisit?.visitType ?? 0,
    );
    bool isEffective = _detailModel?.visitDetailBean?.personalSchedule?.isEffective == 1;
    return Visibility(
        child: Column(
          children: [
            Image.asset(isEffective? "assets/images/visit/icon_effective_schedule.png": "assets/images/visit/icon_no_effective_schedule.png",
                width: 54, height: 54),
            Visibility(
                child: Container(
                  margin: EdgeInsets.only(top: 5),
                  child: Text(
                    _detailModel?.visitDetailBean?.personalSchedule?.invalidReason?? "",
                    style: TextStyle(
                        color: Color(0xFFFF2021),
                        fontSize: 12
                    ),
                  ),
                ),
                visible: !isEffective,
            )
          ],
        ),
        visible: true);
  }

  ///拜访信息
  Widget visitInfo() {
    int type = _detailModel?.visitDetailBean?.merchantVisit?.visitType ?? 0;
    return Container(
      alignment: Alignment.centerLeft,
      margin: EdgeInsets.fromLTRB(10, 10, 10, 5),
      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
      constraints: BoxConstraints(minHeight: 100),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(7),
          border: Border.all(color: Colors.white, width: 0.5)),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text("拜访信息", style: styleBlack),
          // SizedBox(height: 15),
          // _InfoItem(
          //   title: "关联任务",
          //   content: FormatUtils.formatText(
          //       _detailModel?.visitDetailBean?.task?.theme),
          //   show: true,
          // ),
          Visibility(
              child: SizedBox(height: 10),
              visible: VisitTypeCode.showContact(type)),
          _InfoItem(
            title: "联系人",
            content: FormatUtils.formatText(
                _detailModel?.visitDetailBean?.merchantVisit?.contactor),
            show: VisitTypeCode.showContact(type),
          ),
          Visibility(
              child: SizedBox(height: 10), visible: VisitTypeCode.showKP(type)),
          _InfoItem(
            title: "是否KP",
            content:
            _detailModel?.visitDetailBean?.kpFlag == true
                ? "是"
                : "否",
            show: VisitTypeCode.showKP(type),
          ),
          Visibility(
              child: SizedBox(height: 10),
              visible: VisitTypeCode.showCallLogTime(type)),
          _InfoItem(
            title: "系统拨打电话时长",
            content: FormatUtils.formatText(
                _detailModel?.visitDetailBean?.merchantVisit?.talkTimeText),
            show: VisitTypeCode.showCallLogTime(type),
          ),
          Visibility(
              child: SizedBox(height: 10),
              visible: VisitTypeCode.showVisitReason(type)),
          _InfoItem(
            title: "拜访目的",
            content: FormatUtils.formatText(
                _detailModel?.visitDetailBean?.merchantVisit?.visitReasonName),
            show: VisitTypeCode.showVisitReason(type),
          ),
          SizedBox(height: 10),
          _InfoItem(
            title: "拜访总结",
            content: FormatUtils.formatText(
                _detailModel?.visitDetailBean?.merchantVisit?.visitDemo),
            show: true,
          ),
          SizedBox(height: 10),
          imageList(),
          SizedBox(height: 10),
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.max,
            children: [
              Image.asset(
                "assets/images/visit/icon_address_icon_gray.png",
                width: 13,
                height: 15,
              ),
              SizedBox(width: 5),
              Text(
                  FormatUtils.formatText(
                      _detailModel?.visitDetailBean?.merchantVisit?.address),
                  style: styleGray)
            ],
          )
        ],
      ),
    );
  }

  ///拜访图片
  Widget imageList() {
    String? imagePath = _detailModel?.visitDetailBean?.merchantVisit?.image;
    String? host = _detailModel?.visitDetailBean?.domainPath;
    List<String> imageList = [];
    if (imagePath != null &&
        host != null &&
        imagePath.isNotEmpty &&
        host.isNotEmpty) {
      if (!imagePath.contains(";")) {
        imageList.add('$host$imagePath');
      } else {
        List<String> imageArray = imagePath.split(";");
        imageArray.forEach((element) {
          if (element.isNotEmpty) {
            imageList.add('$host$element');
          }
        });
      }
    }
    return Container(
      height: imageList.length == 0 ? 0 : 74,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: imageList.length,
        itemBuilder: (context, index) {
          GalleryItem item =
          GalleryItem(id: "tag$index", imageUrl: imageList[index]);
          return Container(
            width: 74,
            height: 74,
            margin: EdgeInsets.only(right: 10),
            child: GalleryItemThumbnail(
              onTap: () {
                Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (BuildContext context) =>
                        GalleryPhotoView(
                          images: imageList, //传入图片list
                          index: index, //传入当前点击的图片的index
                        ),
                  ),
                );
              },
              galleryItem: item,
            ),
          );
        },
      ),
    );
  }

  ///对象经营状况
  Widget operatingState() {
    int type = _detailModel?.visitDetailBean?.merchantVisit?.visitType ?? 0;
    final merchantBasicInfo = _detailModel?.visitDetailBean?.merchantBasicInfo;
    dynamic areaSize = merchantBasicInfo?.areaSize;
    String areaSizeStr =
    areaSize == null || areaSize == -1.0 || areaSize == "-1.0"
        ? "—"
        : areaSize.toString();
    dynamic monthlySales = merchantBasicInfo?.monthlySales;
    String monthlySalesStr = monthlySales == null || monthlySales == -1.0
        ? "—"
        : monthlySales.toString();

    if (merchantBasicInfo == null) return Container();

    return VisitTypeCode.showOperatingState(merchantBasicInfo, areaSizeStr, monthlySalesStr)
        ? Container(
      alignment: Alignment.topLeft,
      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
      margin: EdgeInsets.fromLTRB(10, 5, 10, 20),
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(7),
          border: Border.all(color: Colors.white, width: 0.5)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text("对象经营状况", style: styleBlack),
          _getOperatingStateItem(
              "是否有医保",
              merchantBasicInfo.medicalInsuranceText,
              divider: 15,
          ),
          _getOperatingStateItem(
              "客户面积(平方米)",
              areaSizeStr == "—"? null: areaSizeStr
          ),
          _getOperatingStateItem(
              "客户成员数(人)",
              merchantBasicInfo.clerkNum
          ),
          _getOperatingStateItem(
              "周边环境",
              merchantBasicInfo.aroundEnvName
          ),
          _getOperatingStateItem(
              "主要消费人群",
              merchantBasicInfo.buyersTypeName
          ),
          _getOperatingStateItem(
              "客户流量(人/天)",
              merchantBasicInfo.buyersAmountText
          ),
          _getOperatingStateItem(
              "客户需求SKU数",
              merchantBasicInfo.buySkusName
          ),
          _getOperatingStateItem(
              "主要消费结构",
              merchantBasicInfo.mainlyConsumeMedTypesName
          ),
          _getOperatingStateItem(
              "是否需要动销",
              merchantBasicInfo.needPullSalesText
          ),
          _getOperatingStateItem(
              "是否需要门店诊断",
              merchantBasicInfo.needMerchantDiagnoseText
          ),
          _getOperatingStateItem(
              "是否需要店员培训",
              merchantBasicInfo.needClerkTrainsText
          ),
          _getOperatingStateItem(
              "月销售额(元)",
              monthlySalesStr == "—"? null: monthlySalesStr
          ),
          _getOperatingStateItem(
              "缺失品种",
              merchantBasicInfo.shortOfTypes
          ),
          _getOperatingStateItem(
              "核心供应商",
              merchantBasicInfo.purchaseWay
          ),
          _getOperatingStateItem(
              "商家需求", merchantBasicInfo.merchantDemand
          ),
        ],
      ),
    )
        : Container();
  }
}

Widget _getOperatingStateItem(String title, String? content, {double divider = 10}) {
  return Visibility(
      visible: content != null && content.isNotEmpty,
      child: Column(
        children: [
          SizedBox(height: divider),
          _InfoItem(
            title: title,
            content: content?? "",
            show: true,
          ),
        ],
      )
  );
}

class _InfoItem extends StatelessWidget {
  final String? title;
  final String? content;
  bool show = false;

  _InfoItem({
    Key? key,
    required this.title,
    required this.content,
    required this.show,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return show
        ? SingleChildScrollView(
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            constraints: BoxConstraints(minWidth: 140),
            child: Text(
              this.title ?? '',
              style: TextStyle(
                fontSize: 15,
                height: 1,
                color: ColorUtils().color_8E8E93,
              ),
            ),
          ),
          Expanded(
            child: Container(
              alignment: Alignment.centerLeft,
              child: Text(
                this.content ?? '',
                style: TextStyle(
                  fontSize: 15,
                  height: 1,
                  color: ColorUtils().color_292933,
                ),
              ),
            ),
          ),
        ],
      ),
    )
        : Container();
  }


}

class VisitDetailModel extends ChangeNotifier {
  String? scheduleId;
  VisitDetailBean? visitDetailBean;

  bool isDisposed = false;

  VisitDetailModel(this.scheduleId);

  void requestData() async {
    EasyLoading.show(status: "加载中");
    NetworkV2<VisitDetailBeanV2>(VisitDetailBeanV2())
        .requestDataV2("visit/visitDetail",
        parameters: {"visitId": scheduleId}, method: RequestMethod.GET)
        .then((value) {
      EasyLoading.dismiss();
      if (isDisposed) {
        return;
      }
      if (value.isSuccess == true) {
        visitDetailBean = value.getData()?.convertToVisitDetailBean();
        notifyListeners();
      } else {
        XYYContainer.toastChannel.toast(value.msg ?? "获取数据失败");
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    isDisposed = true;
  }
}
