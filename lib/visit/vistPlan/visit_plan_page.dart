//拜访计划列表 /app/crm/visit/plan/list

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/task/bean/dotask_save_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/common_show_dialog.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/plan/visit_plan_data.dart';
import 'package:XyyBeanSproutsFlutter/visit/vistPlan/visit_plan_data_item.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

class VisitPlanPage extends BasePage {
  String? lat;
  String? lng;

  VisitPlanPage({this.lat, this.lng});

  @override
  BaseState<StatefulWidget> initState() {
    return _VisitPlanPageState();
  }
}

class _VisitPlanPageState extends BaseState<VisitPlanPage> {
  bool requestSuccess = true;
  List<PlanListModel> dataSource = [];
  List<dynamic> selectDataIds = [];
  VisitPlanDataModel? rootModel;
  EasyRefreshController _controller = EasyRefreshController();

  @override
  void initState() {
    this.requestLocation();
    super.initState();
  }

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Container(
            color: Color(0xFFF7F7F8),
            child: SlidableAutoCloseBehavior(
              //滑动删除
              child: EasyRefresh(
                onRefresh: _refreshData,
                controller: _controller,
                child: ListView.builder(
                  itemCount: this.dataSource.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        // 跳转客户详情
                        PlanListModel model = this.dataSource[index];
                        var routerPath =
                            "/customer_private_detail_page?customerId=${model.customerId}";
                        XYYContainer.open(routerPath);
                      },
                      behavior: HitTestBehavior.opaque,
                      child: _buildMessageItem(index),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
        _buildTipsView(), // tips
        _buildBottomView(), // bottom：生成路线图、添加计划按钮
      ],
    );
  }

  /// 下拉刷新
  Future<void> _refreshData() async {
    // 在这里执行数据刷新逻辑，例如重新加载数据源
    await requestMerchantListData();
  }

  // 滑动删除
  Widget _buildMessageItem(int index) {
    PlanListModel model = this.dataSource[index];
    return Slidable(
        key: ValueKey("$index"),
        groupTag: "ccc", // 把所有item归类到一个组, 保证同时只出现一个滑动的效果
        endActionPane: ActionPane(
          extentRatio: 0.2,
          // 控制滑动项widget的大小
          motion: const DrawerMotion(),
          // 抽屉式效果, 还有另外三种效果: 分别是BehindMotion, ScrollMotion, StretchMotion
          // 滑动出来的widget
          children: [
            SlidableAction(
              onPressed: (_) {
                debugPrint('删除了$index');
                requestPlanDelete(model);
              },
              backgroundColor: Colors.red,
              icon: Icons.delete,
              label: '删除',
            )
          ],
        ),
        // 显示的widget
        child: VisitPlanItem(
          data: model,
          index: index,
          isSelect: selectDataIds.contains(model.id),
          changeTag: (dynamic code) {
            debugPrint("改变了标签$code");
            requestModifyTag(model, code);
          },
          onSelectImg: () {
            if(selectDataIds.contains(model.id)) {
              selectDataIds.remove(model.id);
            }else {
              selectDataIds.add(model.id);
            }
            setState(() {
            });
          },
          onTop: () {
            debugPrint("点击的---置顶按钮");
            requestPlanTop(model);
          },
        ));
  }

  //tips
  Widget _buildTipsView() {
    return Container(
      width: double.infinity,
      height: 34,
      padding: EdgeInsets.only(left: 10, right: 10),
      color: Color(0xffFFF7EF), // 中间文案展示的背景颜色
      child: Align(
        alignment: Alignment.centerLeft, // 设置文本水平靠左、垂直居中
        child: Text(
          '*拜访计划最多添加${this.rootModel?.planMax}个，7天未拜访自动移出计划。',
          style: TextStyle(
            color: Color(0xff99664D), // 文字颜色
            fontSize: 12, // 文字大小
          ),
          textAlign: TextAlign.left, // 设置文本左对齐
        ),
      ),
    );
  }

  //bottom：生成路线图、添加计划按钮
  Widget _buildBottomView() {
    return Container(
      height: 49 + MediaQuery.of(context).padding.bottom, // 容器的高度，根据你的需求设置
      color: Colors.white, // 底部容器的背景颜色
      padding: EdgeInsets.only(left: 10, top: 6, right: 10), // 容器内边距
      child: Align(
        alignment: Alignment.topCenter, // 设置文本水平靠左、垂直居中
        child: Row(
          children: [
            // 左边的选中/未选中按钮
            IconButton(
              disabledColor: Colors.blueAccent,
              onPressed: () {
                if (selectDataIds.length == dataSource.length) {
                  selectDataIds = [];
                }else {
                  selectDataIds = dataSource.map((e) => e.id).toList();
                }
                setState(() {});
              },
              //点击高亮颜色、不需要可设为透明
              highlightColor: Colors.transparent,
              //水波纹效果颜色、不需要可设为透明
              splashColor: Colors.transparent,
              //触觉反馈
              enableFeedback: true,
              icon: Image.asset(
                selectDataIds.length == dataSource.length
                    ? "assets/images/customer/customer_sku_collect_check_selected.png"
                    : "assets/images/customer/customer_sku_collect_check_unselected.png",
                width: 18,
                height: 24,
              ),
            ),
            //生成计划路线图 按钮
            _buildButton(),
            Spacer(),
            // 最右侧的文字按钮
            LoaingButton(
              text: '添加计划',
              isLoading: false,
              onPressed: () async {
                var router = '/add_visit_plan_page';
                router = Uri.encodeFull(router);
                await Navigator.of(context).pushNamed(router).then((value) {
                  _refreshData();
                });
              },
            )
          ],
        ),
      ),
    );
  }

  /// 生成计划路线图 按钮
  Widget _buildButton() {
    bool hasSelectedItems = selectDataIds.length > 0;

    return MaterialButton(
      onPressed: hasSelectedItems
          ?  _junmpToRoadMapPage
          : null, // 如果没有选中项，则禁用按钮
      minWidth: 47,
      height: 37,
      color: hasSelectedItems ? Colors.white : Colors.grey, // 根据是否有选中项设置背景颜色
      elevation: 0, // 禁用阴影
      highlightElevation: 0, // 禁用高亮时的阴影
      disabledElevation: 0, // 禁用状态下的阴影
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0), // 按钮边框
        side: BorderSide.none, // 移除边框
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "生成计划路线图",
            style: TextStyle(
              color: hasSelectedItems
                  ? Color(0xff00B377)
                  : Colors.grey, // 根据是否有选中项设置文字颜色
              fontSize: 14,
            ),
          ),
          SizedBox(width: 2), // 文字和图片之间的间距
          Image.asset(
            hasSelectedItems
                ? 'assets/images/message/message_more_arrow.png'
                : 'assets/images/mine/mine_item_arrow.png',
            width: 10,
            height: 10,
          ),
        ],
      ),
    );
  }

  ///跳转到 生成计划路线图 页面
  _junmpToRoadMapPage() {
    String ids = selectDataIds.join(',');
    track('mc-visit-plan-toRouadMapPage');
    var routerPath = "/add_visit_Planning_roadmap_page?planIdStr=$ids";
    Navigator.of(context).pushNamed(routerPath);
  }

  /// 空数据
  Widget? getEmptyWidget() {
    if (!this.requestSuccess && this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        this.requestLocation();
      });
    }
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  /// 请求定位信息
  void requestLocation() {
    XYYContainer.locationChannel.locate().then((value) {
      if (this.mounted && (value.isSuccess ?? false)) {
        widget.lat = value.latitude;
        widget.lng = value.longitude;
      }
      this.requestMerchantListData();
    });
  }

  /// 当前页面是否获取经纬度
  bool get hasLocation {
    return widget.lat?.isEmpty == false && this.widget.lng?.isEmpty == false;
  }

  /// 请求网络
  Future<void> requestMerchantListData() async {
    if (this.dataSource.length == 0) {
      showLoadingDialog(); 
    }
    var params = {'lat': widget.lat, 'lng': widget.lng};

    var result =
        await NetworkV2<VisitPlanDataModel>(VisitPlanDataModel()).requestDataV2(
      'visit/plan/list',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
      showErrorToast:false,
    );
    dismissLoadingDialog();
    if (mounted) {
      this.requestSuccess = result.isSuccess == true;
      if (result.isSuccess == true) {
        VisitPlanDataModel? source = result.getData();
        this.rootModel = source;
        if (source != null) {
          this.dataSource = source.planList ?? [];
          if (this.dataSource.length == 0) {
            this.getEmptyWidget();
          }
        }
      }
      setState(() {});
    }
  }

  /// 拜访计划-修改标签
  Future<void> requestModifyTag(PlanListModel model, dynamic code) async {
    // if (!this.hasLocation){
    //   showToast('未获取到当前位置信息，请检查设备');
    // }
    track('mc-visit-plan-changeTag');
    showLoadingDialog();
    var params = {'planId': model.id, 'planTag': code};

    var result = await Network<DoTaskSaveModel>(DoTaskSaveModel()).requestData(
      'visit/plan/modify/tag',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        requestMerchantListData();
        // showToast('提交成功', type: ToastType.Success);
        // Navigator.of(context).pop({'SaveSuccess': true});
      } else {
        showToast(result.errorMsg!);
      }
    }
  }

  /// 拜访计划-置顶
  Future<void> requestPlanTop(PlanListModel model) async {
    // if (!this.hasLocation){
    //   showToast('未获取到当前位置信息，请检查设备');
    // }
    showLoadingDialog();
    var params = {
      'planId': model.id,
    };

    var result = await Network<DoTaskSaveModel>(DoTaskSaveModel()).requestData(
      'visit/plan/top',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        track('mc-visit-plan-onTop');
        requestMerchantListData();
        // showToast('提交成功', type: ToastType.Success);
        // Navigator.of(context).pop({'SaveSuccess': true});
      } else {
        showToast(result.errorMsg!);
      }
    }
  }

  /// 拜访计划-删除
  Future<void> requestPlanDelete(PlanListModel model) async {
    // if (!this.hasLocation){
    //   showToast('未获取到当前位置信息，请检查设备');
    // }
    showLoadingDialog();
    var params = {'planId': model.id};

    var result = await Network<DoTaskSaveModel>(DoTaskSaveModel()).requestData(
      'visit/plan/delete',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        track('mc-visit-plan-delPlan');
        requestMerchantListData();
        // showToast('提交成功', type: ToastType.Success);
        // Navigator.of(context).pop({'SaveSuccess': true});
      } else {
        showToast(result.errorMsg!);
      }
    }
  }

  @override
  String getTitleName() {
    if (this.rootModel != null) {
      return "拜访计划(${this.rootModel?.planNums}/${this.rootModel?.planMax})";
    }
    return "拜访计划";
  }
}
