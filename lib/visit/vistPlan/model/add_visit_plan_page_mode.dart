import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class AddVisitPlanPageMode extends BaseModelV2<AddVisitPlanPageMode> {
  /// id
  dynamic id;

  /// 名称
  dynamic customerName;

  /// 注册状态
  dynamic merchantStatus;

  /// 临期阶段：-1正常；1-临期；2-过期
  dynamic licenseValidateMust;

  /// --
  dynamic licenseValidateIssue;

  /// --
  dynamic merchantStatusName;

  /// --
  dynamic distance;

  /// 地址
  dynamic address;

  /// 是否存在拜访计划
  dynamic inPlanFlag;

  /// 是否在私海
  dynamic privateCustomerFlag;

  /// 查询条件
  dynamic searchParams;

  AddVisitPlanPageMode();

  @override
  AddVisitPlanPageMode fromJsonMap(Map<String, dynamic> json) {
    return AddVisitPlanPageMode()
    ..id = json['id']
    ..customerName = json['customerName']
    ..merchantStatus = json['merchantStatus']
    ..licenseValidateMust = json['licenseValidateMust']
    ..licenseValidateIssue = json['licenseValidateIssue']
    ..merchantStatusName = json['merchantStatusName']
    ..distance = json['distance']
    ..address = json['address']
    ..inPlanFlag = json['inPlanFlag']
    ..privateCustomerFlag = json['privateCustomerFlag']
    ..searchParams = json['searchParams'];
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id':this.id,
      'customerName':this.customerName,
      'merchantStatus':this.merchantStatus,
      'licenseValidateMust':this.licenseValidateMust,
      'licenseValidateIssue':this.licenseValidateIssue,
      'merchantStatusName':this.merchantStatusName,
      'distance':this.distance,
      'address':this.address,
      'inPlanFlag':this.inPlanFlag,
      'privateCustomerFlag':this.privateCustomerFlag,
      'searchParams':this.searchParams,
    };
  }
}
