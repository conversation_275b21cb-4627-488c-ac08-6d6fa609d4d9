import 'dart:async';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/customer/data/customer_detail_data_v2.dart';
import 'package:XyyBeanSproutsFlutter/customer/public/widget/customer_sku_collect_dialog.dart';
import 'package:XyyBeanSproutsFlutter/schedule/statistics/data/schedule_join_the_program_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/common_show_dialog.dart';
import 'package:XyyBeanSproutsFlutter/utils/format_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/plan/visit_plan_data.dart';
import 'package:XyyBeanSproutsFlutter/visit/vistPlan/add_visit_plan_filter.dart';
import 'package:XyyBeanSproutsFlutter/visit/vistPlan/model/add_visit_plan_page_mode.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

enum FilterItem { unregistered, ordered }

extension ColorExtension on FilterItem {
  static const Map<FilterItem, String> filterItems = {
    FilterItem.unregistered: '公海未注册',
    FilterItem.ordered: '公海下单客户',
  };

  String get name => filterItems[this]!;
}

// 选中项
class SelectItem {
  final dynamic id;
  final dynamic name;
  SelectItem(this.id, this.name);
}

class AddVisitPlanPage extends BasePage {
  AddVisitPlanPage();

  @override
  BaseState<StatefulWidget> initState() {
    return AddVisitPlanPageState();
  }
}

class AddVisitPlanPageState extends BaseState<AddVisitPlanPage>
    with EventBusObserver {
  // 当前选中公海项
  FilterItem? currentFilter;
  // 列表刷新
  ValueNotifier<bool> dataListNotifier = ValueNotifier<bool>(false);
  // 选中刷新
  ValueNotifier<bool> checkBoxNotifier = ValueNotifier<bool>(false);
  // 计划接口入参存储
  Map<dynamic, dynamic> paramModel = {};
  // 展示数据
  List<AddVisitPlanPageMode> showList = [];
  // 已加入计划列表
  List<PlanListModel> visitPlanDataList = [];
  // 选中项
  List<SelectItem> selectList = [];
  // 当前已认领集合
  List<dynamic> claimist = [];
  // loading
  bool isLoading = true;
  // 经纬度
  String? lat;
  String? lng;
  // 筛选数据
  Map<dynamic, dynamic> filterData = {};
  // 搜索内容
  String? inputValue;
  // 下拉刷新器
  EasyRefreshController easyController = EasyRefreshController();
  // 列表滚动器
  ScrollController controller = ScrollController();
  // 底部flag
  bool showLayer = false;
  // 是否m级账号
  bool isManagerLevel = false;
  @override
  initState() {
    requestLocation();
    super.initState();
  }

  @override
  bool get resizeToAvoidBottomInset => false;
  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          decoration: BoxDecoration(color: Color(0xFFF7F7F8)),
          child: Column(
            children: [
              Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      _buildSearchBox(),
                      _buildFilterBox(),
                    ],
                  )),
              _buildListBox(),
              _buildFooterBox()
            ],
          ),
        ));
  }

  // 搜索
  Widget _buildSearchBox() {
    return ValueListenableBuilder(
        valueListenable: dataListNotifier,
        builder: (BuildContext context, bool refresh, Widget? child) {
          return Container(
            width: double.infinity,
            margin: EdgeInsets.fromLTRB(10, 10, 10, 5),
            height: 34,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(5)),
              color: Color(0xFFF6F6F6),
            ),
            child: InputBox(
              hitText: '搜索对象',
              value: inputValue,
              onSubmit: (value) {
                inputValue = value.trim();
                isLoading = true;
                dataListNotifier.value = !dataListNotifier.value;
                requestData();
              },
            ),
          );
        });
  }

  // 筛选
  Widget _buildFilterBox() {
    Widget fn(FilterItem text, void Function() onTap) {
      return Container(
        height: 30,
        margin: EdgeInsets.only(right: 10),
        child: TextButton(
          onPressed: onTap,
          child: Text(
            text.name,
            style: TextStyle(
              color:
                  currentFilter == text ? Color(0xFF00B377) : Color(0XFF747579),
              fontSize: 12,
              fontWeight: currentFilter == text ? FontWeight.w600 : null,
            ),
          ),
          style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all(
                currentFilter == text ? Color(0xFFE4F8F1) : Color(0xFFF7F7F7)),
          ),
        ),
      );
    }

    return Container(
      margin: EdgeInsets.only(bottom: 5, left: 10, right: 10),
      padding: EdgeInsets.only(top: 5),
      decoration: BoxDecoration(
          border: Border(top: BorderSide(color: Color(0xFFf7f7f7)))),
      child: Row(
        children: [
          ValueListenableBuilder(
              valueListenable: dataListNotifier,
              builder: (BuildContext context, bool refresh, Widget? child) {
                if (isManagerLevel) return SizedBox();
                return fn(FilterItem.unregistered, () {
                  if (currentFilter == FilterItem.unregistered) {
                    currentFilter = null;
                  } else {
                    currentFilter = FilterItem.unregistered;
                  }
                  isLoading = true;
                  filterData = {};
                  dataListNotifier.value = !dataListNotifier.value;
                  requestData();
                });
              }),
          ValueListenableBuilder(
              valueListenable: dataListNotifier,
              builder: (BuildContext context, bool refresh, Widget? child) {
                if (isManagerLevel) return SizedBox();
                return fn(FilterItem.ordered, () {
                  if (currentFilter == FilterItem.ordered) {
                    currentFilter = null;
                  } else {
                    currentFilter = FilterItem.ordered;
                  }
                  isLoading = true;
                  filterData = {};
                  dataListNotifier.value = !dataListNotifier.value;
                  requestData();
                });
              }),
          const Expanded(child: SizedBox()),
          TextButton(
            onPressed: () {
              showModalBottomSheet(
                isScrollControlled: true,
                context: context,
                backgroundColor: Colors.transparent,
                builder: (BuildContext context) {
                  return Container(
                    height: MediaQuery.of(context).size.height - 85,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(8),
                        topRight: Radius.circular(8),
                      ),
                    ),
                    child: Center(
                      child: AddVisitPlanFilter(
                        filterData: filterData,
                        onChange: (value) {
                          filterData = FormatUtils.deepCopy(value);
                          if (filterData.isNotEmpty) {
                            currentFilter = null;
                          }
                          isLoading = true;
                          dataListNotifier.value = !dataListNotifier.value;
                          requestData();
                        },
                      ),
                    ),
                  );
                },
              );
            },
            child: Image.asset(
              'assets/images/customer/customer_tab_filter.png',
              width: 22,
              height: 22,
            ),
            style: ButtonStyle(
              overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
              padding: MaterialStateProperty.all<EdgeInsets>(
                  EdgeInsets.fromLTRB(10, 5, 0, 5)),
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
          ),
        ],
      ),
    );
  }

  // 列表
  Widget _buildListBox() {
    return ValueListenableBuilder(
        valueListenable: dataListNotifier,
        builder: (BuildContext context, bool refresh, Widget? child) {
          if (isLoading) {
            return Expanded(
                child: Center(
              child: CircularProgressIndicator(
                strokeWidth: 4.0,
                backgroundColor: Colors.transparent,
                valueColor:
                    new AlwaysStoppedAnimation<Color>(Color(0xFF00B377)),
              ),
            ));
          }
          return Expanded(
            child: EasyRefresh(
              onRefresh: requestData,
              controller: easyController,
              emptyWidget: (showList.length <= 0)
                  ? PageStateWidget(state: PageState.Empty)
                  : null,
              child: SingleChildScrollView(
                controller: controller,
                child: Column(
                  children: showList.map((item) {
                    Color statusColor = Color(0xFF35C561);
                    if (item.merchantStatus == 3) {
                      // 黄 冻结
                      statusColor = Color(0xFFFFC000);
                    } else if (item.merchantStatus == 1) {
                      // 绿 正常
                      statusColor = Color(0xFF00B377);
                    } else if (item.merchantStatus == 4 ||
                        item.merchantStatus == 2) {
                      // 红 未注册
                      statusColor = Color(0xFFFF2021);
                    }

                    return InkWell(
                      onTap: () {
                        if (!selectList.map((e) => e.id).contains(item.id)) {
                          checkdSelect(item);
                        } else {
                          selectList.removeWhere((e) => e.id == item.id);
                        }
                        checkBoxNotifier.value = !checkBoxNotifier.value;
                      },
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.fromLTRB(15, 15, 20, 10),
                        margin: EdgeInsets.only(top: 10, left: 10, right: 10),
                        alignment: Alignment.centerLeft,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(8)),
                          color: Color(0xFFFFFFFF),
                        ),
                        child: Row(
                          children: [
                            ValueListenableBuilder(
                                valueListenable: checkBoxNotifier,
                                builder: (BuildContext context, bool refresh,
                                    Widget? child) {
                                  return Container(
                                    margin: EdgeInsets.only(right: 15),
                                    width: 20,
                                    height: 20,
                                    child: RoundCheckbox(
                                        value: selectList
                                            .map((e) => e.id)
                                            .contains(item.id),
                                        onChanged: (value) {
                                          if (value) {
                                            checkdSelect(item);
                                          } else {
                                            selectList.removeWhere(
                                                (e) => e.id == item.id);
                                          }
                                          checkBoxNotifier.value =
                                              !checkBoxNotifier.value;
                                        }),
                                  );
                                }),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(children: [
                                    Expanded(
                                      child: Text(
                                        item.customerName ?? '',
                                        style: TextStyle(
                                            fontSize: 16,
                                            fontWeight: FontWeight.w600,
                                            color: Color(0xFF333333)),
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    Text(
                                      item.merchantStatusName ?? '',
                                      style: TextStyle(
                                          fontSize: 14, color: statusColor),
                                    ),
                                  ]),
                                  const SizedBox(height: 10),
                                  Row(children: [
                                    Icon(
                                      Icons.place_rounded,
                                      size: 12,
                                      color: Color(0xFF9494A6),
                                    ),
                                    Text(
                                      '距您 ${item.distance != null ? item.distance.toString() : ''}',
                                      style: TextStyle(
                                        color: Color(0xFF676773),
                                        fontSize: 12,
                                      ),
                                    ),
                                    Expanded(
                                      child: Container(
                                        width: double.infinity,
                                        margin: EdgeInsets.only(left: 7),
                                        padding: EdgeInsets.only(left: 7),
                                        decoration: BoxDecoration(
                                            border: Border(
                                                left: BorderSide(
                                                    width: 1,
                                                    color: Color(0xFFD8D8D8)))),
                                        child: Text(
                                          item.address != null
                                              ? item.address.toString()
                                              : '',
                                          style: TextStyle(
                                            color: Color(0xFF676773),
                                            fontSize: 12,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    )
                                  ]),
                                  tagRow()
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          );
        });
  }

  // 底部
  Widget _buildFooterBox({void Function()? onTap}) {
    List<Widget> selectListNamesFn() {
      return selectList.map((e) {
        return Container(
          padding: EdgeInsets.only(left: 10),
          margin: EdgeInsets.only(bottom: 5),
          child: Row(
            children: [
              Expanded(
                  child: Text(
                e.name != null ? e.name.toString() : '',
                style: TextStyle(color: Colors.black),
                overflow: TextOverflow.ellipsis,
              )),
              SizedBox(
                height: 20,
                child: IconButton(
                    padding: const EdgeInsets.all(0),
                    icon: SizedBox(
                      child: ImageIcon(
                        AssetImage('assets/images/visit/delete.png'),
                        size: 20,
                      ),
                    ),
                    onPressed: () {
                      selectList.remove(e);
                      checkBoxNotifier.value = !checkBoxNotifier.value;
                      dataListNotifier.value = !dataListNotifier.value;
                    }),
              )
            ],
          ),
        );
      }).toList();
    }

    return ValueListenableBuilder(
        valueListenable: checkBoxNotifier,
        builder: (BuildContext context, bool refresh, Widget? child) {
          return Container(
            width: double.infinity,
            height: 49,
            padding: EdgeInsets.only(left: 10, right: 10),
            decoration: BoxDecoration(
                border: Border(top: BorderSide(color: Colors.white))),
            child: Row(
              children: [
                Text(
                  '选中对象：',
                  style: TextStyle(color: Color(0xFF9494A6)),
                ),
                Text(
                  '${selectList.length.toString()}/${20 - visitPlanDataList.length}',
                  style: TextStyle(color: Color(0xFF333333)),
                ),
                InkWell(
                  onTap: onTap ??
                      () {
                        showModalBottomSheet(
                          isScrollControlled: true,
                          context: context,
                          backgroundColor: Colors.transparent,
                          builder: (BuildContext context) {
                            return ValueListenableBuilder(
                                valueListenable: checkBoxNotifier,
                                builder: (BuildContext context, bool refresh,
                                    Widget? child) {
                                  return Wrap(
                                    children: [
                                      Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(8),
                                            topRight: Radius.circular(8),
                                          ),
                                        ),
                                        child: Column(
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            // title
                                            Row(
                                              children: [
                                                Expanded(
                                                    child: Center(
                                                  child: Transform.translate(
                                                    offset: Offset(24, 0),
                                                    child: Text(
                                                      '已选对象',
                                                      style: TextStyle(
                                                          fontSize: 16,
                                                          fontWeight:
                                                              FontWeight.w600),
                                                    ),
                                                  ),
                                                )),
                                                IconButton(
                                                  icon: ImageIcon(
                                                    AssetImage(
                                                        'assets/images/titlebar/icon_close.png'),
                                                    color: Colors.black,
                                                    size: 22,
                                                  ),
                                                  onPressed: () {
                                                    Navigator.of(context).pop();
                                                  },
                                                  highlightColor:
                                                      Colors.transparent,
                                                  splashColor:
                                                      Colors.transparent,
                                                ),
                                              ],
                                            ),
                                            // content
                                            ...selectListNamesFn(),
                                            // footer
                                            _buildFooterBox(onTap: () {
                                              Navigator.pop(context);
                                            })
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                });
                          },
                        ).then((value) {
                          showLayer = false;
                          checkBoxNotifier.value = !checkBoxNotifier.value;
                        });
                        showLayer = true;
                        checkBoxNotifier.value = !checkBoxNotifier.value;
                      },
                  child: Container(
                    margin: EdgeInsets.only(left: 5),
                    child: Row(
                      children: [
                        Text(
                          showLayer ? '收起详情' : '展开详情',
                          style:
                              TextStyle(color: Color(0xFF00B377), fontSize: 13),
                        ),
                        Icon(
                            showLayer
                                ? Icons.expand_less_rounded
                                : Icons.expand_more_rounded,
                            color: Color(0xFF00B377),
                            size: 18)
                      ],
                    ),
                  ),
                ),
                const Expanded(child: SizedBox()),
                LoaingButton(
                  text: '加入计划',
                  isLoading: false,
                  onPressed: addVisitPlanFn,
                )
              ],
            ),
          );
        });
  }

  // 标签
  Widget _buildTag({
    String? content,
    String? type,
  }) {
    BoxDecoration style = BoxDecoration(
      color: Color(0xFFFAFBFA),
      border: Border.all(
        width: 1,
        color: Color(0xFFDADADA),
      ),
      borderRadius: BorderRadius.all(Radius.circular(1)),
    );
    Color textColor = Color(0xFF676773);

    switch (type) {
      case 'ss':
        style = BoxDecoration(
          border: Border.all(
            width: 1,
            color: Color(0xFF35C561),
          ),
          color: Color(0xFF00b3770d),
          borderRadius: BorderRadius.all(Radius.circular(1)),
        );
        textColor = Color(0xFF51CC77);
        break;
      case 'ww':
        style = BoxDecoration(
          border: Border.all(
            width: 1,
            color: Color(0xFFECA100),
          ),
          color: Color(0xFFae000a),
          borderRadius: BorderRadius.all(Radius.circular(1)),
        );
        textColor = Color(0xFFECA100);
        break;
      default:
    }

    return Container(
      padding: EdgeInsets.fromLTRB(5, 1, 5, 1),
      margin: EdgeInsets.only(right: 7),
      decoration: style,
      child: Text(
        content ?? '',
        style: TextStyle(
          fontSize: 12,
          color: textColor,
        ),
      ),
    );
  }

  // 检查展示标签
  Widget tagRow() {
    List<Widget> content = [];
    var order = filterData['orderConditionCode'];
    var visit = filterData['visitCondition'];
    if (order != null) {
      var test = '';
      if (order == '1') {
        test = '本月未动销';
      } else if (order == '3') {
        test = '从未下单';
      } else if (order == '4') {
        test = '30天未动销';
      } else if (order == '5') {
        test = '90天未动销';
      }
      content.add(_buildTag(
        content: test,
      ));
    }
    if (visit != null) {
      var test = '';
      if (visit == '3') {
        test = '从未拜访';
      } else if (visit == '4') {
        test = '30天未拜访';
      } else if (visit == '5') {
        test = '60天未拜访';
      }
      content.add(_buildTag(
        content: test,
      ));
    }
    if (content.isEmpty) {
      return const SizedBox();
    }
    return Container(
      margin: EdgeInsets.only(top: 8),
      child: Row(children: content),
    );
  }

  // 检查选中
  void checkdSelect(AddVisitPlanPageMode item) {
    // 大于20 提示
    if (selectList.length >= (20 - visitPlanDataList.length)) {
      return XYYContainer.toastChannel.toast('已超出上限，无法继续添加');
    }
    if (item.inPlanFlag) {
      return XYYContainer.toastChannel.toast('该对象已在计划中');
    }
    // 未注册且不在私海
    if (!item.privateCustomerFlag) {
      if (!claimist.contains(item.id)) {
        CommonAsyncUtils.dialog(
          context: context,
          title: '提示',
          content: Text('客户尚未认领，请认领客户商品集后再加入拜访计划'),
          onConfirm: () async {
            var result =
                await NetworkV2<CustomerDetailDataV2>(CustomerDetailDataV2())
                    .requestDataV2('customerV2/openDetail',
                        parameters: {'customerId': item.id},
                        method: RequestMethod.GET);

            if (result.isSuccess == true) {
              var data = result.getData();
              CustomerSkuCollectDialog.showSkuCollectDialog(
                      context, data?.bindSkuCollect)
                  .then((value) {
                if (value == null || value.isEmpty) {
                  return;
                }
                showCupertinoDialog(
                  context: this.context,
                  builder: (BuildContext context) {
                    return CupertinoAlertDialog(
                      title: Text("确认是否认领"),
                      actions: [
                        CupertinoDialogAction(
                          child: Text("取消"),
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                        ),
                        CupertinoDialogAction(
                          child: Text("确定"),
                          onPressed: () {
                            Navigator.of(context).pop();
                            this.requestReviceCustomer(
                                value.map((e) => e.skuCollectCode).join(','),
                                item.id);
                          },
                        ),
                      ],
                    );
                  },
                );
              });
            } else {
              showToast('${result.errorMsg ?? "获取客户详情异常"}');
            }
          },
        );
        return;
      }
    }
    if (selectList.map((e) => e.id).contains(item.id)) {
      selectList.removeWhere((e) => e.id == item.id);
    } else {
      selectList.add(SelectItem(item.id, item.customerName));
    }
    checkBoxNotifier.value = !checkBoxNotifier.value;
  }

  // 定位信息请求
  void requestLocation() async {
    isManagerLevel = await UserInfoUtil.isBDMOrGJRBDM();
    dataListNotifier.value = !dataListNotifier.value;
    var location = await XYYContainer.locationChannel.locate();
    if (this.mounted && (location.isSuccess ?? false)) {
      lat = location.latitude;
      lng = location.longitude;
    }
    requestData();
    // 计划列表
    var result =
        await NetworkV2<VisitPlanDataModel>(VisitPlanDataModel()).requestDataV2(
      'visit/plan/list',
      parameters: {
        'lat': lat,
        'lng': lng,
      },
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
      showErrorToast:false,
    );
    visitPlanDataList = result.getData()?.planList ?? [];
    checkBoxNotifier.value = !checkBoxNotifier.value;
  }

  // 数据请求
  Future<void> requestData() async {
    paramModel = {};
    paramModel['lat'] = lat;
    paramModel['lng'] = lng;
    if (currentFilter != null) {
      paramModel['highSeas'] = currentFilter == FilterItem.unregistered ? 1 : 2;
    }
    if (filterData.isNotEmpty) {
      paramModel.addAll(filterData);
    }
    if (inputValue?.isNotEmpty ?? false) {
      paramModel['keyword'] = inputValue;
    }

    var result = await NetworkV2<AddVisitPlanPageMode>(AddVisitPlanPageMode())
        .requestDataV2(
      'visit/plan/search',
      parameters: paramModel,
      method: RequestMethod.POST,
    );
    if (result.isSuccess == true) {
      isLoading = false;
      var data = result.getListData();
      showList = [...data ?? []];
      dataListNotifier.value = !dataListNotifier.value;
    }
  }

  // 认领客户商品集请求
  void requestReviceCustomer(String skuCollectCodes, dynamic id) async {
    showLoadingDialog();
    var result =
        await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2()).requestDataV2(
      'openSea4POI/receive',
      parameters: {"id": id, "skuCollectCodes": skuCollectCodes},
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      claimist.add(id);
      showToast("认领成功");
    } else {
      if (result.code != null && result.code == 405) {
        showCupertinoDialog(
          context: this.context,
          builder: (BuildContext context) {
            return CupertinoAlertDialog(
              title: Text("已达到私海数量最大限度，无法认领"),
              actions: [
                CupertinoDialogAction(
                  child: Text("确定"),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ],
            );
          },
        );
      }
      showToast('${result.errorMsg ?? "认领客户失败"}');
    }
  }

  // 添加计划请求
  Future<void> addVisitPlanFn() async {
    if (selectList.length < 1)
      return XYYContainer.toastChannel.toast('请选择至少一个客户');
    showLoadingDialog();
    var result = await NetworkV2<ScheduleJoinTheProgramData>(
            ScheduleJoinTheProgramData())
        .requestDataV2("visit/plan/batchAdd",
            method: RequestMethod.GET,
            parameters: {'customerIds': selectList.map((e) => e.id).join(',')});
    dismissLoadingDialog();
    if (mounted && result.isSuccess == true) {
      XYYContainer.toastChannel.toast('加入成功');
      await Future.delayed(Duration(seconds: 1));
      if (showLayer) {
        Navigator.popUntil(
            context, (route) => route.settings.name == '/visit_plan_page');
      } else {
        Navigator.maybePop(context);
      }
    }
  }

  // 页面标题
  @override
  String getTitleName() {
    return "选择对象";
  }
}

// 圆形复选框
class RoundCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  RoundCheckbox({
    required this.value,
    required this.onChanged,
  });
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onChanged(!value);
      },
      child: Container(
        width: 20.0, // 调整大小以适应您的需求
        height: 20.0,
        decoration: BoxDecoration(
          shape: BoxShape.circle, // 圆形形状
          border: Border.all(
            color: Color(0xFFD8D8D8), // 边框颜色
            width: 1.0, // 边框宽度
          ),
        ),
        child: Center(
          child: value
              ? Container(
                  width: 15.0, // 调整圆柱的宽度
                  height: 15.0, // 调整圆柱的高度
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color(0xFF00B377),
                  ),
                )
              : null,
        ),
      ),
    );
  }
}
