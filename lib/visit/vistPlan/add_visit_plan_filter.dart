import 'package:XyyBeanSproutsFlutter/utils/format_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:XyyBeanSproutsFlutter/common/filter/common_filter_item.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/data/select_object_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/schedule/select_object/widget/select_object_multiple_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';

class AddVisitPlanFilter extends StatefulWidget {
  final Map<dynamic,dynamic>? filterData;
    final void Function(Map<dynamic,dynamic>)? onChange;
  AddVisitPlanFilter({
    this.filterData,
    this.onChange,
  });

  @override
  State<StatefulWidget> createState() => AddVisitPlanFilterState();
}

class AddVisitPlanFilterState extends State<AddVisitPlanFilter> {
  /// 展示数据
  List<SelectObjectFilterConfigModel> dataSource = [];

  /// 当前选择的参数
  Map<dynamic, dynamic> filterMap = {};
  @override
  initState() {
    filterMap = FormatUtils.deepCopy(widget.filterData ?? {});
    requestFilterData();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
                child: Center(
              child: Transform.translate(offset: Offset(26, 0),child: Text(
                '筛选',
                style: TextStyle(fontSize: 18),
              ),),
            )),
            IconButton(
              icon: ImageIcon(
                AssetImage('assets/images/titlebar/icon_close.png'),
                color: Colors.black,
                size: 22,
              ),
              onPressed: () {
                Navigator.of(context).pop();
              },
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
            ),
          ],
        ),
        Expanded(
          child: ListView.builder(
            cacheExtent: 99999,
            padding: EdgeInsets.zero,
            itemCount: this.dataSource.length,
            itemBuilder: this.builderItem,
          ),
        ),
        bottomWidget(),
      ],
    );
  }

  /// 请求筛选数据
  void requestFilterData() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<SelectObjectFilertRootModel>(
            SelectObjectFilertRootModel())
        .requestDataV2('visit/plan/conditions');
    EasyLoading.dismiss();
    if (mounted && result.isSuccess == true) {
      if (result.data != null) {
        this.handlerConfigData(result.data);
      }
    }
  }
  /// 构建筛选项
  void handlerConfigData(SelectObjectFilertRootModel rootModel) {
    /// 商店资质
    if (rootModel.licenseStatusCondition != null) {
      String? defaultId = rootModel.licenseStatusCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.licenseCodeKeyName,
        itemTitle: '商店资质',
        contents: (rootModel.licenseStatusCondition ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }
    /// 距离范围
    if (rootModel.distanceCondition != null) {
      String? defaultId = rootModel.distanceCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.distanceKeyName,
        itemTitle: '距离范围',
        contents: (rootModel.distanceCondition ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }
    /// 下单情况
    if (rootModel.placeOrderCondition != null) {
      String? defaultId = rootModel.placeOrderCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.orderKeyName,
        itemTitle: '动销情况',
        contents: (rootModel.placeOrderCondition ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }
    /// 拜访情况
    if (rootModel.visitCondition != null) {
      String? defaultId = rootModel.visitCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.visitKeyName,
        itemTitle: '拜访情况',
        contents: (rootModel.visitCondition ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }
    /// 客户等级
    if (rootModel.levelCondition != null) {
      String? defaultId = rootModel.levelCondition
          ?.firstWhere((element) => "${element.selected}" == "1",
              orElse: () => SelectObjectFilterData())
          .code
          .toString();
      SelectObjectFilterConfigModel configModel = SelectObjectFilterConfigModel(
        itemKey: ObjectFilterKey.levelKeyName,
        itemTitle: '客户等级',
        contents: (rootModel.levelCondition ?? [])
            .map((e) =>
                CommonFilterItemEntry(text: "${e.text}", code: "${e.code}"))
            .toList(),
        defaultId: defaultId,
        isAllowMultipleSelection: false,
        isMutexByDefault: true,
        isAllowClean: false,
      );
      this.dataSource.add(configModel);
    }
    
    setState(() {});
  }
  /// 构建底部按钮
  Widget bottomWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 15, 15, 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Color(0xFFD3D3D3),
                  width: 1,
                ),
                borderRadius: BorderRadius.circular(5),
              ),
              child: TextButton(
                onPressed: resetAllItem,
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                      MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    '重置',
                    style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xFF35C561),
                borderRadius: BorderRadius.circular(5),
              ),
              child: TextButton(
                onPressed: determineParams,
                child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding:
                      MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
  /// 构建item
  Widget builderItem(BuildContext context, int index) {
    SelectObjectFilterConfigModel config = this.dataSource[index];
      return Container(
        padding: EdgeInsets.all(10),
        child: SelectObjectFilterItem.single(
          title: config.itemTitle,
          contents: config.contents,
          defaultId: config.defaultId,
          selectedIds: this.getSelectIdsForKey(config.itemKey),
          isAllowMultipleSelection: config.isAllowMultipleSelection,
          isMutexByDefault: config.isMutexByDefault,
          isAllowClean: config.isAllowClean,
          valueChange: (itemKey, selectIds) {
            filterMap[itemKey] = selectIds.join(",");
          },
          itemKey: config.itemKey,
        ),
      );
  }
  /// 筛选事件
  void resetAllItem() {
    this.filterMap = {};
    setState(() {});
  }
  void determineParams() {
    filterMap.removeWhere((key, value) => value == null || value == 'null');
    Navigator.pop(context);
    widget.onChange?.call(filterMap);
  }

  /// 获取当前选中的id
  List<String> getSelectIdsForKey(String itemKey) {
    if (itemKey == ObjectFilterKey.statusKeyName) {
      return this.getCustomerStatusCode();
    }
    return this.filterMap[itemKey]?.split(",") ?? [];
  }

  /// 特殊处理客户状态的选中参数
  List<String> getCustomerStatusCode() {
    if (this.filterMap.keys.contains(ObjectFilterKey.statusKeyName)) {
      String selectValue = this.filterMap[ObjectFilterKey.statusKeyName] ?? "";
      selectValue = selectValue.replaceAll("1,2", "1000");
      List<String> ids = selectValue.split(",");
      int index = ids.indexOf("1000");
      if (index != -1) {
        ids[index] = "1,2";
      }
      return ids;
    }
    return [];
  }
}
