import 'dart:collection';
import 'dart:convert';

import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/check_license_dialog.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/schedule/data/schedule_external_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/plan/visit_plan_data.dart';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/visit/vistPlan/bottomSheet_labelWidget.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../bean/plan/visit_plan_tagList_model.dart';

class VisitPlanItem extends StatelessWidget {
  final PlanListModel data;
  final int index;
  final bool isSelect;
  final Function(dynamic code) changeTag; //改变了标签
  final Function() onSelectImg; //选中、取消
  final Function() onTop; //置顶

  VisitPlanItem(
      {required this.data,
      required this.index,
      required this.isSelect,
      required this.changeTag,
      required this.onSelectImg,
      required this.onTop});

  RichText buildRichText() {
    List<TextSpan> textSpans = [];

    textSpans.add(TextSpan(
      text: "距您${data.distance}",
      style: TextStyle(fontSize: 14, color: Color(0xff676773)),
    ));

    /// 未拜访
    if (data.latestVisitTime == null || data.latestVisitTime.isEmpty) {
      textSpans.add(TextSpan(
        text: "  |  ", // 分割线
        style: TextStyle(
            fontSize: 12, color: Colors.grey, fontWeight: FontWeight.w100),
      ));

      textSpans.add(TextSpan(
        text: '未拜访',
        style: TextStyle(fontSize: 14, color: Color(0xff676773)),
      ));
    }

    /// 等级
    if (data.level != null && data.level.isNotEmpty && data.level != '未评级') {
      textSpans.add(TextSpan(
        text: "  |  ", // 分割线
        style: TextStyle(
            fontSize: 12, color: Colors.grey, fontWeight: FontWeight.w100),
      ));

      textSpans.add(TextSpan(
        text: data.level,
        style: TextStyle(fontSize: 14, color: Color(0xff00B377)),
      ));
    }

    /// 状态 1正常、2正常(未激活)、3 冻结、4未注册
    if ((data.merchantStatus != 1) &&
        data.merchantStatusText != null &&
        data.merchantStatusText.isNotEmpty) {
      textSpans.add(TextSpan(
        text: "  |  ", // 分割线
        style: TextStyle(
            fontSize: 12, color: Colors.grey, fontWeight: FontWeight.w100),
      ));

      textSpans.add(TextSpan(
        text: data.merchantStatusText,
        style: TextStyle(
          fontSize: 14,
          color: data.merchantStatus == 2
              ? Color(0xffFF2021) // 红色
              : data.merchantStatusText == 3
                  ? Color(0xffECA100) // 黄色
                  : Color(0xff00B377), // 默认绿色
        ),
      ));
    }

    debugPrint('textSpanstextSpans: $textSpans');
    return RichText(
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
      text: TextSpan(
        children: textSpans,
      ),
    );
  }

  void showAddVisitActionAlert(BuildContext context, PlanListModel data) {
    // if(data.merchantStatus != 3){
    //   this.addVisit();
    //   return;
    // }

    CheckLicenseDialog().showCheckLicenseDialog(
        context,
        data.licenseValidateMust,
        data.licenseValidateIssue,
        data.merchantStatus == 3,
        continueVisitCallback: addVisit);

    // CommonAlertAction cancelAction = CommonAlertAction(
    //   title: "取消拜访",
    // );
    // CommonAlertAction continueActionGray = CommonAlertAction(
    //   title: "继续拜访",
    //   style: CommonAlertActionStyle.cancle,
    //   onPressed: addVisit,
    // );
    // showCommonAlert(
    //   context: context,
    //   title: "提示",
    //   content: '该客户为已冻结状态客户，拜访已冻结客户将会判定为无效拜访。',
    //   actions: [cancelAction, continueActionGray],
    //   barrierDismissible: true,
    // );
  }

  /// 添加拜访
  void addVisit() async {
    track('mc-visit-plan-addPlan');
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result = await NetworkV2<ScheduleExternalModel>(ScheduleExternalModel())
        .requestDataV2(
      'task/v290/toAddVisit',
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
      parameters: {
        'customerId': data.customerId,
        'customerType': 1,
      },
    );
    EasyLoading.dismiss();
    if (result.isSuccess == true) {
      bool isBDM = await UserInfoUtil.isBDMOrGJRBDM();
      String roleJSON = await UserAuthManager.getRoleJSONString();
      String externalJson = jsonEncode(result.getData()?.toJson() ?? {});
      String roleStr = Uri.encodeComponent(roleJSON);
      String externalStr = Uri.encodeComponent(externalJson);
      // BDM、跟进人BDM跳转陪访   BD、跟进人跳添加拜访
      if (isBDM) {
        var router =
            '/add_accompany_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      } else {
        var router =
            '/add_visit_page?rolesJSON=$roleStr&externalJson=$externalStr';
        XYYContainer.open(router);
      }
    }
  }

  void track(String actionType, {Map<String, String>? extras}) {
    var hashMap = HashMap<String, String>();
    hashMap['action_type'] = actionType;
    if (extras != null && extras.isNotEmpty) {
      hashMap.addAll(extras);
    }
    XYYContainer.bridgeCall('event_track', parameters: hashMap);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: 12, left: 0, bottom: 10, right: 10),
      margin: EdgeInsets.only(top: 10, left: 10, bottom: 0, right: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              debugPrint('图片被点击了');
              this.onSelectImg();
            },
            child: Container(
              //（增加的点击范围）
              // color: data.selected ? Colors.red : Colors.white,
              width: 50,
              height: 60,
              padding: EdgeInsets.only(
                  left: 16, right: 16, bottom: 36), // 设置边距来扩大点击范围
              child: Image.asset(
                isSelect
                    ? "assets/images/customer/customer_sku_collect_check_selected.png"
                    : "assets/images/customer/customer_sku_collect_check_unselected.png",
                width: 18,
                height: 24,
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  data.customerName,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ), //第一排：名称
                // SizedBox(height: 8),
                Row(
                  children: [
                    Image.asset(
                      'assets/images/customer/customer_map_location.png',
                      width: 8,
                      height: 11,
                    ),
                    SizedBox(width: 4),
                    buildRichText(),
                    Spacer(),
                    ElevatedButton(
                      onPressed: () {
                        // 处理按钮点击事件
                        showModalBottomSheet(
                          context: context,
                          backgroundColor: Colors.transparent, //重点
                          builder: (BuildContext context) {
                            return Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(8.0),
                                  topRight: Radius.circular(8.0),
                                ),
                              ),
                              child: BottomSheetLabelWidget(
                                planTag: data.planTag,
                                onSelectTag: (VisitTagModel model) {
                                  debugPrint(
                                      "item:点击了标签${model.text},${model.code}");
                                  this.changeTag(model.code);
                                },
                              ),
                            );
                          },
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        padding: EdgeInsets.zero,
                        minimumSize: Size(52, 18),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(1),
                          side: BorderSide(
                            width: 0.5,
                            color: Color(0xFF35C561), // 边框颜色
                          ),
                        ),
                        primary: Color(0xFFF0FBF5), // 背景颜色
                        elevation: 0, // 禁用阴影
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            data.planTagText, //"标记",
                            style: TextStyle(
                              color: Color(0xff00B377), // 文字颜色
                              fontSize: 12,
                            ),
                          ),
                          SizedBox(width: 4), // 文字和图片之间的间距
                          Image(
                              image: AssetImage(
                                  "assets/images/commodity/commodity_select_drop.png"),
                              width: 7,
                              height: 4),
                        ],
                      ),
                    ),
                  ],
                ), //第二排：位置、文案、…… 、按钮
                // SizedBox(height: 8),
                Row(
                  children: [
                    if (data.latestOrderTime != null &&
                        data.latestOrderTime.isNotEmpty)
                      Text(
                        "最后下单：",
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xff8E8E93),
                        ),
                      ),
                    if (data.latestOrderTime != null &&
                        data.latestOrderTime.isNotEmpty)
                      Text(
                        data.latestOrderTime,
                        style: TextStyle(
                          fontSize: 13,
                          color: Color(0xFF333333),
                        ),
                      ),
                    if (data.latestOrderTime != null &&
                        data.latestOrderTime.isNotEmpty)
                      SizedBox(width: 16),
                    if (data.latestVisitTime != null &&
                        data.latestVisitTime.isNotEmpty)
                      Text(
                        "最后拜访：",
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xff8E8E93),
                        ),
                      ),
                    if (data.latestVisitTime != null &&
                        data.latestVisitTime.isNotEmpty)
                      Text(
                        data.latestVisitTime,
                        style: TextStyle(
                          fontSize: 13,
                          color: Color(0xFF333333),
                        ),
                      ),
                  ],
                ), //第三排：下单、拜访时间
                SizedBox(height: 8),
                Visibility(
                    visible: data.proposeReason != null &&
                        data.proposeReason != "", // 根据条件设置是否可见
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "推荐理由：",
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xff8E8E93),
                          ),
                        ),
                        Flexible(
                          child: Text(
                            data.proposeReason, // 推荐理由
                            style: TextStyle(
                              fontSize: 13,
                              color: Color(0xFF333333),
                            ),
                            softWrap: true, // 启用自动换行
                          ),
                        ),
                      ],
                    )), //第四排：推荐理由
                SizedBox(height: 4),
                Divider(), //分割线
                Row(
                  children: [
                    Visibility(
                      visible: index != 0, // 根据条件设置是否可见
                      child: Row(
                        children: [
                          TextButton.icon(
                            onPressed: () {
                              debugPrint("点击的置顶按钮");
                              this.onTop();
                            },
                            icon: const Image(
                              image: AssetImage(
                                  "assets/images/visit/icon_visit_toTop.png"),
                              width: 10,
                              height: 10,
                            ),
                            label: const Text("置顶",
                                style: TextStyle(
                                    fontSize: 12, color: Color(0xff00B377))),
                            style: ButtonStyle(
                              overlayColor:
                                  MaterialStateProperty.all(Colors.transparent),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Spacer(),
                    TextButton(
                      onPressed: () {
                        debugPrint("点击的导航按钮${data.address}");
                        // 导航
                        if (data.poiLat == null || data.poiLng == null) {
                          XYYContainer.toastChannel.toast("客户信息获取异常");
                          return;
                        }
                        double latitude =
                            double.tryParse("${data.poiLat}") ?? 0;
                        double longitude =
                            double.tryParse("${data.poiLng}") ?? 0;
                        XYYContainer.bridgeCall('map_navigation', parameters: {
                          'latitude': latitude,
                          'longitude': longitude,
                          'address': data.address,
                        });
                      },
                      child: const Text(
                        "导航",
                        style: TextStyle(
                            color: Color(0xff333333),
                            fontSize: 12,
                            fontWeight: FontWeight.w600),
                      ),
                      style: ButtonStyle(
                        //按钮大小
                        minimumSize:
                            MaterialStateProperty.all(const Size(64, 25)),
                        //水波纹颜色 不想使用 设置透明即可
                        overlayColor:
                            MaterialStateProperty.all(Colors.transparent),
                        //设置边框
                        side: MaterialStateProperty.all(
                            const BorderSide(color: Color(0xff8E8E93))),
                        //设置形状、圆角
                        shape: MaterialStateProperty.all(const StadiumBorder()),
                        // 设置内边距
                        padding: MaterialStateProperty.all(EdgeInsets.only(
                            left: 12, right: 12, top: 7, bottom: 7)),
                      ),
                    ),
                    SizedBox(width: 8),
                    TextButton(
                      onPressed: () {
                        debugPrint("点击的添加拜访按钮");
                        showAddVisitActionAlert(context, data);
                      },
                      child: const Text(
                        "添加拜访",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                            fontWeight: FontWeight.w600),
                      ),
                      style: ButtonStyle(
                        //按钮大小
                        minimumSize:
                            MaterialStateProperty.all(const Size(64, 25)),
                        //水波纹颜色 不想使用 设置透明即可
                        overlayColor:
                            MaterialStateProperty.all(Colors.transparent),
                        //背景颜色
                        backgroundColor:
                            MaterialStateProperty.all(Color(0xff00B377)),
                        //设置边框
                        // side: MaterialStateProperty.all( const BorderSide(color: Color(0xff8E8E93))),
                        //设置形状、圆角
                        shape: MaterialStateProperty.all(const StadiumBorder()),
                        // 设置内边距
                        padding: MaterialStateProperty.all(EdgeInsets.only(
                            left: 12, right: 12, top: 7, bottom: 7)),
                      ),
                    ),
                  ],
                ), //第五排：3个按钮
              ],
            ),
          ),
        ],
      ),
    );
  }
}
