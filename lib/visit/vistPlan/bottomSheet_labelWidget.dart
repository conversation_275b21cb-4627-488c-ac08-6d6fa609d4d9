import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import '../bean/plan/visit_plan_tagList_model.dart';

class BottomSheetLabelWidget extends StatefulWidget {
  final planTag;//需要回显的tag id
  final Function(VisitTagModel) onSelectTag;

  BottomSheetLabelWidget({required this.planTag, required this.onSelectTag});

  @override
  _BottomSheetLabelWidgetState createState() => _BottomSheetLabelWidgetState();
}

class _BottomSheetLabelWidgetState extends State<BottomSheetLabelWidget> {
  List<VisitTagModel> dataSource = [];

  @override
  void initState() {
    super.initState();
    requestVisitPlanTagList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 250 + MediaQuery.of(context).padding.bottom,
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          // 最上面一排标题文字和关闭按钮
          Row(
            children: <Widget>[
              SizedBox(width: 50),
              Expanded(
                child: Center(
                  child: Text(
                    "设置标签",
                    style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              IconButton(
                icon: Icon(Icons.close),
                onPressed: () {
                  Navigator.pop(context); // 关闭底部模态表单
                },
              ),
            ],
          ),
          SizedBox(height: 4),
          // 根据返回数据构建横向排列的文字按钮
          Wrap(
            spacing: 15, // 按钮之间的间隔
            children: List.generate(dataSource.length, (index) => _buildButton(index),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildButton(index){
    VisitTagModel model = dataSource[index];

    return TextButton(
      onPressed: () {
        setState(() {
          debugPrint("点击了标签${model.text},${model.code}");          // 选择数据后，调用回调函数传递选中的数据
          widget.onSelectTag(model);
          Navigator.pop(context); // 关闭底部模态表单
        });
      },
      style: ButtonStyle(
        backgroundColor: MaterialStateProperty.all(
          model.isSelected ? Color(0xFFF0FBF5) : Color(0xffF0F0F0),
        ),

        //按钮大小
        minimumSize: MaterialStateProperty.all(const Size(47, 27)),
        //水波纹颜色 不想使用 设置透明即可
        overlayColor: MaterialStateProperty.all(Colors.transparent),
        //设置边框
        side: MaterialStateProperty.all(BorderSide(
            color: model.isSelected ? Color(0xff00B377) : Color(0xffF0F0F0)
        )),
        //设置形状、圆角
        shape: MaterialStateProperty.all(const StadiumBorder()),
        // 设置内边距
        padding: MaterialStateProperty.all(EdgeInsets.only(left: 12, right: 12, top: 5, bottom: 5)),
      ),
      child: Text(
        model.text,
        style: TextStyle(
          color: model.isSelected ? Color(0xff00B377) : Color(0xff666666)
        ),
      ),
    );
  }

  /// 请求标签列表
  void requestVisitPlanTagList() async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var result =
    await NetworkV2<VisitTagModel>(VisitTagModel()).requestDataV2(
      'visit/plan/tagList',
      parameters: null,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    EasyLoading.dismiss();
    // if (result.isSuccess == true) {
    //   XYYContainer.toastChannel.toast("提示");
    // }
    if (mounted) {
      if (result.isSuccess == true) {
        List<VisitTagModel>? source = result.getListData();
        if (source != null) {
          this.dataSource = source;
          // 根据 planTag 匹配选中的按钮
          for (int i = 0; i < dataSource.length; i++) {
            print(
                '--- ${dataSource[i].code}, ${dataSource[i].text}, ${dataSource[i].isSelected}');
            dataSource[i].isSelected =
            (dataSource[i].code.toString() == widget.planTag.toString());
            print(
                '--1 ${dataSource[i].code}, ${dataSource[i].text}, ${dataSource[i].isSelected}');
          }
          setState(() {});
        }
      }
    }
  }
}


