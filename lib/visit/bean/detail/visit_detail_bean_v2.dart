import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/visit/bean/detail/visit_detail_bean.dart';
import 'package:json_annotation/json_annotation.dart';

import 'merchant_basic_info.dart';
import 'merchant_visit_bean.dart';
import 'schedule_bean.dart';
import 'task_bean.dart';

part 'visit_detail_bean_v2.g.dart';

@JsonSerializable()
class VisitDetailBeanV2 extends BaseModelV2<VisitDetailBeanV2> {
  String? address;
  String? domainPath;
  MerchantBasicInfo? merchantBasicInfo;


  int? createTime;
  int? endTime;
  int? id;
  String? remark;
  String? scheduleTheme;
  int? startTime;
  @Json<PERSON>ey(name: "")
  int? type;
  String? visitTypeText;
  int? userId;
  int? visitId;
  int? alertTime;
  String? image;
  bool? effective; // 是否有效日程
  @<PERSON><PERSON><PERSON><PERSON>(name: "oaName")
  String? userName; // 执行人姓名

  String? merchantName;
  String? contactor;
  String? mobile;
  String? visitTime;
  int? visitType;
  int? businessType;
  int? visitReason;
  String? visitDemo;
  int? isEffective; //1-有效；2-无效
  int? isSign;
  int? isDimprice;
  int? createType;
  int? status;
  String? statusName;
  String? branchCode;
  String? branchName;
  String? sysRealName;
  String? sysJobNumber;
  String? visitReasonName;
  int? updateTime;
  String? lng;
  String? lat;
  String? accompanyName;
  String? talkTimeText;
  bool? kpFlag;

  String? invalidReason; //无效原因

  VisitDetailBeanV2();

  VisitDetailBean convertToVisitDetailBean() {
    var visitDetailBean = VisitDetailBean();
    visitDetailBean.kpFlag = kpFlag;
    visitDetailBean.domainPath = domainPath;
    visitDetailBean.merchantBasicInfo = merchantBasicInfo;
    var scheduleBean = ScheduleBean()
    ..createTime = createTime
    ..endTime = endTime
    ..id = id
    ..remark = remark
    ..scheduleTheme = scheduleTheme
    ..startTime = startTime
    ..type = type
    ..userId = userId
    ..visitId = visitId
    ..alertTime = alertTime
    ..image = image
    ..effective = effective
    ..visitTypeText = visitTypeText
    ..isEffective = isEffective
    ..invalidReason = invalidReason
    ..userName = userName;
    visitDetailBean.personalSchedule = scheduleBean;
    var merchantVisitBean = MerchantVisitBean()
    ..id = id
    ..merchantName = merchantName
    ..contactor = contactor
    ..mobile = mobile
    ..visitTime = visitTime
    ..visitType = visitType
    ..businessType = businessType
    ..visitReason = visitReason
    ..visitDemo = visitDemo
    ..isEffective = isEffective
    ..isSign = isSign
    ..isDimprice = isDimprice
    ..createType = createType
    ..status = status
    ..createTime = createTime
    ..statusName = statusName
    ..branchCode = branchCode
    ..branchName = branchName
    ..sysRealName = sysRealName
    ..sysJobNumber = sysJobNumber
    ..visitReasonName = visitReasonName
    ..updateTime = updateTime
    ..image = image
    ..lng = lng
    ..lat = lat
    ..address = address
    ..accompanyName = accompanyName
    ..talkTimeText = talkTimeText;
    visitDetailBean.merchantVisit = merchantVisitBean;
    return visitDetailBean;
  }

  @override
  VisitDetailBeanV2 fromJsonMap(Map<String, dynamic> json) {
    return VisitDetailBeanV2.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$VisitDetailBeanV2ToJson(this);
  }

  factory VisitDetailBeanV2.fromJson(Map<String, dynamic> json) =>
      _$VisitDetailBeanV2FromJson(json);
}
