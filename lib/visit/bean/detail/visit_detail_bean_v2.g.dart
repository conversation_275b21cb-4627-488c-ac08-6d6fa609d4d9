// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'visit_detail_bean_v2.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VisitDetailBeanV2 _$VisitDetailBeanV2FromJson(Map<String, dynamic> json) {
  return VisitDetailBeanV2()
    ..address = json['address'] as String?
    ..domainPath = json['domainPath'] as String?
    ..merchantBasicInfo = json['merchantBasicInfo'] == null
        ? null
        : MerchantBasicInfo.fromJson(
            json['merchantBasicInfo'] as Map<String, dynamic>)
    ..createTime = json['createTime'] as int?
    ..endTime = json['endTime'] as int?
    ..id = json['id'] as int?
    ..remark = json['remark'] as String?
    ..scheduleTheme = json['scheduleTheme'] as String?
    ..startTime = json['startTime'] as int?
    ..type = json[''] as int?
    ..visitTypeText = json['visitTypeText'] as String?
    ..userId = json['userId'] as int?
    ..visitId = json['visitId'] as int?
    ..alertTime = json['alertTime'] as int?
    ..image = json['image'] as String?
    ..effective = json['effective'] as bool?
    ..userName = json['oaName'] as String?
    ..merchantName = json['merchantName'] as String?
    ..contactor = json['contactor'] as String?
    ..mobile = json['mobile'] as String?
    ..visitTime = json['visitTime'] as String?
    ..visitType = json['visitType'] as int?
    ..businessType = json['businessType'] as int?
    ..visitReason = json['visitReason'] as int?
    ..visitDemo = json['visitDemo'] as String?
    ..isEffective = json['isEffective'] as int?
    ..isSign = json['isSign'] as int?
    ..isDimprice = json['isDimprice'] as int?
    ..createType = json['createType'] as int?
    ..status = json['status'] as int?
    ..statusName = json['statusName'] as String?
    ..branchCode = json['branchCode'] as String?
    ..branchName = json['branchName'] as String?
    ..sysRealName = json['sysRealName'] as String?
    ..sysJobNumber = json['sysJobNumber'] as String?
    ..visitReasonName = json['visitReasonName'] as String?
    ..updateTime = json['updateTime'] as int?
    ..lng = json['lng'] as String?
    ..lat = json['lat'] as String?
    ..accompanyName = json['accompanyName'] as String?
    ..talkTimeText = json['talkTimeText'] as String?
    ..invalidReason = json['invalidReason'] as String?
    ..kpFlag = json['kpFlag'] as bool?;
}

Map<String, dynamic> _$VisitDetailBeanV2ToJson(VisitDetailBeanV2 instance) =>
    <String, dynamic>{
      'address': instance.address,
      'domainPath': instance.domainPath,
      'merchantBasicInfo': instance.merchantBasicInfo,
      'createTime': instance.createTime,
      'endTime': instance.endTime,
      'id': instance.id,
      'remark': instance.remark,
      'scheduleTheme': instance.scheduleTheme,
      'startTime': instance.startTime,
      '': instance.type,
      'visitTypeText': instance.visitTypeText,
      'userId': instance.userId,
      'visitId': instance.visitId,
      'alertTime': instance.alertTime,
      'image': instance.image,
      'effective': instance.effective,
      'oaName': instance.userName,
      'merchantName': instance.merchantName,
      'contactor': instance.contactor,
      'mobile': instance.mobile,
      'visitTime': instance.visitTime,
      'visitType': instance.visitType,
      'businessType': instance.businessType,
      'visitReason': instance.visitReason,
      'visitDemo': instance.visitDemo,
      'isEffective': instance.isEffective,
      'isSign': instance.isSign,
      'isDimprice': instance.isDimprice,
      'createType': instance.createType,
      'status': instance.status,
      'statusName': instance.statusName,
      'branchCode': instance.branchCode,
      'branchName': instance.branchName,
      'sysRealName': instance.sysRealName,
      'sysJobNumber': instance.sysJobNumber,
      'visitReasonName': instance.visitReasonName,
      'updateTime': instance.updateTime,
      'lng': instance.lng,
      'lat': instance.lat,
      'accompanyName': instance.accompanyName,
      'talkTimeText': instance.talkTimeText,
      'invalidReason': instance.invalidReason,
      'kpFlag' : instance.kpFlag,
    };
