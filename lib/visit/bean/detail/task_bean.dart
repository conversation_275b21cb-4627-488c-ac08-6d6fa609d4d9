import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'task_bean.g.dart';

@JsonSerializable()
class TaskBean extends BaseModelV2<TaskBean> {
  String? theme;

  TaskBean();

  @override
  TaskBean fromJsonMap(Map<String, dynamic> json) {
    return TaskBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskBeanToJson(this);
  }

  factory TaskBean.fromJson(Map<String, dynamic> json) =>
      _$TaskBeanFromJson(json);
}
