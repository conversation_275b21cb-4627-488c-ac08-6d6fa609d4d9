// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'merchant_visit_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MerchantVisitBean _$MerchantVisitBeanFromJson(Map<String, dynamic> json) {
  return MerchantVisitBean()
    ..id = json['id'] as int?
    ..merchantName = json['merchantName'] as String?
    ..contactor = json['contactor'] as String?
    ..mobile = json['mobile'] as String?
    ..visitTime = json['visitTime'] as String?
    ..visitType = json['visitType'] as int?
    ..visitTypeText = json['visitTypeText'] as String?
    ..businessType = json['businessType'] as int?
    ..visitReason = json['visitReason'] as int?
    ..visitDemo = json['visitDemo'] as String?
    ..isEffective = json['isEffective'] as int?
    ..isSign = json['isSign'] as int?
    ..isDimprice = json['isDimprice'] as int?
    ..createType = json['createType'] as int?
    ..status = json['status'] as int?
    ..createTime = json['createTime'] as int?
    ..statusName = json['statusName'] as String?
    ..branchCode = json['branchCode'] as String?
    ..branchName = json['branchName'] as String?
    ..sysRealName = json['sysRealName'] as String?
    ..sysJobNumber = json['sysJobNumber'] as String?
    ..visitReasonName = json['visitReasonName'] as String?
    ..updateTime = json['updateTime'] as int?
    ..image = json['image'] as String?
    ..lng = json['lng'] as String?
    ..lat = json['lat'] as String?
    ..address = json['address'] as String?
    ..accompanyName = json['accompanyName'] as String?
    ..talkTimeText = json['talkTimeText'] as String?;
}

Map<String, dynamic> _$MerchantVisitBeanToJson(MerchantVisitBean instance) =>
    <String, dynamic>{
      'id': instance.id,
      'merchantName': instance.merchantName,
      'contactor': instance.contactor,
      'mobile': instance.mobile,
      'visitTime': instance.visitTime,
      'visitType': instance.visitType,
      'visitTypeText': instance.visitTypeText,
      'businessType': instance.businessType,
      'visitReason': instance.visitReason,
      'visitDemo': instance.visitDemo,
      'isEffective': instance.isEffective,
      'isSign': instance.isSign,
      'isDimprice': instance.isDimprice,
      'createType': instance.createType,
      'status': instance.status,
      'createTime': instance.createTime,
      'statusName': instance.statusName,
      'branchCode': instance.branchCode,
      'branchName': instance.branchName,
      'sysRealName': instance.sysRealName,
      'sysJobNumber': instance.sysJobNumber,
      'visitReasonName': instance.visitReasonName,
      'updateTime': instance.updateTime,
      'image': instance.image,
      'lng': instance.lng,
      'lat': instance.lat,
      'address': instance.address,
      'accompanyName': instance.accompanyName,
      'talkTimeText': instance.talkTimeText,
    };
