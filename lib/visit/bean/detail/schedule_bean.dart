import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'schedule_bean.g.dart';

@JsonSerializable()
class ScheduleBean extends BaseModelV2<ScheduleBean> {
  int? createTime;
  int? endTime;
  int? id;
  String? remark;
  String? scheduleTheme;
  int? startTime;
  int? type;
  int? userId;
  int? visitId;
  int? alertTime;
  String? image;
  bool? effective; // 是否有效日程
  String? userName; // 执行人姓名
  String? visitTypeText;
  int? isEffective = 1; //1-有效；2-无效
  String? invalidReason; //无效原因

  ScheduleBean();

  @override
  ScheduleBean fromJsonMap(Map<String, dynamic> json) {
    return ScheduleBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ScheduleBeanToJson(this);
  }

  factory ScheduleBean.fromJson(Map<String, dynamic> json) =>
      _$ScheduleBeanFromJson(json);
}
