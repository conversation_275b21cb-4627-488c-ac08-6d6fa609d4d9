// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskBean _$TaskBean<PERSON>rom<PERSON>son(Map<String, dynamic> json) {
  return TaskBean()..theme = json['theme'] as String?;
}

Map<String, dynamic> _$TaskBeanToJson(TaskBean instance) => <String, dynamic>{
      'theme': instance.theme,
    };
