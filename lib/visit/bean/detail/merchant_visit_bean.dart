import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'merchant_visit_bean.g.dart';

@JsonSerializable()
class MerchantVisitBean extends BaseModelV2<MerchantVisitBean> {
  int? id;
  String? merchantName;
  String? contactor;
  String? mobile;
  String? visitTime;
  int? visitType;
  String? visitTypeText;
  int? businessType;
  int? visitReason;
  String? visitDemo;
  int? isEffective;
  int? isSign;
  int? isDimprice;
  int? createType;
  int? status;
  int? createTime;
  String? statusName;
  String? branchCode;
  String? branchName;
  String? sysRealName;
  String? sysJobNumber;
  String? visitReasonName;
  int? updateTime;
  String? image;
  String? lng;
  String? lat;
  String? address;
  String? accompanyName;
  String? talkTimeText;

  MerchantVisitBean();

  @override
  MerchantVisitBean fromJsonMap(Map<String, dynamic> json) {
    return MerchantVisitBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MerchantVisitBeanToJson(this);
  }

  factory MerchantVisitBean.fromJson(Map<String, dynamic> json) =>
      _$MerchantVisitBeanFromJson(json);
}
