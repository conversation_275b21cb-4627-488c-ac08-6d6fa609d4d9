// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'schedule_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ScheduleBean _$ScheduleBeanFromJson(Map<String, dynamic> json) {
  return ScheduleBean()
    ..createTime = json['createTime'] as int?
    ..endTime = json['endTime'] as int?
    ..id = json['id'] as int?
    ..remark = json['remark'] as String?
    ..scheduleTheme = json['scheduleTheme'] as String?
    ..startTime = json['startTime'] as int?
    ..type = json['type'] as int?
    ..userId = json['userId'] as int?
    ..visitId = json['visitId'] as int?
    ..alertTime = json['alertTime'] as int?
    ..image = json['image'] as String?
    ..effective = json['effective'] as bool?
    ..userName = json['userName'] as String?
    ..visitTypeText = json['visitTypeText'] as String?
    ..isEffective = json['isEffective'] as int?
    ..invalidReason = json['invalidReason'] as String?;
}

Map<String, dynamic> _$ScheduleBeanToJson(ScheduleBean instance) =>
    <String, dynamic>{
      'createTime': instance.createTime,
      'endTime': instance.endTime,
      'id': instance.id,
      'remark': instance.remark,
      'scheduleTheme': instance.scheduleTheme,
      'startTime': instance.startTime,
      'type': instance.type,
      'userId': instance.userId,
      'visitId': instance.visitId,
      'alertTime': instance.alertTime,
      'image': instance.image,
      'effective': instance.effective,
      'userName': instance.userName,
      'visitTypeText': instance.visitTypeText,
      'isEffective': instance.isEffective,
      'invalidReason': instance.invalidReason,
    };
