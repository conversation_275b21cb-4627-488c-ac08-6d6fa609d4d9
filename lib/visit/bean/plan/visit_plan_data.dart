import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'visit_plan_data.g.dart';

@JsonSerializable()
class PlanListModel extends BaseModelV2<PlanListModel> {
  /// 计划 id
  dynamic id;
  dynamic customerId;

  /// 客户名称
  dynamic customerName;

  /// 客户类型
  dynamic customerType;

  /// 距离
  dynamic distance;

  /// 地址
  dynamic address;

  /// 等级
  dynamic level;

  /// 状态 1正常、2正常(未激活)、3 冻结、4未注册
  dynamic merchantStatus;
  dynamic merchantStatusText;

  /// 最后下单时间
  dynamic latestOrderTime;

  /// 最后拜访时间
  dynamic latestVisitTime;

  /// 标签
  dynamic planTagText;

  /// 标签 id
  dynamic planTag;

  /// 推荐理由
  dynamic proposeReason;
  dynamic licenseValidateMust;
  dynamic licenseValidateIssue;
  dynamic poiLat;
  dynamic poiLng;
  /// 是否选中
  bool selected = false;

  PlanListModel();

  factory PlanListModel.fromJson(Map<String, dynamic> json) {
    return _$PlanListModelFromJson(json);
  }

  @override
  PlanListModel fromJsonMap(Map<String, dynamic> json) {
    return _$PlanListModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PlanListModelToJson(this);
  }
}


@JsonSerializable()
class VisitPlanDataModel extends BaseModelV2<VisitPlanDataModel> {
  dynamic? id;
  dynamic isSelected;
  dynamic planNums;
  dynamic planMax;
  List<PlanListModel>? planList;

  @override
  VisitPlanDataModel fromJsonMap(Map<String, dynamic> json) {
    return _$VisitPlanDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$VisitPlanDataModelToJson(this);
  }
}
