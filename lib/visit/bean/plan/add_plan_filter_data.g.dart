part of 'add_plan_filter_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AddPlanFilterItemModel _$AddPlanFilterItemModelFromJson(Map<String, dynamic> json) {
  return AddPlanFilterItemModel()
    ..code = json['code']
    ..text = json['text']
    ..selected = json['selected'];
}

Map<String, dynamic> _$AddPlanFilterItemModelToJson(AddPlanFilterItemModel instance) =>
    <String, dynamic>{
      'code': instance.code,
      'text': instance.text,
      'selected': instance.selected,
    };

AddPlanFilterModel _$AddPlanFilterModelFromJson(Map<String, dynamic> json) {
  return AddPlanFilterModel()
    ..distanceCondition = (json['distanceCondition'] as List<dynamic>?)
        ?.map((e) => AddPlanFilterItemModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..licenseStatusCondition = (json['licenseStatusCondition'] as List<dynamic>?)
        ?.map((e) => AddPlanFilterItemModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..placeOrderCondition = (json['placeOrderCondition'] as List<dynamic>?)
        ?.map((e) => AddPlanFilterItemModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..levelCondition = (json['levelCondition'] as List<dynamic>?)
        ?.map((e) => AddPlanFilterItemModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..visitCondition = (json['visitCondition'] as List<dynamic>?)
        ?.map((e) => AddPlanFilterItemModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..highSeaCondition = (json['highSeaCondition'] as List<dynamic>?)
        ?.map((e) => AddPlanFilterItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$AddPlanFilterModelToJson(AddPlanFilterModel instance) =>
    <String, dynamic>{
      'distanceCondition': instance.distanceCondition,
      'licenseStatusCondition': instance.licenseStatusCondition,
      'placeOrderCondition': instance.placeOrderCondition,
      'levelCondition': instance.levelCondition,
      'visitCondition': instance.visitCondition,
      'highSeaCondition': instance.highSeaCondition,
    };
