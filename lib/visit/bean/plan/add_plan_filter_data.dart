import 'package:XyyBeanSproutsFlutter/common/filter/common_filter_item.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

part 'add_plan_filter_data.g.dart';


@JsonSerializable()
class AddPlanFilterItemModel extends BaseModelV2<AddPlanFilterItemModel> {
  dynamic code;
  dynamic text;
  dynamic selected;

  AddPlanFilterItemModel();

  factory AddPlanFilterItemModel.fromJson(Map<String, dynamic> json) {
    return _$AddPlanFilterItemModelFromJson(json);
  }

  @override
  AddPlanFilterItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$AddPlanFilterItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AddPlanFilterItemModelToJson(this);
  }
}


@JsonSerializable()
class AddPlanFilterModel extends BaseModelV2<AddPlanFilterModel> {

  /// 距离范围
  List<AddPlanFilterItemModel>? distanceCondition;

  /// 资质状态
  List<AddPlanFilterItemModel>? licenseStatusCondition;

  /// 动销
  List<AddPlanFilterItemModel>? placeOrderCondition;

  /// 客户等级
  List<AddPlanFilterItemModel>? levelCondition;

  /// 拜访情况
  List<AddPlanFilterItemModel>? visitCondition;

  /// 公海
  List<AddPlanFilterItemModel>? highSeaCondition;

  @override
  AddPlanFilterModel fromJsonMap(Map<String, dynamic> json) {
    return _$AddPlanFilterModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AddPlanFilterModelToJson(this);
  }
}

class SelectObjectFilterConfigModel {
  String itemKey;

  String itemTitle;

  List<CommonFilterItemEntry> contents;

  /// 默认选中
  String? defaultId;

  /// 是否支持多选
  bool isAllowMultipleSelection;

  /// 是否支持多选情况下与默认选中互斥
  bool isMutexByDefault;

  /// 是否可不选中
  bool isAllowClean;

  SelectObjectFilterConfigModel({
    required this.itemKey,
    required this.itemTitle,
    this.contents = const [],
    this.defaultId,
    this.isAllowMultipleSelection = false,
    this.isMutexByDefault = false,
    this.isAllowClean = false,
  });
}

class ObjectFilterKey {
  /// 资质状态
  static String licenseKeyName = "licenseCode";

  /// 距离
  static String distanceKeyName = "distance";

  /// 动销情况
  static String salesKeyName = "orderConditionCode";

  /// 拜访情况
  static String visitKeyName = "visitCondition";

  /// 客户等级
  static String levelKeyName = "customerLevelCode";
}
