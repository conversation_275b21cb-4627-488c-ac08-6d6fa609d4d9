// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'plan_search_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PlanSearchModel _$PlanSearchModelFromJson(Map<String, dynamic> json) {
  return PlanSearchModel()
    ..customerId = json['customerId']
    ..customerName = json['customerName']
    ..merchantStatus = json['merchantStatus']
    ..licenseValidateMust = json['licenseValidateMust']
    ..licenseValidateIssue = json['licenseValidateIssue']
    ..merchantStatusText = json['merchantStatusText']
    ..distance = json['distance']
    ..address = json['address']
    ..inPlanFlag = json['inPlanFlag']
    ..privateCustomerFlag = json['privateCustomerFlag']
    ..selected = false;
}

Map<String, dynamic> _$PlanSearchModelToJson(PlanSearchModel instance) =>
    <String, dynamic>{
      'customerId': instance.customerId,
      'customerName': instance.customerName,
      'merchantStatus': instance.merchantStatus,
      'licenseValidateMust': instance.licenseValidateMust,
      'licenseValidateIssue': instance.licenseValidateIssue,
      'merchantStatusText': instance.merchantStatusText,
      'distance': instance.distance,
      'address': instance.address,
      'inPlanFlag': instance.inPlanFlag,
      'privateCustomerFlag': instance.privateCustomerFlag,
      'selected': instance.selected,
    };
