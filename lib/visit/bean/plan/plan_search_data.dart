import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'plan_search_data.g.dart';

@JsonSerializable()
class PlanSearchModel extends BaseModelV2<PlanSearchModel> {
  dynamic customerId;
  dynamic customerName;
  /// 状态 1正常、2正常(未激活)、3 冻结、4未注册
  dynamic merchantStatus;
  dynamic merchantStatusText;

  /// -1正常；1-临期；2-过期
  dynamic licenseValidateMust;
  dynamic licenseValidateIssue;
  /// 距离
  dynamic distance;
  /// 地址
  dynamic address;
  /// 是否存在拜访计划
  dynamic inPlanFlag;
  /// 是否在私海内
  dynamic privateCustomerFlag;
  /// 是否选中
  bool selected = false;

  @override
  PlanSearchModel fromJsonMap(Map<String, dynamic> json) {
    return _$PlanSearchModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PlanSearchModelToJson(this);
  }
}