import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'visit_plan_tagList_model.g.dart';

@JsonSerializable()
class VisitTagModel extends BaseModelV2<VisitTagModel> {
  dynamic code;
  dynamic text;
  dynamic isSelected;

  @override
  VisitTagModel fromJsonMap(Map<String, dynamic> json) {
    return _$VisitTagModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$VisitTagModelToJson(this);
  }
}