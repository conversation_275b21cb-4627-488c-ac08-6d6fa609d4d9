import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'visit_tag_data.g.dart';

@JsonSerializable()
class VisitTagData extends BaseModelV2<VisitTagData> {
  dynamic? id;
  bool? checked;
  String? value;

  VisitTagData();

  @override
  VisitTagData fromJsonMap(Map<String, dynamic> json) {
    return VisitTagData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$VisitTagDataToJson(this);
  }

  factory VisitTagData.fromJson(Map<String, dynamic> json) =>
      _$VisitTagDataFromJson(json);
}
