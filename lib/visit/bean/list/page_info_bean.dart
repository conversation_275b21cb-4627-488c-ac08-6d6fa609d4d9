import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
import 'row_bean.dart';

part 'page_info_bean.g.dart';

@JsonSerializable()
class PageInfoBean extends BaseModel<PageInfoBean> {
  @JsonKey(name: "isLastPage")
  bool lastPage = false;
  @JsonKey(name: "list")
  List<RowBean>? rows;

  PageInfoBean();

  @override
  PageInfoBean fromJsonMap(Map<String, dynamic> json) {
    return PageInfoBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PageInfoBeanToJson(this);
  }

  factory PageInfoBean.fromJson(Map<String, dynamic> json) =>
      _$PageInfoBeanFromJson(json);
}
