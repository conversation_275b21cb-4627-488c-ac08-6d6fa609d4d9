// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'page_info_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PageInfoBean _$PageInfoBeanFromJson(Map<String, dynamic> json) {
  return PageInfoBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : PageInfoBean.fromJson(json['data'] as Map<String, dynamic>)
    ..lastPage = json['isLastPage'] as bool
    ..rows = (json['list'] as List<dynamic>?)
        ?.map((e) => RowBean.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$PageInfoBeanToJson(PageInfoBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'isLastPage': instance.lastPage,
      'list': instance.rows,
    };
