// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'row_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

RowBean _$RowBeanFromJson(Map<String, dynamic> json) {
  return RowBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : RowBean.fromJson(json['data'] as Map<String, dynamic>)
    ..id = json['id'] as int?
    ..scheduleTheme = json['scheduleTheme'] as String?
    ..contactor = json['contactor'] as String?
    ..type = json['visitType'] as int?
    ..creatorName = json['creatorName'] as String?
    ..mobile = json['mobile'] as String?
    ..effective = json['effective'] as bool?
    ..createTime = json['createTime'] as int?
    ..timeFormat = json['timeFormat'] as String?
    ..typeText = json['typeText'] as String?
    ..visitReasonText = json['visitReasonText'] as String?
    ..remark = json['remark'] as String?
    ..perfect = json['perfect'] as int?
    ..merchantName = json['merchantName'] as String?
    ..merchantId = json['merchantId'] as int?
    ..customerId = json['customerId'] as int?
    ..talkTimeText = json['talkTimeText'] as String?
    ..talkTime = json['talkTime'] as int?
    ..isEffective = json['isEffective'] as int?
    ..poiId = json['poiId'] as int?
    ..registerFlag = json['registerFlag']
    ..visitType = json['visitType'] as int?
    ..visitTypeText = json['visitTypeText'] as String?
    ..visitDemo = json['visitDemo'] as String?;
}

Map<String, dynamic> _$RowBeanToJson(RowBean instance) => <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'id': instance.id,
      'scheduleTheme': instance.scheduleTheme,
      'contactor': instance.contactor,
      'type': instance.type,
      'creatorName': instance.creatorName,
      'mobile': instance.mobile,
      'effective': instance.effective,
      'createTime': instance.createTime,
      'timeFormat': instance.timeFormat,
      'typeText': instance.typeText,
      'visitReasonText': instance.visitReasonText,
      'remark': instance.remark,
      'perfect': instance.perfect,
      'merchantName': instance.merchantName,
      'merchantId': instance.merchantId,
      'customerId': instance.customerId,
      'talkTimeText': instance.talkTimeText,
      'talkTime': instance.talkTime,
      'isEffective': instance.isEffective,
      'poiId': instance.poiId,
      'registerFlag': instance.registerFlag,
      'visitType': instance.visitType,
      'visitTypeText': instance.visitTypeText,
      'visitDemo': instance.visitDemo,
    };
