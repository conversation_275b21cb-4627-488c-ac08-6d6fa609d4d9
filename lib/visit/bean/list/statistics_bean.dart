import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'statistics_bean.g.dart';

@JsonSerializable()
class StatisticsBean extends BaseModel<StatisticsBean> {
  int cnt = 0; //拜访总数
  int effectiveCnt = 0; //有效拜访总数
  int doorCnt = 0; //	上门拜访总数	number
  int effDoorCnt = 0; //有效上门拜访数	number
  int effPhoneCnt = 0; //	有效电话拜访数	number
  int phoneCnt = 0; //电话拜访总数	number

  StatisticsBean();

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return StatisticsBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$StatisticsBeanToJson(this);
  }

  factory StatisticsBean.fromJson(Map<String, dynamic> json) =>
      _$StatisticsBeanFromJson(json);
}
