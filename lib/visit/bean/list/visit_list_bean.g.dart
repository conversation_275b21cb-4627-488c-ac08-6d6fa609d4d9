// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'visit_list_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VisitListBean _$VisitListBeanFromJson(Map<String, dynamic> json) {
  return VisitListBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : VisitListBean.fromJson(json['data'] as Map<String, dynamic>)
    ..statistics = json['statistics'] == null
        ? null
        : StatisticsBean.fromJson(json['statistics'] as Map<String, dynamic>)
    ..pageInfo = json['pageInfo'] == null
        ? null
        : PageInfoBean.fromJson(json['pageInfo'] as Map<String, dynamic>);
}

Map<String, dynamic> _$VisitListBeanToJson(VisitListBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'statistics': instance.statistics,
      'pageInfo': instance.pageInfo,
    };
