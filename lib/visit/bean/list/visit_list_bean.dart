import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

import 'page_info_bean.dart';
import 'statistics_bean.dart';

part 'visit_list_bean.g.dart';

@JsonSerializable()
class VisitListBean extends BaseModel<VisitListBean> {
  StatisticsBean? statistics;
  PageInfoBean? pageInfo;

  VisitListBean();

  @override
  VisitListBean fromJsonMap(Map<String, dynamic> json) {
    return VisitListBean.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$VisitListBeanToJson(this);
  }

  factory VisitListBean.fromJson(Map<String, dynamic> json) =>
      _$VisitListBeanFromJson(json);
}
