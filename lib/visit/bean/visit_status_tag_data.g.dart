// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'visit_status_tag_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

VisitStatusTagData _$VisitStatusTagDataFromJson(Map<String, dynamic> json) {
  return VisitStatusTagData()
    ..dataTimeList = (json['dataTimeList'] as List<dynamic>?)
        ?.map((e) => VisitTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..ifEffectiveList = (json['ifEffectiveList'] as List<dynamic>?)
        ?.map((e) => VisitTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..scheduleTypeList = (json['scheduleTypeList'] as List<dynamic>?)
        ?.map((e) => VisitTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..visitReasonList = (json['visitReasonList'] as List<dynamic>?)
        ?.map((e) => VisitTagData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..perfectList = (json['perfectList'] as List<dynamic>?)
        ?.map((e) => VisitTagData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$VisitStatusTagDataToJson(VisitStatusTagData instance) =>
    <String, dynamic>{
      'dataTimeList': instance.dataTimeList,
      'ifEffectiveList': instance.ifEffectiveList,
      'scheduleTypeList': instance.scheduleTypeList,
      'visitReasonList': instance.visitReasonList,
      'perfectList': instance.perfectList,
    };
