import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:XYYContainer/XYYContainer.dart';

class CounterWidget extends StatefulWidget {
  final int initialValue;
  final int minValue;
  final int maxValue;
  final ValueChanged<int>? onChanged;

  const CounterWidget({
    Key? key,
    this.initialValue = 0,
    this.minValue = 0,
    this.maxValue = 10000000,
    this.onChanged,
  }) : super(key: key);

  @override
  _CounterWidgetState createState() => _CounterWidgetState();
}

class _CounterWidgetState extends State<CounterWidget> {
  late int _count;
  late FocusNode _focusNode;
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _count = widget.initialValue;
    _controller = TextEditingController(text: '$_count');
    _focusNode = FocusNode();
  }

  @override
  void didUpdateWidget(CounterWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 当外部传入的initialValue发生变化时，更新内部状态
    if (oldWidget.initialValue != widget.initialValue) {
      // 确保新值在最小值和最大值之间
      int newValue = widget.initialValue;
      if (newValue < widget.minValue) {
        newValue = widget.minValue;
      } else if (newValue > widget.maxValue) {
        newValue = widget.maxValue;
      }

      setState(() {
        if (_count != newValue) {
          _count = newValue;
          _controller.text = '$_count';
          final text = _controller.text;
          _controller.selection = TextSelection.fromPosition(
            TextPosition(offset: text.length),
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _focusNode.dispose();
    _controller.dispose();
    super.dispose();
  }

  void _increment() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
      _handleSubmitted(_controller.text);
      return;
    }

    if (_count < widget.maxValue) {
      setState(() {
        _count++;
        _controller.text = '$_count';
      });
      widget.onChanged?.call(_count);
    } else {
      XYYContainer.toastChannel.toast("超过可退款的最大商品数量");
    }
  }

  void _decrement() {
    if (_focusNode.hasFocus) {
      _focusNode.unfocus();
      _handleSubmitted(_controller.text);
      return;
    }
    if (_count > widget.minValue) {
      setState(() {
        _count--;
        _controller.text = '$_count';
      });
      widget.onChanged?.call(_count);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildButton("-", _decrement),
          SizedBox(width: 1),
          _buildCounter(),
          SizedBox(width: 1),
          _buildButton("+", _increment),
        ],
      ),
    );
  }

  Widget _buildButton(String symbol, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        height: 26,
        width: 26,
        alignment: Alignment.center,
        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 0),
        decoration: BoxDecoration(
          color: const Color(0xFFF3F5F7),
        ),
        child: Text(
          symbol,
          style: const TextStyle(
            fontSize: 14,
            color: Color(0xFF676773),
            fontWeight: FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildCounter() {
    return Container(
      height: 26,
      width: 46,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: const Color(0xFFF3F5F7),
      ),
      padding: const EdgeInsets.symmetric(horizontal: 1, vertical: 0),
      child: TextField(
        focusNode: _focusNode,
        controller: _controller,
        keyboardType: TextInputType.number,
        textAlign: TextAlign.center,
        maxLines: 1,
        style: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.normal,
          color: Color(0xFF111334),
        ),
        decoration: const InputDecoration(
          isCollapsed: true,
          contentPadding: EdgeInsets.zero,
          border: InputBorder.none,
        ),
        onSubmitted: _handleSubmitted,
        onChanged: _handleSubmitted,
        inputFormatters: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
        ],
      ),
    );
  }

  void setInputValue(int value) {
    final int? newValue = value;
    if (newValue != null) {
      _count = newValue;
    } else {
      _count = 0;
    }
    widget.onChanged?.call(_count);
    _controller.text = '$_count';
    final text = _controller.text;
    _controller.selection = TextSelection.fromPosition(
      TextPosition(offset: text.length),
    );
  }

  void _handleSubmitted(String value) {
    final int? newValue = int.tryParse(value);
    if (newValue != null) {
      if (newValue > widget.maxValue) {
        setState(() {
          setInputValue(widget.maxValue);
        });
        XYYContainer.toastChannel.toast("超过可退款的最大商品数量");
      } else if (newValue < widget.minValue) {
        setState(() {
          setInputValue(widget.minValue);
        });
      } else {
        setState(() {
          setInputValue(newValue);
        });
      }
    } else {
      _count = 0;
    }
  }
}
