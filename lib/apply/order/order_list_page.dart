import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'order_list_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'dart:io';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:XyyBeanSproutsFlutter/apply/custom_checkbox.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/order_info_row_model.dart';

class OrderListPage extends BasePage {
  Map<String, int>? selectedCountMap = {};
  String? preKeywordSave;

  OrderListPage(this.selectedCountMap, this.preKeywordSave);

  @override
  BaseState<StatefulWidget> initState() {
    return OrderManagePageState();
  }
}

class OrderManagePageState extends BaseState<OrderListPage> {
  bool? isRecord;
  late OrderListModel _listModel;
  var _refreshController = EasyRefreshController();
  PageStateWidget? pageStateWidget;

  @override
  void initState() {
    super.initState();
  }

  @override
  void onDestroy() {
    super.onDestroy();
    _listModel.dispose();
    _refreshController.dispose();
  }

  @override
  void onCreate() {
    pageStateWidget = new PageStateWidget();
    _listModel = OrderListModel(_refreshController);
    _listModel.requestSmallImageHost();
    _listModel.preRedoundCountSave = widget.selectedCountMap ?? {};
    _listModel.keyword = widget.preKeywordSave;
    (widget.selectedCountMap ?? {}).forEach((key, value) {
      _listModel.selectedMap[key] = true;
    });
    if (_listModel.keyword != null) {
      _listModel.requestListModel();
    }
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<OrderListModel>(create: (context) => _listModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildHeaderView(),
          Expanded(
            child: Container(
              color: Color(0xffefeff4),
              child: buildListView(),
            ),
          ),
          Container(
            color: Colors.white,
            padding: EdgeInsets.only(left: 15, top: 10, right: 15, bottom: 10),
            alignment: Alignment.centerRight,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Consumer<OrderListModel>(
                      builder: (context, model, _) {
                        return InkWell(
                            onTap: () {
                              model.toggleSelectAll(!model.isAllSelected);
                            },
                            child: Row(
                              children: [
                                CustomCheckbox(
                                  value: model.isAllSelected,
                                  onChanged: (value) {
                                    model.toggleSelectAll(value);
                                  },
                                ),
                                SizedBox(width: 10),
                                Text(model.isAllSelected ? "取消全选" : "全选")
                              ],
                            ));
                      },
                    ),
                    Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.baseline, // 关键属性
                        textBaseline: TextBaseline.alphabetic,
                        children: [
                          Text('已选择商品数：'),
                          Consumer<OrderListModel>(
                            builder: (context, model, _) {
                              return Text(
                                '${model.selectedCount}',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold, // 标准加粗
                                ),
                              );
                            },
                          ),
                          SizedBox(width: 10),
                          TextButton(
                            onPressed: () {
                              onBackPress(context, true);
                            },
                            style: ButtonStyle(
                              backgroundColor: MaterialStateProperty.all<Color>(
                                  Color(0xFF00B377)),
                              foregroundColor: MaterialStateProperty.all<Color>(
                                  Colors.white),
                              padding: MaterialStateProperty.all<EdgeInsets>(
                                EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 12),
                              ),
                              shape: MaterialStateProperty.all<
                                  RoundedRectangleBorder>(
                                RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(4),
                                ),
                              ),
                            ),
                            child: Text('确定'),
                          )
                        ])
                  ],
                ),
                Consumer<OrderListModel>(
                  builder: (context, model, _) {
                    return TextButton(
                      onPressed: () {
                        showCupertinoDialog(
                          context: this.context,
                          builder: (BuildContext context) {
                            return CupertinoAlertDialog(
                              title: Text("确定是否整单退款？"),
                              actions: [
                                CupertinoDialogAction(
                                  child: Text("取消"),
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                  },
                                ),
                                CupertinoDialogAction(
                                  child: Text("确定"),
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                    model.toggleSelectAll(true,
                                        isWholeOrder: true);
                                    // Timer(const Duration(milliseconds: 200),
                                    //     () {
                                    //   onBackPress(context, true);
                                    // });
                                  },
                                ),
                              ],
                            );
                          },
                        );
                      },
                      style: ButtonStyle(
                        backgroundColor:
                            MaterialStateProperty.all<Color>(Color(0xFF00B377)),
                        foregroundColor:
                            MaterialStateProperty.all<Color>(Colors.white),
                        padding: MaterialStateProperty.all<EdgeInsets>(
                          EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                        ),
                        shape:
                            MaterialStateProperty.all<RoundedRectangleBorder>(
                          RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                      ),
                      child: Text('整单退款'),
                    );
                  },
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  buildHeaderView() {
    return Container(
        child: SAppBarSearch(
      hintText: "请输入订单编号进行搜索",
      showLeading: false,
      hideCancel: true,
      autoFocus: true,
      value: widget.preKeywordSave,
      onSearch: (value) {
        if (value.isEmpty) {
          showToast("请输入搜索内容");
          return;
        }
        _listModel.keyword = value;
        _listModel.requestListModel();
      },
    ));
  }

  buildListView() {
    return Consumer<OrderListModel>(builder: (context, model, child) {
      var emptyWidget = getEmptyWidget();
      return Container(
        color: emptyWidget == null ? Colors.transparent : Colors.white,
        child: EasyRefresh.custom(
            controller: _refreshController,
            enableControlFinishRefresh: true,
            enableControlFinishLoad: true,
            onRefresh: () async {
              _listModel.requestListModel();
            },
            onLoad: _listModel.list?.isNotEmpty == true
                ? () async {
                    _listModel.requestListModel();
                  }
                : null,
            slivers: [
              SliverPadding(padding: EdgeInsets.only(top: 5)),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                    //创建列表项
                    return buildOrderItem(index);
                  },
                  childCount: _listModel.list?.length ?? 0,
                ),
              ),
              SliverPadding(
                padding: EdgeInsets.only(top: 5),
              ),
            ],
            emptyWidget: emptyWidget),
      );
    });
  }

  @override
  String getTitleName() {
    return "订单列表";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      onBackPress(context, false);
    });
  }

  void onBackPress(BuildContext context, bool? isConfirm) {
    try {
      FocusScope.of(context).unfocus();
    } catch (err) {}
    List<ApplyOrderListItemData> returnList = [];
    if (isConfirm == true) {
      if (_listModel.list != null && _listModel.list!.isNotEmpty) {
        _listModel.selectedMap.forEach((key, value) {
          for (int i = 0; i < _listModel.list!.length; i++) {
            var item = _listModel.list![i];
            if (value && item.skuId.toString() == key) {
              returnList.add(item);
              break;
            }
          }
        });
      }
      if (returnList.isEmpty) {
        XYYContainer.toastChannel.toast("请至少选择一个商品！");
        return;
      } else {
        for (int i = 0; i < returnList.length; i++) {
          var item = returnList[i];
          if ((item.productNum ?? 0) == 0) {
            XYYContainer.toastChannel.toast("当前发起退款商品数量为0，请选择数量后再提交哦～");
            return;
          }
        }
      }
    }

    int isWholeOrder = 1;
    if (_listModel.isAllSelected && _listModel.list != null) {
      final items = _listModel.list ?? [];
      final listLength = items.length;
      for (int i = 0; i < listLength; i++) {
        if (items[i].productNum != items[i].productAmount) {
          isWholeOrder = 0;
          break;
        }
      }
    } else {
      isWholeOrder = 0;
    }

    if (Navigator.canPop(context)) {
      Navigator.maybePop(
          context,
          ReturnSelectInfoData(
              returnList: returnList,
              keyword: _listModel.keyword,
              isWholeOrder: isWholeOrder));
    } else {
      if (Platform.isIOS) {
        XYYContainer.bridgeCall("app_back");
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }

  buildOrderItem(int index) {
    if (_listModel.list == null || index >= (_listModel.list?.length ?? 0)) {
      return Container();
    }
    var itemData = _listModel.list![index];

    return Stack(
      children: [
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () {
                  try {
                    FocusScope.of(context).unfocus();
                  } catch (err) {}
                  // bool value =
                  //     _listModel.selectedMap[itemData.skuId.toString()] ??
                  //         false;
                  // _listModel.toggleSelected(itemData.skuId.toString(), !value);
                },
                child: ApplyOrderListItemWidget(
                  _listModel.smallImageHost,
                  itemData,
                  () {
                    setState(() {
                      _listModel.setSelectedCount();
                    });
                  },
                  key: ValueKey(itemData.orderNo),
                ),
              ),
            ),
          ],
        ),
        Positioned(
          top: 16,
          left: 25,
          child: CustomCheckbox(
            value: _listModel.isSelected(itemData.skuId.toString()),
            onChanged: (bool? value) {
              _listModel.toggleSelected(itemData.skuId.toString(), value);
            },
          ),
        ),
      ],
    );
  }

  Widget? getEmptyWidget() {
    if (_listModel.isSuccess == null) {
      return null;
    }
    if (_listModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {});
    }
    if ((_listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }
}

class OrderListModel extends ChangeNotifier {
  var _isDisposed = false;
  List<ApplyOrderListItemData>? list;
  String? smallImageHost;
  String? keyword;
  bool? isSuccess;

  Map<String, bool> selectedMap = {};

  Map<String, int> preRedoundCountSave = {};

  int selectedCount = 0;

  final EasyRefreshController _refreshController;

  OrderListModel(this._refreshController);

  requestSmallImageHost() {
    if (smallImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          smallImageHost = value["image_host"];
          notifyListeners();
        }
      });
    }
  }

  void requestNormalList(
      Map<String, String?> paramsMap, List<ApplyOrderListItemData> oldList) {
    NetworkV2<ApplyOrderListItemData>(ApplyOrderListItemData())
        .requestDataV2("worries/order_product",
            method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      value.getListData()?.forEach((item) {});
      handleResult(value.isSuccess, value.getListData(), oldList);
    });
  }

  void toggleSelected(String skuIdString, bool? selected) {
    if (skuIdString.isNotEmpty) {
      selectedMap[skuIdString] = selected ?? false;
      notifyListeners();
      setSelectedCount();
    }
  }

  bool isSelected(String skuIdString) {
    return selectedMap[skuIdString] ?? false;
  }

  bool get isAllSelected {
    if (list == null || list!.isEmpty) return false;
    return list!.every((item) => selectedMap[item.skuId.toString()] == true);
  }

  void toggleSelectAll(bool selectAll, {bool isWholeOrder = false}) {
    if (list != null) {
      for (var item in list!) {
        if (isWholeOrder) {
          item.productNum = item.productAmount ?? 1;
        }
        if (item.skuId != null) {
          selectedMap[item.skuId.toString()] = selectAll;
        }
      }
      notifyListeners();
      setSelectedCount();
    }
  }

  requestListModel() async {
    final List<ApplyOrderListItemData> oldList = List.from(list ?? []);
    list?.clear();
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    var paramsMap = buildParamsMap();
    requestNormalList(paramsMap, oldList);
  }

  void setSelectedCount() {
    int newCount = 0;
    selectedMap.forEach((key, value) {
      if (value == true) {
        var tempList = list ?? [];
        int length = tempList.length;
        for (int i = 0; i < length; i++) {
          if (tempList[i].skuId.toString() == key) {
            newCount += (tempList[i].productNum ?? 0);
            break;
          }
        }
      }
    });
    selectedCount = newCount;
  }

  void handleResult(bool? isSuccess, List<ApplyOrderListItemData>? data,
      List<ApplyOrderListItemData> oldData) {
    EasyLoading.dismiss();
    if (!_isDisposed && (isSuccess ?? false)) {
      this.isSuccess = isSuccess;
      if (data != null) {
        try {
          FocusManager.instance.primaryFocus?.unfocus();
        } catch (err) {}
        list = data;
        // 选中商品回显
        final oldSeMap = Map<String, bool>.from(selectedMap);
        selectedMap.clear();
        oldSeMap.forEach((key, value) {
          if (value) {
            toggleSelected(key, value);
            if ((preRedoundCountSave[key.toString()] ?? 0) == 0) {
              for (var item in oldData) {
                if (item.skuId.toString() == key) {
                  preRedoundCountSave[key.toString()] = item.productNum ?? 0;
                  break;
                }
              }
            }
          }
        });
        //选中商品的退款数量回显
        final afterResetCountMap = Map<String, int>.from(preRedoundCountSave);
        if (preRedoundCountSave.isNotEmpty && (list?.isNotEmpty ?? false)) {
          preRedoundCountSave.forEach((key, value) {
            for (int i = 0; i < list!.length; i++) {
              if (list?[i].skuId.toString() == key) {
                list?[i].productNum = value;
                afterResetCountMap.remove(key);
                break;
              }
            }
          });
        }
        preRedoundCountSave.clear();
        preRedoundCountSave = Map<String, int>.from(afterResetCountMap);
      }
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: true);
      notifyListeners();
      setSelectedCount();
    } else {
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: false);
    }
  }

  Map<String, String?> buildParamsMap() {
    var params = Map<String, String?>();
    params["orderNo"] = keyword;
    return params;
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
