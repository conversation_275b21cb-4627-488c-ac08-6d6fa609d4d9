import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:flutter/material.dart';
import "package:intl/intl.dart";
import './counter_widget.dart';

class ApplyOrderListItemWidget extends StatefulWidget {
  final ApplyOrderListItemData itemData;
  final String? imageHost;
  final Function? onValueChanged;

  ApplyOrderListItemWidget(this.imageHost, this.itemData, this.onValueChanged,
      {Key? key})
      : super(key: key);

  @override
  _ApplyOrderListItemWidgetState createState() =>
      _ApplyOrderListItemWidgetState();
}

class _ApplyOrderListItemWidgetState extends State<ApplyOrderListItemWidget> {
  @override
  Widget build(BuildContext context) {
    var itemData = widget.itemData;
    var moneyText = formatMoneyText(itemData);

    return Container(
      constraints: BoxConstraints(minHeight: 151),
      width: double.infinity,
      margin: EdgeInsets.fromLTRB(15, 5, 15, 5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(2),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 顶部栏
          SizedBox(
            height: 40,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 32),
                Expanded(
                  child: Text(
                    "订单编号：${itemData.orderNo ?? '--'}",
                    style: TextStyle(
                      color: Color(0xFF676773),
                      fontWeight: FontWeight.normal,
                      fontSize: 12,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 10)
              ],
            ),
          ),
          Divider(height: 1, color: Color(0xFFF6F6F6)),
          SizedBox(height: 10),

          // 内容区域
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(width: 10),
              SizedBox(
                width: 90,
                height: 90,
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    ImageWidget(
                      url: "${widget.imageHost}${itemData.imageUrl}",
                      w: 90,
                      h: 90,
                      defImagePath: "assets/images/order/icon_load_failed.png",
                    ),
                    Container(color: Color(0x47D8D8D8)),
                    Positioned(
                        right: 0,
                        bottom: 0,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 8.0, vertical: 4.0),
                          decoration: BoxDecoration(
                            color: Color(0x99000000),
                            borderRadius: BorderRadius.circular(4), // 设置所有角为圆角
                          ),
                          child: Text(
                            "x${itemData.productAmount ?? 0}",
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 11,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                        ))
                  ],
                ),
              ),
              SizedBox(width: 10),
              Expanded(
                child: Container(
                  constraints: BoxConstraints(minHeight: 85),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        itemData.productName ?? "",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: TextStyle(
                          color: Color(0xFF292933),
                          fontSize: 15,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: 5),
                      Text(
                        "下单时间:  ${itemData.createTimeDesc ?? '--'}",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                          color: Color(0xFF676773),
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                      // SizedBox(height: 5),
                      // Text(
                      //   "共${itemData.createTime ?? '0'}种商品",
                      //   overflow: TextOverflow.ellipsis,
                      //   maxLines: 1,
                      //   style: TextStyle(
                      //     color: Color(0xFF676773),
                      //     fontSize: 12,
                      //     fontWeight: FontWeight.normal,
                      //   ),
                      // ),
                      SizedBox(height: 5),
                      Text(
                        "总额:  ¥${itemData.subTotal ?? '-'}",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                          color: Color(0xFF676773),
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(width: 10),
            ],
          ),
          SizedBox(height: 10),
          Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        "可退金额：",
                        style: TextStyle(
                          color: Color(0xFF333333),
                          fontSize: 12,
                          fontWeight: FontWeight.normal,
                        ),
                      ),
                      Text.rich(TextSpan(
                        text: "¥",
                        style: TextStyle(
                          color: Color(0xFF292933),
                          fontSize: 13,
                          fontWeight: FontWeight.w700,
                        ),
                        children: [
                          TextSpan(
                            text:
                                moneyText.substring(0, moneyText.indexOf(".")),
                            style: TextStyle(
                              color: Color(0xFF292933),
                              fontSize: 16,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                          TextSpan(
                            text: moneyText.substring(moneyText.indexOf(".")),
                            style: TextStyle(
                              color: Color(0xFF292933),
                              fontSize: 12,
                              fontWeight: FontWeight.w700,
                            ),
                          ),
                        ],
                      )),
                      SizedBox(width: 6),
                    ],
                  ),
                  Row(
                      // crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "退款数量：",
                          style: TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 12,
                            fontWeight: FontWeight.normal,
                          ),
                        ),
                        CounterWidget(
                          initialValue: itemData.productNum ?? 0,
                          maxValue: itemData.productAmount ?? 0,
                          minValue: 0,
                          onChanged: (value) {
                            setState(() {
                              itemData.productNum = value;
                              if (widget.onValueChanged != null) {
                                widget.onValueChanged!();
                              }
                            });
                          },
                        ),
                      ]),
                ],
              )),
          SizedBox(height: 10),
        ],
      ),
    );
  }

  String formatMoneyText(ApplyOrderListItemData info) {
    double amount = (info.purchasePrice ?? 0) * (info.productNum ?? 0);
    return NumberFormat("0.00").format(amount);
  }
}
