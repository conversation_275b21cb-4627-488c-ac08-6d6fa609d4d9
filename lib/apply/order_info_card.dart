import 'package:flutter/material.dart';
import "package:intl/intl.dart";
import 'package:XYYContainer/XYYContainer.dart';
import 'package:flutter/services.dart';
import 'package:flutter/cupertino.dart';
import 'package:XyyBeanSproutsFlutter/apply/custom-text-input-with-counter.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/order_info_row_model.dart';
import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';

class OrderInfoCard extends StatefulWidget {
  final ApplyOrderInfoModel orderInfoRowModel;
  final Function(ApplyOrderInfoModel)? onValueChanged; // 定义回调函数

  const OrderInfoCard(
      {Key? key, required this.orderInfoRowModel, this.onValueChanged})
      : super(key: key);

  @override
  _OrderInfoCardState createState() => _OrderInfoCardState();
}

class _OrderInfoCardState extends State<OrderInfoCard> {
  late ApplyOrderInfoModel _orderInfo;
  String? preKeywordSave;
  int? isWholeOrder;
  final TextEditingController _controller = TextEditingController();
  List<ApplyOptionItem> _searchResult = [];

  @override
  void initState() {
    _orderInfo = widget.orderInfoRowModel;
    requestSmallImageHost();
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  String? smallImageHost;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      margin: EdgeInsets.only(top: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          _buildHeader(context),
          _drugInfoCardList(context),
          _buildInfoRow(context, _orderInfo.hasBeenReturned),
          _buildInfoRow(context, _orderInfo.refundType),
          _buildInfoRow(context, _orderInfo.customerType),
          _buildInfoRow(context, _orderInfo.orderType),
          _buildInfoRow(context, _orderInfo.businessProvinceRegion),
          _buildInfoRow(context, _orderInfo.shippingProvinceRegion),
          _buildInfoRow(context, _orderInfo.reasonForRefund),
          Visibility(
              visible: _orderInfo.list?.isNotEmpty ?? false,
              child: _buildInputRow(context)),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
        color: Colors.white,
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Container(
              alignment: Alignment.centerRight,
              child: Text(
                '订单信息',
                style: TextStyle(
                  fontFamily: 'PingFangSC-Regular',
                  fontWeight: FontWeight.w400,
                  fontSize: 18,
                  height: 1,
                  color: Color(0xFF111334),
                ),
                textAlign: TextAlign.right,
              ),
            ),
            TextButton(
              onPressed: () async {
                Map<String, int> selectedCountMap = {};
                (_orderInfo.list ?? []).forEach((e) {
                  if (e.skuId != null) {
                    selectedCountMap['${e.skuId}'] = e.productNum ?? 0;
                  }
                });

                var result = await Navigator.pushNamed(
                    context, "/apply_order_list_page",
                    arguments: {
                      'merchantId': '1433223',
                      'selectedCountMap': selectedCountMap,
                      'preKeywordSave': preKeywordSave
                    });
                if (result is ReturnSelectInfoData) {
                  if (result.returnList != null &&
                      result.returnList is List<ApplyOrderListItemData> &&
                      (result.returnList as List).isNotEmpty) {
                    _orderInfo.list =
                        result.returnList as List<ApplyOrderListItemData>;
                    _orderInfo.isWholeOrder = result.isWholeOrder ?? 0;
                  }
                  preKeywordSave = result.keyword;
                  setState(() {
                    if (widget.onValueChanged != null) {
                      widget.onValueChanged!(_orderInfo);
                    }
                  });
                } else {
                  print("没有返回数据，或者类型不对");
                }
              },
              style: ElevatedButton.styleFrom(
                primary: Colors.white,
                padding: EdgeInsets.only(top: 20, bottom: 20, left: 40),
                // 去除内边距
                elevation: 0,
                // 去除阴影
                shadowColor: Colors.transparent,
                // 阴影颜色设为透明
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.zero, // 去除圆角
                  side: BorderSide.none, // 去除边框
                ),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                // 紧缩点击区域
                minimumSize: Size(0, 0), // 最小尺寸为 0
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.end,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    "搜索",
                    style: TextStyle(color: Color(0xFF9494A6), fontSize: 12.0),
                  ),
                  SizedBox(width: 3),
                  Image.asset(
                    'assets/images/item_arrow.png',
                    width: 10,
                    height: 10,
                  ),
                ],
              ),
            ),
          ],
        ));
  }

  Widget _drugInfoCardList(BuildContext context) {
    return Visibility(
        visible: _orderInfo.list?.isNotEmpty ?? false,
        child: Container(
            color: Colors.white,
            padding: EdgeInsets.only(
              left: 20, // 左边距
              top: 0, // 上边距
              right: 20, // 右边距
              bottom: 20, // 下边距
            ),
            // margin: EdgeInsets.only(top: 20),
            child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey[200],
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.all(0),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      ...(_orderInfo.list ?? [])
                          .map((item) => _drugInfoCard(context, item))
                          .toList()
                    ]))));
  }

  Widget _drugInfoCard(BuildContext context, ApplyOrderListItemData _info) {
    return Container(
        padding: EdgeInsets.only(
          left: 20, // 左边距
          top: 20, // 上边距
          right: 20, // 右边距
          bottom: 20, // 下边距
        ),
        // margin: EdgeInsets.only(top: 20),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Container(
                height: 90,
                width: 90,
                margin: EdgeInsets.only(right: 20),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(2),
                ),
                child: Stack(children: [
                  ImageWidget(
                    url: joinUrls(
                        smallImageHost.toString(), _info.imageUrl ?? ""),
                    w: 90,
                    h: 90,
                    defImagePath: "assets/images/order/icon_load_failed.png",
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: Color(0x99000000),
                        borderRadius: BorderRadius.circular(2),
                      ),
                      child: Text(
                        'x${_info.productAmount}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  ),
                ])),
            Expanded(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                        padding: EdgeInsets.only(top: 0),
                        child: Text('${_info.productName}',
                            style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFF333333),
                                fontWeight: FontWeight.bold))),
                    Padding(
                        padding: EdgeInsets.only(top: 6),
                        child: Text('${_info.spec}',
                            style: TextStyle(
                                fontSize: 12, color: Color(0xFF666666)))),
                    // Padding(
                    //     padding: EdgeInsets.only(top: 6),
                    //     child: Text(
                    //         '${_info.varietyNum}', style: TextStyle(
                    //         fontSize: 12,
                    //         color: Color(0xFF666666)
                    //     ))
                    // ),
                    Padding(
                        padding: EdgeInsets.only(top: 6),
                        child: Text('单价：${_info.productPrice}',
                            style: TextStyle(
                                fontSize: 12, color: Color(0xFF666666)))),
                  ]),
            ),
          ]),
          Container(
              margin: EdgeInsets.only(top: 10),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '退款金额',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF666666),
                      ),
                    ),
                    Text(
                      _computeRefundAmount(_info),
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF333333),
                      ),
                    )
                  ])),
          Container(
              margin: EdgeInsets.only(top: 10),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '退款商品数量',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF666666),
                      ),
                    ),
                    Text(
                      '${_info.productNum}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF333333),
                      ),
                    )
                  ])),
          Container(
              margin: EdgeInsets.only(top: 10),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '下单时间',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF666666),
                      ),
                    ),
                    Text(
                      '${_info.createTimeDesc}',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF333333),
                      ),
                    )
                  ])),
          Container(
              margin: EdgeInsets.only(top: 10),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '订单号',
                      style: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF666666),
                      ),
                    ),
                    Row(children: [
                      Text(
                        '${_info.orderNo}',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFF333333),
                        ),
                      ),
                      Visibility(
                        visible: true,
                        child: Container(
                          child: Container(
                            margin: EdgeInsets.only(left: 10),
                            padding: EdgeInsets.fromLTRB(3, 1, 3, 1),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(1),
                              color: Colors.white,
                              border: Border.all(
                                  color: Color(0xFFBFBFBF), width: 0.5),
                            ),
                            child: GestureDetector(
                              onTap: () {
                                Clipboard.setData(
                                    ClipboardData(text: '${_info.orderNo}'));
                                XYYContainer.toastChannel.toast("订单号复制成功");
                              },
                              child: Text(
                                "复制",
                                style: TextStyle(
                                  color: Color(0xFF333333),
                                  fontSize: 11,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ])
                  ])),
        ]));
  }

  String _computeRefundAmount(ApplyOrderListItemData info) {
    double amount = (info.purchasePrice ?? 0) * (info.productNum ?? 0);
    return NumberFormat("0.00").format(amount);
  }

  Widget _buildInfoRow(BuildContext context, ApplyOrderRowModel rowInfo) {
    return Visibility(
        visible: _orderInfo.list?.isNotEmpty ?? false,
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
          child: Row(
            children: [
              SizedBox(
                width: 100,
                child: RichText(
                  text: TextSpan(
                    style: DefaultTextStyle.of(context).style.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.black87,
                          fontSize: 14,
                        ),
                    text: rowInfo.name,
                    children: const [
                      TextSpan(
                        text: ' *',
                        style: TextStyle(color: Colors.red, fontSize: 14),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                child: Align(
                  alignment: Alignment.centerRight,
                  child: ElevatedButton(
                    onPressed: () {
                      if (!(rowInfo.disable ?? false)) {
                        _showBottomDrawer(context, rowInfo);
                      }
                    },
                    style: ElevatedButton.styleFrom(
                      primary: Colors.white,
                      padding: EdgeInsets.only(top: 20, bottom: 20, left: 40),
                      // 去除内边距
                      elevation: 0,
                      // 去除阴影
                      shadowColor: Colors.transparent,
                      // 阴影颜色设为透明
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.zero, // 去除圆角
                        side: BorderSide.none, // 去除边框
                      ),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      // 紧缩点击区域
                      minimumSize: Size(0, 0), // 最小尺寸为 0
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min, // 让按钮大小包住内容
                      children: [
                        Text(rowInfo.valueText ?? '请选择',
                            textAlign: TextAlign.right,
                            style: TextStyle(
                                fontSize: 14, color: Color(0xFF9494A6))),
                        Visibility(
                            visible: !(rowInfo.disable ?? false),
                            child: Row(children: [
                              SizedBox(width: 8),
                              Image.asset(
                                'assets/images/item_arrow.png', // 你的图片路径
                                width: 10,
                                height: 10,
                              ),
                            ])),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ));
  }

  Widget _buildInputRow(BuildContext context) {
    return Container(
        color: Colors.white,
        padding: EdgeInsets.only(
          left: 20, // 左边距
          top: 36, // 上边距
          right: 20, // 右边距
          bottom: 32, // 下边距
        ),
        margin: EdgeInsets.only(top: 20),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          SizedBox(
            child: RichText(
              text: TextSpan(
                style: DefaultTextStyle.of(context).style.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.black87,
                      fontSize: 14,
                    ),
                text: '退款描述',
                children: const [
                  TextSpan(
                    text: ' *',
                    style: TextStyle(color: Colors.red, fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
          Container(
              padding: EdgeInsets.only(top: 16),
              child: CustomTextInputWithCounter(
                onChanged: (value) {
                  _orderInfo.refundDescription = value;
                },
              )),
        ]));
  }

  requestSmallImageHost() {
    if (smallImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          smallImageHost = value["image_host"];
          // notifyListeners();
        }
      });
    }
  }

  void _showBottomDrawer(
      BuildContext context, ApplyOrderRowModel rowInfo) async {
    if (rowInfo.search == true) {
      _controller.clear();
      _searchResult = rowInfo.options;
    }
    final result = await showModalBottomSheet<String>(
      context: context,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
      ),
      builder: (context) {
        // 临时变量存储当前选项
        return StatefulBuilder(
          builder: (context, setModalState) {
            return Container(
              padding: EdgeInsets.only(top: 32),
              height: MediaQuery.of(context).size.height * 0.7,
              child: Column(
                children: [
                  Text(rowInfo.drawerTitle),
                  if (rowInfo.search == true)
                    _buildSearchBar(context, rowInfo.options, setModalState),
                  Expanded(
                      child: ListView(children: [
                    ...(rowInfo.search == true
                            ? _searchResult
                            : rowInfo.options)
                        .map((option) {
                      return GestureDetector(
                          onTap: () {
                            setModalState(() {
                              rowInfo.value = option.value;
                              Future.delayed(const Duration(milliseconds: 200),
                                  () {
                                Navigator.pop(context, option.name);
                              });
                            });
                          },
                          child: Container(
                              padding: EdgeInsets.symmetric(horizontal: 16),
                              color: Colors.white,
                              child: Column(children: [
                                Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 16), // 设置内边距
                                  child: Container(
                                    child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(option.name,
                                              style: TextStyle(
                                                fontSize: 14,
                                                // 字体大小
                                                color: rowInfo.value ==
                                                        option.value
                                                    ? const Color(0xFF00B377)
                                                    : Colors.black,
                                              )),
                                          SizedBox(height: 20),
                                          Visibility(
                                              visible:
                                                  rowInfo.value == option.value,
                                              child: Image.asset(
                                                'assets/images/apply/checked.png',
                                                width: 20,
                                                height: 20,
                                              ))
                                        ]),
                                  ),
                                ),
                                Divider(height: 1, color: Color(0x9E000000)),
                              ])));
                    }).toList(),
                    if (rowInfo.search == true && _searchResult.isEmpty)
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 16),
                        child: Text(
                          '搜不到对应内容～～～',
                          textAlign: TextAlign.center,
                        ),
                      ),
                  ])),
                ],
              ),
            );
          },
        );
      },
    );

    // 更新父组件的状态
    if (result != null) {
      setState(() {
        rowInfo.valueText = result;
      });
    }
  }

  Widget _buildSearchBar(BuildContext context, List<ApplyOptionItem> options,
      void Function(void Function()) modalSetState) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          // 输入框（左侧扩展）
          Expanded(
            child: Container(
              height: 48,
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey, width: 1),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(4),
                  bottomLeft: Radius.circular(4),
                ),
                color: Colors.white,
              ),
              child: TextField(
                controller: _controller,
                decoration: InputDecoration(
                  hintText: '搜索关键词',
                  prefixIcon: Icon(Icons.search, color: Colors.grey),
                  suffixIcon: _controller.text.isEmpty
                      ? null
                      : IconButton(
                          icon: Icon(Icons.clear, size: 20, color: Colors.grey),
                          onPressed: () {
                            _controller.clear();
                            setState(() {});
                            _handleSearch(
                                context, options, false, modalSetState);
                          },
                        ),
                  border: InputBorder.none,
                  // 隐藏TextField自带边框
                  contentPadding: EdgeInsets.symmetric(vertical: 12),
                ),
                onChanged: (value) =>
                    _handleSearch(context, options, false, modalSetState),
                onSubmitted: (_) =>
                    _handleSearch(context, options, true, modalSetState),
              ),
            ),
          ),

          // 搜索按钮（右侧，无缝连接）
          Container(
            height: 48, // 与输入框同高
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey, width: 1),
              borderRadius: BorderRadius.only(
                  topRight: Radius.circular(4),
                  bottomRight: Radius.circular(4)),
              color: Colors.white,
            ),
            child: IconButton(
              icon: Icon(Icons.search, color: Colors.grey),
              onPressed: () {
                _handleSearch(context, options, true, modalSetState);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _handleSearch(
    BuildContext context,
    List<ApplyOptionItem> options,
    bool shouldUnFocus,
    void Function(void Function()) modalSetState,
  ) {
    List<ApplyOptionItem> result =
        searchSalesUnits(data: options, keyword: _controller.text);
    // 更新 modal 的状态
    modalSetState(() {
      _searchResult = result;
    });

    if (shouldUnFocus) {
      FocusScope.of(context).unfocus();
    }
  }

  List<ApplyOptionItem> searchSalesUnits({
    required List<ApplyOptionItem> data,
    required String keyword,
    bool caseSensitive = false,
  }) {
    return data
        .where(
          (item) => caseSensitive
              ? item.value.startsWith(keyword)
              : item.value.toLowerCase().startsWith(keyword.toLowerCase()),
        )
        .toList();
  }

  String joinUrls(String base, String path) {
    base = base.endsWith('/') ? base : '$base/';
    path = path.startsWith('/') ? path.substring(1) : path;
    return '$base$path';
  }
}
