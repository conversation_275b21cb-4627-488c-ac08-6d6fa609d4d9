import 'dart:io';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:XyyBeanSproutsFlutter/apply/basic_info_card.dart';
import 'package:XyyBeanSproutsFlutter/apply/order_info_card.dart';
import 'package:XyyBeanSproutsFlutter/apply/file_upload.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/order_info_row_model.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_request_model_data.dart';
import 'package:XyyBeanSproutsFlutter/apply/application_request_successfull.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class SuperWorryFreeApplication extends BasePage {
  SuperWorryFreeApplication();

  @override
  BaseState initState() {
    return _OrderDetailRefundCwyPageState();
  }
}

class _OrderDetailRefundCwyPageState extends BaseState {
  bool? isRecord;

  late ApplyOrderInfoModel _orderInfo;

  List<String> attachments = [];

  late ApplyBaseStatisticsInfoModel basicInfo = ApplyBaseStatisticsInfoModel();

  _OrderDetailRefundCwyPageState();

  @override
  void initState() {
    super.initState();
    _initialize();
  }

  void _initialize() {
    // 初始化逻辑
    _orderInfo = ApplyOrderInfoModel();
    _orderInfo.initHasBeenReturned();
    _orderInfo.initRefundType();
    _orderInfo.initReasonForRefund();
    _orderInfo.initCustomerType();
    _orderInfo.initOrderType();
    _orderInfo.initBusinessProvinceRegion();
    _orderInfo.initShippingProvinceRegion();
    this._requestOrderDetailData();
  }

  void _requestOrderDetailData() async {
    var result = await NetworkV2<ApplyBaseStatisticsInfoModel>(
            ApplyBaseStatisticsInfoModel())
        .requestDataV2(
      'worries/findUser',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    setState(() {
      basicInfo = ApplyBaseStatisticsInfoModel()
        ..userName = result.data.userName ?? ''
        ..staffNum = result.data.staffNum ?? ''
        ..jobName = result.data.jobName ?? ''
        ..deptPathName = result.data.deptPathName ?? '';
    });
  }

  @override
  Widget buildWidget(BuildContext context) {
    return SingleChildScrollView(
        child: Container(
      color: Color(0xFFF7F7F8),
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BasicInfoCard(basicInfo: basicInfo),
          OrderInfoCard(
              orderInfoRowModel: _orderInfo,
              onValueChanged: (ApplyOrderInfoModel value) {
                setState(() {
                  _orderInfo = value;
                });
              }),
          Visibility(
            visible: (_orderInfo.list?.length ?? 0) > 0,
            child: FileUpload(onValueChanged: (List<String> value) {
              attachments = value;
            }),
          ),
          Container(
            width: double.infinity,
            margin: EdgeInsets.only(left: 20, top: 20, right: 20, bottom: 20),
            child: TextButton(
              onPressed: () {
                checkAllData(() {
                  Navigator.of(context).pushReplacement(
                    MaterialPageRoute(
                        builder: (context) => ApplicationRequestSuccessfull()),
                  );
                });
              },
              style: ButtonStyle(
                backgroundColor: MaterialStateProperty.all<Color>(Colors.green),
                foregroundColor: MaterialStateProperty.all<Color>(Colors.white),
                padding: MaterialStateProperty.all<EdgeInsets>(
                  EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                ),
                shape: MaterialStateProperty.all<RoundedRectangleBorder>(
                  RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              child: Text('立即申请'),
            ),
          )
        ],
      ),
    ));
  }

  @override
  String getTitleName() {
    return "超无忧售后";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      onBackPress(context);
    }, rightButtons: [
      TextButton(
        onPressed: () {
          XYYContainer.open('/apply_history_order_manage_page');
        },
        style: TextButton.styleFrom(
          primary: Color(0xFF111334),
          // 设置字体颜色（2.0.2 用 primary，而非 foregroundColor）
          textStyle: TextStyle(
            fontSize: 14,
          ),
        ),
        child: Text('历史记录'),
      ),
    ]);
  }

  void checkAllData(Function? callBack) async {
    // print("最终校验-订单：${_orderInfo.list?.length ?? 0}");
    if ((_orderInfo.list?.length ?? 0) == 0) {
      XYYContainer.toastChannel.toast("请选择订单");
      return;
    }
    // print("最终校验-是否已退回：${_orderInfo.hasBeenReturned.value}");
    if (_orderInfo.hasBeenReturned.value == null) {
      XYYContainer.toastChannel.toast("请选择${_orderInfo.hasBeenReturned.name}");
      return;
    }
    // print("最终校验-退款类型：${_orderInfo.refundType.value}");
    if (_orderInfo.refundType.value == null) {
      XYYContainer.toastChannel.toast("请选择${_orderInfo.refundType.name}");
      return;
    }
    // print("最终校验-客户类型：${_orderInfo.customerType.value}");
    if (_orderInfo.customerType.value == null) {
      XYYContainer.toastChannel.toast("请选择${_orderInfo.customerType.name}");
      return;
    }
    // print("最终校验-订单类型：${_orderInfo.orderType.value}");
    if (_orderInfo.orderType.value == null) {
      XYYContainer.toastChannel.toast("请选择${_orderInfo.orderType.name}");
      return;
    }
    // print("最终校验-业务省区：${_orderInfo.businessProvinceRegion.value}");
    if (_orderInfo.businessProvinceRegion.value == null) {
      XYYContainer.toastChannel
          .toast("请选择${_orderInfo.businessProvinceRegion.name}");
      return;
    }
    // print("最终校验-发货省区：${_orderInfo.shippingProvinceRegion.value}");
    if (_orderInfo.shippingProvinceRegion.value == null) {
      XYYContainer.toastChannel
          .toast("请选择${_orderInfo.shippingProvinceRegion.name}");
      return;
    }
    // print("最终校验-退款原因：${_orderInfo.reasonForRefund.value}");
    if (_orderInfo.reasonForRefund.value == null) {
      XYYContainer.toastChannel.toast("请选择${_orderInfo.reasonForRefund.name}");
      return;
    }
    // print("最终校验-退款描述：${_orderInfo.refundDescription}");
    if (_orderInfo.refundDescription.length == 0) {
      XYYContainer.toastChannel.toast("请输入退款描述！");
      return;
    }
    // print("最终校验-是否整单退款：${_orderInfo.isWholeOrder}");
    // print("最终校验-附件列表：${attachments}");
    String? orderNo;
    List refundProductInfoList = [];
    _orderInfo.list?.forEach((product) {
      if (orderNo == null) {
        orderNo = product.orderNo;
      }
      final productMap = Map<String, dynamic>.from({
        'productNum': product.productNum,
        'skuId': product.skuId,
      });
      refundProductInfoList.add(productMap);
    });
    Map params = {
      'orderNo': orderNo,
      'applicant': basicInfo.userName, //申请人
      'employeeCode': basicInfo.staffNum, //员工工号
      'position': basicInfo.jobName, //职位名称
      'department': basicInfo.deptPathName, //所属部门
      'refundProductInfoDtos': refundProductInfoList, //订单列表
      'isRefunded': _orderInfo.hasBeenReturned.value, //是否已经退回
      'refundType': _orderInfo.refundType.value.toString(), //退款类型
      'customerType': _orderInfo.customerType.value, //客户类型
      'orderType': _orderInfo.orderType.value, //订单类型
      'provinceCode': _orderInfo.businessProvinceRegion.value, //业务省区
      'provinceCode2': _orderInfo.shippingProvinceRegion.value, //发货省区
      'refundReason': _orderInfo.reasonForRefund.value, //退款原因
      'refundExplain': _orderInfo.refundDescription, //退款描述
      'isWholeOrder': _orderInfo.isWholeOrder, //是否整单退款
      'attachmentUrl': attachments,
    };
    EasyLoading.show(status: "申请中", maskType: EasyLoadingMaskType.clear);
    var result =
        await NetworkV2<NormalRequestModel>(NormalRequestModel()).requestDataV2(
      'worries/apply',
      contentType: RequestContentType.JSON,
      parameters: params,
      method: RequestMethod.POST,
    );
    EasyLoading.dismiss();
    if (result.status == 'success' && callBack != null) {
      callBack();
    }
  }

  void onBackPress(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.maybePop(context, isRecord);
    } else {
      if (Platform.isIOS) {
        XYYContainer.bridgeCall("app_back");
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }
}
