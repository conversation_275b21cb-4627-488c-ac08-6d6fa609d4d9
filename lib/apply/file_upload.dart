import 'package:flutter/material.dart';
import 'dart:ui';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/message_dialog.dart';

class FileUpload extends StatefulWidget {
  final Function(List<String>)? onValueChanged;

  FileUpload({Key? key, this.onValueChanged}) : super(key: key);

  @override
  _FileUploadState createState() => _FileUploadState();
}

class _FileUploadState extends State<FileUpload> {
  String imageHost = "";
  List<String> imageList = [];

  @override
  void initState() {
    XYYContainer.bridgeCall("app_host").then((value) {
      if (value is Map) {
        this.imageHost = value['license_image_host'] ?? "";
      }
    });
    super.initState();
  }

  Widget build(BuildContext context) {
    return Container(
      // 设置外边距
      margin: const EdgeInsets.only(top: 20),

      // 设置内边距
      padding: const EdgeInsets.only(left: 20, top: 0, right: 20, bottom: 20),

      // 设置背景颜色
      color: Colors.white,

      // 内容
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            // width: 144,
            // height: 36,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
            child: const Text(
              '上传附件',
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: 'PingFangSC',
                fontWeight: FontWeight.w400,
                fontSize: 18,
                height: 1.0,
                // 行高等于字体大小
                color: Color(0xFF111334),
                letterSpacing: 0,
              ),
            ),
          ),
          Container(
              child: Wrap(
                  spacing: 8.0, // 水平间距
                  runSpacing: 8.0,
                  children: [
                ...List.generate(
                    imageList.length,
                    (index) => Stack(clipBehavior: Clip.none, children: [
                          GestureDetector(
                            onTap: () {
                              viewPhoto(imageList[index], index, true, true);
                            },
                            child: localPath(imageList[index])
                                ? Image.asset(
                                    imageList[index],
                                    width: 80,
                                    height: 80,
                                  )
                                : SizedBox(
                                    width: 80,
                                    height: 80,
                                    child: FadeInImage(
                                        image: ResizeImage(
                                            NetworkImage(imageList[index]),
                                            width:
                                                window.physicalSize.width ~/ 3),
                                        placeholder: AssetImage(
                                            "assets/images/base/icon_default_image.png"),
                                        fit: BoxFit.cover,
                                        imageErrorBuilder:
                                            (context, error, stackTrace) {
                                          return Image.asset(
                                              "assets/images/base/icon_default_image.png");
                                        }),
                                  ),
                          ),
                          Positioned(
                            right: -5,
                            top: 0,
                            child: GestureDetector(
                                child: Visibility(
                                  child: Image.asset(
                                      "assets/images/licence/icon_license_close.png",
                                      width: 15,
                                      height: 15),
                                  visible: true,
                                ),
                                onTap: () {
                                  showMessageDialog2(
                                      title: "",
                                      message: "确认删除照片?",
                                      negativeText: "取消",
                                      positiveText: "确认",
                                      callBack: () {
                                        deletePhoto(index, imageList[index]);
                                      });
                                }),
                          ),
                        ])),
                Visibility(
                  visible: (imageList.length) < 10,
                  child: GestureDetector(
                      onTap: () {
                        selectAlbum(10 - (imageList.length));
                        // 执行点击后的逻辑
                      },
                      child: Image.asset(
                        'assets/images/apply/upload.png',
                        width: 80,
                        height: 80,
                      )),
                ),
              ])),
        ],
      ),
    );
  }

  /// 先择相册
  void selectAlbum(int maxSize) {
    photoForAlbumWithUpload("worries/upload", maxSize);
  }

  ///打开相册
  void photoForAlbumWithUpload(String imagePath, int maxCount) async {
    List<String>? ids = await XYYContainer.photoForAlbumWithUpload(imagePath,
        imageCount: maxCount,
        extraParams: {"merchantId": 1810152371804, "isUploadOrigin": "true"},
        limitWidth: 1280,
        limitHeight: 1280);
    ids?.forEach((e) {
      // print("附件的链接：${e}");
      setState(() {
        imageList.add('${e.toString()}');
      });
    });
    if (widget.onValueChanged != null) {
      widget.onValueChanged!(imageList);
    }
  }

  ///判断是否是本地图片展示
  bool localPath(String path) {
    if (path.startsWith("assets")) {
      return true;
    }
    return false;
  }

  ///预览图片
  void viewPhoto(String url, int index, bool necessary, bool delete) {
    Navigator.of(context).pushNamed("/photo_view_page", arguments: {
      "urlPath": url,
      "delete": delete,
      "callback": () {
        if (delete) {
          showMessageDialog2(
              title: "",
              message: "确认删除照片?",
              negativeText: "取消",
              positiveText: "确认",
              callBack: () {
                Navigator.of(context).pop();
                deletePhoto(index, url);
              });
        }
      }
    });
  }

  void deletePhoto(int index, String photoPath) {
    imageList.remove(photoPath);
    setState(() {});
  }

  ///弹对话框
  void showMessageDialog2(
      {String title = "提示",
      String? message,
      String negativeText = "确定",
      String positiveText = "取消",
      Function? callBack,
      Function? cancelCallBack}) {
    if (message != null && message.isNotEmpty) {
      showDialog<Null>(
          context: context, //BuildContext对象
          barrierDismissible: false,
          builder: (BuildContext context) {
            return MessageDialog(
              title: title,
              negativeText: negativeText,
              positiveText: positiveText,
              message: message,
              onPositivePressEvent: () {
                Navigator.pop(context);
                if (callBack != null) {
                  callBack();
                }
              },
              onCloseEvent: () {
                Navigator.pop(context);
                if (cancelCallBack != null) {
                  cancelCallBack();
                }
              },
            ); //调用对话框);
          });
    }
  }
}
