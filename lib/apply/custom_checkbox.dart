import 'package:flutter/material.dart';

class CustomCheckbox extends StatelessWidget {
  final bool value;
  final ValueChanged<bool> onChanged;
  final String checkedImage;
  final String uncheckedImage;
  final double size;

  const CustomCheckbox({
    Key? key,
    required this.value,
    required this.onChanged,
    this.checkedImage = 'assets/images/apply/checked.png',
    this.uncheckedImage = 'assets/images/apply/unchecked.png',
    this.size = 20.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChanged(!value);
      },
      child: Image.asset(
        value ? checkedImage : uncheckedImage,
        width: size,
        height: size,
      ),
    );
  }
}
