import 'dart:io';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class ApplicationRequestSuccessfull extends BasePage {
  // 客户id
  final String? merchantId;

  ApplicationRequestSuccessfull({this.merchantId});

  @override
  BaseState initState() {
    return _ApplicationRequestSuccessfullState(this.merchantId);
  }
}

class _ApplicationRequestSuccessfullState extends BaseState {
  // 客户id
  final String? merchantId;

  bool? isRecord;

  int medicenListLength = 0;

  _ApplicationRequestSuccessfullState(this.merchantId);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
        height: MediaQuery.of(context).size.height * 0.6,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Row(mainAxisAlignment: MainAxisAlignment.center, children: [
                Image.asset(
                  'assets/images/apply/checked.png',
                  width: 30,
                  height: 30,
                ),
                SizedBox(width: 10),
                Text(
                  '申请成功',
                  style: TextStyle(
                    color: Colors.black, // 黑色
                    fontWeight: FontWeight.bold, // 加粗
                    fontSize: 18, // 字号18
                  ),
                )
              ]),
              SizedBox(height: 20),
              SizedBox(
                  width: 200, // 设置宽度
                  height: 50, // 设置高度
                  child: TextButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Color(0xFF00B377)), // 背景色
                    ),
                    onPressed: () {
                      XYYContainer.open('/main');
                    },
                    child:
                        const Text('确定', style: TextStyle(color: Colors.white)),
                  )),
              SizedBox(height: 20),
              Container(
                  width: 200, // 设置宽度
                  height: 50, // 设置高度
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Color(0xFFEDEDED), // 边框颜色
                      width: 1.0, // 边框宽度
                    ),
                  ),
                  child: TextButton(
                    style: ButtonStyle(
                      backgroundColor:
                          MaterialStateProperty.all(Colors.white), // 背景色
                    ),
                    onPressed: () {
                      XYYContainer.open('/super_worry_free_application');
                    },
                    child: const Text('再填一单',
                        style: TextStyle(color: Colors.black)),
                  ))
            ],
          ),
        ));
  }

  @override
  String getTitleName() {
    return "超无忧售后";
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName(), onLeftPressed: () {
      onBackPress(context);
    }, rightButtons: [
      TextButton(
        onPressed: () {
          XYYContainer.open('/apply_history_order_manage_page');
        },
        style: TextButton.styleFrom(
          primary: Color(0xFF111334),
          // 设置字体颜色（2.0.2 用 primary，而非 foregroundColor）
          textStyle: TextStyle(
            fontSize: 14,
          ),
        ),
        child: Text('历史记录'),
      ),
    ]);
  }

  void onBackPress(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.maybePop(context, isRecord);
    } else {
      if (Platform.isIOS) {
        XYYContainer.bridgeCall("app_back");
      } else {
        SystemNavigator.pop(animated: true);
      }
    }
  }
}
