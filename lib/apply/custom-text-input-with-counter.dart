import 'package:flutter/material.dart';

class CustomTextInputWithCounter extends StatefulWidget {
  final ValueChanged<String>? onChanged;

  const CustomTextInputWithCounter({Key? key, this.onChanged})
      : super(key: key);

  @override
  _CustomTextInputWithCounterState createState() =>
      _CustomTextInputWithCounterState();
}

class _CustomTextInputWithCounterState
    extends State<CustomTextInputWithCounter> {
  final TextEditingController _controller = TextEditingController();
  int _currentLength = 0;
  final int _maxLength = 300;

  @override
  void initState() {
    super.initState();
    _controller.addListener(() {
      final text = _controller.text;
      setState(() {
        _currentLength = text.length;
      });
      if (widget.onChanged != null) {
        widget.onChanged!(text); // 通知父组件
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        TextField(
          controller: _controller,
          maxLength: _maxLength,
          maxLines: 3,
          minLines: 3,
          decoration: InputDecoration(
            filled: true,
            fillColor: Colors.grey[200],
            hintText: "请描述退款原因（必填）",
            contentPadding: EdgeInsets.fromLTRB(12, 12, 12, 30),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide.none,
            ),
            counterText: "",
          ),
          style: TextStyle(fontSize: 14),
        ),
        Positioned(
          bottom: 8,
          right: 12,
          child: Text(
            '$_currentLength / $_maxLength',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
        ),
      ],
    );
  }
}
