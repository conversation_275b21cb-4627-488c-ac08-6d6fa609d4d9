import 'package:XyyBeanSproutsFlutter/order/bean/preferential_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'apply_order_list_item_data.g.dart';

@JsonSerializable()
class ApplyOrderListItemData extends BaseModelV2<ApplyOrderListItemData> {
  String? orderNo;
  String? productName; //商品名称
  int? productAmount; //商品数量
  int? varietyNum; // 商品种类数（没有）
  String? imageUrl; // 商品图
  double? productPrice; //商品单价
  double? purchasePrice; //实付商品单价
  double? totalAmount; //总额
  double? refundMoney; //实付总额
  double? subTotal; //总额
  int? productNum; //退款数量
  int? createTime; // 下单时间
  String? spec; //规格
  int? skuId;
  String? createTimeDesc;
  String? applyTime;
  String? companyName;

  /////////////////////////////
  String? merchantName; // 药店名称
  // String? payTypeName; // 支付方式
  String? statusName; // 订单状态名称
  double? money; // 订单实付金额
  // int? payType;
  int? status; // 订单状态
  int? merchantId;
  int? id;
  String? appAuditStatusName; //退款状态
  // String? refundChannelName; //退款渠道
  String? refundCreateTime; //申请时间
  double? refundFee;
  String? refundOrderNo; //退款单号
  // int? refundType;
  // String? refundTypeName;
  // int? refundChannel;
  int? auditState;
  String? auditStatusName;
  int? auditProcessState;
  String? remark3;
  String? branchName;

  // 额外赔付
  dynamic additionalFee;

  // 小额打款
  dynamic refundMode;

  // String? branchCode;

  List<PreferentialData>? preferentialList; // 订单 优惠列表

  ApplyOrderListItemData();

  factory ApplyOrderListItemData.fromJson(Map<String, dynamic> json) =>
      _$ApplyOrderListItemDataFromJson(json);

  @override
  ApplyOrderListItemData fromJsonMap(Map<String, dynamic>? json) {
    return ApplyOrderListItemData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ApplyOrderListItemDataToJson(this);
  }
}
