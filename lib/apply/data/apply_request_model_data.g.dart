// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'apply_request_model_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApplyBaseStatisticsInfoModel _$ApplyBaseStatisticsInfoModelFromJson(
    Map<String, dynamic> json) {
  return ApplyBaseStatisticsInfoModel()
    ..userName = json['userName'] as String?
    ..staffNum = json['staffNum'] as String?
    ..jobName = json['jobName'] as String?
    ..deptPathName = json['deptPathName'] as String?;
}

Map<String, dynamic> _$ApplyBaseStatisticsInfoModelToJson(
        ApplyBaseStatisticsInfoModel instance) =>
    <String, dynamic>{
      'userName': instance.userName,
      'staffNum': instance.staffNum,
      'jobName': instance.jobName,
      'deptPathName': instance.deptPathName,
    };

ApplyCustomerInfoModel _$ApplyCustomerInfoModelFromJson(
    Map<String, dynamic> json) {
  return ApplyCustomerInfoModel(
    name: json['name'] as String?,
    code: json['code'] as String?,
  );
}

Map<String, dynamic> _$ApplyCustomerInfoModelToJson(
        ApplyCustomerInfoModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
    };

NormalRequestModel _$NormalRequestModelFromJson(Map<String, dynamic> json) {
  return NormalRequestModel()..data = json['data'] as String?;
}

Map<String, dynamic> _$NormalRequestModelToJson(NormalRequestModel instance) =>
    <String, dynamic>{
      'data': instance.data,
    };
