// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'apply_order_list_item_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApplyOrderListItemData _$ApplyOrderListItemDataFromJson(
    Map<String, dynamic> json) {
  return ApplyOrderListItemData()
    ..orderNo = json['orderNo'] as String?
    ..productName = json['productName'] as String?
    ..productAmount = json['productAmount'] as int?
    ..varietyNum = json['varietyNum'] as int?
    ..imageUrl = json['imageUrl'] as String?
    ..productPrice = (json['productPrice'] as num?)?.toDouble()
    ..purchasePrice = (json['purchasePrice'] as num?)?.toDouble()
    ..totalAmount = (json['totalAmount'] as num?)?.toDouble()
    ..refundMoney = (json['refundMoney'] as num?)?.toDouble()
    ..subTotal = (json['subTotal'] as num?)?.toDouble()
    ..productNum = json['productNum'] as int?
    ..createTime = json['createTime'] as int?
    ..spec = json['spec'] as String?
    ..skuId = json['skuId'] as int?
    ..createTimeDesc = json['createTimeDesc'] as String?
    ..applyTime = json['applyTime'] as String?
    ..companyName = json['companyName'] as String?
    ..merchantName = json['merchantName'] as String?
    ..statusName = json['statusName'] as String?
    ..money = (json['money'] as num?)?.toDouble()
    ..status = json['status'] as int?
    ..merchantId = json['merchantId'] as int?
    ..id = json['id'] as int?
    ..appAuditStatusName = json['appAuditStatusName'] as String?
    ..refundCreateTime = json['refundCreateTime'] as String?
    ..refundFee = (json['refundFee'] as num?)?.toDouble()
    ..refundOrderNo = json['refundOrderNo'] as String?
    ..auditState = json['auditState'] as int?
    ..auditStatusName = json['auditStatusName'] as String?
    ..auditProcessState = json['auditProcessState'] as int?
    ..remark3 = json['remark3'] as String?
    ..branchName = json['branchName'] as String?
    ..additionalFee = json['additionalFee']
    ..refundMode = json['refundMode']
    ..preferentialList = (json['preferentialList'] as List<dynamic>?)
        ?.map((e) => PreferentialData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ApplyOrderListItemDataToJson(
        ApplyOrderListItemData instance) =>
    <String, dynamic>{
      'orderNo': instance.orderNo,
      'productName': instance.productName,
      'productAmount': instance.productAmount,
      'varietyNum': instance.varietyNum,
      'imageUrl': instance.imageUrl,
      'productPrice': instance.productPrice,
      'totalAmount': instance.totalAmount,
      'productNum': instance.productNum,
      'createTime': instance.createTime,
      'spec': instance.spec,
      'skuId': instance.skuId,
      'merchantName': instance.merchantName,
      'statusName': instance.statusName,
      'money': instance.money,
      'status': instance.status,
      'merchantId': instance.merchantId,
      'id': instance.id,
      'appAuditStatusName': instance.appAuditStatusName,
      'refundCreateTime': instance.refundCreateTime,
      'refundFee': instance.refundFee,
      'refundOrderNo': instance.refundOrderNo,
      'auditState': instance.auditState,
      'auditProcessState': instance.auditProcessState,
      'remark3': instance.remark3,
      'branchName': instance.branchName,
      'additionalFee': instance.additionalFee,
      'refundMode': instance.refundMode,
      'preferentialList': instance.preferentialList,
    };
