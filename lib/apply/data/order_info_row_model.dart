import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_request_model_data.dart';

/// 选项项模型（用于下拉选择框的选项）
class ApplyOptionItem {
  final String name; // 选项名称
  final String value; // 选项值

  const ApplyOptionItem({required this.name, required this.value});
}

/// 订单行模型（表示一个可配置的表单项）
class ApplyOrderRowModel {
  String? value; // 当前选中的值（可为空）
  String? valueText; // 当前选中的值（可为空）
  String name; // 表单项名称
  String drawerTitle; // 抽屉标题
  List<ApplyOptionItem> options; // 选项列表
  bool? disable = false;
  bool? search = false;

  ApplyOrderRowModel(
      {this.value,
      this.valueText,
      this.name = '',
      this.drawerTitle = '',
      this.options = const [], // 默认空列表
      this.disable,
      this.search});
}

/// 订单信息模型（主业务逻辑）
class ApplyOrderInfoModel {
  List<ApplyOrderListItemData>? list; // 订单列表（可为空）
  ApplyOrderRowModel hasBeenReturned; // 是否已退回
  ApplyOrderRowModel refundType; // 退款类型
  ApplyOrderRowModel reasonForRefund; // 退款原因
  ApplyOrderRowModel customerType; // 客户类型
  ApplyOrderRowModel orderType; // 订单类型
  ApplyOrderRowModel businessProvinceRegion; // 业务省区
  ApplyOrderRowModel shippingProvinceRegion; // 发货省区
  String refundDescription; // 退款描述
  int isWholeOrder;

  ApplyOrderInfoModel(
      {this.list,
      ApplyOrderRowModel? hasBeenReturned,
      ApplyOrderRowModel? refundType,
      ApplyOrderRowModel? reasonForRefund,
      ApplyOrderRowModel? customerType,
      ApplyOrderRowModel? orderType,
      ApplyOrderRowModel? businessProvinceRegion,
      ApplyOrderRowModel? shippingProvinceRegion,
      this.refundDescription = '', // 非空字段默认值
      this.isWholeOrder = 0})
      : hasBeenReturned = hasBeenReturned ?? ApplyOrderRowModel(),
        refundType = refundType ?? ApplyOrderRowModel(),
        reasonForRefund = reasonForRefund ?? ApplyOrderRowModel(),
        customerType = customerType ?? ApplyOrderRowModel(),
        orderType = orderType ?? ApplyOrderRowModel(),
        businessProvinceRegion = businessProvinceRegion ?? ApplyOrderRowModel(),
        shippingProvinceRegion = shippingProvinceRegion ?? ApplyOrderRowModel();

  /// 初始化“是否已退回”选项
  void initHasBeenReturned() {
    hasBeenReturned = ApplyOrderRowModel(
      value: null,
      // 初始未选择
      valueText: null,
      name: "是否已退回",
      drawerTitle: "请选择是否已退回",
      options: const [
        ApplyOptionItem(name: "是", value: "1"),
        ApplyOptionItem(name: "否", value: "0"),
      ],
    );
  }

  /// 初始化“退款类型”选项
  void initRefundType() {
    refundType = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "退款类型",
      drawerTitle: "请选择退款类型",
      options: const [
        ApplyOptionItem(name: "非破损非近效期", value: "-6700396257248063771"),
        ApplyOptionItem(name: "非近效期破损", value: "-5460415865475481309"),
        ApplyOptionItem(name: "近效期未破损", value: "-6697938055163327598"),
        ApplyOptionItem(name: "近效期且破损", value: "9045680371352617972"),
      ],
    );
  }

  /// 初始化“退款原因”选项
  void initReasonForRefund() {
    reasonForRefund = ApplyOrderRowModel(
      value: '1',
      valueText: '协商一致退款',
      name: "退款原因",
      drawerTitle: "请选择退款原因",
      options: const [
        ApplyOptionItem(name: "协商一致退款", value: "1"),
      ],
      disable: true,
    );
  }

  void initCustomerType() async {
    var result =
        await NetworkV2<ApplyCustomerInfoModel>(ApplyCustomerInfoModel())
            .requestDataV2(
      '/worries/customer_type',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var optionList = result
            .getListData()
            ?.map((item) => ApplyOptionItem(
                  name: item.name ?? "",
                  value: item.code ?? "",
                ))
            .toList() ??
        [];

    customerType = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "客户类型",
      drawerTitle: "请选择客户类型",
      options: optionList,
    );
  }

  void initOrderType() async {
    var result =
        await NetworkV2<ApplyCustomerInfoModel>(ApplyCustomerInfoModel())
            .requestDataV2(
      '/worries/order_type',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var optionList = result
            .getListData()
            ?.map((item) => ApplyOptionItem(
                  name: item.name ?? "",
                  value: item.code ?? "",
                ))
            .toList() ??
        [];
    orderType = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "订单类型",
      drawerTitle: "请选择订单类型",
      options: optionList,
    );
  }

  void initBusinessProvinceRegion() async {
    const result = [
      "广西",
      "广东",
      "天津",
      "甘青宁新销售部",
      "湖北",
      "湖南",
      "浙江-浙北",
      "重庆",
      "山东-鲁东",
      "安徽",
      "福建",
      "江苏-苏北",
      "江西",
      "河北",
      "黑龙江",
      "吉林",
      "辽宁",
      "内蒙古",
      "山西",
      "陕西",
      "贵州",
      "四川-川东",
      "云南",
      "KA中心-湖北KA部",
      "KA中心-湖南KA部",
      "KA中心-浙江KA部",
      "KA中心-粤西KA部",
      "KA中心-河南KA部",
      "KA中心-重庆KA部",
      "KA中心-山东KA部",
      "KA中心-安徽KA部",
      "KA中心-江苏KA部",
      "KA中心-河北KA部",
      "KA中心-四川KA部",
      "KA中心-云南KA部",
      "KA中心-福建KA部",
      "KA中心-江西KA部",
      "KA中心-内蒙古KA部",
      "KA中心-山西KA部",
      "KA中心-广西KA部",
      "KA中心-陕西KA部",
      "KA中心-上海KA部",
      "KA中心-甘青宁KA部",
      "KA中心-贵州KA部",
      "KA中心-北京KA部",
      "KA中心-海南KA部",
      "KA中心-黑龙江KA部",
      "KA中心-吉林KA部",
      "KA中心-辽宁KA部",
      "控销KA-河南",
      "控销KA-陕甘宁",
      "控销KA-云南",
      "控销KA-广西",
      "控销KA-贵州",
      "控销KA-黑龙江",
      "控销KA-吉林",
      "控销KA-辽宁",
      "控销KA-安徽",
      "控销KA-江西",
      "控销KA-湖北",
      "控销KA-湖南",
      "控销KA-京津冀",
      "控销KA-广东",
      "控销KA-福建",
      "控销KA-山西",
      "控销KA-内蒙古",
      "控销KA-山东",
      "控销KA-浙江",
      "控销KA-江苏",
      "控销KA-川西",
      "控销KA-重庆",
      "集采",
      "商务",
      "电销中心",
      "控销KA-川东",
      "KA中心-新疆KA部",
      "KA中心-粤东KA部",
      "控销业务中心",
      "河南-豫西",
      "浙江-浙南",
      "江苏-苏南",
      "河南-豫东",
      "四川-川西",
      "山东-鲁西",
      "山东",
      "浙江",
      "KA中心-天津",
      "总部",
      "粤东",
      "粤西",
      "粤中"
    ];
    var optionList = result
        .map((item) => ApplyOptionItem(
              name: item,
              value: item,
            ))
        .toList();
    businessProvinceRegion = ApplyOrderRowModel(
        value: null,
        valueText: null,
        name: "业务省区",
        drawerTitle: "请选择业务省区",
        options: optionList,
        search: true);
  }

  void initShippingProvinceRegion() async {
    var result =
        await NetworkV2<ApplyCustomerInfoModel>(ApplyCustomerInfoModel())
            .requestDataV2(
      '/worries/allArea',
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var optionList = result
            .getListData()
            ?.map((item) => ApplyOptionItem(
                  name: item.name ?? "",
                  value: item.name ?? "",
                ))
            .toList() ??
        [];
    shippingProvinceRegion = ApplyOrderRowModel(
      value: null,
      valueText: null,
      name: "发货省区",
      drawerTitle: "请选择发货省区",
      options: optionList,
    );
  }
}

//定义订单选择的返回值
class ReturnSelectInfoData {
  final List<ApplyOrderListItemData>? returnList;
  final String? keyword;
  final int? isWholeOrder;

  ReturnSelectInfoData({this.returnList, this.keyword, this.isWholeOrder});
}
