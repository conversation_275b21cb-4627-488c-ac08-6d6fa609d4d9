import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'apply_request_model_data.g.dart';

//获取用户基本信息
@JsonSerializable()
class ApplyBaseStatisticsInfoModel
    extends BaseModelV2<ApplyBaseStatisticsInfoModel> {
  String? userName; //用户姓名
  String? staffNum; //员工工号
  String? jobName; //职位名称
  String? deptPathName; //所属部门

  ApplyBaseStatisticsInfoModel();

  dynamic operator [](String key) {
    switch (key) {
      case 'userName':
        return userName;
      case 'staffNum':
        return staffNum;
      case 'jobName':
        return jobName;
      case 'deptPathName':
        return deptPathName;
      default:
        return null;
    }
  }

  @override
  ApplyBaseStatisticsInfoModel fromJsonMap(Map<String, dynamic> json) {
    return _$ApplyBaseStatisticsInfoModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ApplyBaseStatisticsInfoModelToJson(this);
  }
}

//获取客户类型
@JsonSerializable()
class ApplyCustomerInfoModel extends BaseModelV2<ApplyCustomerInfoModel> {
  String? name;
  String? code;

  ApplyCustomerInfoModel({this.name, this.code});

  @override
  ApplyCustomerInfoModel fromJsonMap(Map<String, dynamic> json) {
    return _$ApplyCustomerInfoModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ApplyCustomerInfoModelToJson(this);
  }

  factory ApplyCustomerInfoModel.fromJson(Map<String, dynamic> json) =>
      _$ApplyCustomerInfoModelFromJson(json);
}

//获取用户基本信息
@JsonSerializable()
class NormalRequestModel
    extends BaseModelV2<NormalRequestModel> {

  String? data; //所属部门

  NormalRequestModel();

  @override
  NormalRequestModel fromJsonMap(Map<String, dynamic> json) {
    return _$NormalRequestModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$NormalRequestModelToJson(this);
  }
}