// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'apply_order_manage_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ApplyOrderManageData _$ApplyOrderManageDataFromJson(Map<String, dynamic> json) {
  return ApplyOrderManageData()
    ..isLastPage = json['isLastPage'] as bool?
    ..lastPage = json['lastPage']
    ..totalMoney = (json['totalMoney'] as num?)?.toDouble()
    ..tips = json['tips'] as String?
    ..page = json['page'] == null
        ? null
        : OrderManagerPageData.fromJson(json['page'] as Map<String, dynamic>)
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => ApplyOrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => ApplyOrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ApplyOrderManageDataToJson(
        ApplyOrderManageData instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'lastPage': instance.lastPage,
      'totalMoney': instance.totalMoney,
      'tips': instance.tips,
      'page': instance.page,
      'list': instance.list,
      'rows': instance.rows,
    };

OrderManagerPageData _$OrderManagerPageDataFromJson(Map<String, dynamic> json) {
  return OrderManagerPageData()
    ..lastPage = json['lastPage']
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => ApplyOrderListItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$OrderManagerPageDataToJson(
        OrderManagerPageData instance) =>
    <String, dynamic>{
      'lastPage': instance.lastPage,
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };
