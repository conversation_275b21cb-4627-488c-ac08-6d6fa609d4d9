import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'apply_order_manage_data.g.dart';

@JsonSerializable()
class ApplyOrderManageData extends BaseModelV2<ApplyOrderManageData> {
  bool? isLastPage = false;
  dynamic lastPage;
  double? totalMoney;
  String? tips;
  OrderManagerPageData? page;
  List<ApplyOrderListItemData>? list;
  List<ApplyOrderListItemData>? rows;

  ApplyOrderManageData();

  @override
  ApplyOrderManageData fromJsonMap(Map<String, dynamic>? json) {
    return ApplyOrderManageData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ApplyOrderManageDataToJson(this);
  }

  factory ApplyOrderManageData.fromJson(Map<String, dynamic> json) =>
      _$ApplyOrderManageDataFromJson(json);
}

@JsonSerializable()
class OrderManagerPageData extends BaseModelV2<OrderManagerPageData> {
  dynamic lastPage;
  dynamic isLastPage;
  List<ApplyOrderListItemData>? list;

  OrderManagerPageData();

  factory OrderManagerPageData.fromJson(Map<String, dynamic> json) =>
      _$OrderManagerPageDataFromJson(json);

  @override
  OrderManagerPageData fromJsonMap(Map<String, dynamic> json) {
    return _$OrderManagerPageDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$OrderManagerPageDataToJson(this);
  }
}
