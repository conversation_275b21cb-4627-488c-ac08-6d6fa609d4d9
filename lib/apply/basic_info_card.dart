import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_request_model_data.dart';

class BasicInfoCard extends StatelessWidget {
  final ApplyBaseStatisticsInfoModel basicInfo;

  const BasicInfoCard({
    Key? key,
    required this.basicInfo,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.only(top: 20),
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 0),
      color: Colors.white,
      child: Column(
        children: [
          Container(
            // width: 144,
            // height: 36,
            alignment: Alignment.centerLeft,
            padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
            child: const Text(
              '基础信息',
              textAlign: TextAlign.left,
              style: TextStyle(
                fontFamily: 'PingFangSC',
                fontWeight: FontWeight.normal,
                fontSize: 18,
                // 行高等于字体大小
                color: Color(0xFF111334),
                letterSpacing: 0,
              ),
            ),
          ),
          _buildInfoRow(context, '申请人', 'userName'),
          _buildInfoRow(context, '员工编号', 'staffNum'),
          _buildInfoRow(context, '岗位', 'jobName'),
          _buildInfoRow(context, '所属部门', 'deptPathName'),
        ],
      ),
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String key) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 20),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 100,
            child: RichText(
              text: TextSpan(
                style: DefaultTextStyle.of(context).style.copyWith(
                      fontWeight: FontWeight.normal,
                      color: Color(0xFF333333),
                      fontSize: 14,
                    ),
                text: label,
                children: const [
                  TextSpan(
                    text: ' *',
                    style: TextStyle(color: Colors.red, fontSize: 14),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                '${basicInfo[key] ?? '--'}',
                textAlign: TextAlign.right,
                style: const TextStyle(fontSize: 14, color: Color(0xFF333333)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
