import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/preferential_data.dart';
import 'package:flutter/material.dart';
import "package:intl/intl.dart";

class ApplyHistoryOrderListItemWidget extends StatelessWidget {
  final ApplyOrderListItemData itemData;
  final String? imageHost;

  ApplyHistoryOrderListItemWidget(this.imageHost, this.itemData);

  @override
  Widget build(BuildContext context) {
    var moneyText = formatMoneyText(itemData.refundMoney ?? 0);
    return Container(
      constraints: BoxConstraints(minHeight: 151),
      width: double.infinity,
      margin: EdgeInsets.fromLTRB(15, 5, 15, 5),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(2)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 40,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Text(
                    "订单编号：${itemData.orderNo ?? "--"}",
                    style: TextStyle(
                        color: Color(0xFF676773),
                        fontWeight: FontWeight.normal,
                        fontSize: 12),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(
                  width: 10,
                ),
                Text(
                  itemData.auditStatusName ?? "--",
                  style: TextStyle(
                      //color: getStatusTextColor(itemData.status ?? 0),
                      fontSize: 12,
                      fontWeight: FontWeight.normal),
                ),
                SizedBox(
                  width: 10,
                )
              ],
            ),
          ),
          Divider(height: 1, color: Color(0xFFF6F6F6)),
          SizedBox(
            height: 10,
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                width: 10,
              ),
              SizedBox(
                width: 90,
                height: 90,
                child: Stack(
                  alignment: Alignment.bottomCenter,
                  children: [
                    ImageWidget(
                        url: "$imageHost${itemData.imageUrl}",
                        w: 90,
                        h: 90,
                        defImagePath:
                            "assets/images/order/icon_load_failed.png"),
                    Container(
                      color: Color(0x66000000),
                      height: 19,
                      alignment: Alignment.center,
                      child: Text(
                        "${itemData.productAmount ?? "-"}件商品",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                            fontWeight: FontWeight.normal),
                      ),
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 10,
              ),
              Expanded(
                child: Container(
                  constraints: BoxConstraints(minHeight: 85),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        itemData.companyName ?? "--",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                        style: TextStyle(
                            color: Color(0xFF292933),
                            fontSize: 15,
                            fontWeight: FontWeight.w500),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        "申请时间：${itemData.applyTime ?? "--"}",
                        // this.createTime != null ? TimeUtils().formatTime(this.createTime) : "--";
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                            color: Color(0xFF676773),
                            fontSize: 12,
                            fontWeight: FontWeight.normal),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        "订单来源:  ${itemData.branchName ?? "-"}",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                            color: Color(0xFF676773),
                            fontSize: 12,
                            fontWeight: FontWeight.normal),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        "审核状态：${itemData.auditStatusName ?? "-"}",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                        style: TextStyle(
                            color: Color(0xFF676773),
                            fontSize: 12,
                            fontWeight: FontWeight.normal),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      // buildPreferentialList(itemData.preferentialList ?? []),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            "总计：",
                            style: TextStyle(
                                color: Color(0xFF333333),
                                fontSize: 12,
                                fontWeight: FontWeight.normal),
                          ),
                          Text.rich(TextSpan(
                              text: "¥",
                              style: TextStyle(
                                  color: Color(0xFF292933),
                                  fontSize: 13,
                                  fontWeight: FontWeight.w700),
                              children: [
                                TextSpan(
                                  text: moneyText.substring(
                                      0, moneyText.indexOf(".")),
                                  style: TextStyle(
                                      color: Color(0xFF292933),
                                      fontSize: 16,
                                      fontWeight: FontWeight.w700),
                                ),
                                TextSpan(
                                    text: moneyText
                                        .substring(moneyText.indexOf(".")),
                                    style: TextStyle(
                                        color: Color(0xFF292933),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w700))
                              ])),
                          // SizedBox(
                          //   width: 6,
                          // ),
                          // Text(
                          //   "¥${formatMoneyText(itemData.totalAmount ?? 0)}",
                          //   style: TextStyle(
                          //       color: const Color(0xFF9494A6),
                          //       decoration: TextDecoration.lineThrough,
                          //       decorationColor: const Color(0xFF9494A6),
                          //       fontSize: 12,
                          //       fontWeight: FontWeight.normal),
                          // )
                        ],
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(
                width: 13,
              )
            ],
          ),
          SizedBox(
            height: 10,
          )
        ],
      ),
    );
  }

  /// 订单状态 0-未审核，1-审核中，2-配送中，3-已完成，4-已取消，5-已删除,6-已拆单,7-出库中,9-审核结束，10-未支付,11-已支付，90-退款审核中,91-已退款,20-已送达,21-已拒签；
  /// 状态颜色：
  /// 红色：未审核、未支付、已拒签 ，
  /// 绿色：审核中、配送中、出库中、退款审核中、已支付、已送达、 已拆单、审核结束，
  /// 灰色：已完成、已取消、删除、已退款
  Color getStatusTextColor(int? status) {
    switch (status) {
      case 0:
      case 10:
      case 21:
        return Color(0xfffe3d3d);
      case 1:
      case 2:
      case 7:
      case 90:
      case 11:
      case 20:
      case 6:
      case 9:
        return Color(0xff35c561);
      case 3:
      case 4:
      case 5:
      case 91:
        return Color(0xff8e8e93);
      default:
        return Color(0xff333333);
    }
  }

  Widget buildPreferentialList(List<PreferentialData>? preferentialList) {
    if (preferentialList == null || preferentialList.isEmpty) {
      return Container(
        padding: EdgeInsets.only(bottom: 10),
      );
    } else {
      return Container(
        constraints: BoxConstraints(minHeight: 31),
        padding: EdgeInsets.only(bottom: 10),
        child: Column(
          children: List.generate(preferentialList.length, (index) {
            var item = preferentialList[index];
            return Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Container(
                      height: 15,
                      padding: EdgeInsets.symmetric(vertical: 1, horizontal: 5),
                      decoration: BoxDecoration(
                          color: Color(0x0DFF4741),
                          borderRadius: BorderRadius.circular(1),
                          border:
                              Border.all(color: Color(0x80FF4741), width: 0.5)),
                      alignment: Alignment.center,
                      child: Text(
                        item.title ?? "",
                        style: TextStyle(
                            color: Color(0xFFFF4741),
                            fontSize: 10,
                            fontWeight: FontWeight.normal),
                      ),
                      margin: EdgeInsets.only(right: 5),
                    ),
                    Text(
                      item.preferential ?? "",
                      style: TextStyle(
                          color: Color(0xFF9494A6),
                          fontSize: 12,
                          fontWeight: FontWeight.normal),
                      strutStyle: StrutStyle(leading: 0.2),
                    )
                  ],
                ),
                SizedBox(
                  height: 4,
                )
              ],
            );
          }),
        ),
      );
    }
  }

  String formatMoneyText(double? money) {
    return NumberFormat("0.00").format(money);
  }
}
