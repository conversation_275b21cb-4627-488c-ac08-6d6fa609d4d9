import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_manage_data.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_tag_data.dart';
import 'order_list_item_widget.dart';

class ApplyHistoryOrderManagePage extends BasePage {
  ApplyHistoryOrderManagePage();

  @override
  BaseState<StatefulWidget> initState() {
    return ApplyHistoryOrderManagePageState();
  }
}

class ApplyHistoryOrderManagePageState
    extends BaseState<ApplyHistoryOrderManagePage> {
  var _statusModel = OrderStatusModel();
  late OrderListModel _listModel;
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();

  PageStateWidget? pageStateWidget;

  @override
  void onDestroy() {
    super.onDestroy();
    _statusModel.dispose();
    _listModel.dispose();
    _refreshController.dispose();
    _scrollController.dispose();
  }

  @override
  void onCreate() {
    pageStateWidget = new PageStateWidget();
    _listModel = OrderListModel(_scrollController, _refreshController);

    _listModel.requestSmallImageHost();
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<OrderStatusModel>(
          create: (context) => _statusModel),
      ChangeNotifierProvider<OrderListModel>(create: (context) => _listModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    _statusModel.updateStatusList();
    _listModel.requestListModel(true, _statusModel.getSelectedStatus());
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildHeaderView(),
          Expanded(
            child: Container(
              color: Color(0xffefeff4),
              child: buildListView(),
            ),
          ),
        ],
      ),
    );
  }

  buildHeaderView() {
    return Consumer<OrderStatusModel>(builder: (context, model, child) {
      return model.statusList?.isNotEmpty != true
          ? Container()
          : Container(
              padding: EdgeInsets.fromLTRB(15, 15, 0, 10),
              color: Colors.white,
              width: double.infinity,
              height: (model.isExpand ? null : 52),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Expanded(
                      child: Wrap(
                    spacing: 10,
                    runSpacing: 10,
                    children: List.generate(
                      model.statusList?.length ?? 0,
                      (index) {
                        return _buildItemTag(index);
                      },
                    ),
                  )),
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      model.toggleExpandStatus();
                    },
                    child: Container(
                      height: 30,
                      child: Container(
                          padding: EdgeInsets.only(right: 25, left: 10),
                          child: Image.asset(
                            model.isExpand
                                ? 'assets/images/order/order_up_gray.png'
                                : 'assets/images/order/order_down_gray.png',
                            width: 14,
                            height: 30,
                          )),
                    ),
                  )
                ],
              ));
    });
  }

  buildListView() {
    return Consumer<OrderListModel>(builder: (context, model, child) {
      var emptyWidget = getEmptyWidget();
      return Container(
        color: emptyWidget == null ? Colors.transparent : Colors.white,
        child: EasyRefresh.custom(
            controller: _refreshController,
            scrollController: _scrollController,
            enableControlFinishRefresh: true,
            enableControlFinishLoad: true,
            onRefresh: () async {
              _listModel.requestListModel(
                  true, _statusModel.getSelectedStatus());
            },
            onLoad: !(_listModel.isLastPage ?? false) &&
                    _listModel.list?.isNotEmpty == true
                ? () async {
                    _listModel.requestListModel(
                        false, _statusModel.getSelectedStatus());
                  }
                : null,
            slivers: [
              SliverPadding(padding: EdgeInsets.only(top: 5)),
              SliverList(
                delegate: SliverChildBuilderDelegate(
                  (BuildContext context, int index) {
                    //创建列表项
                    return buildOrderItem(index);
                  },
                  childCount: _listModel.list?.length ?? 0,
                ),
              ),
              SliverPadding(
                padding: EdgeInsets.only(top: 5),
              ),
            ],
            emptyWidget: emptyWidget),
      );
    });
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [buildSearchButton()],
    );
  }

  @override
  String getTitleName() {
    return "超无忧申请";
  }

  Widget buildSearchButton() {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed("/apply_history_order_search_page",
            arguments: {"status": this._statusModel.getSelectedStatus()});
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_search.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  _buildItemTag(int index) {
    OrderTagData item = _statusModel.statusList![index];
    var textColor;
    var bgColor;
    // var borderColor;
    if (item.isSelect == true) {
      textColor = const Color(0xFF00B377);
      bgColor = const Color(0x1A00B377);
      // borderColor = textColor;
    } else {
      textColor = const Color(0xFF292933);
      bgColor = const Color(0xFFF7F7F8);
      // borderColor = bgColor;
    }

    return GestureDetector(
      onTap: () {
        handleTagClick(index);
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            constraints: BoxConstraints(minWidth: 63),
            height: 32,
            padding: EdgeInsets.symmetric(horizontal: 11),
            decoration: BoxDecoration(
                color: bgColor, borderRadius: BorderRadius.circular(2)),
            alignment: Alignment.center,
            child: Text(
              item.value ?? "",
              style: TextStyle(
                  color: textColor,
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ),
        ],
      ),
    );
  }

  buildOrderItem(int index) {
    if (_listModel.list == null || index >= (_listModel.list?.length ?? 0)) {
      return Container();
    }
    var itemData = _listModel.list![index];
    return GestureDetector(
      onTap: () {
        if (itemData.auditState == 6 || itemData.auditState == 7) {
          //审核中、OA已驳回
          Navigator.of(context).pushNamed("/order_moderation_cwy_page",
              arguments: {"orderNo": '${itemData.id}'});
        } else {
          Navigator.of(context).pushNamed("/order_detail_refund_cwy_page",
              arguments: {"orderNo": '${itemData.id}'});
        }
      },
      child:
          ApplyHistoryOrderListItemWidget(_listModel.smallImageHost, itemData),
    );
  }

  void handleTagClick(int index) {
    _statusModel.setSelectedStatusByIndex(index);
    _statusModel.updateStatusUI();
    _listModel.requestListModel(true, _statusModel.getSelectedStatus());
  }

  Widget? getEmptyWidget() {
    if (_listModel.isSuccess == null) {
      return null;
    }
    if (_listModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _listModel.requestListModel(true, _statusModel.getSelectedStatus());
      });
    }
    if ((_listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }
}

class OrderStatusModel extends ChangeNotifier {
  bool isExpand = false;
  List<OrderTagData>? statusList;

  OrderStatusModel();

  updateStatusUI() {
    notifyListeners();
  }

  toggleExpandStatus() {
    isExpand = !isExpand;
    notifyListeners();
  }

  updateStatusList() {
    final statusMap = {
      0: '全部',
      1: '退款待审核',
      2: '审核通过待退货入库',
      3: '入库完成待退款',
      4: '退款完成',
      5: '退款关闭',
      6: 'OA审核中',
      7: 'OA已驳回',
    };

    List<OrderTagData> refundStatusTags = statusMap.entries.map((entry) {
      return OrderTagData.fromJson({
        'id': entry.key,
        'value': entry.value,
        'isSelect': false,
      });
    }).toList();

    this.statusList = refundStatusTags;
    setSelectedStatusByIndex(0);
    notifyListeners();
  }

  resetSelectedStatus() {
    if (statusList != null && statusList!.isNotEmpty) {
      _clearSelectedStatus();
    }
  }

  _clearSelectedStatus() {
    statusList?.forEach((e) {
      e.isSelect = false;
    });
  }

  setSelectedStatusByIndex(int index) {
    _clearSelectedStatus();
    statusList![index].isSelect = true;
  }

  List<OrderTagData> getSelectedStatusList() {
    return statusList?.where((element) => element.isSelect == true).toList() ??
        List.empty();
  }

  String? getSelectedStatus() {
    return statusList
        ?.firstWhere(
          (element) => element.isSelect == true,
          orElse: () => OrderTagData(), // 如果没找到返回空对象
        )
        .id
        .toString();
  }

  setSelectedStatus(OrderTagData orderTag) {
    _clearSelectedStatus();
    updateSingleStatus(orderTag, true);
  }

  updateSingleStatus(OrderTagData orderTag, bool isSelect) {
    statusList!.forEach((e) {
      if (e == orderTag) {
        e.isSelect = isSelect;
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }
}

class OrderListModel extends ChangeNotifier {
  var _isDisposed = false;
  bool? isLastPage = false;
  int pageNum = 1;
  bool forceRefresh = false;
  List<ApplyOrderListItemData>? list;
  String? smallImageHost;
  bool? isSuccess;
  final EasyRefreshController _refreshController;
  final ScrollController _scrollController;

  OrderListModel(this._scrollController, this._refreshController);

  requestSmallImageHost() {
    if (smallImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          smallImageHost = value["image_host"];
          notifyListeners();
        }
      });
    }
  }

  void requestNormalList(Map<String, String?> paramsMap) {
    NetworkV2<ApplyOrderManageData>(ApplyOrderManageData())
        .requestDataV2("worries/orderList",
            method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      handleResult(value.isSuccess, value.getData());
    });
  }

  requestListModel(bool isRefresh, String? status) async {
    if (isRefresh) {
      pageNum = 1;
      forceRefresh = true;
      list?.clear();
    } else {
      pageNum += 1;
      forceRefresh = false;
    }
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    var paramsMap = buildParamsMap(status);
    requestNormalList(paramsMap);
  }

  void handleResult(bool? isSuccess, ApplyOrderManageData? data) {
    EasyLoading.dismiss();
    if (!_isDisposed && (isSuccess ?? false)) {
      this.isSuccess = isSuccess ?? false;
      if (data != null) {
        var tempList;
        isLastPage = data.isLastPage ?? true;
        tempList = data.list;
        if (forceRefresh) {
          list = tempList;
          _scrollController.jumpTo(0);
        } else {
          if (list == null) {
            list = tempList;
          } else {
            list?.addAll(tempList ?? []);
          }
        }
      } else {
        isLastPage = true;
      }
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: isLastPage ?? true);
      notifyListeners();
    } else {
      _refreshController.finishRefresh();
      _refreshController.finishLoad(noMore: false);
    }
  }

  Map<String, String?> buildParamsMap(String? status) {
    var params = Map<String, String?>();
    //分页参数
    params["pageNum"] = pageNum.toString();
    params["pageSize"] = "10";
    params["status"] = status;
    return params;
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
