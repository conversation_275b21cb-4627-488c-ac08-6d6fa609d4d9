import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_list_item_data.dart';
import 'package:XyyBeanSproutsFlutter/apply/data/apply_order_manage_data.dart';
import 'order_list_item_widget.dart';

class ApplyHistoryOrderSearchPage extends BasePage {
  final String? status; // 订单状态

  ApplyHistoryOrderSearchPage(this.status);

  @override
  BaseState<StatefulWidget> initState() {
    return ApplyHistoryOrderSearchPageState();
  }
}

class ApplyHistoryOrderSearchPageState
    extends BaseState<ApplyHistoryOrderSearchPage> {
  late OrderListModel _listModel;
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();

  PageStateWidget? pageStateWidget;

  @override
  void onCreate() {
    pageStateWidget = new PageStateWidget();
    _listModel =
        OrderListModel(_scrollController, _refreshController, widget.status);
    _listModel.requestSmallImageHost();
    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<OrderListModel>(create: (context) => _listModel)
    ];
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Divider(color: Color(0xFFF6F6F6), height: 1),
          Expanded(
            child: Container(
              color: Color(0xffefeff4),
              child: buildListView(),
            ),
          ),
        ],
      ),
    );
  }

  buildListView() {
    return Consumer<OrderListModel>(builder: (context, model, child) {
      return EasyRefresh.custom(
          controller: _refreshController,
          scrollController: _scrollController,
          enableControlFinishRefresh: true,
          enableControlFinishLoad: true,
          onRefresh: () async {
            _listModel.requestListModel(true);
          },
          onLoad: !_listModel.isLastPage!
              ? () async {
                  _listModel.requestListModel(false);
                }
              : null,
          slivers: [
            SliverList(
              delegate:
                  SliverChildBuilderDelegate((BuildContext context, int index) {
                //创建列表项
                return buildOrderItem(index);
              }, childCount: _listModel.list?.length ?? 0),
            )
          ],
          emptyWidget: getEmptyWidget());
    });
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return SAppBarSearch(
      hintText: "请输入退款单号或者订单编号",
      showLeading: false,
      hideCancel: false,
      autoFocus: true,
      onSearch: (value) {
        if (value.isEmpty) {
          showToast("请输入搜索内容");
          return;
        }
        _listModel.keyword = value;
        _listModel.requestListModel(true);
      },
    );
  }

  @override
  String getTitleName() {
    return "超无忧申请";
  }

  Widget buildSearchButton() {
    return GestureDetector(
      onTap: () {
        showToast("search");
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_search.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  buildOrderItem(int index) {
    if (_listModel.list == null || index >= (_listModel.list?.length ?? 0)) {
      return Container();
    }
    var itemData = _listModel.list![index];
    return GestureDetector(
        onTap: () {
          if (itemData.auditState == 6 || itemData.auditState == 7) {
            //审核中、OA已驳回
            Navigator.of(context).pushNamed("/order_moderation_cwy_page",
                arguments: {"orderNo": '${itemData.id}'});
          } else {
            Navigator.of(context).pushNamed("/order_detail_refund_cwy_page",
                arguments: {"orderNo": '${itemData.id}'});
          }
        },
        child: ApplyHistoryOrderListItemWidget(
            _listModel.smallImageHost, itemData));
  }

  Widget? getEmptyWidget() {
    if (_listModel.isSuccess == null) {
      return null;
    }
    if (_listModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _listModel.requestListModel(true);
      });
    }

    if ((_listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }
}

class OrderListModel extends ChangeNotifier {
  var _isDisposed = false;
  bool? isLastPage = false;
  int pageNum = 1;
  bool forceRefresh = false;
  List<ApplyOrderListItemData>? list;
  String? smallImageHost;
  bool? isSuccess;
  String? keyword;

  final String? status; // 订单状态

  final EasyRefreshController _refreshController;
  final ScrollController _scrollController;

  OrderListModel(this._scrollController, this._refreshController, this.status);

  requestSmallImageHost() {
    if (smallImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          smallImageHost = value["image_host"];
          notifyListeners();
        }
      });
    }
  }

  void requestNormalList(Map<String, String?> paramsMap) {
    NetworkV2<ApplyOrderManageData>(ApplyOrderManageData())
        .requestDataV2("worries/orderList",
            method: RequestMethod.GET, parameters: paramsMap)
        .then((value) {
      handleResult(value.isSuccess, value.getData());
    });
  }

  requestListModel(bool isRefresh) async {
    if (isRefresh) {
      pageNum = 1;
      forceRefresh = true;
      list?.clear();
    } else {
      pageNum += 1;
      forceRefresh = false;
    }
    EasyLoading.show(status: "加载中");
    var paramsMap = buildParamsMap();
    requestNormalList(paramsMap);
  }

  void handleResult(bool? isSuccess, ApplyOrderManageData? data) {
    EasyLoading.dismiss();
    if (!_isDisposed) {
      if (isSuccess == true) {
        this.isSuccess = isSuccess;
        if (data != null) {
          var tempList;
          isLastPage = data.isLastPage ?? true;
          tempList = data.list;
          tempList.forEach((item){
            print("查询出来的订单编号：${item.orderNo}");
          });
          if (forceRefresh) {
            this.list = tempList;
            _scrollController.jumpTo(0);
          } else {
            this.list?.addAll(tempList ?? []);
          }
        } else {
          isLastPage = true;
        }
        _refreshController.finishRefresh();
        _refreshController.finishLoad(noMore: isLastPage == true);
        notifyListeners();
      } else {
        _refreshController.finishRefresh();
        _refreshController.finishLoad();
      }
    }
  }

  Map<String, String?> buildParamsMap() {
    var params = Map<String, String?>();
    params["pageNum"] = pageNum.toString();
    params["pageSize"] = "10";
    params["status"] = status;
    params["keyword"] = keyword;
    return params;
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}
