// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'control_goodset_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ControlGoodSetListData _$ControlGoodSetListDataFromJson(
    Map<String, dynamic> json) {
  return ControlGoodSetListData()
    ..skuCollectCode = json['skuCollectCode']
    ..skuCollectName = json['skuCollectName']
    ..downRule = json['downRule']
    ..payGmv = json['payGmv']
    ..skuNum = json['skuNum']
    ..skuList = (json['skuList'] as List<dynamic>?)
        ?.map((e) => ControlGoodImgItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ControlGoodSetListDataToJson(
        ControlGoodSetListData instance) =>
    <String, dynamic>{
      'skuCollectCode': instance.skuCollectCode,
      'skuCollectName': instance.skuCollectName,
      'downRule': instance.downRule,
      'payGmv': instance.payGmv,
      'skuNum': instance.skuNum,
      'skuList': instance.skuList,
    };

ControlGoodImgItemData _$ControlGoodImgItemDataFromJson(
    Map<String, dynamic> json) {
  return ControlGoodImgItemData()
    ..imgUrl = json['imgUrl']
    ..skuPrice = json['skuPrice'];
}

Map<String, dynamic> _$ControlGoodImgItemDataToJson(
        ControlGoodImgItemData instance) =>
    <String, dynamic>{
      'imgUrl': instance.imgUrl,
      'skuPrice': instance.skuPrice,
    };
