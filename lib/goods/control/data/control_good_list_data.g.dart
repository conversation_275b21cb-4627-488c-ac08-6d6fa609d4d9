// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'control_good_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ControlGoodListPageData _$ControlGoodListPageDataFromJson(
    Map<String, dynamic> json) {
  return ControlGoodListPageData()
    ..lastPage = json['lastPage']
    ..total = json['total']
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => ControlGoodListData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$ControlGoodListPageDataToJson(
        ControlGoodListPageData instance) =>
    <String, dynamic>{
      'lastPage': instance.lastPage,
      'total': instance.total,
      'rows': instance.rows,
    };

ControlGoodListData _$ControlGoodListDataFromJson(Map<String, dynamic> json) {
  return ControlGoodListData()
    ..skuId = json['skuId']
    ..skuName = json['skuName']
    ..skuSpec = json['skuSpec']
    ..shopName = json['shopName']
    ..skuPrice = json['skuPrice']
    ..skuStore = json['skuStore']
    ..imgUrl = json['imgUrl'];
}

Map<String, dynamic> _$ControlGoodListDataToJson(
        ControlGoodListData instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'skuName': instance.skuName,
      'skuSpec': instance.skuSpec,
      'shopName': instance.shopName,
      'skuPrice': instance.skuPrice,
      'skuStore': instance.skuStore,
      'imgUrl': instance.imgUrl,
    };
