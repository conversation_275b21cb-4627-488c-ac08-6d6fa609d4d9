import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'control_goodset_list_data.g.dart';

@JsonSerializable()
class ControlGoodSetListData extends BaseModelV2<ControlGoodSetListData> {
  /// 商品集编码
  dynamic skuCollectCode;

  /// 商品集名称
  dynamic skuCollectName;

  /// 商品集掉落规则
  dynamic downRule;

  /// 本月实付GMV
  dynamic payGmv;

  /// 商品数量
  dynamic skuNum;

  List<ControlGoodImgItemData>? skuList;

  ControlGoodSetListData();

  @override
  ControlGoodSetListData fromJsonMap(Map<String, dynamic> json) {
    return _$ControlGoodSetListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ControlGoodSetListDataToJson(this);
  }
}

@JsonSerializable()
class ControlGoodImgItemData extends BaseModelV2<ControlGoodImgItemData> {
  dynamic imgUrl;
  dynamic skuPrice;

  ControlGoodImgItemData();

  factory ControlGoodImgItemData.fromJson(Map<String, dynamic> json) {
    return _$ControlGoodImgItemDataFromJson(json);
  }

  @override
  ControlGoodImgItemData fromJsonMap(Map<String, dynamic> json) {
    return _$ControlGoodImgItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ControlGoodImgItemDataToJson(this);
  }
}
