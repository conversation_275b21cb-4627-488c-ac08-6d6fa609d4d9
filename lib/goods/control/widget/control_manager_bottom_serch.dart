import 'package:flutter/material.dart';

class ControlManagerBottomSearch extends StatelessWidget {
  final VoidCallback searchClick;

  ControlManagerBottomSearch({required this.searchClick});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 54,
      color: Color(0xFFFFFFFF),
      child: Column(
        children: [
          Container(
            height: 0.5,
            color: Color(0xFFF7F7F8),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.fromLTRB(15, 10, 15, 10),
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: this.searchClick,
                child: Container(
                  decoration: BoxDecoration(
                    color: Color(0xFFF5F5F5),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Row(
                    children: [
                      SizedBox(width: 6),
                      SizedBox(
                        width: 22,
                        height: 22,
                        child: Image.asset(
                            "assets/images/titlebar/icon_search_bar.png"),
                      ),
                      SizedBox(width: 3),
                      Text(
                        '请输入商品名/厂家名进行搜索',
                        style: TextStyle(
                          color: Color(0xFF676773),
                          fontSize: 13,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
