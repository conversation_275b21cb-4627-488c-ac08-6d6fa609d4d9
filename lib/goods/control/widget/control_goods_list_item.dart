import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/authorize_goods_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/util/authorize_type.dart';
import 'package:flutter/material.dart';

// ignore: must_be_immutable
class ControlGoodsListItemWidget extends StatelessWidget {
  final AuthorizeGoodsItemData itemData;
  final ValueNotifier<bool> batchOperationNotifier;
  final VoidCallback jumpCallback;
  final AuthorizeType authorizeType;
  final OperationCallback operationCallback;

  ValueNotifier<bool> selectNotifier = new ValueNotifier(false);

  ControlGoodsListItemWidget(this.authorizeType, this.itemData,
      this.batchOperationNotifier, this.jumpCallback, this.operationCallback);

  @override
  Widget build(BuildContext context) {
    print("guan build");
    selectNotifier = new ValueNotifier(itemData.isSelected ?? false);
    return ValueListenableBuilder<bool>(
        valueListenable: batchOperationNotifier,
        builder: (context, value, child) {
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              handleItemClick(value);
            },
            child: buildListItemWidget(value),
          );
        });
  }

  void handleItemClick(bool isBatchOperation) {
    print("guan handleItemClick 0 ${itemData.isSelected}");
    if (batchOperationNotifier.value) {
      // 批量操作
      switch (authorizeType) {
        case AuthorizeType.Authorized:
          itemData.isSelected = !(itemData.isSelected ?? false);
          break;
        case AuthorizeType.Unauthorized:
          if (itemData.grantStatus == false) {
            return;
          } else {
            itemData.isSelected = !(itemData.isSelected ?? false);
          }
          break;
      }
      print("guan handleItemClick 1 ${itemData.isSelected}");
      selectNotifier.value = itemData.isSelected ?? false;
    } else {
      // 非批量操作
      jumpCallback();
    }
  }

  Widget buildListItemWidget(bool isBatchOperation) {
    return Container(
      margin: EdgeInsets.only(left: 10, right: 10, bottom: 10),
      child: Container(
        padding: EdgeInsets.only(left: 10, right: 10, top: 12, bottom: 10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(6), color: Colors.white),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              width: double.infinity,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Visibility(
                      visible: isBatchOperation,
                      child: buildSelectWidget(itemData)),
                  SizedBox(
                    width: 9,
                  ),
                  Container(
                    width: 90,
                    height: 90,
                    child: Stack(
                      children: [
                        ImageWidget(
                            url: itemData.imgUrl?.toString() ?? "",
                            w: 90,
                            h: 90,
                            defImagePath:
                                "assets/images/order/icon_load_failed.png"),
                        Positioned(
                          bottom: 0,
                          left: 0,
                          right: 0,
                          child: Visibility(
                            visible: getOperationType() == OperationType.Disable,
                            child: Container(
                              height: 20,
                              decoration: BoxDecoration(
                                  color: const Color(0x66000000),
                                  borderRadius: BorderRadius.only(
                                      topLeft: Radius.circular(4),
                                      topRight: Radius.circular(4))),
                              alignment: Alignment.center,
                              child: Text(
                                "不可授权",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                    fontWeight: FontWeight.normal),
                              ),
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Expanded(
                      child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        itemData.skuName ?? "",
                        style: TextStyle(
                            color: Color(0xff333333),
                            fontWeight: FontWeight.w500,
                            fontSize: 16),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Row(
                        children: [
                          Text(
                            itemData.skuSpec,
                            style: TextStyle(
                                color: Color(0xff9494a6),
                                fontWeight: FontWeight.normal,
                                fontSize: 12),
                          ),
                          Container(
                            margin: EdgeInsets.symmetric(
                                vertical: 1, horizontal: 5),
                            color: Color(0xffdddddd),
                            width: 0.5,
                            height: 8,
                          ),
                          Text(
                            itemData.skuStore?.toString() ?? "",
                            style: TextStyle(
                                color: Color(0xff9494a6),
                                fontWeight: FontWeight.normal,
                                fontSize: 12),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      RichText(
                        textAlign: TextAlign.end,
                        text: TextSpan(
                            text: "¥",
                            style: TextStyle(
                                color: Color(0xfffd4c4c),
                                fontSize: 10,
                                fontWeight: FontWeight.w500),
                            children: [
                              TextSpan(
                                text: itemData.skuPrice?.toString() ?? "",
                                style: TextStyle(
                                    color: Color(0xfffd4c4c),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w500),
                              )
                            ]),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Text(
                        itemData.shopName ?? "",
                        style: TextStyle(
                            color: Color(0xff9494a6),
                            fontWeight: FontWeight.normal,
                            fontSize: 12),
                      ),
                      SizedBox(
                        height: 15,
                      )
                    ],
                  ))
                ],
              ),
            ),
            Container(
              color: Color(0xfff6f6f6),
              height: 1,
            ),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                handleItemOperationClick();
              },
              child: Container(
                margin: EdgeInsets.only(left: 10, top: 10),
                padding: EdgeInsets.symmetric(vertical: 4, horizontal: 7),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                        color: getOperationType() == OperationType.Disable
                            ? Color(0xffd1d1d6)
                            : Color(0xff00b377),
                        width: 0.5)),
                child: Text(
                  getOperationText(),
                  style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.normal,
                      color: getOperationType() == OperationType.Disable
                          ? Color(0xffd1d1d6)
                          : Color(0xff00b377)),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildSelectWidget(AuthorizeGoodsItemData itemData) {
    return Container(
        width: 18,
        height: 18,
        child: ValueListenableBuilder<bool>(
          builder: (context, value, child) {
            SelectedState currentState = SelectedState.Normal;
            if (itemData.grantStatus == false) {
              currentState = SelectedState.Disable;
            } else if (itemData.isSelected == true) {
              currentState = SelectedState.Selected;
            } else {
              currentState = SelectedState.Normal;
            }
            print("guan buildSelectWidget $value,$currentState");
            return Stack(
              children: [
                Visibility(
                  visible: currentState == SelectedState.Disable,
                  child: Container(
                    decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border:
                            Border.all(width: 0.5, color: Color(0xfffafafb))),
                  ),
                ),
                Visibility(
                    visible: currentState == SelectedState.Selected,
                    child: Image.asset(
                      "assets/images/customer/customer_sku_collect_check_selected.png",
                      width: 18,
                      height: 18,
                    )),
                Visibility(
                    visible: currentState == SelectedState.Normal,
                    child: Image.asset(
                      "assets/images/customer/customer_sku_collect_check_unselected.png",
                      width: 18,
                      height: 18,
                    ))
              ],
            );
          },
          valueListenable: selectNotifier,
        ));
  }

  void handleItemOperationClick() {
    operationCallback.call(getOperationType());
  }

  String getOperationText() {
    switch (getOperationType()) {
      case OperationType.Authorize:
        return "授权";
      case OperationType.CancelAuthorize:
        return "取消授权";
      case OperationType.Disable:
        return "查看原因";
    }
  }

  OperationType getOperationType() {
    if (authorizeType == AuthorizeType.Authorized) {
      return OperationType.CancelAuthorize;
    } else if (itemData.grantStatus == false) {
      return OperationType.Disable;
    } else {
      return OperationType.Authorize;
    }
  }
}

typedef OperationCallback = void Function(OperationType operationType);

enum OperationType { CancelAuthorize, Disable, Authorize }

enum SelectedState { Disable, Selected, Normal }
