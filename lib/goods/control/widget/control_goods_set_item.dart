import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/common/popover/common_popover.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/data/control_goodset_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';

class ControlGoodsSetItem extends StatelessWidget {
  final String imageHost;

  final GlobalKey _anchorKey = GlobalKey();

  final ControlGoodSetListData data;

  ControlGoodsSetItem({required this.imageHost, required this.data});

  @override
  Widget build(BuildContext context) {
    List<ControlGoodImgItemData> skuList = this.data.skuList ?? [];
    bool numberFlag = Permission.isPermission('productControlNumber');
    bool monthFlag = Permission.isPermission('productActualMonth');
    return Container(
      color: Color(0xFFF7F7F8),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(6),
        ),
        padding: EdgeInsets.fromLTRB(10, 12, 10, 12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  '${this.data.skuCollectName ?? "--"}',
                  style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
                GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    this.showPopoverTips(context);
                  },
                  child: IntrinsicHeight(
                    key: _anchorKey,
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.only(top: 2, bottom: 2),
                          child: Text(
                            '（掉落规则）',
                            style: TextStyle(
                                color: Color(0xFF676773),
                                fontSize: 12,
                                fontWeight: FontWeight.w400),
                          ),
                        ),
                        Image.asset(
                          'assets/images/funnel/funnel_tips_icon.png',
                          width: 12,
                          height: 12,
                        ),
                      ],
                    ),
                  ),
                ),
                Spacer(),
                Text(
                  '商品数量：${numberFlag ? (this.data.skuNum ?? 0) : '--'}',
                  style: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF676773),
                      fontWeight: FontWeight.w400),
                ),
                Image.asset(
                  'assets/images/base/icon_arrow_right.png',
                  width: 12,
                  height: 12,
                )
              ],
            ),
            Container(
              padding: EdgeInsets.only(left: 5, right: 5, top: 12, bottom: 12),
              child: Row(
                children: [
                  Expanded(
                    child: Visibility(
                      visible: skuList.length > 0,
                      child: _ControlGoodsSetGoodImageItem(
                        imageHost: this.imageHost,
                        imageData: skuList.length > 0
                            ? skuList[0]
                            : ControlGoodImgItemData(),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Visibility(
                      visible: skuList.length > 1,
                      child: _ControlGoodsSetGoodImageItem(
                        imageHost: this.imageHost,
                        imageData: skuList.length > 1
                            ? skuList[1]
                            : ControlGoodImgItemData(),
                      ),
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Visibility(
                      visible: skuList.length > 2,
                      child: _ControlGoodsSetGoodImageItem(
                        imageHost: this.imageHost,
                        imageData: skuList.length > 2
                            ? skuList[2]
                            : ControlGoodImgItemData(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Container(
              color: Color(0xFFF6F6F6),
              height: 1,
            ),
            SizedBox(height: 12),
            Row(
              children: [
                Text(
                  '本月实付金额:',
                  style: TextStyle(
                      color: Color(0xFF676773),
                      fontSize: 12,
                      fontWeight: FontWeight.w400),
                ),
                Spacer(),
                Text(
                  monthFlag ? '¥${ (this.data.payGmv ?? "--")}'  : '--',
                  style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 10,
                      fontWeight: FontWeight.w500),
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  void showPopoverTips(BuildContext context) {
    showTipsPopover(
        context: context,
        anchorKey: this._anchorKey,
        content: Text(
          this.data.downRule ?? "--",
          style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 14),
        ));
  }
}

class _ControlGoodsSetGoodImageItem extends StatelessWidget {
  final String imageHost;
  final ControlGoodImgItemData imageData;
  _ControlGoodsSetGoodImageItem(
      {required this.imageHost, required this.imageData});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: IntrinsicWidth(
        child: Stack(
          children: [
            AspectRatio(
              aspectRatio: 1,
              child: ImageWidget(
                  url: "${imageData.imgUrl}",
                  defImagePath: "assets/images/order/icon_load_failed.png"),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              top: 0,
              child: Container(
                color: Color(0x19D8D8D8),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                decoration: BoxDecoration(
                    color: Color(0x99000000),
                    borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(4),
                        topRight: Radius.circular(4))),
                alignment: AlignmentDirectional.center,
                child: Text(
                  '单价:¥${this.imageData.skuPrice ?? 0.00}',
                  style: TextStyle(
                      color: Color(0xFFFFFFFF),
                      fontSize: 12,
                      fontWeight: FontWeight.w400),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
