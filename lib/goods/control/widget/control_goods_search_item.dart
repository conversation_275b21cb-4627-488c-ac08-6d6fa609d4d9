import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/data/control_good_list_data.dart';
import 'package:flutter/material.dart';

class ControlGoodsSearchItem extends StatelessWidget {
  final ControlGoodListData data;

  ControlGoodsSearchItem({required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6),
          color: Color(0xFFFFFFFF),
        ),
        child: IntrinsicHeight(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Stack(
                children: [
                  ImageWidget(
                      url: "${data.imgUrl}",
                      w: 85,
                      h: 85,
                      defImagePath: "assets/images/order/icon_load_failed.png"),
                  Positioned(
                    top: 0,
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: Container(
                      color: Color(0x19D8D8D8),
                    ),
                  ),
                ],
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(left: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${this.data.skuName}',
                        style: TextStyle(
                            color: Color(0xFF333333),
                            fontSize: 16,
                            fontWeight: FontWeight.w500),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      SizedBox(height: 4),
                      Row(
                        children: [
                          Text(
                            '${this.data.skuSpec}',
                            style: TextStyle(
                                color: Color(0xFF9494A6), fontSize: 12),
                          ),
                          Container(
                            margin: EdgeInsets.only(left: 5, right: 5),
                            color: Color(0xFF9494A6),
                            width: 0.5,
                            height: 8,
                          ),
                          Text(
                            '库存${this.data.skuStore}',
                            style: TextStyle(
                                color: Color(0xFF9494A6), fontSize: 12),
                          ),
                        ],
                      ),
                      SizedBox(height: 10),
                      RichText(
                        text: TextSpan(
                            text: '¥',
                            style: TextStyle(
                                color: Color(0xFFFD4C4C),
                                fontSize: 10,
                                fontWeight: FontWeight.w600),
                            children: [
                              TextSpan(
                                text: "${this.data.skuPrice}",
                                style: TextStyle(
                                    color: Color(0xFFFD4C4C),
                                    fontSize: 16,
                                    fontWeight: FontWeight.w600),
                              )
                            ]),
                      ),
                      SizedBox(height: 4),
                      Text(
                        '${this.data.shopName ?? '--'}',
                        style:
                            TextStyle(color: Color(0xFF292933), fontSize: 12),
                      )
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
