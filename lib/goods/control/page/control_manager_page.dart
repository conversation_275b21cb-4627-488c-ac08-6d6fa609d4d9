import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/data/control_goodset_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/widget/control_goods_set_item.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/widget/control_manager_bottom_serch.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ControlManagerPage extends BasePage {
  final String? merchantId;

  final dynamic customerId;

  final bool isSubPage;

  ControlManagerPage(
      {this.merchantId, this.customerId, this.isSubPage = false});

  @override
  BaseState<StatefulWidget> initState() {
    return ControlManagerPageState();
  }
}

class ControlManagerPageState extends BaseState<ControlManagerPage> {
  EasyRefreshController _controller = EasyRefreshController();

  String imageHost = "";

  List<ControlGoodSetListData> dataSource = [];

  @override
  void initState() {
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        imageHost = value["image_host"] ?? "";
        if (mounted) {
          setState(() {});
        }
      }
    });

    this.requestListData();

    super.initState();
  }

  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: Column(
        children: [
          Visibility(
            visible: widget.merchantId == null,
            child: ControlManagerBottomSearch(
              searchClick: jumpToSearchPage,
            ),
          ),
          buildListView(),
        ],
      ),
    );
  }

  Widget buildListView() {
    return Expanded(
      child: EasyRefresh(
        controller: this._controller,
        onRefresh: () async {
          this.requestListData();
        },
        child: ListView.builder(
          itemCount: this.dataSource.length,
          itemBuilder: (context, index) {
            print(index);
            ControlGoodSetListData data = this.dataSource[index];
            return GestureDetector(
              onTap: () {
                this.selectedItemAction(
                    data.skuCollectCode, data.skuCollectName);
              },
              behavior: HitTestBehavior.opaque,
              child: ControlGoodsSetItem(
                imageHost: this.imageHost,
                data: data,
              ),
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if ((this.dataSource.length) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void selectedItemAction(String skuCollectCode, String skuCollectName) {
    if (widget.merchantId == null) {
      Navigator.of(context).pushNamed(
          '/control_goodset_detail?skuCollectCode=$skuCollectCode&skuCollectName=$skuCollectName&customerId=${widget.customerId}');
    } else if (widget.merchantId == "-1") {
      showToast("未注册客户无法进行商品授权");
    } else {
      Navigator.of(context).pushNamed(
          '/control_authorize_goods_page?skuCollectCode=$skuCollectCode&merchantId=${widget.merchantId}&customerId=${widget.customerId}');
    }
  }

  void requestListData() async {
    if (this.dataSource.length <= 0) {
      showLoadingDialog();
    }
    String requestPath = 'skuCollectMgr/list';

    Map<String, dynamic> params = {};
    if (widget.merchantId != null) {
      params['merchantId'] = widget.merchantId;
      requestPath = 'skuCollectMgr/merchant/list';
    }

    var result =
        await NetworkV2<ControlGoodSetListData>(ControlGoodSetListData())
            .requestDataV2(
      requestPath,
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      _controller.finishRefresh();
      if (result.isSuccess == true) {
        List<ControlGoodSetListData>? source = result.getListData();
        if (source != null) {
          print(source.length);
          this.dataSource = source;
          setState(() {});
        }
      }
    }
  }

  // 跳转搜索页面
  void jumpToSearchPage() {
    XYYContainer.open('/control_search_history?searchType=1');
  }

  @override
  String getTitleName() {
    return "控销管理";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    if (widget.isSubPage) {
      return null;
    }
    return CommonTitleBar(
      this.getTitleName(),
    );
  }
}
