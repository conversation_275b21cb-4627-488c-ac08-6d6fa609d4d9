import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_authorize_merchant_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/util/authorize_type.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class ControlAuthorizeMerchantParentPage extends BasePage {
  final String? skuId;
  final String? skuCollectCode;

  ControlAuthorizeMerchantParentPage(this.skuId, this.skuCollectCode);

  @override
  BaseState<StatefulWidget> initState() {
    return ControlAuthorizeMerchantParentState();
  }
}

class ControlAuthorizeMerchantParentState
    extends BaseState<ControlAuthorizeMerchantParentPage>
    with SingleTickerProviderStateMixin {
  List _tabTitles = ["授权客户", "未授权客户"];

  TabController? _tabController;
  ValueNotifier<String?> dataRangeNotifier = ValueNotifier(null);

  // 判断是否是M级账号
  bool isManagerLevel = true;

  // 数据范围BD名
  String? dataRangeName;

  String? searchUserId;

  void _requestRoleType() {
    UserInfoUtil.getUserInfo().then((value) {
      isManagerLevel = value != null &&
          (value.roleType == UserInfoUtil.TYPE_BDM ||
              value.roleType == UserInfoUtil.TYPE_GJR_BDM);
      if (!isManagerLevel) {
        searchUserId = value?.sysUserId ?? "";
        dataRangeNotifier.value = searchUserId;
      }
    });
  }

  @override
  void onCreate() {
    super.onCreate();
    _tabController = new TabController(length: _tabTitles.length, vsync: this);
    _requestRoleType();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
        child: TabBarView(
      controller: _tabController,
      children: [
        ControlAuthorizeMerchantPage(widget.skuId, widget.skuCollectCode,
            AuthorizeType.Authorized, dataRangeNotifier, openDataRangePage),
        ControlAuthorizeMerchantPage(widget.skuId, widget.skuCollectCode,
            AuthorizeType.Unauthorized, dataRangeNotifier, openDataRangePage),
      ],
    ));
  }

  @override
  String getTitleName() {
    return "";
  }

  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      titleWidget: buildTitleWidget(),
      rightButtons: [buildDataRangeWidget()],
    );
  }

  Widget buildDataRangeWidget() {
    return ValueListenableBuilder(
        valueListenable: dataRangeNotifier,
        builder: (context, value, child) {
          print("guan ValueListenableBuilder $value");
          if (!isManagerLevel) {
            return Container(
              width: 90,
            );
          }
          return Container(
            width: 90,
            child: GestureDetector(
              onTap: () {
                // 数据范围
                openDataRangePage();
              },
              child: Container(
                padding: EdgeInsets.only(right: 15),
                constraints: BoxConstraints(maxWidth: 90),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Expanded(
                      child: Text(
                        this.dataRangeName ?? "全部",
                        textAlign: TextAlign.right,
                        style: TextStyle(
                            color: Color(0xFF00B377),
                            fontSize: 16,
                            fontWeight: FontWeight.w500),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(width: 5),
                    Image.asset(
                      'assets/images/pop_data/pop_statistics_right.png',
                      width: 12,
                      height: 12,
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  void openDataRangePage() {
    XYYContainer.open(
        'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=false',
        callback: (params) {
      try {
        if (!(params?.containsKey("name") ?? false)) {
          return;
        } else if (params?["isgroup"] == 'true') {
          // 不能选择组织结构
          showToast("请选中BD后查看详细数据");
          return;
        }
        //通知下级页面刷新
        this.dataRangeName = params?["name"];
        if (dataRangeName == "全部") {
          searchUserId = null;
        } else {
          searchUserId = params?['id'];
        }
        this.dataRangeNotifier.value = searchUserId;
      } catch (e) {}
    });
  }

  Widget buildTitleWidget() {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.bottomCenter,
      margin: EdgeInsets.only(left: 25),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Color(0xFF292933),
        indicator: TabCustomIndicator(
            wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
        labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        unselectedLabelColor: Color(0xFF676733),
        unselectedLabelStyle:
            TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
        tabs: _tabTitles.map((e) => Tab(text: e)).toList(),
      ),
    );
  }
}
