import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_filter_location_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_location_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/data/control_good_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_goods_search_history.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/widget/control_goods_search_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_root_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ControlGoodsResultPage extends BasePage {
  final String keyword;

  /// 商品集编码
  final String? skuCollectCode;

  // 已选择的区域名称
  final String? branchName;

  // 已选择的区域编码
  final dynamic? branchCode;

  ControlGoodsResultPage({
    required this.keyword,
    this.skuCollectCode,
    this.branchName,
    this.branchCode,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return ControlGoodsResultState();
  }
}

class ControlGoodsResultState extends BaseState<ControlGoodsResultPage> {
  String? searchKeyword;

  TextEditingController _controller = TextEditingController();

  FocusNode _focusNode = FocusNode();

  int page = 1;

  bool isLastPage = false;

  List<ControlGoodListData> dataSource = [];

  EasyRefreshController _refreshController = EasyRefreshController();

  // 筛选区域数据
  List<CommodityFilterLocationData>? locationSource;
  GlobalKey rightKey = GlobalKey();

  // 筛选参数
  String locationTitle = '';
  dynamic provinceCode = -1;

  @override
  void initState() {
    super.initState();
    this.searchKeyword = widget.keyword;
    _controller.text = widget.keyword;
    if (widget.branchCode != null && widget.branchName != null) {
      this.locationTitle = widget.branchName!;
      if (widget.branchCode is int) {
        this.provinceCode = widget.branchCode;
      } else if (widget.branchCode is String) {
        this.provinceCode = int.tryParse(widget.branchCode) ?? -1;
      }
    }
    this.refreshListData();
    this.requestLocationData();
  }

  @override
  void dispose() {
    this._controller.dispose();
    this._refreshController.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: EasyRefresh(
        controller: this._refreshController,
        onRefresh: () async {
          this.refreshListData();
        },
        onLoad: this.isLastPage ? null : this.loadMoreListData,
        child: ListView.builder(
          itemCount: this.dataSource.length,
          itemBuilder: (context, index) {
            return GestureDetector(
              onTap: () {
                this.jumpToGoodDetail(index);
              },
              behavior: HitTestBehavior.opaque,
              child: ControlGoodsSearchItem(
                data: this.dataSource[index],
              ),
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  void jumpToGoodDetail(int index) {
    var data = this.dataSource[index];
    // 全国权限无法跳转商品详情， 需要选择单独区域
    if (this.provinceCode == -1 || this.provinceCode == "-1") {
      showToast('请先选择区域');
      return;
    }
    String router =
        "xyy://crm-app.ybm100.com/good_detail?id=${data.skuId}&isFromCustomer=1";
    if (this.provinceCode != -1) {
      router = router + "&branchCode=XS${this.provinceCode}";
    }
    XYYContainer.open(router);
  }

  Widget? getEmptyWidget() {
    if ((this.dataSource.length) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshListData() {
    this.page = 1;
    this.requestListData();
  }

  Future<void> loadMoreListData() async {
    this.requestListData();
  }

  void requestListData() async {
    if (this.dataSource.length <= 0) {
      showLoadingDialog();
    }

    Map<String, dynamic> params = {
      "searchKey": this.searchKeyword,
      "limit": 10,
      "offset": this.page
    };
    if (widget.skuCollectCode != null) {
      params['skuCollectCode'] = widget.skuCollectCode;
    }

    var result =
        await NetworkV2<ControlGoodListPageData>(ControlGoodListPageData())
            .requestDataV2(
      'skuCollectMgr/skuList',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        List<ControlGoodListData>? source = result.getData()?.rows;
        if (source != null) {
          if (this.page == 1) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          this.page += 1;
        }
        setState(() {});
      }
      this._refreshController.finishRefresh();
      this._refreshController.resetLoadState();
      this
          ._refreshController
          .finishLoad(noMore: result.getData()?.lastPage == true);
      this.isLastPage = result.getData()?.lastPage == true;
    }
  }

  /// 区域筛选项
  void showLocationPopup() async {
    if (this.locationSource == null) {
      await requestLocationData();
    }
    // 无区域数据则不展示弹窗
    if (this.locationSource?.isEmpty == true) {
      return;
    }
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityLocationFilterPopup(
          distance: distance,
          selectId: this.provinceCode,
          itemChanged: (name, param) {
            setState(() {
              this.provinceCode = param['provinceCode'];
              this.locationTitle = name;
            });
          },
          source: this.locationSource ?? [],
        );
      },
      key: this.rightKey,
    );
  }

  Future<BaseRootModel<CommodityFilterLocationData>>
      requestLocationData() async {
    var result = await NetworkV2<CommodityFilterLocationData>(
            CommodityFilterLocationData())
        .requestDataV2(
      'skuRank/getProvince',
      method: RequestMethod.GET,
    );
    if (result.isSuccess == true) {
      this.locationSource = result.getListData() ?? [];
      if ((widget.branchName == null || widget.branchCode == null) &&
          this.locationSource!.length > 1) {
        this.provinceCode = this.locationSource?.first.provinceCode ?? -1;
        this.locationTitle = this.locationSource?.first.provinceName ?? "";
      }
      setState(() {});
    } else {
      showToast(result.errorMsg ?? '获取区域错误');
    }
    return result;
  }

  @override
  String getTitleName() {
    return "搜索结果";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonSearchBar(
      hintText: "请输入商品名/厂家名进行搜索",
      showLeading: true,
      showClear: false,
      hideCancel: true,
      suffix: null,
      focusNode: this._focusNode,
      controller: this._controller,
      actions: [
        (this.locationSource ?? []).length > 1
            ? this.locationWidget()
            : Container(width: 15)
      ],
      onTap: () async {
        this._focusNode.unfocus();
        dynamic result =
            await Navigator.of(context).push(CustomSearchResultRoute(
          builder: (context) => ControlGoodsSearchHistoryPage(
            searchType: 1,
            keyword: this._controller.text,
            skuCollectCode: widget.skuCollectCode,
          ),
        ));
        if (result != null) {
          this.searchKeyword = "$result";
          this._controller.text = this.searchKeyword ?? "";
          this._refreshController.callRefresh();
        }
      },
    );
  }

  Widget locationWidget() {
    return Container(
      padding: EdgeInsets.only(left: 10, right: 10),
      key: this.rightKey,
      child: GestureDetector(
        onTap: () {
          this.showLocationPopup();
        },
        child: Row(
          children: [
            Image.asset(
              'assets/images/commodity/commodity_location_drop.png',
              width: 10,
              height: 13,
            ),
            SizedBox(width: 4),
            ConstrainedBox(
              constraints: BoxConstraints(maxWidth: 60),
              child: Text(
                this.locationTitle,
                style: TextStyle(
                  color: Color(0xFF292933),
                  fontSize: 14,
                ),
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
              ),
            )
          ],
        ),
      ),
    );
  }
}

class CustomSearchResultRoute extends MaterialPageRoute {
  CustomSearchResultRoute({required WidgetBuilder builder})
      : super(builder: builder);

  @override
  Duration get transitionDuration => Duration(milliseconds: 0);

  Duration get reverseTransitionDuration => Duration(milliseconds: 0);
}
