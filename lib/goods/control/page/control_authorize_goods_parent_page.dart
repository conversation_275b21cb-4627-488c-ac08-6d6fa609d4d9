import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_authorize_goods_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/util/authorize_type.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:flutter/material.dart';

class ControlAuthorizeGoodsParentPage extends BasePage {
  final String? skuCollectCode;
  final String? merchantId;
  final dynamic customerId;

  ControlAuthorizeGoodsParentPage(
    this.skuCollectCode,
    this.merchantId,
    this.customerId,
  );

  @override
  BaseState<StatefulWidget> initState() {
    return ControlAuthorizeGoodsParentPageState();
  }
}

class ControlAuthorizeGoodsParentPageState
    extends BaseState<ControlAuthorizeGoodsParentPage>
    with SingleTickerProviderStateMixin {
  List _tabTitles = ["授权商品", "未授权商品"];

  TabController? _tabController;
  ValueNotifier<bool> batchOperationStatusNotifier = ValueNotifier<bool>(false);

  @override
  void onCreate() {
    super.onCreate();
    _tabController = new TabController(length: _tabTitles.length, vsync: this);
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
        child: TabBarView(
      controller: _tabController,
      children: [
        ControlAuthorizeGoodsPage(
            AuthorizeType.Authorized,
            widget.skuCollectCode,
            widget.merchantId,
            batchOperationStatusNotifier,
            widget.customerId),
        ControlAuthorizeGoodsPage(
            AuthorizeType.Unauthorized,
            widget.skuCollectCode,
            widget.merchantId,
            batchOperationStatusNotifier,
            widget.customerId)
      ],
    ));
  }

  @override
  String getTitleName() {
    return "";
  }

  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      titleWidget: buildTitleWidget(),
      rightButtons: [buildBatchOperationWidget()],
    );
  }

  Widget buildBatchOperationWidget() {
    return ValueListenableBuilder<bool>(
        valueListenable: batchOperationStatusNotifier,
        builder: (context, value, child) {
          return GestureDetector(
            behavior: HitTestBehavior.opaque,
            onTap: () {
              batchOperationStatusNotifier.value = !value;
            },
            child: Container(
              height: 44,
              width: 90,
              alignment: Alignment.center,
              child: Text(
                value ? "退出操作" : "批量操作",
                style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: value ? Color(0xff00b377) : Color(0xff676773)),
              ),
            ),
          );
        });
  }

  Widget buildTitleWidget() {
    return Container(
      width: double.maxFinite,
      alignment: Alignment.bottomCenter,
      margin: EdgeInsets.only(left: 25),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        labelColor: Color(0xFF292933),
        indicator: TabCustomIndicator(
            wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
        labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
        unselectedLabelColor: Color(0xFF676733),
        unselectedLabelStyle:
            TextStyle(fontSize: 14, fontWeight: FontWeight.normal),
        tabs: _tabTitles.map((e) => Tab(text: e)).toList(),
      ),
    );
  }
}
