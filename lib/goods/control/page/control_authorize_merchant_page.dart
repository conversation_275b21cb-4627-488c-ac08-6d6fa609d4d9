import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/authorize_merchant_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/util/authorize_type.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_merchant_check.dart';
import 'package:XyyBeanSproutsFlutter/utils/jump_page_utils.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ControlAuthorizeMerchantPage extends BasePage {
  final AuthorizeType authorizeType;
  final ValueNotifier<String?> dataRangeNotifier;
  final Function? openDataRangeFunction;
  final String? skuId;
  final String? skuCollectCode;

  ControlAuthorizeMerchantPage(this.skuId, this.skuCollectCode,
      this.authorizeType, this.dataRangeNotifier, this.openDataRangeFunction);

  @override
  BaseState<StatefulWidget> initState() {
    return ControlAuthorizeMerchantPageState();
  }
}

class ControlAuthorizeMerchantPageState
    extends BaseState<ControlAuthorizeMerchantPage> {
  EasyRefreshController _easyRefreshController = new EasyRefreshController();

  bool requestSuccess = true;

  List<AuthorizeMerchantItemData> dataSource = [];

  String? keyword = "";

  String? total;

  @override
  bool isSubPage() {
    return true;
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  void onCreate() {
    super.onCreate();
    widget.dataRangeNotifier.addListener(() {
      showLoadingDialog();
      requestListData();
    });
    showLoadingDialog();
    requestListData();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      child: Column(
        children: [buildSearchWidget(), buildMerchantListWidget()],
      ),
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  Widget buildSearchWidget() {
    return Container(
      color: Colors.green,
      child: SAppBarSearch(
        hintText: "搜索客户名称/手机号/客户编号",
        showLeading: false,
        hideCancel: true,
        autoFocus: false,
        height: 44,
        onClear: () {
          keyword = null;
          showLoadingDialog();
          requestListData();
        },
        onCancel: () {
          keyword = null;
          showLoadingDialog();
          requestListData();
        },
        onSearch: (value) {
          keyword = value;
          showLoadingDialog();
          requestListData();
        },
      ),
    );
  }

  Widget buildMerchantListWidget() {
    return Expanded(
      child: Container(
        color: Color(0xfff6f6f6),
        child: Column(
          children: [
            buildMerchantCountWidget(),
            Expanded(
              child: buildContainerWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildListItemWidget(int index) {
    var itemData = dataSource[index];
    return Container(
      margin: EdgeInsets.only(left: 10, right: 10, bottom: 10),
      child: Container(
        padding: EdgeInsets.only(left: 10, right: 10, top: 15, bottom: 12),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(7), color: Colors.white),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      itemData.merchantName ?? "--",
                      style: TextStyle(
                          color: Color(0xff333333),
                          fontSize: 16,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Image.asset(
                    "assets/images/item_arrow.png",
                    width: 12,
                    height: 12,
                  )
                ],
              ),
            ),
            SizedBox(
              height: 11,
            ),
            Container(
              alignment: Alignment.centerLeft,
              child: Text(
                itemData.merchantAddress ?? "--",
                style: TextStyle(
                    color: Color(0xff676773),
                    fontWeight: FontWeight.normal,
                    fontSize: 13),
              ),
            ),
            SizedBox(
              height: 11,
            ),
            buildSaleDataWidget(itemData)
          ],
        ),
      ),
    );
  }

  Widget buildSaleDataWidget(AuthorizeMerchantItemData itemData) {
    return Visibility(
        visible: widget.authorizeType == AuthorizeType.Authorized,
        child: Container(
          padding: EdgeInsets.all(10),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2), color: Color(0xfffafbfa)),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildSaleDataItemWidget("本月实付金额", itemData.thisMonthPayGmv),
                    SizedBox(
                      height: 11,
                    ),
                    buildSaleDataItemWidget("本月购买数量", itemData.thisMonthBuyNum),
                  ],
                ),
              ),
              SizedBox(
                width: 5,
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildSaleDataItemWidget("上月实付金额", itemData.lastMonthPayGmv),
                    SizedBox(
                      height: 11,
                    ),
                    buildSaleDataItemWidget("上月购买数量", itemData.lastMonthBuyNum)
                  ],
                ),
              ),
            ],
          ),
        ));
  }

  Widget buildSaleDataItemWidget(String title, String? data) {
    return RichText(
      text: TextSpan(
          text: title + " ",
          style: TextStyle(
              color: Color(0xff8e8e93),
              fontWeight: FontWeight.normal,
              fontSize: 12),
          children: [
            TextSpan(
                text: data ?? "--",
                style: TextStyle(
                    color: Color(0xff333333),
                    fontWeight: FontWeight.normal,
                    fontSize: 12))
          ]),
      overflow: TextOverflow.ellipsis,
    );
  }

  Widget buildContainerWidget() {
    if (widget.dataRangeNotifier.value == null) {
      return PageStateWidget.pageEmpty(PageState.Empty,
          buttonText: "去选择", stateText: "请点击下方或右上角按钮选择BD后 查看详细数据", errorClick: () {
        widget.openDataRangeFunction?.call();
      });
    } else {
      return EasyRefresh(
          onRefresh: () async {
            return await requestListData();
          },
          onLoad: null,
          emptyWidget: buildEmptyWidget(),
          controller: _easyRefreshController,
          child: ListView.builder(
              itemCount: this.dataSource.length,
              itemBuilder: (context, index) {
                return GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: () {
                    print("guan ontap item");
                    jumpCustomerPageByMerchantId(dataSource[index].merchantId?.toString(),canJumpPublic: false);
                  },
                  child: buildListItemWidget(index),
                );
              }));
    }
  }

  Widget? buildEmptyWidget() {
    if (!this.requestSuccess && this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        showLoadingDialog();
        this.requestListData();
      });
    }
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget buildMerchantCountWidget() {
    return Visibility(
      visible: buildEmptyWidget() == null,
      child: Container(
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
        child: Text(
          getMerchantCountText(),
          style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Color(0xff676773)),
        ),
      ),
    );
  }

  String getMerchantCountText() {
    switch (widget.authorizeType) {
      case AuthorizeType.Authorized:
        return "已授权客户：${total ?? "--"}个";
      case AuthorizeType.Unauthorized:
        return "未授权客户：${total ?? "--"}个";
    }
  }

  String getRequestPath() {
    switch (widget.authorizeType) {
      case AuthorizeType.Authorized:
        return "skuCollectMgr/grantedMerchantList";
      case AuthorizeType.Unauthorized:
        return "skuCollectMgr/nonGrantMerchantList";
    }
  }

  Future<void> requestListData() async {
    if (widget.dataRangeNotifier.value == null) {
      this.dismissLoadingDialog();
      return;
    }
    var params = {
      "searchUserId": widget.dataRangeNotifier.value,
      "skuId": widget.skuId ?? "",
      "skuCollectCode": widget.skuCollectCode ?? ""
    };
    if (keyword != null && keyword!.isNotEmpty) {
      params["searchKey"] = keyword;
    }

    var result =
        await NetworkV2<AuthorizeMerchantListData>(AuthorizeMerchantListData())
            .requestDataV2(getRequestPath(),
                method: RequestMethod.GET, parameters: params);
    this.dismissLoadingDialog();
    requestSuccess = result.isSuccess ?? false;
    if (result.isSuccess == true && mounted) {
      _easyRefreshController.finishRefresh();
      dataSource = result.getData()?.merchantList ?? [];
      total = result.getData()?.total?.toString();
      setState(() {});
    }
  }

}
