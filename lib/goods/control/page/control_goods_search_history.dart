import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/page/control_goods_result.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ControlGoodsSearchHistoryPage extends BasePage {
  // 搜索关键字 如果为空则认为是第一次搜索
  final String? keyword;
  // 1: 商品集搜索  2: 授权客户搜索
  final dynamic searchType;

  // 商品集code
  final String? skuCollectCode;

  // 已选择的区域名称
  final String? branchName;

  // 已选择的区域编码
  final String? branchCode;

  ControlGoodsSearchHistoryPage({
    required this.searchType,
    this.keyword,
    this.skuCollectCode,
    this.branchName,
    this.branchCode,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return ControlGoodsSearchHistoryState();
  }
}

class ControlGoodsSearchHistoryState
    extends BaseState<ControlGoodsSearchHistoryPage> {
  TextEditingController _controller = TextEditingController();

  List<String> history = [];

  String sharedKey = "control_goods_history";

  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    this.initHistoryList();
    this._controller.text = widget.keyword ?? "";
  }

  @override
  void dispose() {
    this._focusNode.unfocus();
    this._controller.dispose();
    super.dispose();
  }

  void initHistoryList() async {
    List<dynamic> result =
        await XYYContainer.storageChannel.getValue(sharedKey) ?? [];
    this.history = result.map((e) => '$e').toList();
    setState(() {});
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this._focusNode.unfocus();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.only(left: 15, right: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '历史搜索',
                  style: TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    this.deleteAllHoistory();
                  },
                  child: IntrinsicHeight(
                    child: Row(
                      children: [
                        SizedBox(
                          height: 40,
                          child: Image.asset(
                            'assets/images/base/history_delete_icon.png',
                            width: 14,
                            height: 14,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          '清空',
                          style:
                              TextStyle(color: Color(0xFF9494A6), fontSize: 12),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
            Wrap(
              alignment: WrapAlignment.start,
              spacing: 10,
              runSpacing: 10,
              children: this.historyItem(),
            )
          ],
        ),
      ),
    );
  }

  // 历史记录item
  List<Widget> historyItem() {
    return this
        .history
        .map((e) => GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                this.changeKeyword(e);
              },
              child: Container(
                padding: EdgeInsets.fromLTRB(8, 5, 8, 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: Color(0xFFF7F7F8),
                ),
                child: Text(e,
                    style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 13,
                    )),
              ),
            ))
        .toList();
  }

  // 删除历史记录
  void deleteAllHoistory() {
    showCupertinoDialog(
      context: this.context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text("是否清空历史记录"),
          actions: [
            CupertinoDialogAction(
              child: Text("取消"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            CupertinoDialogAction(
              child: Text("确定"),
              onPressed: () {
                XYYContainer.storageChannel.delete(this.sharedKey);
                this.history = [];
                setState(() {});
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // 增加搜索关键词
  void changeKeyword(String keyword) async {
    // 存储搜索历史
    if (this.history.contains(keyword)) {
      this.history.remove(keyword);
    }
    if (this.history.length >= 10) {
      this.history = this.history.sublist(0, 8);
    }
    this.history.insert(0, keyword);
    await XYYContainer.storageChannel.put(this.sharedKey, this.history);

    // 收起键盘
    this._focusNode.unfocus();

    if (widget.keyword != null) {
      Navigator.of(context).pop(keyword);
    } else {
      Navigator.of(context).pushReplacement(CustomSearchRoute(
        builder: (context) => ControlGoodsResultPage(
          keyword: keyword,
          skuCollectCode: widget.skuCollectCode,
          branchCode: widget.branchCode,
          branchName: widget.branchName,
        ),
      ));
    }
  }

  @override
  String getTitleName() {
    return "搜索历史";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonSearchBar(
      focusNode: this._focusNode,
      hintText:
          "${widget.searchType}" == "1" ? "请输入商品名/厂家名进行搜索" : "搜索客户名称/手机号/客户编号",
      showLeading: false,
      hideCancel: false,
      autoFocus: true,
      controller: this._controller,
      onSearch: (value) {
        String inputValue = value.trim();
        if (inputValue.isEmpty) {
          this._controller.text = inputValue;
          showToast("请输入搜索内容");
          return;
        }
        this.changeKeyword(inputValue);
      },
    );
  }
}

class CustomSearchRoute extends MaterialPageRoute {
  CustomSearchRoute({required WidgetBuilder builder}) : super(builder: builder);

  @override
  Duration get transitionDuration => Duration(milliseconds: 0);

  Duration get reverseTransitionDuration => Duration(milliseconds: 400);
}
