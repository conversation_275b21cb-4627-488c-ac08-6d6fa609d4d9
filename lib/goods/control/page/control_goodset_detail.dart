import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_filter_location_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_location_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/data/control_good_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/control/widget/control_goodset_detail_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_root_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ControlGoodSetDetailPage extends BasePage {
  /// 商品集编码
  final String? skuCollectCode;

  final String? skuCollectName;

  final dynamic customerId;

  ControlGoodSetDetailPage(
      {this.skuCollectCode, this.skuCollectName, this.customerId});

  @override
  BaseState<StatefulWidget> initState() {
    return ControlGoodSetDetailPageState();
  }
}

class ControlGoodSetDetailPageState
    extends BaseState<ControlGoodSetDetailPage> {
  List<ControlGoodListData> dataSource = [];

  EasyRefreshController _refreshController = EasyRefreshController();

  int page = 1;
  bool isLastPage = false;

  dynamic totalCount = 0;

  // 筛选区域数据
  List<CommodityFilterLocationData>? locationSource;
  GlobalKey rightKey = GlobalKey();

  // 筛选参数
  String locationTitle = '';
  dynamic provinceCode = -1;

  @override
  void initState() {
    this.refreshListData();
    this.requestLocationData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 10, top: 10),
            child: Text(
              '商品数量：${Permission.isPermission('productControlLeftNumber') ? this.totalCount : '--'}',
              style: TextStyle(
                  color: Color(0xFF676773),
                  fontSize: 14,
                  fontWeight: FontWeight.w500),
            ),
          ),
          Expanded(
            child: EasyRefresh(
              onRefresh: () async {
                this.refreshListData();
              },
              onLoad: this.isLastPage ? null : this.loadMoreListData,
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  ControlGoodListData data = this.dataSource[index];
                  return GestureDetector(
                    onTap: () {
                      // 全国权限无法跳转商品详情， 需要选择单独区域
                      if (this.provinceCode == -1 ||
                          this.provinceCode == "-1") {
                        showToast('请先选择区域');
                        return;
                      }
                      String router =
                          "xyy://crm-app.ybm100.com/good_detail?id=${data.skuId}&isFromCustomer=1";
                      if (this.provinceCode != -1) {
                        router = router + "&branchCode=XS${this.provinceCode}";
                      }
                      if (widget.customerId != null) {
                        router = router + "&merchantId=${widget.customerId}";
                      }
                      XYYContainer.open(router);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: ControlGoodSetDetailItem(
                      data: data,
                      authClick: () {
                        XYYContainer.open(
                            '/control_authorize_merchant_page?skuId=${data.skuId}&skuCollectCode=${widget.skuCollectCode}');
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if ((this.dataSource.length) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshListData() {
    this.page = 1;
    this.requestListData();
  }

  Future<void> loadMoreListData() async {
    this.requestListData();
  }

  void requestListData() async {
    if (this.dataSource.length <= 0) {
      showLoadingDialog();
    }

    Map<String, dynamic> params = {"limit": 10, "offset": this.page};
    if (widget.skuCollectCode != null) {
      params['skuCollectCode'] = widget.skuCollectCode;
    }

    var result =
        await NetworkV2<ControlGoodListPageData>(ControlGoodListPageData())
            .requestDataV2(
      'skuCollectMgr/skuList',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        this.totalCount = result.getData()?.total;
        List<ControlGoodListData>? source = result.getData()?.rows;
        if (source != null) {
          if (this.page == 1) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          this.page += 1;
        }
        setState(() {});
      }
      this._refreshController.finishRefresh();
      this._refreshController.resetLoadState();
      this
          ._refreshController
          .finishLoad(noMore: result.getData()?.lastPage == true);
      this.isLastPage = result.getData()?.lastPage == true;
    }
  }

  /// 区域筛选项
  void showLocationPopup() async {
    if (this.locationSource == null) {
      await requestLocationData();
    }
    // 无区域数据则不展示弹窗
    if (this.locationSource?.isEmpty == true) {
      return;
    }
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityLocationFilterPopup(
          distance: distance,
          selectId: this.provinceCode,
          itemChanged: (name, param) {
            setState(() {
              this.provinceCode = param['provinceCode'];
              this.locationTitle = name;
            });
          },
          source: this.locationSource ?? [],
        );
      },
      key: this.rightKey,
    );
  }

  Future<BaseRootModel<CommodityFilterLocationData>>
      requestLocationData() async {
    var result = await NetworkV2<CommodityFilterLocationData>(
            CommodityFilterLocationData())
        .requestDataV2(
      'skuRank/getProvince',
      method: RequestMethod.GET,
    );
    if (result.isSuccess == true) {
      this.locationSource = result.getListData() ?? [];
      this.provinceCode = this.locationSource?.first.provinceCode ?? -1;
      this.locationTitle = this.locationSource?.first.provinceName ?? "";
      setState(() {});
    } else {
      showToast(result.errorMsg ?? '获取区域错误');
    }
    return result;
  }

  // 跳转搜索
  void searchIconClick() {
    String router =
        '/control_search_history?searchType=1?skuCollectCode=${widget.skuCollectCode}&branchCode=${this.provinceCode}&branchName=${this.locationTitle}';
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  @override
  String getTitleName() {
    return widget.skuCollectName ?? "商品集详情";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      this.getTitleName(),
      rightButtons: [
        Visibility(
          // 如果地区数据长度小于1 则不展示按钮
          visible: (this.locationSource ?? []).length > 1,
          child: Container(
            key: this.rightKey,
            child: GestureDetector(
              onTap: () {
                this.showLocationPopup();
              },
              child: Row(
                children: [
                  Image.asset(
                    'assets/images/commodity/commodity_location_drop.png',
                    width: 10,
                    height: 13,
                  ),
                  SizedBox(width: 4),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 60),
                    child: Text(
                      this.locationTitle,
                      style: TextStyle(
                        color: Color(0xFF292933),
                        fontSize: 14,
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
        IconButton(
          padding: EdgeInsets.zero,
          highlightColor: Colors.transparent,
          hoverColor: Colors.transparent,
          splashColor: Colors.transparent,
          icon: Image.asset(
            "assets/images/titlebar/icon_search_bar.png",
            width: 25,
            height: 25,
          ),
          onPressed: searchIconClick,
        )
      ],
    );
  }
}
