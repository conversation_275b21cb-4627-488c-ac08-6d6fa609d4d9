import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_discount_price_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_rank_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/widgets/commodity_detail_list_item.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/widgets/commodity_rank_footer.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CommodityDetailListPage extends BasePage {
  final String? goodsName;
  final dynamic merchantId;
  final dynamic customerId;
  final dynamic productType;
  final dynamic provinceCode;
  final dynamic rankType;
  final dynamic sortType;
  final dynamic searchType;
  final dynamic searchSkuId;

  CommodityDetailListPage({
    this.goodsName,
    this.merchantId,
    this.customerId,
    this.productType,
    this.provinceCode,
    this.rankType,
    this.sortType,
    this.searchSkuId,
    this.searchType,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return CommodityDetailListPageState();
  }
}

class CommodityDetailListPageState extends BaseState<CommodityDetailListPage> {
  PageState pageState = PageState.Empty;

  List<CommodityDetailGoodItemModel> dataSource = [];

  @override
  void initState() {
    requestListData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
      child: EasyRefresh(
        onRefresh: requestListData,
        footer: CommodityRankFooter(),
        emptyWidget: getEmptyWidget(),
        child: ListView.builder(
          itemCount: dataSource.length,
          itemBuilder: (context, index) {
            CommodityDetailGoodItemModel model = dataSource[index];
            return GestureDetector(
              onTap: () {
                var skuId = model.skuId;
                String router =
                    "xyy://crm-app.ybm100.com/good_detail?id=$skuId&isFromCustomer=1";
                if (widget.provinceCode != -1) {
                  router = router + "&branchCode=XS${widget.provinceCode}";
                }
                if (widget.customerId != null) {
                  router = router + "&merchantId=${widget.customerId}";
                }
                XYYContainer.open(router);
              },
              child: CommodityDetailListItem(
                isMerchantEnter: isMerchantEnter(),
                model: model,
              ),
            );
          },
        ),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          requestListData();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
          stateText: '暂无数据',
        );
      default:
        return null;
    }
  }

  /// 页面进入前台调用
  @override
  void onForeground() {
    super.onForeground();

    // 刷新一次页面，重置倒计时时间使用
    setState(() {});
  }

  Future<void> requestListData() async {
    showLoadingDialog(msg: "加载中");
    NetworkV2(CommodityDetailGoodItemModel())
        .requestDataV2('skuRank/getSkuChildRankList',
            method: RequestMethod.GET, parameters: buildListParams())
        .then((value) {
      dismissLoadingDialog();
      if (mounted) {
        if (value.isSuccess == true) {
          var listData = value.getListData();
          if (listData == null || listData.isEmpty) {
            pageState = PageState.Empty;
          } else {
            pageState = PageState.Normal;

            /// 记录请求时的时间戳
            int timeStamp = DateTime.now().millisecondsSinceEpoch;
            listData.forEach((element) {
              element.ptActivityInfo?.timeStamp = timeStamp;
            });

            dataSource = listData;
          }
        } else {
          pageState = PageState.Error;
        }
        if (isMerchantEnter()) {
          this.refreshDiscountPrice();
        }
        setState(() {});
      }
    }).onError((error, stackTrace) {
      dismissLoadingDialog();
    });
  }

  Map<String, dynamic> buildListParams() {
    Map<String, dynamic> params = {
      'provinceCode': widget.provinceCode,
      'productType': widget.productType,
      'rankType': widget.rankType,
      'sortType': widget.sortType,
      'searchType': widget.searchType,
      'searchSkuId': widget.searchSkuId,
    };
    if (widget.merchantId != null) {
      params['merchantId'] = widget.merchantId;
    }
    return params;
  }

  void refreshDiscountPrice() {
    NetworkV2<CommodityDiscountPriceData>(CommodityDiscountPriceData())
        .requestDataV2("product/manage/getDiscountPrice",
            method: RequestMethod.GET, parameters: buildDiscountPriceParams())
        .then((value) {
      if (mounted) {
        if (value.isSuccess == true) {
          var listData = value.getData()?.rows;
          if (listData != null && listData.isNotEmpty) {
            listData.forEach((element) {
              dataSource.firstWhere((value) {
                return value.skuId == element.id;
              }).discountPrice = element.discountPrice;
            });
            setState(() {});
          }
        }
        setState(() {});
      }
    }).onError((error, stackTrace) {});
  }

  /// 构建到手价请求参数
  Map<String, dynamic> buildDiscountPriceParams() {
    var skuIds = dataSource.map((e) {
      return e.skuId;
    }).join(",");
    return {"idList": skuIds, "merchantId": widget.customerId};
  }

  /// 是否从客户详情进入
  bool isMerchantEnter() {
    return widget.merchantId?.isNotEmpty == true;
  }

  @override
  String getTitleName() {
    return widget.goodsName ?? "在售商品";
  }
}
