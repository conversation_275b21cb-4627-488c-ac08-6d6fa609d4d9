import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_filter_location_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

class CommodityLocationFilterPopup extends StatefulWidget with EasyPopupChild {
  late final VoidCallback _actionForDismiss;
  final double distance;
  final CommodityFilterItemChanged itemChanged;
  final dynamic? selectId;
  final List<CommodityFilterLocationData> source;

  CommodityLocationFilterPopup({
    this.distance = 0,
    required this.itemChanged,
    required this.source,
    this.selectId,
  });

  @override
  State<StatefulWidget> createState() {
    return CommodityLocationFilterPopupState();
  }

  @override
  dismiss() {
    this._actionForDismiss();
  }
}

class CommodityLocationFilterPopupState
    extends State<CommodityLocationFilterPopup>
    with CommodityFilterPopupBase, SingleTickerProviderStateMixin {
  late Animation<Offset> animation;
  late AnimationController controller;

  dynamic selectedId = -1;

  void initalAnimation() {
    this.controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
      reverseDuration: Duration(milliseconds: 300),
    );

    this.animation = Tween<Offset>(begin: Offset(0, -1), end: Offset.zero)
        .animate(this.controller);
    this.controller.forward();
  }

  @override
  void initState() {
    this.initalAnimation();
    widget._actionForDismiss = () {
      this.controller.reverse();
    };
    if (widget.selectId != null) {
      this.selectedId = widget.selectId;
    }
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: widget.distance),
      child: ClipRect(
        child: SlideTransition(
          position: this.animation,
          child: Container(
            child: Column(
              children: [
                Divider(
                  color: Color(0xFFF7F7F8),
                  height: 0.5,
                  thickness: 0.5,
                ),
                Container(
                  color: Color(0xFFFFFFFF),
                  padding: EdgeInsets.only(top: 10),
                  constraints: BoxConstraints(
                    minHeight: 108,
                    maxHeight: MediaQuery.of(context).size.height * 0.5,
                    minWidth: MediaQuery.of(context).size.width,
                  ),
                  child: SingleChildScrollView(
                    padding: EdgeInsets.only(
                      left: 15,
                      right: 15,
                      bottom: 15,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Visibility(
                          visible: this.hasAllPermissions(),
                          child: CommodityFilterItem(
                            name: '全国',
                            id: -1,
                            isSelected: this.selectedId == -1,
                            onPressed: (value) {
                              this.selectedId = value;
                              setState(() {});
                            },
                          ),
                        ),
                        Visibility(
                          visible: this.hasAllPermissions(),
                          child: SizedBox(height: 10),
                        ),
                        Wrap(
                          spacing: 10,
                          runSpacing: 10,
                          children: generateItem(),
                        )
                      ],
                    ),
                  ),
                ),
                bottomButton()
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> generateItem() {
    return widget.source
        .where((element) =>
            element.provinceCode != -1 || element.provinceName != "全国")
        .map((e) => CommodityFilterItem(
              name: e.provinceName,
              id: e.provinceCode,
              isSelected: this.selectedId == e.provinceCode,
              onPressed: (value) {
                this.selectedId = value;
                setState(() {});
              },
            ))
        .toList();
  }

  bool hasAllPermissions() {
    return widget.source.first.provinceCode == -1 ||
        widget.source.first.provinceName == '全国';
  }

  @override
  void determineAction() {
    String name = '全国';
    if (this.selectedId != -1) {
      name = widget.source
              .firstWhere((element) => element.provinceCode == this.selectedId)
              .provinceName ??
          "全国";
    }
    widget.itemChanged(name, {'provinceCode': this.selectedId});
    EasyPopup.pop(context);
  }

  @override
  void resetAction() {
    this.selectedId = widget.source.first.provinceCode ?? -1;
    setState(() {});
  }
}
