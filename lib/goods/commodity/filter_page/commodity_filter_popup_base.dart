import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

typedef CommodityFilterBuilder = EasyPopupChild Function(
  double topDistance,
);

typedef CommodityFilterItemChanged = void Function(
  String name,
  Map<String, dynamic> params,
);

Future showCommodityFilterPopup({
  required BuildContext context,
  required CommodityFilterBuilder pageBuilder,
  GlobalKey? key,
}) {
  double topDistance = 0;
  if (key != null) {
    RenderBox? renderBox = key.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      Offset offset = renderBox.localToGlobal(Offset(0, renderBox.size.height));
      topDistance = offset.dy.ceil() * 1.0;
    }
  }
  return EasyPopup.show(
    context,
    pageBuilder(topDistance),
    offsetLT: Offset(0, topDistance),
    outsideTouchCancelable: true,
    duration: Duration(milliseconds: 300),
  );
}

// Bottom Button
mixin CommodityFilterPopupBase<T extends StatefulWidget> on State<T> {
  void resetAction();
  void determineAction();

  Widget bottomButton() {
    return Container(
      height: 50,
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: resetAction,
              behavior: HitTestBehavior.opaque,
              child: Column(
                children: [
                  Divider(
                    color: Color(0xFFF7F7F8),
                    height: 0.5,
                    thickness: 0.5,
                  ),
                  Expanded(
                    child: Container(
                      color: Color(0xFFFFFFFF),
                      child: Center(
                        child: Text(
                          '重置',
                          style: TextStyle(
                            color: Color(0xFF292933),
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: determineAction,
              behavior: HitTestBehavior.opaque,
              child: Container(
                color: Color(0xFF00B377),
                child: Center(
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Color(0xFFFFFFFF),
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Item
class CommodityFilterItem extends StatelessWidget {
  final String name;
  final dynamic id;
  final ValueChanged<dynamic> onPressed;
  final bool isSelected;

  CommodityFilterItem({
    required this.name,
    required this.id,
    required this.onPressed,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this.onPressed(this.id);
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2),
          color: this.isSelected ? Color(0xFFE5F7F1) : Color(0xFFF7F7F8),
        ),
        padding: EdgeInsets.only(left: 20, top: 6.5, right: 20, bottom: 6.5),
        child: Text(
          this.name,
          style: TextStyle(
            color: this.isSelected ? Color(0xFF00B377) : Color(0xFF676773),
            fontSize: 12,
          ),
        ),
      ),
    );
  }
}

// Category FirstLevel Item
class CommodityFilterCategoryItem extends StatelessWidget {
  final String name;
  final dynamic id;
  final ValueChanged<dynamic> onPressed;
  final bool isSelected;

  CommodityFilterCategoryItem({
    required this.name,
    required this.onPressed,
    required this.id,
    this.isSelected = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this.onPressed(this.id);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        color: isSelected ? Color(0xFFFFFFFF) : Color(0xFFF5F5F5),
        alignment: Alignment.center,
        height: 40,
        child: Text(
          this.name,
          style: TextStyle(
            color: isSelected ? Color(0xFF00B377) : Color(0xFF292933),
            fontSize: 15,
          ),
        ),
      ),
    );
  }
}
