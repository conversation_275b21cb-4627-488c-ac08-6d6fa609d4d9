import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_filter_category_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class CommodityCategoryFilterPopup extends StatefulWidget with EasyPopupChild {
  late final VoidCallback _actionForDismiss;
  final double distance;
  final CommodityFilterItemChanged itemChanged;
  final dynamic? selectId;
  final List<CommodityFilterCategoryData> categoryList;

  CommodityCategoryFilterPopup({
    this.distance = 0,
    required this.itemChanged,
    required this.categoryList,
    this.selectId,
  });

  @override
  State<StatefulWidget> createState() {
    return CommodityCategoryFilterPopupState();
  }

  @override
  dismiss() {
    this._actionForDismiss();
  }
}

class CommodityCategoryFilterPopupState
    extends State<CommodityCategoryFilterPopup>
    with CommodityFilterPopupBase, SingleTickerProviderStateMixin {
  late Animation<Offset> animation;
  late AnimationController controller;

  // 数据控制器
  late _CategoryController sourceController;

  dynamic selectedId = -1;

  void initalAnimation() {
    this.controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
      reverseDuration: Duration(milliseconds: 300),
    );

    this.animation = Tween<Offset>(begin: Offset(0, -1), end: Offset.zero)
        .animate(this.controller);
    this.controller.forward();
  }

  void initalSourceController() {
    this.sourceController = _CategoryController(
        model: _CategorySelectModel(categoryList: widget.categoryList));
    if (widget.selectId != null) {
      this.selectedId = widget.selectId;
    }
    if (this.selectedId == -1) {
      this.sourceController.value.firstLevelId = -1;
    } else {
      widget.categoryList.forEach((element) {
        element.childCate?.forEach((e) {
          if (e.cateId == this.selectedId) {
            this.sourceController.value.firstLevelId = element.cateId;
          }
        });
      });
    }
  }

  @override
  void initState() {
    this.initalAnimation();
    widget._actionForDismiss = () {
      this.controller.reverse();
    };
    this.initalSourceController();
    super.initState();
  }

  @override
  void dispose() {
    this.controller.dispose();
    this.sourceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: widget.distance),
      child: ClipRect(
        child: SlideTransition(
          position: this.animation,
          child: Container(
            child: Column(
              children: [
                Divider(
                  color: Color(0xFFF7F7F8),
                  height: 0.5,
                  thickness: 0.5,
                ),
                Container(
                  color: Color(0xFFFFFFFF),
                  height: MediaQuery.of(context).size.height * 0.5,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      this.generateFirstLevelList(),
                      this.generateSecondLevelList(),
                    ],
                  ),
                ),
                bottomButton()
              ],
            ),
          ),
        ),
      ),
    );
  }

  // 左侧一级列表
  Widget generateFirstLevelList() {
    print('FirstLevelId - ${this.sourceController.value.firstLevelId}');
    return Container(
      width: 100,
      color: Color(0xFFF5F5F5),
      child: ListView(
        padding: EdgeInsets.zero,
        children: widget.categoryList
            .map((e) => CommodityFilterCategoryItem(
                  name: e.cateName,
                  id: e.cateId,
                  isSelected:
                      this.sourceController.value.firstLevelId == e.cateId,
                  onPressed: firstLevelOnPressed,
                ))
            .toList(),
      ),
    );
  }

  // 右侧二级列表
  Widget generateSecondLevelList() {
    return Expanded(
      child: Container(
        child: ValueListenableBuilder(
          valueListenable: this.sourceController,
          builder: (BuildContext context, _CategorySelectModel model,
              Widget? child) {
            return Container(
              padding: EdgeInsets.all(15),
              child: SingleChildScrollView(
                child: Wrap(
                  spacing: 15,
                  runSpacing: 15,
                  children: model.secondCategoryList
                      .map((e) => CommodityFilterItem(
                            name: e.cateName,
                            id: e.cateId,
                            isSelected: this.selectedId == e.cateId,
                            onPressed: secondLevelOnPressed,
                          ))
                      .toList(),
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  // 一级列表点击事件
  void firstLevelOnPressed(dynamic id) {
    // 一级列表点击分类与当前选中的不相同 再处理二级列表逻辑
    if (id != this.sourceController.value.firstLevelId) {
      this.sourceController.setFirstLevelSelectedId(id);

      List secondList = this.sourceController.value.secondCategoryList;
      if (secondList.isNotEmpty) {
        dynamic secondId = secondList.first.cateId;
        this.sourceController.setSecondLevelSelectedId(secondId);
        this.selectedId = secondId;
      } else {
        this.sourceController.setSecondLevelSelectedId(-1);
        this.selectedId = -1;
      }
      setState(() {});
    }
  }

  // 二级列表点击事件
  void secondLevelOnPressed(dynamic value) {
    this.selectedId = value;
    this.sourceController.setSecondLevelSelectedId(value);
  }

  @override
  void determineAction() {
    XYYContainer.bridgeCall('event_track',
        parameters: HashMap.from({"action_type": "MC-CommodityRankPage-CategoryFilterConfirm"}));
    String name = this.selectedId == -1
        ? '全部'
        : this
                .sourceController
                .value
                .secondCategoryList
                .firstWhere((element) => element.cateId == this.selectedId)
                .cateName ??
            '';
    widget.itemChanged(name, {'cateId': this.selectedId});
    EasyPopup.pop(context);
  }

  @override
  void resetAction() {
    XYYContainer.bridgeCall('event_track',
        parameters: HashMap.from({"action_type": "MC-CommodityRankPage-CategoryFilterReset"}));
    this.sourceController.setFirstLevelSelectedId(-1);
    this.sourceController.setSecondLevelSelectedId(-1);
    this.selectedId = -1;
    setState(() {});
  }
}

class _CategorySelectModel {
  List<CommodityFilterCategoryData> categoryList;
  dynamic firstLevelId;
  dynamic secondLevelId;

  _CategorySelectModel({required this.categoryList});

  List<CommodityFilterCategoryData> get secondCategoryList =>
      this.categoryList.firstWhere(
          (element) => element.cateId == this.firstLevelId, orElse: () {
        return CommodityFilterCategoryData();
      }).childCate ??
      [];
}

class _CategoryController extends ChangeNotifier
    implements ValueListenable<_CategorySelectModel> {
  _CategorySelectModel _model = _CategorySelectModel(categoryList: []);

  _CategoryController({required _CategorySelectModel model}) {
    _model = model;
  }

  @override
  _CategorySelectModel get value => _model;

  void setFirstLevelSelectedId(dynamic firstLevelId) {
    _model.firstLevelId = firstLevelId;
    notifyListeners();
  }

  void setSecondLevelSelectedId(dynamic secondLevelId) {
    _model.secondLevelId = secondLevelId;
    notifyListeners();
  }
}
