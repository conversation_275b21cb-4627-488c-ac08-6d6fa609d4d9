import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

class CommodityTimeFilterPopup extends StatefulWidget with EasyPopupChild {
  late final VoidCallback _actionForDismiss;
  final double distance;
  final CommodityFilterItemChanged itemChanged;
  final dynamic? selectId;

  CommodityTimeFilterPopup({
    this.distance = 0,
    required this.itemChanged,
    this.selectId,
  });

  @override
  State<StatefulWidget> createState() {
    return CommodityTimeFilterPopupState();
  }

  @override
  dismiss() {
    this._actionForDismiss();
  }
}

class CommodityTimeFilterPopupState extends State<CommodityTimeFilterPopup>
    with CommodityFilterPopupBase, SingleTickerProviderStateMixin {
  late Animation<Offset> animation;
  late AnimationController controller;

  dynamic selectedId = 1;

  void initalAnimation() {
    this.controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
      reverseDuration: Duration(milliseconds: 300),
    );

    this.animation = Tween<Offset>(begin: Offset(0, -1), end: Offset.zero)
        .animate(this.controller);
    this.controller.forward();
  }

  @override
  void initState() {
    this.initalAnimation();
    widget._actionForDismiss = () {
      this.controller.reverse();
    };
    if (widget.selectId != null) {
      this.selectedId = widget.selectId;
    }
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: widget.distance),
      child: ClipRect(
        child: SlideTransition(
          position: this.animation,
          child: Container(
            child: Column(
              children: [
                Divider(
                  color: Color(0xFFF7F7F8),
                  height: 0.5,
                  thickness: 0.5,
                ),
                Container(
                  color: Color(0xFFFFFFFF),
                  padding: EdgeInsets.only(left: 15, top: 10),
                  height: 108,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: generateItem(),
                  ),
                ),
                bottomButton()
              ],
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> generateItem() {
    return [
      CommodityFilterItem(
        name: '今日',
        id: 1,
        isSelected: this.selectedId == 1,
        onPressed: (value) {
          this.selectedId = value;
          setState(() {});
        },
      ),
      SizedBox(width: 10),
      CommodityFilterItem(
        name: '近30天',
        id: 2,
        isSelected: this.selectedId == 2,
        onPressed: (value) {
          this.selectedId = value;
          setState(() {});
        },
      ),
    ];
  }

  @override
  void determineAction() {
    XYYContainer.bridgeCall('event_track',
        parameters: HashMap.from({"action_type": "MC-CommodityRankPage-TimeFilterConfirm"}));
    String name = this.selectedId == 1 ? '今日' : '近30天';
    widget.itemChanged(name, {'rankType': this.selectedId});
    EasyPopup.pop(context);
  }

  @override
  void resetAction() {
    XYYContainer.bridgeCall('event_track',
        parameters: HashMap.from({"action_type": "MC-CommodityRankPage-TimeFilterReset"}));
    this.selectedId = 1;
    setState(() {});
  }
}
