import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_rank_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';

class CommodityRankGoodItem extends StatelessWidget {
  final CommodityRankData itemData;
  final bool isMerchantEnter;
  final bool isFirst;
  final dynamic sortType;

  CommodityRankGoodItem({
    required this.itemData,
    this.isMerchantEnter = false,
    this.isFirst = false,
    this.sortType,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 0, 10, 10),
      color: Color(0xFFF7F7F8),
      child: Container(
        decoration: BoxDecoration(
          gradient: (this.itemData.getRank() ?? 1) <= 3
              ? LinearGradient(
                  colors: this.getBackgroundColors(),
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                )
              : null,
          color: (this.itemData.getRank() ?? 1) <= 3 ? null : Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.only(bottom: 10),
        child: Container(
          padding: EdgeInsets.only(top: 5),
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.only(right: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(width: 5),
                    // 商品图片部分
                    this.getProductImage(),
                    SizedBox(width: 10),
                    // 商品信息
                    this.getProductInfo(),
                  ],
                ),
              ),
              lastPurchaseWidget(),
            ],
          ),
        ),
      ),
    );
  }

  /// 排行标识
  Widget getRankWidget() {
    return Container(
      height: 32,
      width: 26,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset(
            getRankBgAssetsPath(itemData.getRank()),
            width: 26,
            height: 32,
          ),
          Positioned(
            top: 5,
            child: Text(
              itemData.getFormatRank(),
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: Colors.white),
            ),
          )
        ],
      ),
    );
  }

  /// 商品图片
  Widget getProductImage() {
    return Container(
      height: 80,
      width: 83,
      child: Stack(
        children: [
          // 图片
          Positioned(
            left: 13,
            top: 10,
            child: Container(
              decoration: BoxDecoration(
                color: Color(0xFFFFFFFF),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(width: 0.5, color: Color(0xFFEDEDED)),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: ImageCatchWidget(
                  url: itemData.skuImageUrl,
                  w: 70,
                  h: 70,
                ),
              ),
            ),
          ),
          // 排行标识
          Positioned(
            left: 0,
            top: 0,
            child: this.getRankWidget(),
          ),
        ],
      ),
    );
  }

  /// 商品信息
  Widget getProductInfo() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(top: 10),
            child: Text(
              itemData.skuName ?? "",
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: this.getGoodsNameColor(),
              ),
            ),
          ),
          SizedBox(height: 3),
          this.getSpecWidget(),
          SizedBox(
            height: 10,
          ),
          this.getAverPriceWidget(),
        ],
      ),
    );
  }

  /// 规格
  Widget getSpecWidget() {
    bool sold = Permission.isPermission('productHotSold');
    bool purchased = Permission.isPermission('productCustomerPurchased');
    return Row(
      children: [
        Expanded(
          child: Text(
            itemData.skuSpec ?? "",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                fontSize: 11,
                color: const Color(0xff676773),
                fontWeight: FontWeight.normal),
          ),
        ),
        SizedBox(width: 20),
        Visibility(
          visible: this.isShowSalesNum(),
          child: Text(
              "${this.sortType}" == "1"
                  ? "已售" + "${(sold ? itemData.skuNum : '--' ) ?? "--"}"
                  : "已购客户" + " ${(purchased ? itemData.customerNum : '--' ) ?? "--"}",
              style: TextStyle(
                  fontSize: 11,
                  fontWeight: FontWeight.normal,
                  color: const Color(0xff9494a6))),
        )
      ],
    );
  }

  /// 平均到手价
  Widget getAverPriceWidget() {
    bool sale = Permission.isPermission('productAgvSale');
    return Container(
      child: Row(
        children: [
          Visibility(
            visible: itemData.skuAverPrice != null,
            child: RichText(
              text: TextSpan(
                text: "平均到手价",
                style: TextStyle(color: Color(0xFFFC741B), fontSize: 11),
                children: [
                  TextSpan(
                    text: sale ? "¥" : ' ',
                    style: TextStyle(
                      color: Color(0xFFFC6B0B),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: sale ? this.getAverPriceIntegerPart() : '--',
                    style: TextStyle(
                      color: Color(0xFFFC6B0B),
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  TextSpan(
                    text: sale ? this.getAverPriceDecimalPart() : '',
                    style: TextStyle(
                      color: Color(0xFFFC6B0B),
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Spacer(),
          getMoreGoodsItem()
        ],
      ),
    );
  }

  /// 查看再售商品
  Widget getMoreGoodsItem() {
    return Row(
      children: [
        Text(
          "查看在售商品",
          style: TextStyle(color: Color(0xFF949498), fontSize: 12),
        ),
        Image.asset(
          'assets/images/commodity/commodity_rank_arrow.png',
          width: 12,
          height: 12,
        )
      ],
    );
  }

  /// 最后一次购买时间
  Widget lastPurchaseWidget() {
    return Visibility(
      visible: this.isMerchantEnter,
      child: Container(
        padding: EdgeInsets.only(top: 5),
        child: Column(
          children: [
            Divider(color: Color(0xFFEEEEEE), height: 1, thickness: 0.5),
            SizedBox(height: 10),
            Padding(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '上次采购时间',
                    style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
                  ),
                  Spacer(),
                  Text(
                    hasPurchased ? '${itemData.lastPurchaseDate}' : "从未采购",
                    style: TextStyle(
                        fontSize: 12,
                        color: hasPurchased
                            ? const Color(0xff676773)
                            : const Color(0xffff2121),
                        fontWeight:
                            hasPurchased ? FontWeight.normal : FontWeight.w500),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool get hasPurchased =>
      itemData.lastPurchaseDate != null &&
      itemData.lastPurchaseDate.toString().isNotEmpty;

  /// 平均到手价 整数部分
  String getAverPriceIntegerPart() {
    dynamic price = itemData.skuAverPrice;
    if (price == null) {
      return "";
    }
    if (!price.toString().contains(".")) {
      return price.toString();
    } else {
      return price.toString().split(".")[0];
    }
  }

  /// 平均到手价 小数部分
  String getAverPriceDecimalPart() {
    dynamic price = itemData.skuAverPrice;
    if (price == null) {
      return "";
    }
    if (!price.toString().contains(".")) {
      return "";
    } else {
      return "." + price.toString().split(".")[1];
    }
  }

  /// 排行标志的背景图片
  String getRankBgAssetsPath(int? rank) {
    switch (rank) {
      case 1:
        return "assets/images/commodity/commodity_rank_first.png";
      case 2:
        return "assets/images/commodity/commodity_rank_second.png";
      case 3:
        return "assets/images/commodity/commodity_rank_third.png";
      default:
        return "assets/images/commodity/commodity_rank_other.png";
    }
  }

  /// 是否展示销量/已购客户字段
  bool isShowSalesNum() {
    return "${this.sortType}" == "1"
        ? "${itemData.skuNum}".isNotEmpty
        : "${itemData.customerNum}" != "0" &&
            "${itemData.customerNum}" != "null" &&
            "${itemData.customerNum}" != "--" &&
            "${itemData.customerNum}".isNotEmpty;
  }

  /// 获取背景色
  List<Color> getBackgroundColors() {
    switch (this.itemData.getRank()) {
      case 1:
        return [Color(0xFFFFE4E0), Color(0xFFFFFEFE), Color(0xFFFFFFFF)];
      case 2:
        return [Color(0xFFFFF0E0), Color(0xFFFFFEFE), Color(0xFFFFFFFF)];
      case 3:
        return [Color(0xFFECF3FF), Color(0xFFFFFEFE), Color(0xFFFFFFFF)];
      default:
        return [Color(0xFFFFFFFF), Color(0xFFFFFFFF)];
    }
  }

  // 商品名称颜色
  Color getGoodsNameColor() {
    switch (this.itemData.getRank()) {
      case 1:
        return Color(0xFFD02834);
      case 2:
        return Color(0xFFD9851B);
      case 3:
        return Color(0xFF4A71D4);
      default:
        return Color(0xFF2D2D2D);
    }
  }
}
