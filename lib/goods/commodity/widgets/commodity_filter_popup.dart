import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

// 单选下拉 菜单
class CommodityFilterPopup extends CommonFilterPopupBase {
  final List<ShopFilterModel> models;
  final String? selectedCode;
  final ValueChanged<ShopFilterModel> selectAction;

  CommodityFilterPopup({
    required double distance,
    required this.models,
    required this.selectAction,
    this.selectedCode,
  }) : super(distance: distance);

  @override
  CommonFilterPopupBaseState createState() {
    return CommodityFilterPopupState();
  }
}

class CommodityFilterPopupState extends CommonFilterPopupBaseState<CommodityFilterPopup> {
  String? selectedCode;

  @override
  void initState() {
    this.selectedCode = widget.selectedCode ?? '';
    super.initState();
  }

  @override
  Widget buildContent() {
    return Container(
      color: Color(0xFFFFFFFF),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: this.items(),
        ),
      ),
    );
  }

  List<Widget> items() {
    return widget.models
        .map((e) => GestureDetector(
              onTap: () {
                if (selectedCode == e.dictValue) {
                  return;
                } else {
                  selectedCode = e.dictValue;
                  setState(() {});
                  widget.selectAction(e);
                }
                EasyPopup.pop(context);
              },
              behavior: HitTestBehavior.opaque,
              child: Container(
                height: 44,
                child: CustomerFilterItem(
                  title: e.dictLabel,
                  isSelected: e.dictValue == this.selectedCode,
                ),
              ),
            ))
        .toList();
  }
}

// 选项item
class CustomerFilterItem extends StatelessWidget {
  final String title;
  final bool isSelected;

  CustomerFilterItem({required this.title, required this.isSelected});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 44,
      padding: EdgeInsets.only(left: 15),
      child: Column(
        children: [
          Spacer(),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                title,
                style: TextStyle(
                  color:
                      this.isSelected ? Color(0xFF35C561) : Color(0xFF666666),
                  fontSize: 14,
                ),
              ),
              Spacer(),
              Visibility(
                visible: this.isSelected,
                child: Image.asset(
                  'assets/images/customer/customer_filter_select_icon.png',
                  width: 22,
                  height: 22,
                ),
              ),
              SizedBox(width: 15),
            ],
          ),
          Spacer(),
          Divider(color: Color(0xFFF6F6F6), height: 0.5, thickness: 0.5),
        ],
      ),
    );
  }
}
