import 'dart:collection';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_filter_category_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_category_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_high_gross_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_shop_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_time_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/widgets/commodity_type_filter_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_root_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class CommodityTopFilter extends StatefulWidget {
  final Widget body;

  final ValueChanged<Map<String, dynamic>>? filterChange;

  final Widget? locationWidget;

  CommodityTopFilter(
      {Key? key, required this.body, this.filterChange, this.locationWidget});

  @override
  State<StatefulWidget> createState() {
    return CommodityTopFilterState();
  }
}

class CommodityTopFilterState extends State<CommodityTopFilter> {
  // 当前展开按钮的控制器
  DropButtonController? curController;

  // 时间筛选key
  GlobalKey timeKey = GlobalKey();

  // 当前参数
  Map<String, dynamic> filterParams = {
    "sortType": 1, // 1：销售额 2：下单客户数
    "productType": 0, // 0:全部 1:自营 2:POP 3:优选 4:控销
    "rankType": 1, // 今日（1）；30天（2）
  };

  // 品类数据
  List<CommodityFilterCategoryData>? categoryData;

  void track(String actionType) {
    XYYContainer.bridgeCall('event_track',
        parameters: HashMap.from({"action_type": actionType}));
  }

  @override
  Widget build(BuildContext context) {
    var widgetList = [
      Expanded(
        flex: 1,
        child: Container(
          alignment: Alignment.center,
          child: DropControllerButton(
            key: this.timeKey,
            title: "今日",
            selectedText: "今日",
            onPressed: (controller) {
              track("MC-CommodityRankPage-TimeFilter");
              changeController(controller);
              showTimeFilter(controller);
            },
          ),
        ),
      ),
      Expanded(
        flex: 2,
        child: Container(
          alignment: Alignment.center,
          child: CommodityTypeFilterWidget(
            filterCallBack: (sortType) {
              this.filterParams['sortType'] = sortType;
              widget.filterChange!(this.filterParams);
            },
          ),
        ),
      ),
    ];
    if (widget.locationWidget != null) {
      widgetList.add(Expanded(flex: 1, child: widget.locationWidget!));
    } else {
      widgetList.add(Expanded(flex: 1, child: Container()));
    }

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Divider(
            color: Color(0xE1E1E5FF),
            height: 0.5,
            thickness: 0.5,
          ),
          Row(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: widgetList,
          ),
          CommodityFilterButtonWidget(
            changed: (value) {
              this.filterParams['productType'] = value['productType'] ?? 0;
              this.filterParams['categoryType'] = value['categoryType'] ?? 0;
              widget.filterChange!(this.filterParams);
            },
          ),
          widget.body,
        ],
      ),
    );
  }

  void changeController(DropButtonController controller) {
    curController?.setIsOpen(false);
    curController = controller;
  }

  void showHighGrossFilter(DropButtonController controller) async {
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityHighGrossFilterPopup(
          distance: distance,
          selectId: this.filterParams['isHighGross'],
          itemChanged: (name, param) {
            if (param['isHighGross'] == 0) {
              controller.setSelectText(null);
            } else {
              controller.setSelectText(name);
            }
            this.filterParams.addAll(param);
            if (widget.filterChange != null) {
              widget.filterChange!(this.filterParams);
            }
          },
        );
      },
      key: this.timeKey,
    );
    controller.setIsOpen(false);
  }

  void showTimeFilter(DropButtonController controller) async {
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityTimeFilterPopup(
          distance: distance,
          selectId: this.filterParams['rankType'],
          itemChanged: (name, param) {
            controller.setSelectText(name);
            this.filterParams.addAll(param);
            if (widget.filterChange != null) {
              widget.filterChange!(this.filterParams);
            }
          },
        );
      },
      key: this.timeKey,
    );
    controller.setIsOpen(false);
  }

  void showShopFilter(DropButtonController controller) async {
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityShopFilterPopup(
          distance: distance,
          selectId: this.filterParams['businessType'],
          itemChanged: (name, param) {
            if (param['businessType'] == -1) {
              controller.setSelectText(null);
            } else {
              controller.setSelectText(name);
            }
            this.filterParams.addAll(param);
            if (widget.filterChange != null) {
              widget.filterChange!(this.filterParams);
            }
          },
        );
      },
      key: this.timeKey,
    );
    controller.setIsOpen(false);
  }

  void showCategoryFilter(DropButtonController controller) async {
    if (this.categoryData == null) {
      EasyLoading.show(
          maskType: EasyLoadingMaskType.clear, dismissOnTap: false);
      var result = await this.requestCategoryData();
      EasyLoading.dismiss();

      if (result.isSuccess == true) {
        this.categoryData = result.getListData() ?? [];
      } else {
        XYYContainer.toastChannel.toast(result.errorMsg ?? "获取分类失败");
        return;
      }
    }
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityCategoryFilterPopup(
          distance: distance,
          selectId: this.filterParams['cateId'],
          categoryList: this.categoryData ?? [],
          itemChanged: (name, param) {
            if (param['cateId'] == -1) {
              controller.setSelectText(null);
            } else {
              controller.setSelectText(name);
            }
            this.filterParams.addAll(param);
            if (widget.filterChange != null) {
              widget.filterChange!(this.filterParams);
            }
          },
        );
      },
      key: this.timeKey,
    );
    controller.setIsOpen(false);
  }

  Future<BaseRootModel<CommodityFilterCategoryData>>
      requestCategoryData() async {
    return NetworkV2<CommodityFilterCategoryData>(CommodityFilterCategoryData())
        .requestDataV2(
      'skuRank/skuCategory',
      method: RequestMethod.GET,
    );
  }
}
