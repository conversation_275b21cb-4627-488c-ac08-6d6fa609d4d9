import 'package:XyyBeanSproutsFlutter/businessOperation/shop/data/shop_filter_data.dart';
import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/widgets/commodity_filter_popup.dart';
import 'package:flutter/material.dart';

class CommodityTypeFilterWidget extends StatefulWidget {
  final ValueChanged<dynamic> filterCallBack;

  CommodityTypeFilterWidget({required this.filterCallBack});

  @override
  State<StatefulWidget> createState() {
    return CommodityTypeFilterWidgetState();
  }
}

class CommodityTypeFilterWidgetState extends State<CommodityTypeFilterWidget> {
  ValueNotifier<BackgroundButtonState> salesController =
      ValueNotifier(BackgroundButtonState.selected);
  ValueNotifier<BackgroundButtonState> countController =
      ValueNotifier(BackgroundButtonState.normal);

  dynamic sortType = 1;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 180,
      height: 26,
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Color(0xFF00B377), width: 0.5),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(4),
        child: Row(
          children: [
            Expanded(
              child: BackgroundStateButton(
                onPressed: (controller, value) {
                  this.countController.value = BackgroundButtonState.normal;
                  this.sortType = 1;
                  widget.filterCallBack(this.sortType);
                },
                title: '销售额榜',
                option: 1,
                controller: salesController,
                alignment: Alignment.center,
                selectStyle: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 12.5,
                  fontWeight: FontWeight.w500,
                ),
                textStyle: TextStyle(
                  color: Color(0xFF00B377),
                  fontSize: 12.5,
                  fontWeight: FontWeight.w500,
                ),
                normalColor: Color(0xFFFFFFFF),
                selectColor: Color(0xFF00B377),
              ),
            ),
            Expanded(
              child: BackgroundStateButton(
                onPressed: (controller, value) {
                  this.salesController.value = BackgroundButtonState.normal;
                  this.sortType = 2;
                  widget.filterCallBack(this.sortType);
                },
                title: '下单客户数榜',
                option: 2,
                controller: countController,
                alignment: Alignment.center,
                selectStyle: TextStyle(
                  color: Color(0xFFFFFFFF),
                  fontSize: 12.5,
                  fontWeight: FontWeight.w500,
                ),
                textStyle: TextStyle(
                  color: Color(0xFF00B377),
                  fontSize: 12.5,
                  fontWeight: FontWeight.w500,
                ),
                normalColor: Color(0xFFFFFFFF),
                selectColor: Color(0xFF00B377),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class CommodityFilterButtonWidget extends StatefulWidget {
  final ValueChanged<dynamic> changed;

  CommodityFilterButtonWidget({required this.changed});

  @override
  State<StatefulWidget> createState() {
    return CommodityFilterButtonWidgetState();
  }
}

class CommodityFilterButtonWidgetState
    extends State<CommodityFilterButtonWidget> {
  List<ShopFilterModel> popList = [
    ShopFilterModel(dictLabel:'全部商品',dictValue: '0'),
    ShopFilterModel(dictLabel:'POP',dictValue: '2'),
    ShopFilterModel(dictLabel:'控销',dictValue: '4'),
    ShopFilterModel(dictLabel:'自营',dictValue: '1'),
    ShopFilterModel(dictLabel:'甄选',dictValue: '5'),
    ShopFilterModel(dictLabel:'优选',dictValue: '3'),
  ];
  List<ShopFilterModel> drugList = [
    ShopFilterModel(dictLabel:'全部类目',dictValue: '0'),
    ShopFilterModel(dictLabel:'中药',dictValue: '2'),
    ShopFilterModel(dictLabel:'非药',dictValue: '4'),
  ];
  ShopFilterModel popCurrent = ShopFilterModel(dictLabel:'全部商品',dictValue: '0');
  ShopFilterModel drugCurrent = ShopFilterModel(dictLabel:'全部类目',dictValue: '0');
  bool popIsOpen = false;
  bool drugIsOpen = false;
  final GlobalKey leftButtonKey = GlobalKey();
  final GlobalKey rightButtonKey = GlobalKey();
  ValueNotifier<BackgroundButtonState> curController =
      ValueNotifier<BackgroundButtonState>(BackgroundButtonState.selected);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      padding: EdgeInsets.all(10),
      child: Row(
        children: [
          Container(
            width: 60,
            child: TextButton(
                  onPressed: () {
                      popCurrent = ShopFilterModel(dictLabel:'全部商品',dictValue: '0');
                      drugCurrent = ShopFilterModel(dictLabel:'全部类目',dictValue: '0');
                      widget.changed({
                        'productType':int.tryParse(popCurrent.dictValue),
                        'categoryType':int.tryParse(drugCurrent.dictValue),
                      });
                  },
                  child: Container(
                    height: 26,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(15),
                      color: (popCurrent.dictValue == '0' && drugCurrent.dictValue == '0') ? Color(0xFF00B377) : Color(0xFFEEEEEE),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          '全部',
                          style:
                              TextStyle(color: (popCurrent.dictValue == '0' && drugCurrent.dictValue == '0') ? Color(0xFFFFFFFF) : Color(0xFF676773), fontSize: 12,fontWeight: FontWeight.w400),
                        ),
                      ],
                    ),
                  ),
                  style: ButtonStyle(
                    overlayColor:
                        MaterialStateProperty.all<Color>(Colors.transparent),
                    padding: MaterialStateProperty.all<EdgeInsets>(
                        EdgeInsets.only(right: 10)),
                    minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                    tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  )),
          ),
          Expanded(
            child: TextButton(
              key: leftButtonKey,
                onPressed: () {
                  popIsOpen = true;
                  showCommonFilterPopup(
                    key: leftButtonKey,
                    context: context,
                    pageBuilder: (distance) {
                      return CommodityFilterPopup(
                        models: popList,
                        selectedCode: popCurrent.dictValue ?? '',
                        selectAction: (value) {
                          popCurrent = value;
                          widget.changed({
                            'productType':int.tryParse(popCurrent.dictValue),
                            'categoryType':int.tryParse(drugCurrent.dictValue),
                          });
                        },
                        distance: distance,
                      );
                    },
                  ).then((value) => {
                    setState(() {
                      popIsOpen = false;
                    })
                     
                  });
                  setState(() {});
                },
                child: Container(
                  height: 26,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: popIsOpen ? Color(0xFF00B377) : Color(0xFFEEEEEE),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(width: 5),
                      Text(
                        popCurrent.dictLabel ?? '',
                        style:
                            TextStyle(color: popIsOpen ? Color(0xFFFFFFFF) : Color(0xFF676773), fontSize: 12,fontWeight: FontWeight.w400),
                      ),
                      Icon(
                        Icons.arrow_drop_down_rounded,
                        color: popIsOpen ? Colors.white : Color(0xFF676773),
                        size: 16,
                      )
                    ],
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
          ),
          Expanded(
            child: TextButton(
              key: rightButtonKey,
                onPressed: () {
                  drugIsOpen = true;
                  showCommonFilterPopup(
                    key: leftButtonKey,
                    context: context,
                    pageBuilder: (distance) {
                      return CommodityFilterPopup(
                        models: drugList,
                        selectedCode: drugCurrent.dictValue ?? '',
                        selectAction: (value) {
                          drugCurrent = value;
                          widget.changed({
                            'productType':int.tryParse(popCurrent.dictValue),
                            'categoryType':int.tryParse(drugCurrent.dictValue),
                          });
                        },
                        distance: distance,
                      );
                    },
                  ).then((value) => {
                     setState(() {
                       drugIsOpen = false;
                     })
                  });
                  setState(() {});
                },
                child: Container(
                  height: 26,
                  width: double.infinity,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: drugIsOpen ? Color(0xFF00B377) : Color(0xFFEEEEEE),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        drugCurrent.dictLabel ?? '',
                        style:
                            TextStyle(color: drugIsOpen ? Color(0xFFFFFFFF) : Color(0xFF676773), fontSize: 12,fontWeight: FontWeight.w400),
                      ),
                      const SizedBox(width: 2),
                      Icon(
                        Icons.arrow_drop_down_rounded,
                        color: drugIsOpen ? Colors.white : Color(0xFF676773),
                        size: 16,
                      )
                    ],
                  ),
                ),
                style: ButtonStyle(
                  overlayColor:
                      MaterialStateProperty.all<Color>(Colors.transparent),
                  padding: MaterialStateProperty.all<EdgeInsets>(
                      EdgeInsets.only(right: 10)),
                  minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                )),
          )
        ],
      ),
    );
  }

  void selectItem(ValueNotifier<BackgroundButtonState> ctl, dynamic option) {
    if (ctl == curController) {
      return;
    }
    curController.value = BackgroundButtonState.normal;
    curController = ctl;

    widget.changed(option);
  }
}

class _CommodityButtonOptionModel {
  final String title;
  final dynamic option;
  _CommodityButtonOptionModel({required this.title, this.option});
}
