import 'dart:collection';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/widgets/commodity_rank_footer.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/widgets/commodity_rank_good_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_root_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

import 'data/commodity_filter_location_data.dart';
import 'data/commodity_rank_data.dart';
import 'filter_page/commodity_filter_popup_base.dart';
import 'filter_page/commodity_location_filter_popup.dart';
import 'widgets/commodity_top_filter.dart';

class CommodityRankPage extends BasePage {
  final String? merchantId;

  /// 跳转商品详情使用
  final dynamic? customerId;

  final bool isSubPage;

  CommodityRankPage({this.merchantId, this.customerId, this.isSubPage = false});

  @override
  BaseState<StatefulWidget> initState() {
    return CommodityRankPageState();
  }
}

class CommodityRankPageState extends BaseState<CommodityRankPage> {
  GlobalKey rightKey = GlobalKey();

  EasyRefreshController _controller = new EasyRefreshController();

  PageState pageState = PageState.Empty;

  bool isShowTips = false; // 默认不展示tips

  ScrollController _scrollController = ScrollController();

  Map<String, dynamic>? filterParams = {
    "sortType": 1, // 1：销售额 2：下单客户数
    "productType": 0, // 0:全部 1:自营 2:POP 3:优选 4:控销 5.甄选
    "rankType": 1, // 今日（1）；30天（2）
    "categoryType": 0, // 0:全部 2:中药 4:非药
  };

// 筛选区域数据
  List<CommodityFilterLocationData>? locationSource;

  // 筛选参数
  String locationTitle = '--';
  dynamic provinceCode = -1;

  // 列表数据
  List<CommodityRankData>? rankData;

  @override
  void onCreate() {
    super.onCreate();
    checkTips();
    refreshListData();
    _controller.finishLoad(noMore: true);
    if (isMerchantEnter()) {
      track("MV-CommodityRankPage-Merchant");
    } else {
      track("MV-CommodityRankPage-Mine");
    }
  }

  void filterValueUpdate(Map<String, dynamic> map) {
    filterParams = map;
    setState(() {});
    refreshListData();
  }

  /// 构建请求参数
  Map<String, dynamic> buildListParams() {
    Map<String, dynamic> params;
    if (filterParams == null) {
      params = HashMap.of({
        "sortType": 1, // 1：销售额 2：下单客户数
        "productType": 0, // 0:全部 1:自营 2:POP 3:优选 4:控销 5甄选
        "rankType": 1, // 今日（1）；30天（2）
        "categoryType": 0, // 0:全部 2:中药 4:非药
      });
    } else {
      params = HashMap.from(filterParams!);
    }
    params["provinceCode"] = provinceCode;
    if (isMerchantEnter()) {
      params["merchantId"] = widget.merchantId;
    }
    return params;
  }

  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return CommodityTopFilter(
      filterChange: (map) => filterValueUpdate(map),
      locationWidget: buildLocationFilterWidget(),
      body: Expanded(
        child: Container(
          color: Color(0xFFF7F7F8),
          child: Column(
            children: [
              Container(
                height: 1,
                color: const Color(0xfff6f6f6),
              ),
              Expanded(
                child: EasyRefresh(
                  controller: _controller,
                  onLoad: () async {
                    _controller.finishLoad(noMore: true);
                  },
                  onRefresh: () async {
                    this.refreshListData();
                  },
                  bottomBouncing: true,
                  footer: CommodityRankFooter(),
                  emptyWidget: getEmptyWidget(),
                  child: ListView.builder(
                    controller: _scrollController,
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).padding.bottom),
                    itemCount: rankData?.length ?? 0,
                    itemBuilder: (context, index) {
                      return getItemView(context, index);
                    },
                  ),
                ),
              ),
              getTipsWidget(context),
            ],
          ),
        ),
      ),
    );
  }

  /// 列表item widget
  Widget getItemView(BuildContext context, int index) {
    CommodityRankData? itemData = rankData?[index];
    if (itemData == null) {
      return Container(
        color: Colors.green,
        height: 1,
      );
    }
    return GestureDetector(
      onTap: () {
        // 全国权限无法跳转商品详情， 需要选择单独区域
        if (this.provinceCode == -1) {
          showToast('请先选择区域');
          return;
        }
        var router = '/commodity_detail_list?';
        if (widget.merchantId != null) {
          router += "merchantId=${widget.merchantId}";
        }
        if (widget.customerId != null) {
          router += "&customerId=${widget.customerId}";
        }
        if (this.provinceCode != null) {
          router += "&provinceCode=${this.provinceCode}";
        }
        if (this.filterParams?.containsKey("productType") == true) {
          router += "&productType=${this.filterParams?['productType']}";
        }
        if (this.filterParams?.containsKey("rankType") == true) {
          router += "&rankType=${this.filterParams?['rankType']}";
        }
        if (this.filterParams?.containsKey("sortType") == true) {
          router += "&sortType=${this.filterParams?['sortType']}";
        }
        router += "&searchSkuId=${itemData.searchSkuId}";
        router += "&searchType=${itemData.searchType}";
        router += "&goodsName=${itemData.skuName}";
        router = Uri.encodeFull(router);
        XYYContainer.open(router);
      },
      behavior: HitTestBehavior.opaque,
      child: CommodityRankGoodItem(
        itemData: itemData,
        isFirst: index == 0,
        isMerchantEnter: this.isMerchantEnter(),
        sortType: filterParams?['sortType'] ?? 1,
      ),
    );
  }

  /// 提示信息widget
  Widget getTipsWidget(BuildContext context) {
    return Visibility(
      visible: isShowTips && this.isSalesRank(),
      child: Container(
        color: const Color(0xFFFFF7EF),
        child: Row(
          children: [
            Expanded(
              child: Container(
                padding: EdgeInsets.only(left: 10, top: 8.5, bottom: 8.5),
                child: Text(
                  "该榜单以商品实付GMV为标准，实时更新最热商品，查看商品预估到手价需通过客户详情入口进入才能查看。",
                  style: TextStyle(
                      fontSize: 12,
                      color: const Color(0xff99664d),
                      fontWeight: FontWeight.normal),
                ),
              ),
            ),
            GestureDetector(
              onTap: () => handleTipsClick(),
              behavior: HitTestBehavior.opaque,
              child: Container(
                padding: EdgeInsets.only(
                    right: 9.5, left: 13, top: 8.5, bottom: 8.5),
                child: Image.asset(
                  "assets/images/commodity/commodity_tips_close.png",
                  width: 18,
                  height: 18,
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          refreshListData();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
          stateText: this.locationSource?.isEmpty == true
              ? '暂无蜂窝配置'
              : '当前尚无采购数据，请稍后查看',
        );
      default:
        return null;
    }
  }

  @override
  String getTitleName() {
    return "热销榜单";
  }

  /// 标题栏widget
  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    if (widget.isSubPage) {
      return null;
    }
    return CommonTitleBar(
      this.getTitleName(),
    );
  }

  Widget? buildLocationFilterWidget() {
    return Container(
      key: this.rightKey,
      alignment: Alignment.center,
      height: 40,
      child: GestureDetector(
        onTap: () {
          this.showLocationPopup();
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(
              'assets/images/commodity/commodity_location_drop.png',
              width: 10,
              height: 13,
            ),
            SizedBox(width: 4),
            Text(
              this.locationTitle,
              style: TextStyle(
                color: Color(0xFF292933),
                fontSize: 14,
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 区域筛选项
  void showLocationPopup() async {
    if (this.locationSource == null) {
      await requestLocationData();
    }
    // 无区域数据则不展示弹窗
    if (this.locationSource?.isEmpty ?? true) {
      return;
    }
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityLocationFilterPopup(
          distance: distance,
          selectId: this.provinceCode,
          itemChanged: (name, param) {
            setState(() {
              this.provinceCode = param['provinceCode'];
              this.locationTitle = name;
              this.refreshListData();
            });
          },
          source: this.locationSource ?? [],
        );
      },
      key: this.rightKey,
    );
  }

  Future<BaseRootModel<CommodityFilterLocationData>>
      requestLocationData() async {
    var result = await NetworkV2<CommodityFilterLocationData>(
            CommodityFilterLocationData())
        .requestDataV2(
      'skuRank/getProvince',
      method: RequestMethod.GET,
    );
    if (result.isSuccess == true) {
      this.locationSource = result.getListData() ?? [];
      if (locationSource == null || locationSource!.isEmpty) {
        this.provinceCode = -1;
        this.locationTitle = "--";
      } else {
        this.provinceCode = this.locationSource?.first.provinceCode ?? -1;
        this.locationTitle = this.locationSource?.first.provinceName ?? "";
      }
      setState(() {});
    } else {
      XYYContainer.toastChannel.toast(result.errorMsg ?? '获取区域错误');
    }
    return result;
  }

  void refreshListData() async {
    if (this.locationSource == null) {
      await requestLocationData();
    }
    // 无蜂窝数据则不请求接口
    if (this.locationSource?.isEmpty == true) {
      return;
    }

    // 列表滚动到顶部
    _scrollController.animateTo(0,
        duration: Duration(milliseconds: 200), curve: Curves.easeInOut);

    showLoadingDialog(msg: "加载中");
    NetworkV2(CommodityRankData())
        .requestDataV2(
            isMerchantEnter()
                ? "skuRank/getMerchantSkuRankListV2"
                : "skuRank/getSkuRankListV2",
            method: RequestMethod.GET,
            parameters: buildListParams())
        .then((value) {
      dismissLoadingDialog();
      if (mounted) {
        if (value.isSuccess == true) {
          var listData = value.getListData();
          if (listData == null || listData.isEmpty) {
            pageState = PageState.Empty;
          } else {
            pageState = PageState.Normal;
            rankData = listData;
          }
        } else {
          pageState = PageState.Error;
        }
        setState(() {});
      }
    }).onError((error, stackTrace) {
      dismissLoadingDialog();
    });
  }

  /// 检查是否应该展示提示条
  /// 提示文案仅在销售额榜单下存在
  /// 提示条关闭时会向sp中存储pid，检查时判断当前pid与存储的pid是否相同即可得知app是否重启过
  void checkTips() {
    if (!isMerchantEnter()) {
      XYYContainer.storageChannel.getValue("rank_pid_record").then((value) {
        setState(() {
          isShowTips = (value != pid.toString());
        });
      });
    }
  }

  /// 关闭提示条，同时存储pid到sp
  void handleTipsClick() {
    XYYContainer.storageChannel.put("rank_pid_record", pid.toString());
    setState(() {
      isShowTips = false;
    });
  }

  /// 是否是销售榜单排行
  bool isSalesRank() {
    return (filterParams?.containsKey("sortType") == true) &&
        (filterParams?['sortType'] == 1);
  }

  /// 是否从客户详情进入
  bool isMerchantEnter() {
    return widget.merchantId?.isNotEmpty == true;
  }
}
