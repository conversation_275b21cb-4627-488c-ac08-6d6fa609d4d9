// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commodity_rank_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommodityRankData _$CommodityRankDataFromJson(Map<String, dynamic> json) {
  return CommodityRankData()
    ..skuId = json['skuId']
    ..searchSkuId = json['searchSkuId']
    ..searchType = json['searchType']
    ..skuImageUrl = json['skuImageUrl']
    ..skuName = json['skuName']
    ..skuNum = json['skuNum']
    ..skuSpec = json['skuSpec']
    ..skuGrossMargin = json['skuGrossMargin']
    ..skuAverPrice = json['skuAverPrice']
    ..skuLastPrice = json['skuLastPrice']
    ..skuSellOut = json['skuSellOut']
    ..shopName = json['shopName']
    ..rank = json['rank']
    ..skuPrice = json['skuPrice']
    ..shopPropertyName = json['shopPropertyName']
    ..customerNum = json['customerNum']
    ..lastPurchaseDate = json['lastPurchaseDate']
    ..skuBuyLimit = json['skuBuyLimit'];
}

Map<String, dynamic> _$CommodityRankDataToJson(CommodityRankData instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'searchSkuId': instance.searchSkuId,
      'searchType': instance.searchType,
      'skuImageUrl': instance.skuImageUrl,
      'skuName': instance.skuName,
      'skuNum': instance.skuNum,
      'skuSpec': instance.skuSpec,
      'skuGrossMargin': instance.skuGrossMargin,
      'skuAverPrice': instance.skuAverPrice,
      'skuLastPrice': instance.skuLastPrice,
      'skuSellOut': instance.skuSellOut,
      'shopName': instance.shopName,
      'rank': instance.rank,
      'skuPrice': instance.skuPrice,
      'shopPropertyName': instance.shopPropertyName,
      'customerNum': instance.customerNum,
      'lastPurchaseDate': instance.lastPurchaseDate,
      'skuBuyLimit': instance.skuBuyLimit,
    };

CommodityDetailGoodItemModel _$CommodityDetailGoodItemModelFromJson(
    Map<String, dynamic> json) {
  return CommodityDetailGoodItemModel()
    ..skuId = json['skuId']
    ..pSkuId = json['pSkuId']
    ..mSkuId = json['mSkuId']
    ..skuImageUrl = json['skuImageUrl']
    ..skuName = json['skuName']
    ..skuSpec = json['skuSpec']
    ..manufacturer = json['manufacturer']
    ..shopName = json['shopName']
    ..skuSellOut = json['skuSellOut']
    ..skuBuyLimit = json['skuBuyLimit']
    ..effectTime = json['effectTime']
    ..skuPrice = json['skuPrice']
    ..skuGrossMargin = json['skuGrossMargin']
    ..discountPrice = json['discountPrice']
    ..skuAverPrice = json['skuAverPrice']
    ..skuCollectType = json['skuCollectType']
    ..isHighGross = json['isHighGross']
    ..productType = json['productType']
    ..ptActivityInfo = json['ptActivityInfo'] == null
        ? null
        : CommodityDetailActivityModel.fromJson(
            json['ptActivityInfo'] as Map<String, dynamic>)
    ..tagList = (json['tagList'] as List<dynamic>?)
        ?.map(
            (e) => CommodityDetailTagModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CommodityDetailGoodItemModelToJson(
        CommodityDetailGoodItemModel instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'pSkuId': instance.pSkuId,
      'mSkuId': instance.mSkuId,
      'skuImageUrl': instance.skuImageUrl,
      'skuName': instance.skuName,
      'skuSpec': instance.skuSpec,
      'manufacturer': instance.manufacturer,
      'shopName': instance.shopName,
      'skuSellOut': instance.skuSellOut,
      'skuBuyLimit': instance.skuBuyLimit,
      'effectTime': instance.effectTime,
      'skuPrice': instance.skuPrice,
      'skuGrossMargin': instance.skuGrossMargin,
      'discountPrice': instance.discountPrice,
      'skuAverPrice': instance.skuAverPrice,
      'skuCollectType': instance.skuCollectType,
      'isHighGross': instance.isHighGross,
      'productType': instance.productType,
      'ptActivityInfo': instance.ptActivityInfo,
      'tagList': instance.tagList,
    };

CommodityDetailActivityModel _$CommodityDetailActivityModelFromJson(
    Map<String, dynamic> json) {
  return CommodityDetailActivityModel()
    ..leastPurchaseNum = json['leastPurchaseNum']
    ..countdown = json['countdown']
    ..priceInfo = json['priceInfo']
    ..timeStamp = json['timeStamp'];
}

Map<String, dynamic> _$CommodityDetailActivityModelToJson(
        CommodityDetailActivityModel instance) =>
    <String, dynamic>{
      'leastPurchaseNum': instance.leastPurchaseNum,
      'countdown': instance.countdown,
      'priceInfo': instance.priceInfo,
      'timeStamp': instance.timeStamp,
    };

CommodityDetailTagModel _$CommodityDetailTagModelFromJson(
    Map<String, dynamic> json) {
  return CommodityDetailTagModel()
    ..name = json['name']
    ..uiType = json['uiType']
    ..uiStyle = json['uiStyle'];
}

Map<String, dynamic> _$CommodityDetailTagModelToJson(
        CommodityDetailTagModel instance) =>
    <String, dynamic>{
      'name': instance.name,
      'uiType': instance.uiType,
      'uiStyle': instance.uiStyle,
    };
