import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'commodity_discount_price_data.g.dart';

@JsonSerializable()
class CommodityDiscountPriceData extends BaseModelV2<CommodityDiscountPriceData> {
  List<CommodityDiscountPriceItemData>? rows;

  CommodityDiscountPriceData();

  factory CommodityDiscountPriceData.fromJson(Map<String, dynamic> json) =>
      _$CommodityDiscountPriceDataFromJson(json);

  @override
  CommodityDiscountPriceData fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityDiscountPriceDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityDiscountPriceDataToJson(this);
  }
}

@JsonSerializable()
class CommodityDiscountPriceItemData extends BaseModelV2<CommodityDiscountPriceItemData> {
  dynamic? id;
  dynamic? discountPrice;

  CommodityDiscountPriceItemData();

  factory CommodityDiscountPriceItemData.fromJson(Map<String, dynamic> json) =>
      _$CommodityDiscountPriceItemDataFromJson(json);

  @override
  CommodityDiscountPriceItemData fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityDiscountPriceItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityDiscountPriceItemDataToJson(this);
  }
}

