// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commodity_discount_price_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommodityDiscountPriceData _$CommodityDiscountPriceDataFromJson(
    Map<String, dynamic> json) {
  return CommodityDiscountPriceData()
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) =>
            CommodityDiscountPriceItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CommodityDiscountPriceDataToJson(
        CommodityDiscountPriceData instance) =>
    <String, dynamic>{
      'rows': instance.rows,
    };

CommodityDiscountPriceItemData _$CommodityDiscountPriceItemDataFromJson(
    Map<String, dynamic> json) {
  return CommodityDiscountPriceItemData()
    ..id = json['id']
    ..discountPrice = json['discountPrice'];
}

Map<String, dynamic> _$CommodityDiscountPriceItemDataToJson(
        CommodityDiscountPriceItemData instance) =>
    <String, dynamic>{
      'id': instance.id,
      'discountPrice': instance.discountPrice,
    };
