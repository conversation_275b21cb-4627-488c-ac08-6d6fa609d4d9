import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'commodity_filter_category_data.g.dart';

@JsonSerializable()
class CommodityFilterCategoryData
    extends BaseModelV2<CommodityFilterCategoryData> {
  dynamic cateId;
  dynamic cateName;
  List<CommodityFilterCategoryData>? childCate;

  CommodityFilterCategoryData();

  factory CommodityFilterCategoryData.fromJson(Map<String, dynamic> json) =>
      _$CommodityFilterCategoryDataFromJson(json);

  @override
  CommodityFilterCategoryData fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityFilterCategoryDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityFilterCategoryDataToJson(this);
  }
}
