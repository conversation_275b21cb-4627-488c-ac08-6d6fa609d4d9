// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'commodity_filter_category_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CommodityFilterCategoryData _$CommodityFilterCategoryDataFromJson(
    Map<String, dynamic> json) {
  return CommodityFilterCategoryData()
    ..cateId = json['cateId']
    ..cateName = json['cateName']
    ..childCate = (json['childCate'] as List<dynamic>?)
        ?.map((e) =>
            CommodityFilterCategoryData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CommodityFilterCategoryDataToJson(
        CommodityFilterCategoryData instance) =>
    <String, dynamic>{
      'cateId': instance.cateId,
      'cateName': instance.cateName,
      'childCate': instance.childCate,
    };
