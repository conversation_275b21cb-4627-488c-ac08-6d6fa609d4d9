import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'commodity_filter_location_data.g.dart';

@JsonSerializable()
class CommodityFilterLocationData
    extends BaseModelV2<CommodityFilterLocationData> {
  dynamic provinceCode;

  dynamic provinceName;

  CommodityFilterLocationData();

  @override
  CommodityFilterLocationData fromJsonMap(Map<String, dynamic> json) {
    return _$CommodityFilterLocationDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CommodityFilterLocationDataToJson(this);
  }
}
