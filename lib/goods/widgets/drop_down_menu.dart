import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

typedef DropDownMenuCallBack = void Function(int index, String value);

class DropDownMenu extends StatefulWidget with EasyPopupChild {
  final _PopController controller = _PopController();
  final VoidCallback? cancelOnPressed;
  final DropDownMenuCallBack surePressed;
  final List<String> values;
  String? selectValue;

  DropDownMenu({
    Key? key,
    required this.surePressed,
    required this.values,
    this.selectValue,
    this.cancelOnPressed,
  }) : super(key: key);
  @override
  _DropDownMenuState createState() => _DropDownMenuState();
  @override
  dismiss() {
    // this.cancelOnPressed();
    controller.dismiss();
  }
}

class _DropDownMenuState extends State<DropDownMenu>
    with SingleTickerProviderStateMixin {
  late Animation<Offset> _animation;
  AnimationController? _controller;
  // final List<String> values = values;
  @override
  void initState() {
    super.initState();
    widget.controller._bindState(this);
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );

    _animation = Tween<Offset>(begin: Offset(0, -1), end: Offset.zero)
        .animate(_controller!);
    _controller!.forward();
  }

  dismiss() {
    widget.cancelOnPressed!();
    _controller?.reverse();
  }

  @override
  void dispose() {
    super.dispose();
    _controller?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      // backgroundColor: Color(0xFFF7F7F8),
      body:GestureDetector(
         onTap: () {
        // widget.cancelOnPressed(); 
              },
        child: 
       Container(
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 90),
        child: ClipRect(
          child: SlideTransition(
            position: _animation,
            child: Container(
              color: Colors.white,
              child: ListView.builder(
                padding: EdgeInsets.all(0),
                shrinkWrap: true,
                itemCount: widget.values.length,
                itemExtent:40,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: () {
                      widget.selectValue = widget.values[index];
                      widget.surePressed(index, widget.values[index]);
                      EasyPopup.pop(context);
                    },
                    child: Container(
                      color: Color(0xFFF7F7F8),
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.only(left: 15),
                            child: Text(
                              widget.values[index],
                              style: TextStyle(
                                fontSize: 14,
                                color:
                                    widget.values[index] == widget.selectValue
                                        ? Color(0xFF00B377)
                                        : Color(0xFF292933),
                                height: 1,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Container(),
                          ),
                          Visibility(
                              visible:
                                  widget.values[index] == widget.selectValue,
                              child: Container(
                                padding: EdgeInsets.only(right: 15),
                                child: Image.asset(
                                  "assets/images/task/icon_task_select.png",
                                  width: 18,
                                  height: 18,
                                ),
                              )),
                          // Container(
                          //   padding: EdgeInsets.only(right: 15),
                          //   child: Image.asset(
                          //     "assets/images/task/icon_task_select.png",
                          //     width: 18,
                          //     height: 18,
                          //   ),
                          // )
                        ],
                      ),
                      // alignment: Alignment.center,
                      // child: Text(
                      //   'item$index',
                      // ),
                    ),
                  );
                },
              ),
            ),
          ),
        ),
      ),
      ),
    );
  }
}

class _PopController {
  _DropDownMenuState? state;

  _bindState(_DropDownMenuState state) {
    this.state = state;
  }

  dismiss() {
    state?.dismiss();
  }
}
