import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/customer/private/sub_page/refund/data/customer_refund_order_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/carts/data/shopping_carts_parent_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/carts/shopping_carts_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_discount_price_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/widgets/framework.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ShoppingCartsPage extends BasePage {
  final dynamic merchantId;
  final dynamic customerId;

  ShoppingCartsPage({this.merchantId, this.customerId});

  @override
  BaseState<StatefulWidget> initState() {
    return ShoppingCartsPageState();
  }
}

class ShoppingCartsPageState extends BaseState<ShoppingCartsPage> {
  EasyRefreshController refreshController = EasyRefreshController();

  List<ShoppingCartsItemData>? dataSource;

  PageState pageState = PageState.Empty;

  int page = 1;

  Map<String, dynamic> params = {};

  bool? isLastPage = true;

  dynamic productTotal;
  dynamic totalPrice;
  dynamic selectedProductNum;
  dynamic selectedTotalPrice;

  @override
  void onCreate() {
    super.onCreate();
    params["merchantId"] = widget.merchantId;
    showLoadingDialog(msg: "加载中...");
    requestListData(true);
  }

  @override
  bool isSubPage() {
    return true;
  }

  @override
  void dispose() {
    super.dispose();
    refreshController.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      height: double.infinity,
      width: double.infinity,
      color: const Color(0xffefeff4),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(top: 10, bottom: 10, left: 20, right: 20),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    "购物车商品",
                    style: TextStyle(
                        color: const Color(0xff8e8e93),
                        fontSize: 12,
                        fontWeight: FontWeight.normal),
                  ),
                ),
                Text.rich(
                  TextSpan(text: "全部商品", children: [
                    TextSpan(
                      text: "${productTotal?.toString() ?? "--"}",
                      style: TextStyle(
                          fontSize: 12,
                          color: const Color(0xffff2121),
                          fontWeight: FontWeight.normal),
                    ),
                    TextSpan(text: "种，合计总金额："),
                    TextSpan(
                      text: "${totalPrice?.toString() ?? "--"}",
                      style: TextStyle(
                          fontSize: 12,
                          color: const Color(0xffff2121),
                          fontWeight: FontWeight.normal),
                    ),
                    TextSpan(text: "元"),
                  ]),
                  style: TextStyle(
                      fontSize: 12,
                      color: const Color(0xff8e8e93),
                      fontWeight: FontWeight.normal),
                ),
              ],
            ),
          ),
          Expanded(
            child: EasyRefresh(
              controller: refreshController,
              onRefresh: () async {
                return await requestListData(true);
              },
              onLoad: isLastPage != false
                  ? null
                  : () async {
                      return await requestListData(false);
                    },
              child: buildContentWidget(),
              // child: SingleChildScrollView(child: Container(),),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
          Divider(
            height: 0.5,
            color: const Color(0xffE1E1E5),
          ),
          Container(
            height: 50,
            padding: EdgeInsets.only(right: 15),
            color: Colors.white,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text(
                    "已勾选商品：${selectedProductNum?.toString() ?? "--"}种  合计：${selectedTotalPrice?.toString() ?? "--"}元")
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget buildContentWidget() {
    return ListView.builder(
      itemCount: this.dataSource?.length ?? 0,
      itemBuilder: (BuildContext context, int index) {
        ShoppingCartsItemData? model = this.dataSource?[index];
        if (model == null) {
          return Container();
        }
        return GestureDetector(
          onTap: () {
            handleItemClick(model);
          },
          behavior: HitTestBehavior.opaque,
          child: ShoppingCartsItemWidget(model),
        );
      },
    );
  }

  Future<void> requestListData(bool isRefresh) async {
    print("guan requestListData ${isRefresh} ${page} ${isLastPage}");
    if (!isRefresh && isLastPage == true) {
      refreshController.finishLoad(noMore: true);
      return;
    }
    params["limit"] = 20;
    if (isRefresh) {
      params["offset"] = 1;
    } else {
      params["offset"] = page + 1;
    }
    var value =
        await NetworkV2<ShoppingCartsParentData>(ShoppingCartsParentData())
            .requestDataV2("merchant/shopping/cart",
                parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      setState(() {
        if (value.isSuccess == true) {
          var shoppingCartsParentData = value.getData();
          if (shoppingCartsParentData != null) {
            productTotal = shoppingCartsParentData.productTotal;
            totalPrice = shoppingCartsParentData.totalPrice;
            selectedProductNum = shoppingCartsParentData.selectedProductNum;
            selectedTotalPrice = shoppingCartsParentData.selectedTotalPrice;
          }
          var result = shoppingCartsParentData?.cartPageVo;
          if (result != null) {
            isLastPage = result.isLastPage;
            print(
                "guan requestListData result ${isRefresh} ${page} ${isLastPage}");
            if (isRefresh) {
              if (result.row?.isNotEmpty == true) {
                dataSource = result.row!;
                pageState = PageState.Normal;
                this.refreshDiscountPrice();
              } else {
                dataSource = [];
                pageState = PageState.Empty;
              }
            } else {
              if (result.row?.isNotEmpty == true) {
                if (dataSource == null) {
                  dataSource = [];
                }
                dataSource?.addAll(result.row!);
                pageState = PageState.Normal;
                this.refreshDiscountPrice();
              }
            }
          } else {
            pageState = PageState.Empty;
          }
          if (!isRefresh) {
            page++;
          } else {
            page = 1;
          }
        } else {
          pageState = PageState.Error;
        }
        refreshController.finishRefresh();
        refreshController.finishLoad(noMore: isLastPage ?? false);
      });
    }
  }

  void refreshDiscountPrice() {
    NetworkV2<CommodityDiscountPriceData>(CommodityDiscountPriceData())
        .requestDataV2("product/manage/getDiscountPrice",
            method: RequestMethod.GET, parameters: buildDiscountPriceParams())
        .then((value) {
      if (mounted) {
        if (value.isSuccess == true) {
          var listData = value.getData()?.rows;
          if (listData != null && listData.isNotEmpty) {
            listData.forEach((element) {
              dataSource?.firstWhere((value) {
                return value.skuId == element.id;
              }).discountPrice = element.discountPrice;
            });
            setState(() {});
          }
        }
        setState(() {});
      }
    }).onError((error, stackTrace) {});
  }

  /// 构建到手价请求参数
  Map<String, dynamic> buildDiscountPriceParams() {
    var skuIds = dataSource?.map((e) {
      return e.skuId;
    }).join(",");
    return {"idList": skuIds, "merchantId": widget.customerId};
  }

  void handleItemClick(ShoppingCartsItemData model) {
    String router =
        "xyy://crm-app.ybm100.com/good_detail?id=${model.skuId?.toString()}&isFromCustomer=1&merchantId=${widget.customerId}";
    XYYContainer.open(router);
  }

  /// 空页面
  Widget? getEmptyWidget() {
    if (dataSource?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          showLoadingDialog(msg: "加载中...");
          requestListData(true);
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  String getTitleName() {
    return "";
  }
}
