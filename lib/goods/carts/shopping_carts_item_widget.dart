import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/goods/carts/data/shopping_carts_parent_data.dart';
import 'package:flutter/material.dart';

class ShoppingCartsItemWidget extends StatelessWidget {
  final ShoppingCartsItemData itemData;

  ShoppingCartsItemWidget(this.itemData);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
      color: Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          buildSelectStatusWidget(),
          Expanded(
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  width: 10,
                ),
                Stack(
                  children: [
                    buildGoodsLogoWidget(),
                    Positioned.fill(
                        child: Center(child: buildGoodsStatusWidget()))
                  ],
                ),
                SizedBox(
                  width: 7,
                ),
                Expanded(child: buildGoodsInfoWidget())
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget buildGoodsInfoWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        buildGoodsBaseInfoWidget(),
        SizedBox(
          height: 2,
        ),
        buildGoodsTagListWidget(),
        buildGoodsPriceWidget(),
        buildEstimatePriceWidget(),
        buildShopWidget(),
      ],
    );
  }

  Widget buildGoodsTagListWidget() {
    var tagList = itemData.tagList?.map((tag) {
      return Visibility(
        visible: tag?.isNotEmpty == true,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 4),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(1),
              border: Border.all(color: const Color(0xffff4741), width: 1)),
          child: Text(
            tag ?? "--",
            style: TextStyle(
                color: const Color(0xffff4741),
                fontSize: 10,
                fontWeight: FontWeight.normal),
          ),
        ),
      );
    }).toList();
    return Visibility(
      visible: (itemData.tagList?.length ?? 0) > 0,
      child: Container(
          margin: EdgeInsets.only(top: 2),
          child: Wrap(
            direction: Axis.horizontal,
            spacing: 5,
            runSpacing: 5,
            children: tagList ?? List.empty(),
          )),
    );
  }

  Widget buildGoodsBaseInfoWidget() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                itemData.name ?? "--",
                style: TextStyle(
                    color: const Color(0xff333333),
                    fontSize: 15,
                    fontWeight: FontWeight.normal),
              ),
              SizedBox(
                height: 6,
              ),
              Text(
                itemData.spec ?? "--",
                style: TextStyle(
                    color: const Color(0xff8e8e93),
                    fontSize: 12,
                    fontWeight: FontWeight.normal),
              ),
              SizedBox(
                height: 3,
              ),
              Text(
                "¥${itemData.price?.toString() ?? "--"}",
                style: TextStyle(
                    color: const Color(0xff8e8e93),
                    fontSize: 12,
                    fontWeight: FontWeight.normal),
              )
            ],
          ),
        ),
        SizedBox(
          width: 5,
        ),
        Text(
          "X${itemData.amount?.toString() ?? "--"}",
          style: TextStyle(
              color: const Color(0xff333333),
              fontSize: 14,
              fontWeight: FontWeight.normal),
        )
      ],
    );
  }

  Widget buildSelectStatusWidget() {
    return Image.asset(
      itemData.selectStatus?.toString() == "true"
          ? "assets/images/goods/ic_cart_selected.png"
          : "assets/images/goods/ic_cart_unselect.png",
      width: 15,
      height: 15,
    );
  }

  Widget buildGoodsLogoWidget() {
    return ImageWidget(
      url: itemData.imageUrl,
      w: 85,
      h: 85,
      defImagePath: "assets/images/base/icon_default_avatar.png",
    );
  }

  Widget buildGoodsStatusWidget() {
    var statusText = getGoodsStatusText();
    return Visibility(
        visible: statusText.isNotEmpty,
        child: Container(
          width: 50,
          height: 50,
          alignment: Alignment.center,
          decoration: BoxDecoration(
              shape: BoxShape.circle, color: const Color(0x66000000)),
          child: Text(
            statusText,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: const Color(0xffffffff),
                fontSize: 12,
                fontWeight: FontWeight.normal),
          ),
        ));
  }

  String getGoodsStatusText() {
    switch (itemData.skuStatus?.toString()) {
      case "0":
        return "已失效";
      case "2":
        return "已售罄";
      case "4":
        return "已下架";
      default:
        return "";
    }
  }

  Widget buildGoodsPriceWidget() {
    return Container(
      margin: EdgeInsets.only(top: 2),
      child: Text.rich(
        TextSpan(
            text: "合计总价：",
            style: TextStyle(
                color: const Color(0xff333333),
                fontWeight: FontWeight.normal,
                fontSize: 12),
            children: [
              TextSpan(
                text: "¥",
                style: TextStyle(
                    color: Color(0xFFFF2121),
                    fontSize: 13,
                    fontWeight: FontWeight.w500),
              ),
              TextSpan(
                text: itemData.subtotal?.toString() ?? "--",
                style: TextStyle(
                    color: Color(0xFFFF2121),
                    fontSize: 16,
                    fontWeight: FontWeight.w500),
              ),
            ]),
      ),
    );
  }

  /// 预估到手价
  Widget buildEstimatePriceWidget() {
    return Visibility(
      visible: itemData.discountPrice != null,
      child: Padding(
        padding: const EdgeInsets.only(top: 2),
        child: Text(
          '${itemData.discountPrice}',
          style: TextStyle(
              color: Color(0xFFFF2121),
              fontSize: 12,
              fontWeight: FontWeight.normal),
        ),
      ),
    );
  }

  String getPriceIntegerPart() {
    if (itemData.price == null) {
      return "-";
    }
    if (!itemData.price.toString().contains(".")) {
      return itemData.price.toString();
    } else {
      return itemData.price.toString().split(".")[0];
    }
  }

  String getPriceDecimalPart() {
    if (itemData.price == null) {
      return "-";
    }
    if (!itemData.price.toString().contains(".")) {
      return "";
    } else {
      return "." + itemData.price.toString().split(".")[1];
    }
  }

  Widget buildShopWidget() {
    return Visibility(
      visible: itemData.shopName?.toString().isNotEmpty == true,
      child: Container(
        margin: EdgeInsets.only(top: 2),
        child: Row(
          children: [
            Image.asset(
              "assets/images/goods/ic_shop_name.png",
              width: 10,
              height: 10,
            ),
            SizedBox(
              width: 3,
            ),
            Text(
              itemData.shopName?.toString() ?? "--",
              style: TextStyle(
                  color: const Color(0xff676773),
                  fontWeight: FontWeight.normal,
                  fontSize: 12),
            )
          ],
        ),
      ),
    );
  }
}
