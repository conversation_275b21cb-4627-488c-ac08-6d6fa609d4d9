// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shopping_carts_parent_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShoppingCartsParentData _$ShoppingCartsParentDataFromJson(
    Map<String, dynamic> json) {
  return ShoppingCartsParentData()
    ..productTotal = json['productTotal']
    ..totalPrice = json['totalPrice']
    ..selectedProductNum = json['selectedProductNum']
    ..selectedTotalPrice = json['selectedTotalPrice']
    ..cartPageVo = json['cartPageVo'] == null
        ? null
        : ShoppingCartsListData.fromJson(
            json['cartPageVo'] as Map<String, dynamic>?);
}

Map<String, dynamic> _$ShoppingCartsParentDataToJson(
        ShoppingCartsParentData instance) =>
    <String, dynamic>{
      'productTotal': instance.productTotal,
      'totalPrice': instance.totalPrice,
      'selectedProductNum': instance.selectedProductNum,
      'selectedTotalPrice': instance.selectedTotalPrice,
      'cartPageVo': instance.cartPageVo,
    };

ShoppingCartsListData _$ShoppingCartsListDataFromJson(
    Map<String, dynamic> json) {
  return ShoppingCartsListData()
    ..total = json['total']
    ..isLastPage = json['isLastPage']
    ..row = (json['row'] as List<dynamic>?)
        ?.map((e) => ShoppingCartsItemData.fromJson(e as Map<String, dynamic>?))
        .toList();
}

Map<String, dynamic> _$ShoppingCartsListDataToJson(
        ShoppingCartsListData instance) =>
    <String, dynamic>{
      'total': instance.total,
      'isLastPage': instance.isLastPage,
      'row': instance.row,
    };

ShoppingCartsItemData _$ShoppingCartsItemDataFromJson(
    Map<String, dynamic> json) {
  return ShoppingCartsItemData()
    ..skuId = json['skuId']
    ..name = json['name']
    ..spec = json['spec']
    ..price = json['price']
    ..amount = json['amount']
    ..selectStatus = json['selectStatus']
    ..skuStatus = json['skuStatus']
    ..imageUrl = json['imageUrl']
    ..subtotal = json['subtotal']
    ..shopName = json['shopName']
    ..tagList = json['tagList'] as List<dynamic>?;
}

Map<String, dynamic> _$ShoppingCartsItemDataToJson(
        ShoppingCartsItemData instance) =>
    <String, dynamic>{
      'skuId': instance.skuId,
      'name': instance.name,
      'spec': instance.spec,
      'price': instance.price,
      'amount': instance.amount,
      'selectStatus': instance.selectStatus,
      'skuStatus': instance.skuStatus,
      'imageUrl': instance.imageUrl,
      'subtotal': instance.subtotal,
      'shopName': instance.shopName,
      'tagList': instance.tagList,
    };
