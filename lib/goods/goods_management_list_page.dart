import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/header_footer/header_footer_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/goods/bean/goods_management_list_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/rounded_gradient_Linear_progress.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/text_tag_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/ImageWidget.dart';
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/button/sort_button_widget.dart';
import 'dart:math';

class GoodsManagementListPage extends BasePage {
  final int selectedTab;

  GoodsManagementListPage({this.selectedTab = 0});

  @override
  BaseState initState() {
    return GoodsManagementListPageState(selectedTab: selectedTab);
  }
}

class GoodsManagementListPageState extends BaseState {
  int selectedTab;

  GoodsManagementListPageState({this.selectedTab = 0});

  var sortType = 0; //(0, "默认排序"), (9, "任务完成降序"), (10, "任务完成升序")

  var sortSelectType = 1;

  var filter = 3; //3是本月，8是上月

  var offset = 0;

  var refresh = false;

  var _controller = EasyRefreshController();

  var list = <GoodsManagementListRow>[];

  String? mImageHost;

  GoodsManagementList detailData = GoodsManagementList();

  PageStateWidget pageStateWidget = PageStateWidget();

  // 时间参数
  List<_HomeSalesTimeFilter> _filterDatas = [
    _HomeSalesTimeFilter('本月', '3', isSelected: true),
    _HomeSalesTimeFilter('上月', '8', isSelected: false),
  ];

  bool isSubPage() {
    return true;
  }

  @override
  void initState() {
    super.initState();
    this.requestData();
  }

  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Color(0xFFFFFFFF),
        child: listView(),
      ),
    );
  }

  void requestData() async {
    EasyLoading.show(status: "加载中");
    if (mImageHost == null) {
      XYYContainer.bridgeCall('app_host').then((value) {
        if (value is Map) {
          setState(() {
            mImageHost = value['image_host'];
          });
        }
      });
    }
    var result = await Network<GoodsManagementList>(GoodsManagementList())
        .requestData('sku/task/list', method: RequestMethod.GET, parameters: {
      "filter": filter,
      "sortType": sortType,
      "limit": 10,
      "offset": offset,
    });
    if (mounted) {
      if (result.isSuccess == true) {
        requestSuccess(result);
      } else {
        showToast(result.message ?? "");
      }
      EasyLoading.dismiss();
      if (refresh) {
        _controller.resetLoadState();
      }
      _controller.finishRefresh();
      _controller.finishLoad(noMore: result.lastPage!);
    }
  }

  //请求成功刷新数据
  void requestSuccess(GoodsManagementList result) {
    setState(() {
      detailData = result;
      if (result.rows != null) {
        if (offset == 0) {
          this.list.clear();
          refresh = false;
        }
        this.list.addAll(result.rows!);
      }
      if (this.list.length == 0) {
        pageStateWidget.setPageState(PageState.Empty);
      } else {
        pageStateWidget.setPageState(PageState.Normal);
      }
    });
  }

  void refreshData() {
    offset = 0;
    requestData();
  }

  ///订单列表每条item
  Widget listView() {
    pageStateWidget = new PageStateWidget();
    return EasyRefresh.custom(
      controller: _controller,
      enableControlFinishRefresh: true,
      enableControlFinishLoad: true,
      header: HeaderFooterHelp().getHeader(),
      footer: HeaderFooterHelp().getFooter(),
      onRefresh: () async {
        refresh = true;
        refreshData();
      },
      onLoad: () async {
        refresh = false;
        offset++;
        requestData();
      },
      headerIndex: 1,
      slivers: [
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverAppBarDelegate(
              minHeight: 80,
              maxHeight: 80,
              child: Column(
                children: [
                  buildHeaderWidget(),
                ],
              )),
        ),
        SliverList(
          delegate:
              SliverChildBuilderDelegate((BuildContext context, int index) {
            return orderItem(this.list[index], index);
          }, childCount: this.list.length), //thi
        )
      ],
      emptyWidget: getEmptyWidget(),
    );
  }

  Widget? getEmptyWidget() {
    if ((this.list.length) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty,
          stateText: "暂无数据，请联系当地销运上传任务商品");
    }
    return null;
  }

  Widget buildHeaderWidget() {
    return Container(
      padding: EdgeInsets.only(top: 15),
      color: Color(0xFFFFFFFF),
      height: 80,
      width: double.maxFinite,
      child: Column(
        children: [
          Container(
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.only(left: 15),
                  child: this._timeConditions(context),
                ),
                Expanded(
                  child: Container(),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  child: SortButtonWidget(
                    title: "默认排序",
                    isSelected: sortSelectType == 1 || sortSelectType == 2,
                    type: _buttoType(1),
                    isNeedImage: false,
                    onPressed: () {
                      this.updateSort(1);
                    },
                  ),
                ),
                SizedBox(
                  width: 1,
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 15),
                  child: SortButtonWidget(
                    title: "达成度",
                    isSelected: sortSelectType == 3 || sortSelectType == 4,
                    type: _buttoType(2),
                    onPressed: () {
                      this.updateSort(2);
                    },
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 10, left: 15),
            width: double.infinity,
            child: Text(
              "共${this.detailData.other?.totalTaskNum != null ? this.detailData.other?.totalTaskNum.toString() : "--"}个任务，${this.detailData.other?.totalProductNum != null ? this.detailData.other?.totalProductNum.toString() : "--"}个商品品种",
              style: TextStyle(
                  color: Color(0xFFA1A5B0),
                  fontSize: 13,
                  fontWeight: FontWeight.w300),
            ),
          ),
        ],
      ),
    );
  }

  /// 时间筛选
  Widget _timeConditions(BuildContext context) {
    List<Widget> items = [];
    //  List items = [];
    this._filterDatas.forEach((element) {
      TextButton button = TextButton(
        style: TextButton.styleFrom(
          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
          minimumSize: Size(55, 30),
          textStyle: TextStyle(
              fontSize: 12,
              color: Color(0xFF676773),
              fontWeight: FontWeight.w400),
          padding: EdgeInsets.zero,
          backgroundColor: Color(0xFFF7F7F8),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(3),
          ),
        ),
        onPressed: element.isSelected == false
            ? () {
                this.timeFilterAction(context, element);
              }
            : null,
        child: Text(
          element.title,
        ),
      );
      // items.add(Expanded(child: button, flex: 1));
      items.add(button);
    });
    return SizedBox(
      height: 30,
      child: Wrap(
        alignment: WrapAlignment.spaceEvenly,
        spacing: 10,
        children: items,
      ),
    );
  }

  // 时间筛选按钮事件
  void timeFilterAction(BuildContext context, _HomeSalesTimeFilter filterData) {
    // XYYContainer.toastChannel.toast('点击了时间筛选 - ${filterData.title}');
    setState(() {
      _HomeSalesTimeFilter item =
          this._filterDatas.firstWhere((element) => element.isSelected == true);
      item.isSelected = false;
      filterData.isSelected = true;
      filter = int.parse(filterData.filter);
    });
    //刷新数据
    refreshData();
  }

  /*
   * 更新排序状态
   * index: 当前点击按钮位置
   */
  void updateSort(int index) {
    if (index == 1) {
      if (sortSelectType == 1) {
        sortSelectType = 2;
        sortType = 0;
      } else {
        sortSelectType = 1;
        sortType = 0;
      }
    } else if (index == 2) {
      if (sortSelectType == 3) {
        sortSelectType = 4;
        sortType = 10;
      } else {
        sortSelectType = 3;
        sortType = 9;
      }
    }
    //刷新数据
    refreshData();
  }

  SortButtonWidgetType _buttoType(int index) {
    if (index == 1) {
      if (sortSelectType == 1) {
        return SortButtonWidgetType.desc;
      } else if (sortSelectType == 2) {
        return SortButtonWidgetType.asce;
      } else {
        return SortButtonWidgetType.normal;
      }
    } else if (index == 2) {
      if (sortSelectType == 3) {
        return SortButtonWidgetType.desc;
      } else if (sortSelectType == 4) {
        return SortButtonWidgetType.asce;
      } else {
        return SortButtonWidgetType.normal;
      }
    }
    return SortButtonWidgetType.normal;
  }

  //订单列表item
  Widget orderItem(GoodsManagementListRow list, int index) {
    return GestureDetector(
      child: Container(
        padding: EdgeInsets.all(10),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            color: Colors.white,
            border: Border.all(color: Color(0xFFE7E7E7), width: 0.5),
            boxShadow: [
              BoxShadow(
                color: Color(0x0D292933),
                offset: Offset(0, 0.5),
                blurRadius: 1,
              )
            ]),
        // decoration: BoxDecoration(
        //     borderRadius: BorderRadius.circular(4), color: Color(0xFFFFFFFF)),

        margin: EdgeInsets.fromLTRB(15, 10, 15, 0),
        child: Stack(
          children: [
            /// 商品组 title
            _getGoodsGroupTitle(list),

            /// 商品组 content
            Container(
              margin: EdgeInsets.only(top: 25),
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4),
                  color: Color(0xFFFFFF)),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  /// 销售额进度
                  _getGoodsSalesProgress(list),

                  /// 商品信息（分为三种情况：商品组内品种数量为1、商品组内品种数量大于1、商品组内品种数量为0）
                  Visibility(
                    /// C3, visible: false,
                    visible: (list.imagesList?.length ?? 0) > 0,
                    child: Container(
                      margin: EdgeInsets.only(bottom: 10),
                      child: Stack(
                        children: [
                          /// C1, 一张图 + 商品详情
                          _getSingleGoodContainer(list),
                          // /// C2, 三张图
                          _getMultiGoodsContainer(list),
                        ],
                      ),
                    ),
                  ),

                  Visibility(
                    visible: list.isTeam!,
                    child: Row(
                      children: [
                        Expanded(
                          child: Container(),
                        ),
                        Container(
                          width: 100,
                          margin: EdgeInsets.only(right: 1),
                          padding: EdgeInsets.all(5),
                          decoration: new BoxDecoration(
                              border: new Border.all(
                                  color: Color(0xFF00B377), width: 1),
                              color: Color(0xFFFFFFFF),
                              borderRadius: new BorderRadius.circular((3.0))),
                          child: GestureDetector(
                            onTap: () {
                              teamTaskMethod(list);
                            },
                            child: Text(
                              "团队任务拆解",
                              style: TextStyle(
                                  color: Color(0xFF00B377),
                                  fontSize: 13,
                                  fontWeight: FontWeight.w300),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        XYYContainer.open(
            "xyy://crm-app.ybm100.com/task/goods/detail?taskId=${list.taskId.toString()}&timeFilter=${filter.toString()}");
      },
    );
  }

  void teamTaskMethod(GoodsManagementListRow list) {
    XYYContainer.open(
        "xyy://crm-app.ybm100.com/task/goods/team?taskId=${list.taskId.toString()}");
  }

  Widget _getGoodsGroupTitle(GoodsManagementListRow itemGoods) {
    return Container(
      height: 45,
      width: double.infinity,
      padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
      alignment: Alignment.centerLeft,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4), topRight: Radius.circular(4)),
        color: Color(0x676773),
      ),
      child: Row(
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(bottom: 4),
              child: Text(
                "任务名称：${itemGoods.taskName ?? ""}",
                style: TextStyle(
                    color: Color(0xFF292933),
                    fontSize: 15,
                    fontWeight: FontWeight.w500),
                softWrap: true,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          Container(
            width: 44,
            height: 20,
            child: Stack(
              children: [
                Image.asset(
                  "assets/images/goods_management/ic_right1.png",
                  width: 18,
                  height: 18,
                ),
                // Positioned(
                //   right: 0,
                //   child: Visibility(
                //     visible: (itemGoods?.status ?? 0) == 0,
                //     child: Image.asset(
                //       "assets/images/goods_management/ic_right1.png",
                //       width: 18,
                //       height: 18,
                //     ),
                //   ),
                // ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _getGoodsSalesProgress(GoodsManagementListRow itemGoods) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichText(
            text: TextSpan(children: [
              /// 销售额进度(元)，本月销售额目标(元)
              TextSpan(
                text: itemGoods.taskTypeStr,
                style: TextStyle(fontSize: 12, color: Color(0xFF676773)),
              ),

              /// 全部商品显示
              TextSpan(
                text: "${itemGoods.taskFinishNum ?? "--"}",
                style: TextStyle(fontSize: 12, color: Color(0xFF00B377)),
              ),
              TextSpan(
                text: "/${itemGoods.taskTargetNum ?? "--"}",
                style: TextStyle(fontSize: 12, color: Color(0xFF676773)),
              ),
            ]),
          ),
          Container(
            margin: EdgeInsets.only(left: 0, top: 5),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 8,
                    // width: MediaQuery.of(this.context).size.width - 135,
                    margin: EdgeInsets.only(top: 0),
                    // padding: EdgeInsets.only(right: 100),
                    child: RoundedGradientLinearProgressBar(
                      strokeCapRound: true,
                      strokeWidth: 8,
                      colors: [Color(0xFF00C675), Color(0xFF00C675)],
                      backgroundColor: Colors.grey[200],
                      value: double.parse((itemGoods.taskRate ?? 0).toString()),
                      // itemGoods?.taskRate
                    ),
                  ),
                ),

                // ),

                Container(
                  margin: EdgeInsets.only(left: 15),
                  child: RichText(
                    text: TextSpan(children: [
                      /// 销售额进度(元)，本月销售额目标(元)
                      TextSpan(
                        text: "达成:",
                        style:
                            TextStyle(fontSize: 12, color: Color(0xFF676773)),
                      ),
                      TextSpan(
                        text: "${itemGoods.taskRate ?? "--"}%",
                        // text: "${itemGoods?.taskRate.to ?? "--"}%",
                        style:
                            TextStyle(fontSize: 12, color: Color(0xFF00B377)),
                      ),
                    ]),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _getMultiGoodsContainer(GoodsManagementListRow itemGoods) {
    List imagesList = itemGoods.imagesList.split(',');
    String? imgeUrl1 = (imagesList.length) > 0 ? imagesList[0] : null;
    String? imgeUrl2 = (imagesList.length) > 1 ? imagesList[1] : null;
    String? imgeUrl3 = (imagesList.length) > 2 ? imagesList[2] : null;

    return Visibility(
      visible: (imagesList.length) > 1,
      child: Container(
        color: Color(0xFFFFFF),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Expanded(
              child: Row(
                children: [
                  LayoutBuilder(
                    builder:
                        (BuildContext context, BoxConstraints constraints) {
                      var itemWidth = 80.0;
                      if (constraints.maxWidth >= 240) {
                        itemWidth = 80;
                      } else {
                        if (imagesList.length >= 3) {
                          itemWidth = constraints.maxWidth / 3;
                        }
                      }
                      return Opacity(
                        opacity: 1,
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(3),
                              child: ImageWidget(
                                url: '$mImageHost$imgeUrl1',
                                w: itemWidth,
                                h: itemWidth,
                                fit: BoxFit.fitWidth,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  LayoutBuilder(
                    builder:
                        (BuildContext context, BoxConstraints constraints) {
                      var itemWidth = 80.0;
                      if (constraints.maxWidth >= 240) {
                        itemWidth = 80;
                      } else {
                        if (imagesList.length >= 3) {
                          itemWidth = constraints.maxWidth / 3;
                        }
                      }
                      return Opacity(
                        opacity: 1,
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(3),
                              child: ImageWidget(
                                url: '$mImageHost$imgeUrl2',
                                w: itemWidth,
                                h: itemWidth,
                                fit: BoxFit.fitWidth,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                  LayoutBuilder(
                    builder:
                        (BuildContext context, BoxConstraints constraints) {
                      var itemWidth = 80.0;
                      if (constraints.maxWidth >= 240) {
                        itemWidth = 80;
                      } else {
                        if (imagesList.length >= 3) {
                          itemWidth = constraints.maxWidth / 3;
                        }
                      }
                      return Opacity(
                        opacity: 1,
                        child: Stack(
                          children: [
                            ClipRRect(
                              borderRadius: BorderRadius.circular(3),
                              child: ImageWidget(
                                url: '$mImageHost$imgeUrl3',
                                w: itemWidth,
                                h: itemWidth,
                                fit: BoxFit.fitWidth,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
            Container(
              margin: EdgeInsets.only(right: 15),
              color: Color(0xFFF6F6F6),
              width: 1,
              height: 45,
            ),
            Container(
              // margin: EdgeInsets.only(left: 20),
              child: Column(
                children: [
                  Text(
                    "${itemGoods.taskProductNum.toString()}",
                    style: TextStyle(
                        color: Color(0xFF292933),
                        fontSize: 17,
                        fontWeight: FontWeight.w300),
                  ),
                  Text(
                    "品种数量",
                    style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _getSingleGoodContainer(GoodsManagementListRow itemGoods) {
    List imagesList = itemGoods.imagesList.split(',');
    String priceDetailStr = itemGoods.fob ?? "0.00";
    var priceDetailStrList = priceDetailStr.split(".");
    String integer = "";
    String decimal = "";

    if ((priceDetailStrList.length) > 1) {
      integer = priceDetailStrList.first;
      decimal = priceDetailStrList.last;
    }

    return Visibility(
      visible: (imagesList.length) == 1,
      // visible: true,
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            ///图片、零售价
            Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(3),
                  child: ImageWidget(
                    url: '$mImageHost${imagesList[0]}',
                    w: 100,
                    h: 100,
                    fit: BoxFit.contain,
                  ),
                ),
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    color: Color(0x08292933),
                    borderRadius: BorderRadius.circular(3),
                  ),
                ),
                Positioned(
                  bottom: 0,
                  width: 100,
                  height: 18,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Color(0xFFF6F6F6),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(3),
                        bottomRight: Radius.circular(3),
                      ),
                    ),
                    alignment: Alignment.center,
                    child: Text(
                      "零售价¥${itemGoods.suggestPrice ?? "--"}",
                      style: TextStyle(color: Color(0xFF676773), fontSize: 12),
                    ),
                  ),
                ),
              ],
            ),

            ///goodsName...
            Expanded(
              child: Container(
                margin: EdgeInsets.only(left: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    /// 商品名称
                    Container(
                      width: double.infinity,
                      child: Text(
                        "${itemGoods.showName ?? ""}",
                        softWrap: true,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                            color: Color(0xFF292933), fontSize: 15, height: 1),
                      ),
                    ),

                    /// 规格、库存
                    Container(
                      margin: EdgeInsets.fromLTRB(0, 4, 0, 6),
                      child: Text(
                        "${itemGoods.spec ?? ""}",
                        style: TextStyle(
                            color: Color(0xFF9494A6), fontSize: 12, height: 1),
                      ),
                    ),

                    /// 价钱
                    Container(
                      margin: EdgeInsets.fromLTRB(0, 6, 0, 4),
                      child: RichText(
                        /// 元符号
                        text: TextSpan(
                            text: '¥',
                            style: TextStyle(
                                fontSize: 14,
                                color: Color(0xFFFF2121),
                                height: 1),
                            children: [
                              /// 小数点前
                              TextSpan(
                                text: integer,
                                style: TextStyle(
                                  fontSize: 20,
                                  color: Color(0xFFFF2121),
                                  height: 1,
                                ),
                              ),

                              /// 小数点后
                              TextSpan(
                                text: ".$decimal",
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Color(0xFFFF2121),
                                  height: 1,
                                ),
                              ),

                              /// 终端毛利率
                              TextSpan(
                                text:
                                    "(终端毛利率：${itemGoods.grossMargin ?? "--"})",
                                // text: itemGoods?.grossMargin ?? "--",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Color(0xFF9494A6),
                                  height: 1,
                                ),
                              ),
                            ]),
                      ),
                    ),
                    buildTagWidget(itemGoods),
                  ],
                ),
              ),
            ),

            // Expanded(child: Container(),),
            Container(
              margin: EdgeInsets.only(right: 15, top: 30),
              color: Color(0xFFF6F6F6),
              width: 1,
              height: 45,
            ),
            Container(
              margin: EdgeInsets.only(top: 30),
              child: Column(
                children: [
                  Text(
                    "${itemGoods.taskProductNum.toString()}",
                    style: TextStyle(
                        color: Color(0xFF292933),
                        fontSize: 17,
                        fontWeight: FontWeight.w300),
                  ),
                  Text(
                    "品种数量",
                    style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildTagWidget(GoodsManagementListRow itemGoods) {
    if (itemGoods.promoList != null) {
    } else {
      return Container();
    }
    var promoList = itemGoods.promoList!;
    List<Widget> itemlist = [];
    for (var model in promoList) {
      itemlist.add(TextTagWidget(
        model.promoTypeStr,
        textStyle: TextStyle(color: Color(0xFFFF4741), fontSize: 10),
        borderColor: Color(0x80FF4741),
        backgroundColor: Color(0x0DFF4741),
      ));
    }
    return Expanded(
      child: Wrap(
        children: itemlist,
      ),
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }
}

class _SliverAppBarDelegate extends SliverPersistentHeaderDelegate {
  _SliverAppBarDelegate({
    required this.minHeight,
    required this.maxHeight,
    required this.child,
  });

  final double minHeight;
  final double maxHeight;
  final Widget child;

  @override
  double get minExtent => minHeight;

  @override
  double get maxExtent => max(maxHeight, minHeight);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return new SizedBox.expand(child: child);
  }

  @override
  bool shouldRebuild(_SliverAppBarDelegate oldDelegate) {
    return maxHeight != oldDelegate.maxHeight ||
        minHeight != oldDelegate.minHeight ||
        child != oldDelegate.child;
  }
}

class VerticalDottedPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint() // 画笔
      ..strokeWidth = 1 // 画笔宽度
      ..isAntiAlias = true // 是否开启抗锯齿
      ..color = Color(0xFFFFB1AD); // 画笔颜色

    var dashHeight = 2;
    var dashSpace = 1;
    double startY = 0;
    final space = (dashSpace + dashHeight);

    while (startY < size.height) {
      canvas.drawLine(Offset(0, startY), Offset(0, startY + dashHeight), paint);
      startY += space;
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}

/// 数据模型
class _HomeSalesTimeFilter {
  final String title;
  final String filter;
  bool isSelected = false;

  _HomeSalesTimeFilter(this.title, this.filter, {this.isSelected = false});
}
