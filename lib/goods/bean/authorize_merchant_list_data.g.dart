// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'authorize_merchant_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

AuthorizeMerchantListData _$AuthorizeMerchantListDataFromJson(
    Map<String, dynamic> json) {
  return AuthorizeMerchantListData()
    ..total = json['total']
    ..merchantList = (json['merchantList'] as List<dynamic>?)
        ?.map((e) =>
            AuthorizeMerchantItemData.fromJson(e as Map<String, dynamic>?))
        .toList();
}

Map<String, dynamic> _$AuthorizeMerchantListDataToJson(
        AuthorizeMerchantListData instance) =>
    <String, dynamic>{
      'total': instance.total,
      'merchantList': instance.merchantList,
    };

AuthorizeMerchantItemData _$AuthorizeMerchantItemDataFromJson(
    Map<String, dynamic> json) {
  return AuthorizeMerchantItemData()
    ..merchantId = json['merchantId']
    ..merchantName = json['merchantName']
    ..merchantAddress = json['merchantAddress']
    ..thisMonthPayGmv = json['thisMonthPayGmv']
    ..thisMonthBuyNum = json['thisMonthBuyNum']
    ..lastMonthPayGmv = json['lastMonthPayGmv']
    ..lastMonthBuyNum = json['lastMonthBuyNum'];
}

Map<String, dynamic> _$AuthorizeMerchantItemDataToJson(
        AuthorizeMerchantItemData instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'merchantAddress': instance.merchantAddress,
      'thisMonthPayGmv': instance.thisMonthPayGmv,
      'thisMonthBuyNum': instance.thisMonthBuyNum,
      'lastMonthPayGmv': instance.lastMonthPayGmv,
      'lastMonthBuyNum': instance.lastMonthBuyNum,
    };
