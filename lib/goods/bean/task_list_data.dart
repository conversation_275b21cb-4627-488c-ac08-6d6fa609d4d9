import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'task_list_data.g.dart';

@JsonSerializable()
/*
{
  "data": {
    "total": 200,
    "offset": 1,
    "limit": 10,
    "rows": [
      {
        "taskType": "系统/软件售卖",
        "taskStatus": "草稿",
        "theme": "任务1",
        "startTime": "2021.02.25 12:00:55",
        "endTime": "2021.02.26 12:00:55",
        "id": 11,
        "type": 2,
        "status": 1
      }
    ],
    "pageCount": 2615,
    "currentPage": 1,
    "currentPageTmp": 1,
    "unReadCount": 0,
    "lastPage": false
  },
  "status": "success"
}
*/

@JsonSerializable()
class TaskManagementList extends BaseModelV2<TaskManagementList> {
  var total;
  var data;
  List<TaskManagementListRow>? rows;
  TaskManagementList();
  bool? lastPage;

  factory TaskManagementList.fromJson(Map<String, dynamic>? json) =>
      _$TaskManagementListFromJson(json!);

  @override
  TaskManagementList fromJsonMap(Map<String, dynamic>? json) {
    return TaskManagementList.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskManagementListToJson(this);
  }
}

@JsonSerializable()
class TaskManagementListRow extends BaseModelV2<TaskManagementListRow> {
  var taskType;
  var taskStatus;
  var theme;
  var startTime;
  var endTime;
  var id;
  var type;
  var creatorName;
  var publishTime;

  // var status;

  

  TaskManagementListRow();

  factory TaskManagementListRow.fromJson(Map<String, dynamic>? json) =>
      _$TaskManagementListRowFromJson(json!);

  @override
  TaskManagementListRow fromJsonMap(Map<String, dynamic>? json) {
    return TaskManagementListRow.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskManagementListRowToJson(this);
  }
}
