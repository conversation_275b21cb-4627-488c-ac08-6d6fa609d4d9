// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskManagementList _$TaskManagementListFromJson(Map<String, dynamic> json) {
  return TaskManagementList()
    ..total = json['total']
    ..data = json['data']
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => TaskManagementListRow.fromJson(e as Map<String, dynamic>?))
        .toList()
    ..lastPage = json['lastPage'] as bool?;
}

Map<String, dynamic> _$TaskManagementListToJson(TaskManagementList instance) =>
    <String, dynamic>{
      'total': instance.total,
      'data': instance.data,
      'rows': instance.rows,
      'lastPage': instance.lastPage,
    };

TaskManagementListRow _$TaskManagementListRowFromJson(
    Map<String, dynamic> json) {
  return TaskManagementListRow()
    ..taskType = json['taskType']
    ..taskStatus = json['taskStatus']
    ..theme = json['theme']
    ..startTime = json['startTime']
    ..endTime = json['endTime']
    ..id = json['id']
    ..type = json['type']
    ..creatorName = json['creatorName']
    ..publishTime = json['publishTime'];
}

Map<String, dynamic> _$TaskManagementListRowToJson(
        TaskManagementListRow instance) =>
    <String, dynamic>{
      'taskType': instance.taskType,
      'taskStatus': instance.taskStatus,
      'theme': instance.theme,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'id': instance.id,
      'type': instance.type,
      'creatorName': instance.creatorName,
      'publishTime': instance.publishTime,
    };
