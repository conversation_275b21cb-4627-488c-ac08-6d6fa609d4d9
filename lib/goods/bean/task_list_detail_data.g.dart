// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_list_detail_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskListDetailData _$TaskListDetailDataFromJson(Map<String, dynamic> json) {
  return TaskListDetailData()
    ..data = json['data']
    ..id = json['id']
    ..theme = json['theme']
    ..taskType = json['taskType']
    ..startTime = json['startTime']
    ..endTime = json['endTime']
    ..remark = json['remark']
    ..taskStatus = json['taskStatus']
    ..isImage = json['isImage']
    ..visitType = json['visitType']
    ..visitTypeName = json['visitTypeName']
    ..collectContent = json['collectContent']
    ..total = json['total']
    ..creatorName = json['creatorName']
    ..isChildTask = json['isChildTask']
    ..type = json['type']
    ..publishTime = json['publishTime']
    ..childTask = json['childTask'] == null
        ? null
        : ChildTask.fromJson(json['childTask'] as Map<String, dynamic>);
}

Map<String, dynamic> _$TaskListDetailDataToJson(TaskListDetailData instance) =>
    <String, dynamic>{
      'data': instance.data,
      'id': instance.id,
      'theme': instance.theme,
      'taskType': instance.taskType,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'remark': instance.remark,
      'taskStatus': instance.taskStatus,
      'isImage': instance.isImage,
      'visitType': instance.visitType,
      'visitTypeName': instance.visitTypeName,
      'collectContent': instance.collectContent,
      'total': instance.total,
      'creatorName': instance.creatorName,
      'isChildTask': instance.isChildTask,
      'type': instance.type,
      'publishTime': instance.publishTime,
      'childTask': instance.childTask,
    };

ChildTask _$ChildTaskFromJson(Map<String, dynamic> json) {
  return ChildTask()
    ..customerName = json['customerName'] as String?
    ..customerId = json['customerId'] as int?
    ..merchantId = json['merchantId'] as int?
    ..status = json['status'] as int?
    ..taskStatus = json['taskStatus'] as String?
    ..sysUserName = json['sysUserName'] as String?
    ..orderNo = json['orderNo'] as String?
    ..orderId = json['orderId'] as int?
    ..realMoney = json['realMoney'] as String?
    ..bindBD = json['bindBD'] as bool?;
}

Map<String, dynamic> _$ChildTaskToJson(ChildTask instance) => <String, dynamic>{
      'customerName': instance.customerName,
      'customerId': instance.customerId,
      'merchantId': instance.merchantId,
      'status': instance.status,
      'taskStatus': instance.taskStatus,
      'sysUserName': instance.sysUserName,
      'orderNo': instance.orderNo,
      'orderId': instance.orderId,
      'realMoney': instance.realMoney,
      'bindBD': instance.bindBD,
    };
