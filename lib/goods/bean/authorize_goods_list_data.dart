import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'authorize_goods_list_data.g.dart';

@JsonSerializable()
class AuthorizeGoodsListData extends BaseModelV2<AuthorizeGoodsListData> {
  dynamic total;
  dynamic lastPage;
  List<AuthorizeGoodsItemData>? rows;

  AuthorizeGoodsListData();

  factory AuthorizeGoodsListData.fromJson(Map<String, dynamic>? json) =>
      _$AuthorizeGoodsListDataFromJson(json!);

  @override
  AuthorizeGoodsListData fromJsonMap(Map<String, dynamic>? json) {
    return AuthorizeGoodsListData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AuthorizeGoodsListDataToJson(this);
  }
}

@JsonSerializable()
class AuthorizeGoodsItemData extends BaseModelV2<AuthorizeGoodsItemData> {
  dynamic skuId;
  dynamic skuName;
  dynamic skuPrice;
  dynamic shopName;
  dynamic imgUrl;
  dynamic skuStore; // 库存
  dynamic skuSpec; // 规格
  dynamic grantStatus; // 可授权状态
  dynamic reason; // 不可授权原因

  bool? isSelected; // 本地状态，是否选中

  AuthorizeGoodsItemData();

  factory AuthorizeGoodsItemData.fromJson(Map<String, dynamic>? json) =>
      _$AuthorizeGoodsItemDataFromJson(json!);

  @override
  AuthorizeGoodsItemData fromJsonMap(Map<String, dynamic>? json) {
    return AuthorizeGoodsItemData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AuthorizeGoodsItemDataToJson(this);
  }
}
