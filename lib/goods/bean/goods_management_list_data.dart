import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'goods_management_list_data.g.dart';

@JsonSerializable()
class GoodsManagementList extends BaseModel<GoodsManagementList> {
	dynamic  total;
	List<GoodsManagementListRow>? rows;
  GoodsManagementListOther? other;
	GoodsManagementList();
  bool?  lastPage;

	factory GoodsManagementList.fromJson(Map<String, dynamic>? json) =>
			_$GoodsManagementListFromJson(json!);

	@override
	GoodsManagementList fromJsonMap(Map<String, dynamic>? json) {
		return GoodsManagementList.fromJson(json);
	}

	@override
	Map<String, dynamic> toJson() {
		return _$GoodsManagementListToJson(this);
	}
}

@JsonSerializable()
class GoodsManagementListOther extends BaseModel<GoodsManagementListOther> {
	var  totalProductNum; //商品品种总数
	var  totalTaskNum; //商品任务总数
	GoodsManagementListOther();
	factory GoodsManagementListOther.fromJson(Map<String, dynamic>? json) =>
			_$GoodsManagementListOtherFromJson(json!);
	@override
	GoodsManagementListOther fromJsonMap(Map<String, dynamic>? json) {
		return GoodsManagementListOther.fromJson(json);
	}

	@override
	Map<String, dynamic> toJson() {
		return _$GoodsManagementListOtherToJson(this);
	}
}

@JsonSerializable()
class GoodsManagementListPromoList extends BaseModel<GoodsManagementListPromoList> {
	dynamic  planDescription; //活动计划描述
	dynamic  promoType; //商品任务总数
  dynamic  promoTypeStr; //活动类型名
   GoodsManagementListPromoList();
	factory GoodsManagementListPromoList.fromJson(Map<String, dynamic>? json) =>
			_$GoodsManagementListPromoListFromJson(json!);

	@override
	GoodsManagementListPromoList fromJsonMap(Map<String, dynamic>? json) {
		return GoodsManagementListPromoList.fromJson(json);
	}

	@override
	Map<String, dynamic> toJson() {
		return _$GoodsManagementListPromoListToJson(this);
	}
}

@JsonSerializable()
class GoodsManagementListRow extends BaseModel<GoodsManagementListRow> {
	dynamic fob;
	dynamic grossMargin;
	dynamic imagesList;
	bool?  isTeam;
	dynamic showName;
	dynamic spec;
	dynamic  suggestPrice;
	dynamic taskFinishNum;
	dynamic taskId;
	dynamic taskName;
	dynamic taskProductNum;
	dynamic taskRate;
	dynamic taskTargetNum;
	dynamic taskTypeStr;
  List<GoodsManagementListPromoList>? promoList;
	
	GoodsManagementListRow();

	factory GoodsManagementListRow.fromJson(Map<String, dynamic>? json) =>
			_$GoodsManagementListRowFromJson(json!);

	@override
	GoodsManagementListRow fromJsonMap(Map<String, dynamic>? json) {
		return GoodsManagementListRow.fromJson(json);
	}

	@override
	Map<String, dynamic> toJson() {
		return _$GoodsManagementListRowToJson(this);
	}
}
@JsonSerializable()
class GoodsManagementGroupDetailListRow extends BaseModel<GoodsManagementGroupDetailListRow> {
	dynamic id;
	dynamic branchCode;
	dynamic branchName;
	dynamic goodsCode;
	dynamic goodsName;
	dynamic goodsLabel;
	dynamic goodsLabelName;
	dynamic goodsSpec;
	dynamic goodsId;
	dynamic categoryId;
	dynamic goodsManufacturer;
	dynamic goodsShelvesStatus;
	dynamic goodsChannel;
	dynamic goodsStatus;
	dynamic goodsStatusStr;
	dynamic onTheShelfTime;
	dynamic offTheShelfTime;
	dynamic goodsCycle;
	dynamic goodsCycleStr;
	dynamic goodsCycleTips;
	dynamic rebateRatio;
	dynamic rebateRatioStr;
	dynamic rebateRatioTips;
	dynamic getCount;
	dynamic createTime;
	dynamic updateTime;
	List<dynamic>? imageList;
	dynamic imageUrl;
	dynamic price;
	dynamic priceStr;
	dynamic priceDetailStr;
	dynamic retailPrice;
	dynamic retailPriceStr;
	dynamic grossMargin;
	dynamic stock;
	dynamic stockStr;
	dynamic rebateAmount;
	dynamic rebateAmountStr;
	dynamic validity;
	dynamic mediumPackage;
	dynamic piecePackage;
	dynamic approvalNumber;
	dynamic shelfLife;
	dynamic invalidDay;
	dynamic invalidDayStr;
	dynamic claimStatus;
	dynamic salesVolume;
	dynamic salesAmount;
	dynamic salesAmountStr;

	GoodsManagementGroupDetailListRow();

	factory GoodsManagementGroupDetailListRow.fromJson(Map<String, dynamic>? json) =>
			_$GoodsManagementGroupDetailListRowFromJson(json!);

	@override
	GoodsManagementGroupDetailListRow fromJsonMap(Map<String, dynamic>? json) {
		return GoodsManagementGroupDetailListRow.fromJson(json);
	}

	@override
	Map<String, dynamic> toJson() {
		return _$GoodsManagementGroupDetailListRowToJson(this);
	}
}
