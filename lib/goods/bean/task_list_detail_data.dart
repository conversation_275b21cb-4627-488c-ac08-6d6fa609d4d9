import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'task_list_detail_data.g.dart';

@JsonSerializable()
/*
{
  "data": {
    "id": 1,
    "theme": "incididunt sunt",
    "taskType": "信息收集",
    "startTime": "ex",
    "endTime": "laborum Lorem commodo",
    "remark": "nostrud",
    "taskStatus": "cillum ad dolore qui sit",
    "isImage": -18640351.81016712,
    "visitType": 67000916.96391085,
    "collectContent": "deserunt sit",
    "creatorName": "ex",
    "isChildTask": 48320433.14193472,
    "type": -30297420.008112624,
    "status": -79629440.47495916
  },
  "status": "sucess"
}
*/
@JsonSerializable()
class TaskListDetailData extends BaseModelV2<TaskListDetailData> {
  static const TASK_TYPE_COLLECT = 1;
  static const TASK_TYPE_SELL = 2;
  static const TASK_TYPE_BLOCK_BY_LICENSE = 3;

  var data;
  var id;
  var theme;
  var taskType;
  var startTime;
  var endTime;
  var remark;
  var taskStatus;
  var isImage;
  var visitType;
  var visitTypeName;
  var collectContent;
  var total;
  var creatorName;
  var isChildTask;
  var type;

  // var status;
  var publishTime;

  ChildTask? childTask;

  TaskListDetailData();

  List<OrderEntryData> orderBaseList() {
    List<OrderEntryData> list = [];
    // 发布人
    OrderEntryData status = OrderEntryData();
    status.title = "发布人";
    status.content = this.creatorName ?? "--";
    list.add(status);
    // 任务类型
    OrderEntryData number = OrderEntryData();
    number.title = "任务类型";
    number.content = this.taskType ?? "--";
    list.add(number);

    //
    OrderEntryData publishTime = OrderEntryData();
    publishTime.title = "发布时间";
    publishTime.content = this.publishTime ?? "--";
    list.add(publishTime);

    // 任务时间
    OrderEntryData taskTime = OrderEntryData();
    taskTime.title = "任务时间";
    taskTime.content = formatNullText(this.startTime.toString()) +
        " - " +
        formatNullText(this.endTime.toString());
    list.add(taskTime);

    // 是否要求拍照
    OrderEntryData isImage = OrderEntryData();
    isImage.title = "是否要求拍照";
    isImage.content = this.isImage == 1 ? "是" : "否";
    list.add(isImage);

    // 拜访方式
    OrderEntryData visitType = OrderEntryData();
    visitType.title = "拜访方式";
    visitType.content = this.visitTypeName ?? "--";
    list.add(visitType);

    // 任务类型
    OrderEntryData collectContent = OrderEntryData();
    collectContent.title = "收集内容";
    /*
     * 一个特殊的逻辑，如果任务类型为订单卡单（资质过期）类型，那么收集内容要展示为"客户资质"
     * 虽然这个字段是后端统一返回的，但是在其他任务类型时，这个字段包含很多特殊逻辑，展示的内容是根据crm后端的模板配置的。
     * 最后商定结果是端上在这个任务类型上写死这个字段内容。
     * 其实是为了混下工作量，免得字段都是配置的，客户端没工作量，不然不可能向欢哥妥协
     */
    if (this.type == TaskListDetailData.TASK_TYPE_BLOCK_BY_LICENSE &&
        (this.collectContent == null ||
            this.collectContent.toString().isEmpty)) {
      collectContent.content = "客户资质";
    } else {
      collectContent.content = formatNullText(this.collectContent.toString());
    }
    list.add(collectContent);

    // 任务要求
    OrderEntryData remark = OrderEntryData();
    remark.title = "任务要求";
    remark.content = formatNullText(this.remark);
    list.add(remark);
    return list;
  }

  List<OrderEntryData> getChildTaskList() {
    List<OrderEntryData> list = [];
    // 执行人
    list.add(
        OrderEntryData(title: "执行人", content: childTask?.sysUserName ?? "--"));
    // 卡单订单
    list.add(OrderEntryData(
        title: "卡单订单",
        content: childTask?.orderNo?.toString() ?? "--",
        jumpUrl:
            "/order_detail_page?orderId=${childTask?.orderId}&merchantId=${childTask?.merchantId}"));
    // 实付金额
    list.add(OrderEntryData(
        title: "实付金额", content: "¥${childTask?.realMoney ?? "--"}"));

    return list;
  }

  String formatNullText(String? text) {
    if (text == null || text.isEmpty || text == "null") {
      return "--";
    }
    return text;
  }

  factory TaskListDetailData.fromJson(Map<String, dynamic>? json) =>
      _$TaskListDetailDataFromJson(json!);

  @override
  TaskListDetailData fromJsonMap(Map<String, dynamic>? json) {
    return TaskListDetailData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskListDetailDataToJson(this);
  }
}

@JsonSerializable()
class ChildTask extends BaseModelV2<ChildTask> {
  String? customerName; //客户名称
  int? customerId; //客户ID
  int? merchantId; //客户ID，用于跳转
  int? status; //执行状态0 未完成 1已完成
  String? taskStatus; //执行状态0 未完成 1已完成
  String? sysUserName; //执行人
  String? orderNo; //订单编号
  int? orderId; //订单编号，订单主键ID，用于跳转订单详情
  String? realMoney; //实付金额
  bool? bindBD; //是否绑定BD
  ChildTask();

  factory ChildTask.fromJson(Map<String, dynamic> json) =>
      _$ChildTaskFromJson(json);

  @override
  ChildTask fromJsonMap(Map<String, dynamic> json) {
    return ChildTask.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ChildTaskToJson(this);
  }
}

class OrderEntryData {
  String? title;
  String? content;
  int? textColor;
  bool? canCopy = false; // 是否可复制
  String? jumpUrl; //跳转链接

  OrderEntryData(
      {this.title, this.content, this.textColor, this.canCopy, this.jumpUrl});
}
