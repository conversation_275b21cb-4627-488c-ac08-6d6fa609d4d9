import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'authorize_merchant_list_data.g.dart';

@JsonSerializable()
class AuthorizeMerchantListData extends BaseModelV2<AuthorizeMerchantListData> {
  dynamic total;
  List<AuthorizeMerchantItemData>? merchantList;

  AuthorizeMerchantListData();

  factory AuthorizeMerchantListData.fromJson(Map<String, dynamic>? json) =>
      _$AuthorizeMerchantListDataFromJson(json!);

  @override
  AuthorizeMerchantListData fromJsonMap(Map<String, dynamic>? json) {
    return AuthorizeMerchantListData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AuthorizeMerchantListDataToJson(this);
  }
}

@JsonSerializable()
class AuthorizeMerchantItemData extends BaseModelV2<AuthorizeMerchantItemData> {

  dynamic merchantId;
  dynamic merchantName;
  dynamic merchantAddress;
  dynamic thisMonthPayGmv; // 本月实付
  dynamic thisMonthBuyNum; // 本月购买
  dynamic lastMonthPayGmv; // 上月实付
  dynamic lastMonthBuyNum; // 上月购买


  AuthorizeMerchantItemData();

  factory AuthorizeMerchantItemData.fromJson(Map<String, dynamic>? json) =>
      _$AuthorizeMerchantItemDataFromJson(json!);

  @override
  AuthorizeMerchantItemData fromJsonMap(Map<String, dynamic>? json) {
    return AuthorizeMerchantItemData.fromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$AuthorizeMerchantItemDataToJson(this);
  }
}
