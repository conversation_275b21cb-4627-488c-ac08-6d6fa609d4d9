import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/goods_management_list_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/task_management_list_page.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:XYYContainer/XYYContainer.dart';
import 'dart:io';

class GoodsManagement extends BasePage {
  final String? selectedTab;
  final roleType; //区分入口
  GoodsManagement({this.selectedTab = "0", this.roleType = "0"});

  @override
  BaseState initState() {
    return GoodsManagementState(selectedTab: selectedTab??"0", roleType: roleType);
  }
}

class GoodsManagementState extends BaseState<GoodsManagement>
    with SingleTickerProviderStateMixin {
  TabController? _tabController;
  List _tabTitles = ["发布", "收到", "商品"];
  String? selectedTab;
  final roleType;
  double paddingLeft = 50;

  GoodsManagementState({this.selectedTab, this.roleType});

  List<Widget> _tabViews = [
    GoodsManagementListPage(
      selectedTab: 0,
    ),
    TaskManagementListPage(
      selectedTab: 1,
    ),
  ];

  @override
  void initState() {
    super.initState();
  }

  @override
  void onCreate() {
    super.onCreate();
    if (this.roleType == "1" || this.roleType == "3") {
      //BDM 或者跟进人bdm
      _tabTitles = ["发布", "收到", "商品"];
      _tabViews = [
        TaskManagementListPage(
          selectedTab: 1,
          queryType: 1,
        ),
        TaskManagementListPage(
          selectedTab: 1,
          queryType: 2,
        ),
        GoodsManagementListPage(
          selectedTab: 0,
        ),
      ];
    } else {
      _tabTitles = ["我的任务", "任务商品"];
      _tabViews = [
        TaskManagementListPage(
          selectedTab: 1,
          queryType: 2,
        ),
        GoodsManagementListPage(
          selectedTab: 0,
        ),
      ];
    }
    _tabController = TabController(length: _tabTitles.length, vsync: this);
    _tabController!.index = int.parse(selectedTab!);
    // paddingLeft = getScreenWidth()/2 - (getScreenWidth() -100)/2;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xFFFFFFFF),
      body: Container(
        child: SafeArea(
          child: Builder(
            // key: GoodsManagementTabPageState.key,
            builder: (context) => Container(
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  buildAppBar(),
                  // Divider(color: Color(0xFFF0F0F2), height: 1),
                  Divider(color: const Color(0xFFE1E1E5), height: 1),

                  /// TabView
                  Expanded(
                    child: TabBarView(
                      children: _tabViews.map<Widget>((e) => e).toList(),
                      controller: _tabController,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
        // onWillPop: () async {
        //   onBackPressed(context);
        //   return true;
        // },
      ),
    );
  }

  Widget buildAppBar() {
    return Container(
      // padding: EdgeInsets.only(right: showFilter ? 15 : 0),
      height: 42,
      child: Row(
        mainAxisSize: MainAxisSize.max,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: PreferredSize(
              child: Stack(
                // crossAxisAlignment: CrossAxisAlignment.center,
                // Stack
                children: [
                  LeftButton(
                    leftBtnType: LeftButtonType.back,
                    onPressed: () {
                      // Navigator.of(context).pop();
                      if (Navigator.canPop(context)) {
                        Navigator.maybePop(context, "true");
                      } else {
                        if (Platform.isIOS) {
                          XYYContainer.bridgeCall("app_back");
                        } else {
                          SystemNavigator.pop(animated: true);
                        }
                      }
                    },
                  ),
                  Container(
                    // width: 200,
                    margin: EdgeInsets.only(left: 50),
                    padding: EdgeInsets.only(right: 50),
                    alignment: Alignment.center,
                    child: TabBar(
                      controller: _tabController,
                      isScrollable: true,
                      labelColor: Color(0xFF292933),
                      indicator: TabCustomIndicator(wantWidth: 16),
                      // indicatorColor: Color(0xFF00B377,
                      labelStyle:
                          TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
                      unselectedLabelColor: Color(0xFF575766),
                      unselectedLabelStyle: TextStyle(
                          fontSize: 15, fontWeight: FontWeight.normal),
                      tabs: _tabTitles.map((e) => Tab(text: e)).toList(),
                    ),
                  ),
                ],
              ),
              preferredSize: Size.fromHeight(30),
            ),
          ),
        ],
      ),
    );
  }

  /// 首次登录返回数据给上一界面
  void onBackPressed(BuildContext context) {
    if (Platform.isIOS) {
      XYYContainer.bridgeCall("app_back");
    } else {
      SystemNavigator.pop(animated: true);
    }
  }

  @override
  void onDestroy() {}

  @override
  String getTitleName() {
    return "任务系统";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    // return CommonTitleBar(getTitleName(), onLeftPressed: () {

    // });
    return null;
  }
}
