import 'package:XyyBeanSproutsFlutter/goods/buy/data/goods_already_buy_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:flutter/material.dart';

class GoodsAlreadyBuyItem extends StatelessWidget {
  final GoodsAlreadyBuyListData model;
  final bool isFirst;
  final bool isLast;

  GoodsAlreadyBuyItem({
    required this.model,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, isFirst ? 20 : 15, 15, 0),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: getRadius(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  "${model.customerName ?? "--"}",
                  style: TextStyle(
                    color: Color(0xFF292933),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: EdgeInsets.only(top: 5, left: 25),
                child: Image.asset(
                  'assets/images/funnel/funnel_arrow_right.png',
                  width: 13,
                  height: 13,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.5),
          Row(
            children: [
              Expanded(
                flex: 1,
                child: FunnelContentSpan(
                  title: '购买金额：',
                  content: '${model.buyGmv ?? "0.00"}',
                ),
              ),
              Expanded(
                flex: 1,
                child: FunnelContentSpan(
                  title: '购买数量：',
                  content: '${model.buyNum ?? "0"}',
                ),
              ),
            ],
          ),
          SizedBox(height: 8.5),
          FunnelContentSpan(
            title: '购买时间（最近）：',
            content: '${model.lastBuyDate ?? "--"}',
          ),
          SizedBox(height: 15),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: Color(0xFFF5F5F5),
          ),
        ],
      ),
    );
  }

  BorderRadius getRadius() {
    return BorderRadius.only(
      topLeft: this.isFirst ? Radius.circular(4) : Radius.zero,
      topRight: this.isFirst ? Radius.circular(4) : Radius.zero,
      bottomLeft: this.isLast ? Radius.circular(4) : Radius.zero,
      bottomRight: this.isLast ? Radius.circular(4) : Radius.zero,
    );
  }
}
