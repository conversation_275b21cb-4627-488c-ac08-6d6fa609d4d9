import 'package:XyyBeanSproutsFlutter/goods/buy/data/goods_no_buy_data.dart';
import 'package:flutter/material.dart';

class GoodsNoBugItem extends StatelessWidget {
  final GoodNoBuyListData model;
  final bool isFirst;
  final bool isLast;

  GoodsNoBugItem({
    required this.model,
    this.isFirst = false,
    this.isLast = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, isFirst ? 20 : 15, 15, 0),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: getRadius(),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Text(
                  "${model.customerName ?? "--"}",
                  style: TextStyle(
                    color: Color(0xFF292933),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Container(
                padding: EdgeInsets.only(top: 5, left: 25),
                child: Image.asset(
                  'assets/images/funnel/funnel_arrow_right.png',
                  width: 13,
                  height: 13,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.5),
          Row(
            children: [
              Text(
                '最近下单时间：',
                style: TextStyle(
                  color: Color(0xFF666666),
                  fontSize: 12,
                  fontWeight: FontWeight.normal,
                ),
              ),
              Text(
                '${model.lastBuyDate}'.isEmpty ? "--" : '${model.lastBuyDate}',
                style: TextStyle(
                  color: Color(0xFF292933),
                  fontSize: 12,
                  fontWeight: FontWeight.normal,
                ),
              ),
            ],
          ),
          SizedBox(height: 15),
          Divider(
            height: 0.5,
            thickness: 0.5,
            color: Color(0xFFF5F5F5),
          ),
        ],
      ),
    );
  }

  BorderRadius getRadius() {
    return BorderRadius.only(
      topLeft: this.isFirst ? Radius.circular(4) : Radius.zero,
      topRight: this.isFirst ? Radius.circular(4) : Radius.zero,
      bottomLeft: this.isLast ? Radius.circular(4) : Radius.zero,
      bottomRight: this.isLast ? Radius.circular(4) : Radius.zero,
    );
  }
}
