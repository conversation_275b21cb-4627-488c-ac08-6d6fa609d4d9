import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/button/sort_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/goods/buy/data/goods_already_buy_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/buy/widget/goods_already_buy_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class GoodsAlreadyBuyPage extends BasePage {
  // 商品ID
  final dynamic skuId;
  // 类型 1:本月 2:上月
  final dynamic type;

  GoodsAlreadyBuyPage({
    required this.skuId,
    required this.type,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return GoodsAlreadyBuyPageState();
  }
}

class GoodsAlreadyBuyPageState extends BaseState<GoodsAlreadyBuyPage> {
  // 排序参数
  dynamic sortyKey = 3;
  dynamic sortType = 1;
  // 排序按钮当前选中得控制器
  ValueNotifier<SortControllerButtonState> sortController =
      ValueNotifier(SortControllerButtonState.desc);
  // 刷新控制器
  EasyRefreshController _refreshController = EasyRefreshController();

  // 列表数据源
  List<GoodsAlreadyBuyListData> source = [];

  @override
  void initState() {
    this.requestAlreadyBuyData();
    super.initState();
  }

  @override
  void dispose() {
    this._refreshController.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          getSortWidget(),
          Expanded(
            child: Container(
              padding: EdgeInsets.only(left: 15, right: 15),
              child: EasyRefresh(
                controller: _refreshController,
                onRefresh: () async {
                  this.requestAlreadyBuyData();
                },
                child: ListView.builder(
                  itemCount: this.source.length,
                  itemBuilder: (BuildContext context, int index) {
                    GoodsAlreadyBuyListData model = this.source[index];
                    return GestureDetector(
                      onTap: () {
                        FunnelUtil.jumpMerchantInfo(model.merchantId);
                      },
                      behavior: HitTestBehavior.opaque,
                      child: GoodsAlreadyBuyItem(
                        isFirst: index == 0,
                        isLast: index == this.source.length - 1,
                        model: model,
                      ),
                    );
                  },
                ),
                emptyWidget: this.getEmptyWidget(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.source.isEmpty) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getSortWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 10, 20, 10),
      child: Row(
        children: [
          SortControllerButton(
            name: '购买时间',
            sortKey: 3,
            controller: this.sortController,
            onPressed: selectSortAction,
          ),
          SizedBox(width: 25),
          SortControllerButton(
            name: '购买量',
            sortKey: 1,
            onPressed: selectSortAction,
          ),
          SizedBox(width: 25),
          SortControllerButton(
            name: '购买金额',
            sortKey: 2,
            onPressed: selectSortAction,
          ),
        ],
      ),
    );
  }

  void selectSortAction(
      dynamic key, ValueNotifier<SortControllerButtonState> controller) {
    if (this.sortController != controller) {
      this.sortController.value = SortControllerButtonState.normal;
      this.sortController = controller;
    }
    this.sortyKey = key;
    this.sortType = controller.value == SortControllerButtonState.desc ? 1 : 0;
    showLoadingDialog();
    this.requestAlreadyBuyData();
  }

  void requestAlreadyBuyData() async {
    if (this.source.isEmpty) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<GoodsAlreadyBuyListData>(GoodsAlreadyBuyListData())
            .requestDataV2(
      'sku/buyCustomers',
      parameters: {
        "skuId": widget.skuId,
        "sceneType": widget.type,
        "sortFiled": this.sortyKey,
        "sortType": this.sortType,
      },
    );
    dismissLoadingDialog();
    if (mounted) {
      this._refreshController.finishRefresh();
      if (result.isSuccess == true) {
        this.source = result.getListData() ?? [];
        setState(() {});
      } else {
        showToast(result.errorMsg ?? '客户列表请求失败');
      }
    }
  }

  @override
  String getTitleName() {
    return "已购客户";
  }
}
