import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'goods_no_buy_data.g.dart';

@JsonSerializable()
class GoodNoBuyListData extends BaseModelV2<GoodNoBuyListData> {
  dynamic customerName;
  dynamic lastBuyDate;
  dynamic customerId;
  dynamic merchantId;
  dynamic addCar;
  dynamic browse;
  dynamic lastMonthBuy;

  GoodNoBuyListData();

  @override
  GoodNoBuyListData fromJsonMap(Map<String, dynamic> json) {
    return _$GoodNoBuyListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$GoodNoBuyListDataToJson(this);
  }
}
