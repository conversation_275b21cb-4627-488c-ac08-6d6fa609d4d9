import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/button/text_state_button.dart';
import 'package:XyyBeanSproutsFlutter/goods/buy/data/goods_no_buy_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/buy/widget/goods_no_buy_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

enum GoodsNoBuyPageType {
  all,
  add,
  browse,
  last,
}

class GoodsNoBuyPage extends BasePage {
  final dynamic skuId; // 商品id
  final dynamic sceneType; // 时间参数 3:本月 8:上月

  GoodsNoBuyPage({this.skuId, this.sceneType});

  @override
  BaseState<StatefulWidget> initState() {
    return GoodsNoBuyPageState();
  }
}

class GoodsNoBuyPageState extends BaseState<GoodsNoBuyPage> {
  /// 筛选按钮控制器
  ValueNotifier<TextButtonState> filterController =
      ValueNotifier(TextButtonState.selected);

  GoodsNoBuyPageType filterType = GoodsNoBuyPageType.all;

  List<GoodNoBuyListData> source = [];

  EasyRefreshController refreshController = EasyRefreshController();

  @override
  void dispose() {
    this.refreshController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.requestListData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    var showSource = this.currentSource;
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          this.getTopTypeFilter(),
          Expanded(
            child: Container(
              padding: EdgeInsets.only(left: 15, right: 15),
              child: EasyRefresh(
                controller: this.refreshController,
                onRefresh: () async {
                  this.requestListData();
                },
                child: ListView.builder(
                  itemCount: showSource.length,
                  itemBuilder: (BuildContext context, int index) {
                    var model = showSource[index];
                    return GestureDetector(
                      onTap: () {
                        FunnelUtil.jumpMerchantInfo(model.merchantId);
                      },
                      behavior: HitTestBehavior.opaque,
                      child: GoodsNoBugItem(
                        model: model,
                        isFirst: index == 0,
                        isLast: index == showSource.length - 1,
                      ),
                    );
                  },
                ),
                emptyWidget: emptyWidget(),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget? emptyWidget() {
    if (this.currentSource.isEmpty) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopTypeFilter() {
    return Container(
      height: 40,
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(left: 20, right: 20),
      child: Row(
        children: [
          Expanded(
            child: TextStateButton(
              title: "全部",
              onPressed: (controller) {
                this.changeFilterAction(controller);
                if (this.filterType != GoodsNoBuyPageType.all) {
                  this.filterType = GoodsNoBuyPageType.all;
                  setState(() {});
                }
              },
              controller: this.filterController,
            ),
          ),
          Expanded(
            child: TextStateButton(
              title: "已加购",
              onPressed: (controller) {
                this.changeFilterAction(controller);
                if (this.filterType != GoodsNoBuyPageType.add) {
                  this.filterType = GoodsNoBuyPageType.add;
                  setState(() {});
                }
              },
            ),
          ),
          Expanded(
            child: TextStateButton(
              title: "7日浏览",
              onPressed: (controller) {
                this.changeFilterAction(controller);
                if (this.filterType != GoodsNoBuyPageType.browse) {
                  this.filterType = GoodsNoBuyPageType.browse;
                  setState(() {});
                }
              },
            ),
          ),
          Expanded(
            child: TextStateButton(
              title: "上月购买",
              onPressed: (controller) {
                this.changeFilterAction(controller);
                if (this.filterType != GoodsNoBuyPageType.last) {
                  this.filterType = GoodsNoBuyPageType.last;
                  setState(() {});
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  void changeFilterAction(ValueNotifier<TextButtonState> controller) {
    if (controller == this.filterController) {
      return;
    }
    this.filterController.value = TextButtonState.normal;
    this.filterController = controller;
  }

  @override
  String getTitleName() {
    return "未购客户";
  }

  // Request
  void requestListData() async {
    if (source.length == 0) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<GoodNoBuyListData>(GoodNoBuyListData()).requestDataV2(
      'sku/notBuyCustomers',
      parameters: {
        'skuId': widget.skuId,
        'sceneType': widget.sceneType,
      },
    );
    dismissLoadingDialog();
    if (mounted) {
      this.refreshController.finishRefresh();
      if (result.isSuccess == true) {
        this.source = result.getListData() ?? [];
        setState(() {});
      } else {
        showToast(result.errorMsg ?? "获取客户列表失败");
      }
    }
  }

  List<GoodNoBuyListData> get currentSource {
    return source.where((element) {
      switch (this.filterType) {
        case GoodsNoBuyPageType.all:
          return true;
        case GoodsNoBuyPageType.add:
          return "${element.addCar}" == "true";
        case GoodsNoBuyPageType.browse:
          return "${element.browse}" == "true";
        case GoodsNoBuyPageType.last:
          return "${element.lastMonthBuy}" == "true";
        default:
          return false;
      }
    }).toList();
  }
}
