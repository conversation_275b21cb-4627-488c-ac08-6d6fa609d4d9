///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-20 14:27

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_advantage_model.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_health_model.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_sku_model.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/price_trend_chart.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_price_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_sku_item.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_count_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

const List<Map<String, dynamic>> tabs = [
  {
    'title': '连锁',
    'businessFormatType': 1,
  },
  {
    'title': '非连锁',
    'businessFormatType': 2,
  },
];

const int defaultPageSize = 5;

class PriceHealthSkuList extends BasePage {
  // spu编号
  final String? spuId;

  final String? businessFormatType;

  final String? platformType;

  final String? provinceCode;

  PriceHealthSkuList({this.spuId,this.businessFormatType,this.platformType,this.provinceCode});

  @override
  BaseState<StatefulWidget> initState() => PriceHealthSkuListState();
}

class PriceHealthSkuListState extends BaseState<PriceHealthSkuList> with TickerProviderStateMixin {
  List<PriceSkuItemModel> skuDataSource = [];

  // EasyRefreshController controller = EasyRefreshController();

  TabController? tabController;

  List<int> productCounts = []; // 全部 优 劣

  String? mImageHost;

  List<PriceShopModel> shopList = [];

  // 接口请求参数
  Map<String, dynamic> params = {'pageNum': 1, 'salesVolume': 1, 'timeType': 1};

  bool _hasMore = true;

  bool _isSearch = false;

  @override
  void initState() {
    super.initState();

    initData();
  }

  void initData() {
    params['spuId'] = widget.spuId;
    params['businessFormatType'] = widget.businessFormatType;
    params['platformType'] = widget.platformType;
    params['provinceCode'] = widget.provinceCode;
    tabController = TabController(length: tabs.length, vsync: this);
    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        setState(() {
          mImageHost = value['image_host'];
        });
      }
    });
  }

  @override
  void dispose() {
    tabController!.dispose();
    super.dispose();
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      "商品价格力",
      backgroundColor: Colors.white,
      leftType: LeftButtonType.back,
      rightButtons: [buildSearchButton()],
    );
  }

  Widget buildSearchButton() {
    return GestureDetector(
      onTap: () {
        XYYContainer.open("/product_search_history", callback: (callBackParams) {
          String keyword = callBackParams?['keyword'];
          if (keyword.isNotEmpty) {
            params['keyword'] = keyword;
            searchData();
          }
        });
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_search.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      child: Column(
        children: [
          ProductCountFilterBar(
              initialCounts: productCounts,
              onTapFun: (index) {
                if (index > 0) {
                  params['productPricePowerType'] = index;
                } else {
                  params.remove('productPricePowerType');
                }
                _resetAndLoadData(callSkuCount: false);
              }),
          ProductPriceFilterBar(
            filterType: PriceFilterType.TypeSku,
            provinceCode: widget.provinceCode,
            shopList: shopList,
            onSelectChangedFun: (filterParams) {
              params = {...params, ...filterParams};
              params.removeWhere((key, value) => value == null);
              refreshData();
            },
          ),
          buildListContent(context),
        ],
      ),
    );
  }

  Expanded buildListContent(BuildContext context) {
    return Expanded(
      child: EasyRefresh(
        onRefresh: refreshData,
        onLoad: _hasMore ? loadMoreList : null, // 没有更多数据时禁用加载更多
        child: ListView.builder(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
          itemCount: skuDataSource.length,
          itemBuilder: (context, index) {
            return ProductSkuItem(
              mImageHost: mImageHost,
              skuItemModel: skuDataSource[index],
              onTapPriceTrend: (skuItemModel) {
                requestSkuTrend(context, skuItemModel);
              },
              rank: index + 1,
            );
          },
        ),
        emptyWidget: getEmptyWidget(),
      ),
    );
  }

  // 自定义导航栏
  TabBar buildTabBarWidget() {
    return TabBar(
      controller: tabController,
      isScrollable: true,
      labelColor: Color(0xFF292933),
      indicator: TabCustomIndicator(wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
      labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      unselectedLabelColor: Color(0xFF676773),
      unselectedLabelStyle: TextStyle(fontSize: 16),
      tabs: tabs.map((e) => Tab(text: e['title'])).toList(),
      onTap: (index) {
        Map<String, dynamic> tabItem = tabs[index];
        params['businessFormatType'] = tabItem['businessFormatType'];
        refreshData();
      },
    );
  }

  Widget? getEmptyWidget() {
    if (skuDataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "";
  }

  void showPriceTrendSheet(BuildContext context, List<PriceTrendModel> skuTrendModels, PriceSkuItemModel skuItemModel) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      ),
      builder: (BuildContext context) {
        return PriceTrendChart(trendList: skuTrendModels, skuItemModel: skuItemModel);
      },
    );
  }

  requestSkuTrend(BuildContext context, PriceSkuItemModel skuItemModel) async {
    showLoadingDialog(msg: '加载中...');
    var result = await NetworkV2<PriceTrendModel>(PriceTrendModel()).requestDataV2(
      '/price/skuTrend',
      method: RequestMethod.GET,
      parameters: {'skuId': skuItemModel.skuId},
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      List<PriceTrendModel> trendModels = result.getListData() ?? [];
      showPriceTrendSheet(context, trendModels, skuItemModel);
    }
  }

  requestShopData() async {
    var result = await NetworkV2<PriceShopModel>(PriceShopModel()).requestDataV2(
      '/price/customer/id',
      method: RequestMethod.GET,
      parameters: params,
    );
    if (result.isSuccess == true) {
      setState(() {
        shopList = result.getListData() ?? [];
      });
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
    }
  }

  void reuqestSkuCount() async {
    Map<String, dynamic> countParams = {...params};
    countParams.remove('pageNum');
    countParams.remove('pageSize');
    countParams.remove('productPricePowerType');
    var result = await NetworkV2<PriceSpuModel>(PriceSpuModel()).requestDataV2('/price/sku/count', method: RequestMethod.GET, parameters: countParams);
    if (result.isSuccess == true) {
      final PriceSpuModel? priceSpuModel = result.getData();
      productCounts = [
        priceSpuModel?.totalProductCount ?? 0,
        priceSpuModel?.priceAdvantageProductCount ?? 0,
        priceSpuModel?.priceDisadvantageProductCount ?? 0,
      ];
      setState(() {});
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
    }
  }

  void reuqestPriceAdvantageList() async {
    Map<String, dynamic> advangeParams = {
      ...params,
      'pageSize': defaultPageSize,
    };
    showLoadingDialog();
    var result = await NetworkV2<PriceSkuModel>(PriceSkuModel()).requestDataV2('/price/product/sku', method: RequestMethod.GET, parameters: advangeParams);
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      setState(() {
        final List<PriceSkuItemModel> sourceList = result.getData()?.list ?? [];
        skuDataSource.addAll(sourceList);
        _hasMore = sourceList.length >= defaultPageSize;
      });
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
    }
  }

// 搜索方法
  Future<void> searchData() async {
    _isSearch = true;
    _resetAndLoadData();
  }

// 刷新方法
  Future<void> refreshData() async {
    _isSearch = false;
    _resetAndLoadData();
  }

  Future<void> _resetAndLoadData({bool callSkuCount = true}) async {
    params['pageNum'] = 1;
    if (!_isSearch) {
      params.remove('keyword'); // 非搜索时移除 keyword
    }
    skuDataSource = [];
    if (callSkuCount) {
      reuqestSkuCount();
    }
    reuqestPriceAdvantageList();
    requestShopData();
  }

  Future<void> loadMoreList() async {
    int currentPage = (params['pageNum'] as int?) ?? 1;
    params['pageNum'] = currentPage + 1;
    reuqestPriceAdvantageList();
  }
}
