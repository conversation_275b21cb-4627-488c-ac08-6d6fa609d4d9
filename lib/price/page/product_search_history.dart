///@Description(描述)     xxxx
///@author(作者)          zhang<PERSON>nhong
///@create(时间)          2025-01-21 09:43

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_bar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ProductSearchHistory extends BasePage {
  // 搜索关键字 如果为空则认为是第一次搜索
  final String? keyword;

  ProductSearchHistory({
    // required this.searchType,
    this.keyword,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return ProductSearchHistoryState();
  }
}

class ProductSearchHistoryState extends BaseState<ProductSearchHistory> {
  TextEditingController _controller = TextEditingController();

  List<String> searchHistory = [];

  String priceHealthSearchKey = "price_health_search_history";

  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    initsearchHistoryList();
    _controller.text = widget.keyword ?? "";
  }

  @override
  void dispose() {
    _focusNode.unfocus();
    _controller.dispose();
    super.dispose();
  }

  void initsearchHistoryList() async {
    List<dynamic> result = await XYYContainer.storageChannel.getValue(priceHealthSearchKey) ?? [];
    searchHistory = result.map((e) => '$e').toList();
    setState(() {});
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _focusNode.unfocus();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        padding: EdgeInsets.only(left: 15, right: 15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  '历史搜索',
                  style: TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Spacer(),
                GestureDetector(
                  onTap: () {
                    deleteAllHoistory();
                  },
                  child: IntrinsicHeight(
                    child: Row(
                      children: [
                        SizedBox(
                          height: 40,
                          child: Image.asset(
                            'assets/images/base/searchHistory_delete_icon.png',
                            width: 14,
                            height: 14,
                          ),
                        ),
                        SizedBox(width: 4),
                        Text(
                          '清空',
                          style: TextStyle(color: Color(0xFF9494A6), fontSize: 12),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
            Wrap(
              alignment: WrapAlignment.start,
              spacing: 10,
              runSpacing: 10,
              children: searchHistoryItem(),
            )
          ],
        ),
      ),
    );
  }

  // 历史记录item
  List<Widget> searchHistoryItem() {
    return searchHistory
        .map((e) => GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                changeKeyword(e);
              },
              child: Container(
                padding: EdgeInsets.fromLTRB(8, 5, 8, 5),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: Color(0xFFF7F7F8),
                ),
                child: Text(e,
                    style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 13,
                    )),
              ),
            ))
        .toList();
  }

  // 删除历史记录
  void deleteAllHoistory() {
    showCupertinoDialog(
      context: this.context,
      builder: (BuildContext context) {
        return CupertinoAlertDialog(
          title: Text("是否清空历史记录"),
          actions: [
            CupertinoDialogAction(
              child: Text("取消"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            CupertinoDialogAction(
              child: Text("确定"),
              onPressed: () {
                XYYContainer.storageChannel.delete(priceHealthSearchKey);
                searchHistory = [];
                setState(() {});
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // 增加搜索关键词
  void changeKeyword(String keyword) async {
    // 存储搜索历史
    if (searchHistory.contains(keyword)) {
      searchHistory.remove(keyword);
    }
    if (searchHistory.length >= 10) {
      searchHistory = searchHistory.sublist(0, 8);
    }
    searchHistory.insert(0, keyword);
    await XYYContainer.storageChannel.put(priceHealthSearchKey, searchHistory);

    // 收起键盘
    _focusNode.unfocus();

    Navigator.of(context).pop({"keyword": keyword});
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonSearchBar(
      focusNode: _focusNode,
      hintText: "请输入商品名称/ECSPU/厂家",
      showLeading: false,
      hideCancel: false,
      autoFocus: true,
      controller: _controller,
      onSearch: (value) {
        String inputValue = value.trim();
        if (inputValue.isEmpty) {
          _controller.text = inputValue;
          showToast("请输入搜索内容");
          return;
        }
        changeKeyword(inputValue);
      },
    );
  }
}
