import 'dart:math';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_advantage_model.dart';
import 'package:XyyBeanSproutsFlutter/price/data/provice_item_model.dart';
import 'package:XyyBeanSproutsFlutter/price/logic/location_logic.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/price_health_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/price_health_statistics.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_spu_item.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/home_province_price_rank.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/select_skew_type_bar.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

const List<Map<String, dynamic>> tabs = [
  {
    'title': '全部',
    'businessFormatType': 3,
  },
  {
    'title': '连锁',
    'businessFormatType': 1,
  },
  {
    'title': '非连锁',
    'businessFormatType': 2,
  },
];

const List<Map<String, dynamic>> productTypes = [
  {
    'title': '优价商品',
    'productPricePowerType': 1,
  },
  {
    'title': '劣价商品',
    'productPricePowerType': 2,
  },
];

class PriceHealthHomePage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() => PriceHealthHomePageState();
}

class PriceHealthHomePageState extends BaseState<PriceHealthHomePage> with TickerProviderStateMixin {
  // 定义固定的头部数据
  final List<PriceAdvantageItemModel> headerItems = [PriceAdvantageItemModel(), PriceAdvantageItemModel()];

  // 实际数据源
  List<PriceAdvantageItemModel> priceDataSource = [];

  EasyRefreshController controller = EasyRefreshController();

  final globalKey = GlobalKey();

  TabController? tabController;

  int skewType = 0; //优价劣价

  // 接口请求参数
  Map<String, dynamic> params = {
    'businessFormatType': 3, //1-连锁，2-非连锁，3-全部 默认全部
    'timeType': 1, //1-今日，2-近7天，3-近14天，4-近30天
    'platformType': 3, //1-自营，2-POP，3-全平台
  };
  HomePriceItemModel? statisticsPriceModel;

  List<PriceProviceItemModel> provinceList = [];

  String? mImageHost;

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: tabs.length, vsync: this);

    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        setState(() {
          mImageHost = value['image_host'];
        });
      }
    });

    updateDataSource([]);
    getLocation();
  }

  // 更新数据源的方法
  void updateDataSource(List<PriceAdvantageItemModel> newItems) {
    setState(() {
      priceDataSource = [...headerItems, ...newItems];
    });
  }

  @override
  void dispose() {
    controller.dispose();
    tabController!.dispose();
    super.dispose();
  }

  Future<void> getLocation() async {
    showLoadingDialog();
    final locationData = await LocationLogic.getLocationData();
    if (locationData != null) {
      params["provinceCode"] = int.tryParse(locationData.provinceCode ?? '-1');
    }
    refreshData();
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      "",
      backgroundColor: Colors.white,
      leftType: LeftButtonType.back,
      titleWidget: buildTabBarWidget(),
    );
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      child: Column(
        children: [
          PriceHealthFilterBar(
              globalKey: globalKey,
              onSelectTypeChanged: (filterParams) {
                params = {...params, ...filterParams}; // 深拷贝后面的值会覆盖前面的
                refreshData();
              }),
          buildListContent(context),
        ],
      ),
    );
  }

  Expanded buildListContent(BuildContext context) {
    return Expanded(
      child: EasyRefresh(
        onRefresh: refreshData,
        // controller: _controller,
        child: ListView.builder(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
          itemCount: priceDataSource.length + 2,
          itemBuilder: (context, index) {
            if (index == 0) {
              return PriceHealthStatistics(priceItemModel: statisticsPriceModel);
            } else if (index == 1) {
              return SelectSkewTypeBar(
                productTypes: productTypes,
                initialIndex: skewType,
                onTabSelected: (typeMap, index) {
                  skewType = index;
                  params['productPricePowerType'] = typeMap['productPricePowerType'];
                  reuqestPriceTopList();
                },
              );
            } else if (index == priceDataSource.length) {
              return buildLookMore();
            } else if (index == priceDataSource.length + 1) {
              return HomeProvincePriceRank(
                provinces: provinceList,
                onTapMore: () {
                  XYYContainer.open("/provice_price_health_list");
                },
              );
            }
            return ProductSpuItem(
              rank: index + 1 - 2,
              priceAdvantageItemModel: priceDataSource[index],
              onTapLookSale: (priceAdvantageItemModel) {
                String businessFormatType = priceAdvantageItemModel.businessFormatType.toString();
                String platformType = priceAdvantageItemModel.platformType.toString();
                String provinceCode = priceAdvantageItemModel.provinceCode.toString();
                String url =
                    "/price_health_sku_list?spuId=${priceAdvantageItemModel.spuId}&businessFormatType=$businessFormatType&platformType=$platformType&provinceCode=$provinceCode";
                XYYContainer.open(url);
              },
              mImageHost: mImageHost,
            );
          },
        ),
      ),
    );
  }

  // 自定义导航栏
  TabBar buildTabBarWidget() {
    return TabBar(
      controller: tabController,
      isScrollable: true,
      labelColor: Color(0xFF292933),
      indicator: TabCustomIndicator(wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
      labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      unselectedLabelColor: Color(0xFF676773),
      unselectedLabelStyle: TextStyle(fontSize: 16),
      tabs: tabs.map((e) => Tab(text: e['title'])).toList(),
      onTap: (index) {
        Map<String, dynamic> tabItem = tabs[index];
        params['businessFormatType'] = tabItem['businessFormatType'];

        refreshData();
      },
    );
  }

  /// 查看更多
  Widget buildLookMore() {
    return Container(
      height: 44,
      color: Colors.white,
      child: GestureDetector(
        onTap: () {
          String provinceCode = params['provinceCode'].toString();
          XYYContainer.open("/price_health_ecspu_list?provinceCode=$provinceCode");
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              "查看更多",
              style: TextStyle(color: Color(0xFF949498), fontSize: 14),
            ),
            Image.asset(
              'assets/images/commodity/commodity_rank_arrow.png',
              width: 12,
              height: 12,
            )
          ],
        ),
      ),
    );
  }

  @override
  String getTitleName() {
    return "";
  }

  Future<void> refreshData() async {
    if (params['businessFormatType'] == 3) {
      params.remove('businessFormatType');
    }
    reuqestPriceStatistics();
    reuqestPriceTopList();
    reuqestProviceList();
  }

  //获取顶部卡片的统计
  void reuqestPriceStatistics() async {
    Map<String, dynamic> newParams = Map.from(params);
    newParams.remove('productPricePowerType');
    var result = await NetworkV2<HomePriceItemModel>(HomePriceItemModel()).requestDataV2('/price/statistics', method: RequestMethod.GET, parameters: newParams);
    if (result.isSuccess == true) {
      statisticsPriceModel = result.getData();
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
    }
    setState(() {});
  }

  //查询价优或价劣Top3商品
  void reuqestPriceTopList() async {
    Map<String, dynamic> topParams = {
      ...params,
      'pageNum': 1,
      'pageSize': 3,
    };
    showLoadingDialog();
    var result =
        await NetworkV2<PriceAdvantageModel>(PriceAdvantageModel()).requestDataV2('/price/advantage/top', method: RequestMethod.GET, parameters: topParams);
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      final List<PriceAdvantageItemModel> sourceList = result.getData()?.list ?? [];
      updateDataSource(sourceList);
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
      updateDataSource([]);
    }
  }

  void reuqestProviceList() async {
    Map<String, dynamic> proviceParams = Map.from(params);
    proviceParams.remove('productPricePowerType');
    var result =
        await NetworkV2<PriceProviceItemModel>(PriceProviceItemModel()).requestDataV2('/price/province', method: RequestMethod.GET, parameters: proviceParams);
    if (result.isSuccess == true) {
      setState(() {
        provinceList = result.getListData() ?? [];
      });
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
    }
  }
}
