import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';

///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-20 10:06

import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_advantage_model.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_count_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_price_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_spu_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

const List<Map<String, dynamic>> tabs = [
  {
    'title': '连锁',
    'businessFormatType': 1,
  },
  {
    'title': '非连锁',
    'businessFormatType': 2,
  },
];

const int defaultPageSize = 5;

class PriceHealthEcspuList extends BasePage {

  final String? provinceCode;
  PriceHealthEcspuList({this.provinceCode});

  @override
  BaseState<StatefulWidget> initState() => PriceHealthEcspuListState();
}

class PriceHealthEcspuListState extends BaseState<PriceHealthEcspuList> with TickerProviderStateMixin {
  List<PriceAdvantageItemModel> priceDataSource = [];

  EasyRefreshController controller = EasyRefreshController();

  final globalKey = GlobalKey();

  TabController? tabController;
  List<int> productCounts = []; // 全部 优 劣

  String? mImageHost;

  // 接口请求参数
  Map<String, dynamic> params = {
    // 'keyword': '',
    'pageNum': 1,
    'businessFormatType': 1, //1-连锁，2-非连锁，
  };

  bool _hasMore = true;
  bool _isSearch = false;//是否为搜索

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: tabs.length, vsync: this);

    XYYContainer.bridgeCall('app_host').then((value) {
      if (value is Map) {
        setState(() {
          mImageHost = value['image_host'];
        });
      }
    });

    params['provinceCode'] = widget.provinceCode;
  }

  @override
  void dispose() {
    controller.dispose();
    tabController!.dispose();
    super.dispose();
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      "",
      backgroundColor: Colors.white,
      leftType: LeftButtonType.back,
      titleWidget: buildTabBarWidget(),
      rightButtons: [buildSearchButton()],
    );
  }

  Widget buildSearchButton() {
    return GestureDetector(
      onTap: () {
        XYYContainer.open("/product_search_history", callback: (callBackParams) {
          String keyword = callBackParams?['keyword'];
          if (keyword.isNotEmpty) {
            params['keyword'] = keyword;
            searchData();
          }
        });
      },
      child: Container(
        height: 44,
        width: 44,
        alignment: Alignment.center,
        child: Image.asset(
          "assets/images/titlebar/icon_search.png",
          width: 21,
          height: 21,
        ),
      ),
    );
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      child: Column(
        children: [
          ProductCountFilterBar(
              initialCounts: productCounts,
              onTapFun: (index) {
                if (index > 0) {
                  params['productPricePowerType'] = index;
                } else {
                  params.remove('productPricePowerType');
                }
                _resetAndLoadData(callSkuCount: false);
              }),
          ProductPriceFilterBar(
            filterType: PriceFilterType.TypeEcspu,
            provinceCode: widget.provinceCode,
            onSelectChangedFun: (filterParams) {
              print(filterParams);
              params = {...params, ...filterParams}; // 深拷贝后面的值会覆盖前面的
              params.removeWhere((key, value) => value == null);
              refreshData();
            },
          ),
          buildListContent(context),
        ],
      ),
    );
  }

  Expanded buildListContent(BuildContext context) {
    return Expanded(
      child: EasyRefresh(
        onRefresh: refreshData,
        onLoad: _hasMore ? loadMoreList : null, // 没有更多数据时禁用加载更多
        child: ListView.builder(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
          itemCount: priceDataSource.length,
          itemBuilder: (context, index) {
            PriceAdvantageItemModel priceAdvantageItemModel = priceDataSource[index];
            return ProductSpuItem(
              mImageHost: mImageHost,
              rank: index + 1,
              priceAdvantageItemModel: priceAdvantageItemModel,
              onTapLookSale: (val) {
                String businessFormatType =  priceAdvantageItemModel.businessFormatType.toString();
                String platformType = priceAdvantageItemModel.platformType.toString();
                String provinceCode = priceAdvantageItemModel.provinceCode.toString();
                String url =
                    "/price_health_sku_list?spuId=${priceAdvantageItemModel.spuId}&businessFormatType=$businessFormatType&platformType=$platformType&provinceCode=$provinceCode";
                XYYContainer.open(url);
              },
            );
          },
        ),
        emptyWidget: getEmptyWidget(),
      ),
    );
  }

  // 自定义导航栏
  TabBar buildTabBarWidget() {
    return TabBar(
      controller: tabController,
      isScrollable: true,
      labelColor: Color(0xFF292933),
      indicator: TabCustomIndicator(wantWidth: 30, insets: EdgeInsets.only(bottom: 6)),
      labelStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      unselectedLabelColor: Color(0xFF676773),
      unselectedLabelStyle: TextStyle(fontSize: 16),
      tabs: tabs.map((e) => Tab(text: e['title'])).toList(),
      onTap: (index) {
        Map<String, dynamic> tabItem = tabs[index];
        params['businessFormatType'] = tabItem['businessFormatType'];
        refreshData();
      },
    );
  }

  Widget? getEmptyWidget() {
    if (priceDataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "";
  }

  void reuqestSpuCount() async {
    Map<String, dynamic> countParams = {...params};
    countParams.remove('pageNum');
    countParams.remove('pageSize');
    countParams.remove('productPricePowerType');
    var result = await NetworkV2<PriceSpuModel>(PriceSpuModel()).requestDataV2('/price/spu/count', method: RequestMethod.GET, parameters: countParams);
    if (result.isSuccess == true) {
      final PriceSpuModel? priceSpuModel = result.getData();
      productCounts = [
        priceSpuModel?.totalProductCount ?? 0,
        priceSpuModel?.priceAdvantageProductCount ?? 0,
        priceSpuModel?.priceDisadvantageProductCount ?? 0,
      ];
      setState(() {});
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
    }
  }

  void reuqestPriceAdvantageList() async {
    Map<String, dynamic> advangeParams = {
      ...params,
      'pageSize': defaultPageSize,
    };
    showLoadingDialog();
    var result =
        await NetworkV2<PriceAdvantageModel>(PriceAdvantageModel()).requestDataV2('/price/product/spu', method: RequestMethod.GET, parameters: advangeParams);
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      setState(() {
        final List<PriceAdvantageItemModel> sourceList = result.getData()?.list ?? [];
        priceDataSource.addAll(sourceList);
        _hasMore = sourceList.length >= defaultPageSize;
      });
    } else {
      if (result.errorMsg != null) {
        showToast('${result.errorMsg}');
      }
    }
  }

// 搜索方法
  Future<void> searchData() async {
    _isSearch = true;
    _resetAndLoadData();
  }

// 刷新方法
  Future<void> refreshData() async {
    _isSearch = false;
    _resetAndLoadData();
  }

  Future<void> _resetAndLoadData({bool callSkuCount = true}) async {
    params['pageNum'] = 1;
    if (!_isSearch) {
      params.remove('keyword'); // 非搜索时移除 keyword
    }
    priceDataSource = [];
    if(callSkuCount){
      reuqestSpuCount();
    }
    reuqestPriceAdvantageList();
  }


  Future<void> loadMoreList() async {
    int currentPage = (params['pageNum'] as int?) ?? 1;
    params['pageNum'] = currentPage + 1;
    reuqestPriceAdvantageList();
  }
}
