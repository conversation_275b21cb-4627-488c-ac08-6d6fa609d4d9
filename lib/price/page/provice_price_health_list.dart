///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-21 08:05

import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/price/data/provice_item_model.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/product_price_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/price/widget/province_price_rank_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class ProvicePriceHealthList extends BasePage {
  @override
  BaseState<StatefulWidget> initState() => ProvicePriceHealthListState();
}

class ProvicePriceHealthListState extends BaseState<ProvicePriceHealthList> {
  List<ProviceListItemModel> dataSource = [];

  EasyRefreshController controller = EasyRefreshController();

  Map<String, dynamic> params = {};

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      child: Column(
        children: [
          ProductPriceFilterBar(
            filterType: PriceFilterType.TypeProvice,
            onSelectChangedFun: (filterParams) {
              params = filterParams;
              params.removeWhere((key, value) => value == null);
              reuqestProviceList();
            },
          ),
          buildListContent(context),
        ],
      ),
    );
  }

  Expanded buildListContent(BuildContext context) {
    return Expanded(
      child: EasyRefresh(
        onRefresh: reuqestProviceList,
        // onLoad: _hasMore ? loadMoreList : null, // 没有更多数据时禁用加载更多
        child: ListView.builder(
          padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewPadding.bottom),
          itemCount: dataSource.length,
          itemBuilder: (context, index) {
            return ProvincePriceRankItem(provinceItemModel: dataSource[index], rank: index + 1);
          },
        ),
        emptyWidget: getEmptyWidget(),
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "省份价格健康度";
  }

  Future<void> reuqestProviceList() async {
    showLoadingDialog();
    var result =
        await NetworkV2<ProviceListItemModel>(ProviceListItemModel()).requestDataV2('/price/province/detail', method: RequestMethod.GET, parameters: params);
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      setState(() {
        dataSource = result.getListData() ?? [];
      });
    } else {
      showToast('${result.errorMsg}');
    }
  }
}
