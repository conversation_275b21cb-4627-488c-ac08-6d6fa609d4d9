import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';

///@Description(描述)     xxxx
///@author(作者)          z<PERSON>yi<PERSON>ong
///@create(时间)          2025-01-18 19:20

import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_progress_new.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class PriceHealthStatistics extends StatelessWidget {
  final HomePriceItemModel? priceItemModel;

  PriceHealthStatistics({
    required this.priceItemModel,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(10),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 16),
        decoration: BoxDecoration(
          color: Color(0xFF09B475), // 背景颜色
          borderRadius: BorderRadius.circular(10),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              // mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                HomeSalesProgressNew(
                  progressBackGroundColor: Color.fromRGBO(229, 229, 229, 1.0),
                  w: 80,
                  h: 45,
                  progress: (priceItemModel?.productHealth ?? 0)/100,
                  progressText: '${priceItemModel?.productHealth ?? 0}',
                  progressColor: Color(0xFFFED448),
                ),
                SizedBox(width: 10),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('价格健康度', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w600, color: Colors.white)),
                    SizedBox(height: 5),
                    Row(
                      children: [
                        Text(
                          '同比上周：',
                          style: TextStyle(
                            fontSize: 12,
                            color: Color(0xFFFFFFFF),
                          ),
                        ),
                        Text(
                          '${(priceItemModel?.productHealthWeekPercentage ?? 0).abs()}%',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                            color: Color(0xFFFFFFFF),
                          ),
                        ),
                        SizedBox(width: 5),
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: Color(0xFFFFFFFF),
                          ),
                          width: 12,
                          height: 12,
                          child: Center(
                            child: Transform.rotate(
                              angle: 0,
                              child: Image.asset(
                                (priceItemModel?.productHealthWeekPercentage ?? 0) < 0
                                    ? 'assets/images/home/<USER>'
                                    : 'assets/images/home/<USER>',
                                width: 6,
                                height: 7,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(height: 20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildItemCard('在售商品数', '${priceItemModel?.onSaleProductCount ?? 0}', formatPercentage(priceItemModel?.onSaleProductCountWeekPercentage)),
                _buildItemCard(
                    '优价商品数', '${priceItemModel?.priceAdvantageProductCount ?? 0}', formatPercentage(priceItemModel?.priceAdvantageProductCountWeekPercentage)),
                _buildItemCard('劣价商品数', '${priceItemModel?.priceDisadvantageProductCount ?? 0}',
                    formatPercentage(priceItemModel?.priceDisadvantageProductCountWeekPercentage)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemCard(String title, String value, String comparison) {
    return Expanded(
      child: Container(
        // margin: EdgeInsets.symmetric(horizontal: 8),
        // padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(color: Colors.grey.withOpacity(0.2), blurRadius: 4, spreadRadius: 2),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(title, style: TextStyle(fontSize: 14, color: Colors.white)),
            SizedBox(height: 8),
            Text(value, style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600, color: Colors.white)),
            SizedBox(height: 8),
            Text(comparison, style: TextStyle(fontSize: 12, color: Colors.white)),
          ],
        ),
      ),
    );
  }

  String formatPercentage(double? value) {
    if (value == null || value == 0) return '同比上周 0%';

    final absValue = value.abs(); // 取绝对值
    final arrow = value > 0 ? '↑' : '↓';
    return '同比上周$absValue% $arrow';
  }
}
