///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-20 15:18

import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_sku_model.dart';
import 'package:date_format/date_format.dart';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class ProductSkuItem extends StatelessWidget {
  final PriceSkuItemModel? skuItemModel; // 药品模型
  final int? rank;
  final Function(PriceSkuItemModel) onTapPriceTrend; // 点击事件回调
  final String? mImageHost;

  // 定义样式常量
  final TextStyle itemStyle = TextStyle(color: Color(0xFF777777), fontSize: 14);
  final TextStyle valueStyle = TextStyle(color: Color(0xFF222222), fontWeight: FontWeight.w500, fontSize: 14);

  ProductSkuItem({Key? key, required this.skuItemModel, required this.onTapPriceTrend, this.rank, this.mImageHost}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 10),
      color: Color(0xFFF5F5F5),
      child: Container(
        padding: const EdgeInsets.all(10.0),
        color: Colors.white,
        child: Column(
          children: [
            Row(
              children: [
                getProductImage(),
                getRightProductInfo(),
              ],
            ),
            Container(
              padding: EdgeInsets.fromLTRB(86, 3, 0, 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${skuItemModel?.merchantName ?? '-'}',
                    style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
                  ),
                  Container(
                    child: buildPriceTrend(),
                  ),
                ],
              ),
            ),
            Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Color(0xFFF9F9F9),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: buildDynamicSalesExposure())
          ],
        ),
      ),
    );
  }

  Widget getRightProductInfo() {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(left: 5),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${skuItemModel?.productName ?? '-'}/${skuItemModel?.spec ?? '-'}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4),
            Text(
              '${skuItemModel?.manufacturer ?? '-'}',
              style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
            ),
            SizedBox(height: 6),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Text(
                      '¥',
                      style: TextStyle(color: Colors.red, fontSize: 11, fontWeight: FontWeight.w500),
                    ),
                    Text(
                      '${skuItemModel?.salePrice ?? '-'}',
                      style: TextStyle(color: Colors.red, fontSize: 18, fontWeight: FontWeight.w500),
                    ),
                  ],
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget buildPriceTrend() {
    return GestureDetector(
      onTap: () {
        if (skuItemModel != null) {
          onTapPriceTrend(skuItemModel!);
        }
      },
      child: Row(
        children: [
          Image.asset(
            'assets/images/price/trend.png',
            width: 16,
            height: 10,
          ),
          SizedBox(width: 4),
          Text(
            "价格走势",
            style: TextStyle(color: Color(0xFF949498), fontSize: 12),
          ),
          Image.asset(
            'assets/images/commodity/commodity_rank_arrow.png',
            width: 12,
            height: 12,
          )
        ],
      ),
    );
  }

  Widget getProductImage() {
    return Container(
      height: 80,
      width: 83,
      child: Stack(
        children: [
          Positioned(
            left: 13,
            top: 10,
            child: Container(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: ImageCatchWidget(
                  url: '${mImageHost}${skuItemModel?.productImage}',
                  w: 70,
                  h: 70,
                ),
              ),
            ),
          ),
          Positioned(
            left: 0,
            top: 0,
            child: buildRankTag(),
          ),
        ],
      ),
    );
  }

  Widget buildRankTag() {
    if (rank == null) return SizedBox();
    String url = "";
    if (rank! <= 3) {
      switch (rank) {
        case 1:
          url = "assets/images/price/provice_rank_1.png";
          break;
        case 2:
          url = "assets/images/price/provice_rank_2.png";
          break;
        case 3:
          url = "assets/images/price/provice_rank_3.png";
          break;
      }
      return Image.asset(url, height: 26, width: 22);
    } else {
      return Stack(
        alignment: Alignment.center,
        children: [
          Image.asset("assets/images/price/provice_rank_other.png", height: 26, width: 22),
          Transform.translate(
            offset: Offset(0, 2),
            child: Text(
              '$rank',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Color(0xFF999999),
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget buildDynamicSalesExposure() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: buildInfoItem('在售店铺：', skuItemModel?.purchaseStoreCount),
            ),
            Expanded(
              child: buildInfoItem('已售数量：', skuItemModel?.sold),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: buildInfoItem('覆盖省份：', formatProvinces()),
            ),
          ],
        ),
      ],
    );
  }

  String formatProvinces() {
    final provinces = skuItemModel?.productListingInfo?.coveredProvinces;
    if (provinces == null || provinces.isEmpty) return '-';
    return provinces.join('、');
  }

  Widget buildInfoItem(String label, dynamic? value) {
    return Row(
      children: [
        SizedBox(
          width: 80,
          child: Text(label, style: itemStyle),
        ),
        Expanded(
          child: Text(
            value?.toString() ?? '-',
            style: valueStyle,
            overflow: TextOverflow.visible,
            softWrap: true,
            maxLines: null,
          ),
        ),
      ],
    );
  }
}
