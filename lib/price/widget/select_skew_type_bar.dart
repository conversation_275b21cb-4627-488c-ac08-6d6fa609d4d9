

///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-19 05:46


import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tab_controller.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tabs.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/block_tab_base_painter.dart';
import 'package:flutter/material.dart';
import 'package:flutter/material.dart';

class SelectSkewTypeBar extends StatefulWidget {
  // final List<Map<String, dynamic>> productTypes;
  final List<Map<String, dynamic>> productTypes;
  final Function(Map<String, dynamic>,int type) onTabSelected; // 外抛的事件
  final int initialIndex; // 初始选中的索引

  SelectSkewTypeBar({
    required this.productTypes,
    required this.onTabSelected,
    required this.initialIndex,
  });

  @override
  _SelectSkewTypeBarState createState() => _SelectSkewTypeBarState();
}

class _SelectSkewTypeBarState extends State<SelectSkewTypeBar> with SingleTickerProviderStateMixin {
  late CustomTabController _customTabController;
  late ValueNotifier<int> _factor;

  @override
  void initState() {
    super.initState();
    _factor = ValueNotifier(widget.initialIndex);
    _customTabController = CustomTabController(
      length: widget.productTypes.length,
      initialIndex: widget.initialIndex,
      vsync: this,
      animationDuration: Duration.zero,
    );
  }

  @override
  void dispose() {
    _customTabController.dispose();
    _factor.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFE9F0F4),
      child: CustomPaint(
        painter: BlockTabBasePainter(
          factor: _factor,
        ),
        child: SizedBox(
          height: 35,
          child: Container(
            padding: EdgeInsets.only(top: 1),
            child: CustomTabBar(
              controller: _customTabController,
              isScrollable: false,
              indicatorColor: Colors.transparent,
              indicatorWeight: 0.1,
              indicatorSize: CustomTabBarIndicatorSize.label,
              unselectedLabelColor: Color(0xFF9494A6),
              unselectedLabelStyle: TextStyle(fontSize: 15),
              labelColor: Color(0xFF0D0E10),
              labelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.w600),
              labelPadding: EdgeInsets.zero,
              tabs: widget.productTypes.map((e) => Container(height: 34, child: Tab(text: e['title']))).toList(),
              onTap: (index) {
                _factor.value = index; // 更新因子
                widget.onTabSelected(widget.productTypes[index],index); // 外抛事件
              },
            ),
          ),
        ),
      ),
    );
  }
}
