import 'dart:math';
import 'package:XyyBeanSproutsFlutter/price/data/price_sku_model.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

const int PARTS = 3; //Y轴等份

class PriceTrendChart extends StatefulWidget {
  final List<PriceTrendModel> trendList;
  final PriceSkuItemModel? skuItemModel;

  PriceTrendChart({
    Key? key,
    required List<PriceTrendModel> trendList,
    this.skuItemModel,
  })  : trendList = trendList.reversed.toList(),
        super(key: key);

  @override
  _PriceTrendChartState createState() => _PriceTrendChartState();
}

class _PriceTrendChartState extends State<PriceTrendChart> {
  final ScrollController _scrollController = ScrollController();
  final List<Color> gradientColors = const [Color(0xff00B377), Color(0xff00B377)];

  late double maxValue;
  late double adjustedMaxY;
  late double gap;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance!.addPostFrameCallback((_) {
      _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
    });

    _initializeValues();
  }

  void _initializeValues() {
    maxValue = widget.trendList.isEmpty ? 0 : widget.trendList.map((e) => e.price ?? 0).reduce(max);
    adjustedMaxY = (maxValue * 1.5 * 10).ceilToDouble() / 10;
    gap = adjustedMaxY / PARTS;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.all(16),
          height: 320,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Center(
                child: Text(
                  '商品价格走势',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
              ),
              SizedBox(height: 18),
              Row(
                children: [
                  Row(children: [
                    Text('常卖价:', style: TextStyle(fontSize: 14)),
                    Text('¥${widget.skuItemModel?.salePrice ?? '-'}', style: TextStyle(fontSize: 14, fontWeight: FontWeight.w500, color: Color(0xFF00B377))),
                    // SizedBox(width: 5),
                    // Transform.translate(
                    //   offset: Offset(0, -4),
                    //   child: Container(
                    //     padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                    //     decoration: BoxDecoration(
                    //       color: Color(0xFFF75341),
                    //       borderRadius: BorderRadius.circular(4),
                    //     ),
                    //     child: Text(
                    //       '历史最低',
                    //       style: TextStyle(color: Color(0xFFFFFFFF), fontSize: 12),
                    //     ),
                    //   )
                    // )
                  ]),
                ],
              ),
              SizedBox(height: 28),
              LayoutBuilder(
                builder: (context, constraints) {
                  return SizedBox(
                    width: constraints.maxWidth,
                    height: 165,
                    child: Row(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 手绘Y轴
                        Transform.translate(
                          offset: Offset(0, -2),
                          child: Container(
                            width: 50,
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  color: Color(0xFFEEEEEE),
                                  width: 1,
                                ),
                              ),
                            ),
                            margin: EdgeInsets.only(bottom: 26),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: List.generate(PARTS + 1, (index) {
                                double value = adjustedMaxY - gap * index;
                                return Container(
                                  child: Text(
                                    value.toStringAsFixed(0),
                                    style: TextStyle(color: Color(0xff67727d), fontSize: 14),
                                  ),
                                );
                              }),
                            ),
                          ),
                        ),
                        // 图表区域
                        Expanded(
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            scrollDirection: Axis.horizontal,
                            child: Container(
                              width: calculateChartWidth(context),
                              child: LineChart(
                                drawLineChartData(),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ],
          ),
        ),
        Positioned(
          right: 0,
          top: 0,
          child: IconButton(
            icon: Icon(Icons.close),
            onPressed: () {
              Navigator.pop(context);
            },
          ),
        ),
      ],
    );
  }

  double calculateChartWidth(BuildContext context) {
    const double pointWidth = 50.0;
    return max(pointWidth * widget.trendList.length, MediaQuery.of(context).size.width - 102);
  }

  double _calculateMaxY() {
    if (widget.trendList.isEmpty) return 6;
    double maxValue = widget.trendList.map((e) => e.price ?? 0).reduce(max);
    return (maxValue * 1.5 * 10).ceilToDouble() / 10;
  }

  LineChartData drawLineChartData() {
    return LineChartData(
      gridData: FlGridData(
        show: true,
        drawVerticalLine: true,
        getDrawingHorizontalLine: (value) {
          // 计算所有刻度值
          // List<double> scaleValues = [0, gap, gap * 2, gap * 3]; // [0, 18, 36, 54]
          // // 检查当前value是否接近任何一个刻度值
          // for (double scaleValue in scaleValues) {
          //   if ((value - scaleValue).abs() < 1.0) {
          //     return FlLine(
          //       color: const Color(0xffF1F1F1), // 改回灰色
          //       strokeWidth: 1,
          //     );
          //   }
          // }
          return FlLine(
            color: Colors.transparent,
            strokeWidth: 0,
          );
        },
        getDrawingVerticalLine: (value) {
          return FlLine(
            color: const Color(0xffF1F1F1),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        bottomTitles: SideTitles(
          showTitles: true,
          reservedSize: 22,
          getTextStyles: (context, value) {
            return TextStyle(color: Color(0xff777777), fontSize: 13);
          },
          getTitles: (value) {
            int index = value.toInt();
            if (index >= 0 && index < widget.trendList.length) {
              List<int> date = widget.trendList[index].date ?? [];
              if (date.length >= 3) {
                return '${date[1].toString().padLeft(2, '0')}-${date[2]}';
                return '${date[2]}日';
                return '${date[0]}-${date[1]}-${date[2]}';
              }
            }
            return '';
          },
          margin: 8,
        ),
        leftTitles: SideTitles(
          showTitles: false,
        ),
      ),
      borderData: FlBorderData(
        show: true,
        border: Border.all(color: const Color(0xffF1F1F1), width: 1),
      ),
      minX: 0,
      maxX: (widget.trendList.length - 1).toDouble(),
      minY: 0,
      maxY: adjustedMaxY,
      lineBarsData: linesBarData1(),
    );
  }

  List<LineChartBarData> linesBarData1() {
    final LineChartBarData lineChartBarData1 = LineChartBarData(
      spots: List.generate(
        widget.trendList.length,
        (index) => FlSpot(
          index.toDouble(),
          widget.trendList[index].price ?? 0,
        ),
      ),
      isCurved: true,
      colors: gradientColors,
      barWidth: 2,
      isStrokeCapRound: true,
      dotData: FlDotData(
        show: false,
      ),
      belowBarData: BarAreaData(
        show: true,
        colors: [
          gradientColors[0].withOpacity(0.1),
          gradientColors[1].withOpacity(0.3),
        ],
      ),
    );
    return [lineChartBarData1];
  }
}
