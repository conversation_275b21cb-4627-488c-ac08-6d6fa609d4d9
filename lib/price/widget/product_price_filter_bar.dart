import 'dart:math';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';

///@Description(描述)     xxxx
///@author(作者)          z<PERSON><PERSON><PERSON>ong
///@create(时间)          2025-01-21 14:22

import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/drop_filter_bar/common_drop_filter_bar.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_filter_location_data.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_filter_popup_base.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/filter_page/commodity_location_filter_popup.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_health_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';

class PriceFilterModal {
  int? timeType; // 日期 1-今日，2-近7天，3-近14天，4-近30天
  int? salesVolume; // 销量   1-采购店数、2-采购数量、3-实付金额  默认采购店数
  int? platformType; // 商品范围 1-自营，2-POP，3-全平台 默认为【全平台】
  String? shopCodes; // 店铺
  int? businessFormatType; // 业态  1-连锁，2-非连锁，3-全部(省份价格健康度列表需要)
  int? provinceCode; // 省份

  PriceFilterModal({this.timeType, this.salesVolume, this.platformType, this.shopCodes, this.businessFormatType, this.provinceCode});
}

class FilterParamKeys {
  static const String timeType = "timeType";
  static const String salesVolume = "salesVolume";
  static const String shopCodes = "shopCodes";
  static const String businessFormatType = "businessFormatType";
  static const String platformType = "platformType";
  static const String proviceCode = "proviceCode";
}

enum PriceFilterType {
  TypeEcspu,
  TypeSku,
  TypeProvice,
}

class ProductPriceFilterBar extends StatefulWidget {
  final PriceFilterType filterType;
  final String? provinceCode;
  final Function(Map<String, dynamic>) onSelectChangedFun; // 回调函数
  final List<PriceShopModel>? shopList;
  ProductPriceFilterBar({required this.filterType, required this.onSelectChangedFun, this.provinceCode, this.shopList = const []});

  @override
  _ProductPriceFilterBarState createState() => _ProductPriceFilterBarState();
}

class _ProductPriceFilterBarState extends State<ProductPriceFilterBar> {
  PriceFilterModal priceFilterModal = PriceFilterModal(timeType: 1, salesVolume: 1, businessFormatType: 3, platformType: 3, provinceCode: -1);

  final Map<String, DropButtonController> controllers = {
    FilterParamKeys.timeType: DropButtonController(model: DropButtonModel(normalText: "今日")),
    FilterParamKeys.salesVolume: DropButtonController(model: DropButtonModel(normalText: "采购店数")),
    FilterParamKeys.shopCodes: DropButtonController(model: DropButtonModel(normalText: "全部")),
    FilterParamKeys.platformType: DropButtonController(model: DropButtonModel(normalText: "全部商品")),
    FilterParamKeys.businessFormatType: DropButtonController(model: DropButtonModel(normalText: "全部")),
    FilterParamKeys.proviceCode: DropButtonController(model: DropButtonModel(normalText: "全国")),
  };

  List<CommodityFilterLocationData>? locationSource;

  List<CustomerConditionModel> platformTypeModels = [
    CustomerConditionModel(text: '全部商品', code: 3),
    CustomerConditionModel(text: '自营商品', code: 1),
    CustomerConditionModel(text: 'POP商品', code: 2)
  ];

  @override
  void initState() {
    super.initState();

    initData();
  }

  Future<void> initData() async {
    // todo spu列表TypeEcspu 商品去掉全部商品 默认自营商品
    if (widget.filterType == PriceFilterType.TypeEcspu) {
      priceFilterModal.platformType = 1;
      platformTypeModels = [CustomerConditionModel(text: '自营商品', code: 1), CustomerConditionModel(text: 'POP商品', code: 2)];
      controllers[FilterParamKeys.platformType] = DropButtonController(model: DropButtonModel(normalText: "自营商品"));
    }
    if (widget.filterType == PriceFilterType.TypeProvice) {
      onChangeBackFun();
      return;
    }
    String provinceCodeString = (widget.provinceCode?.toString() ?? '');
    priceFilterModal.provinceCode = int.parse(provinceCodeString);
    await requestLocationData();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 6),
      child: CommonDropFilterBar(
        configs: filterConfig(widget.filterType),
        action: showDropPopupView,
      ),
    );
  }

  List<CommonDropConfigModel> filterConfig(PriceFilterType filterType) {
    List<CommonDropConfigModel> config = [
      CommonDropConfigModel(
        defaultTitle: "今日",
        paramKey: FilterParamKeys.timeType,
        controller: controllers[FilterParamKeys.timeType]!,
      ),
    ];

    if (filterType == PriceFilterType.TypeEcspu) {
      config.addAll(_getEcspuConfig());
    } else if (filterType == PriceFilterType.TypeSku) {
      config.addAll(_getSkuConfig());
    } else if (filterType == PriceFilterType.TypeProvice) {
      config.addAll(_getProviceConfig());
    }
    config.add(CommonDropConfigModel(
      defaultTitle: "全国",
      paramKey: FilterParamKeys.proviceCode,
      controller: controllers[FilterParamKeys.proviceCode]!,
    ));

    return config;
  }

  List<CommonDropConfigModel> _getEcspuConfig() {
    return [
      CommonDropConfigModel(
        defaultTitle: "采购店数",
        paramKey: FilterParamKeys.salesVolume,
        controller: controllers[FilterParamKeys.salesVolume]!,
      ),
      // CommonDropConfigModel(
      //   defaultTitle: "全部商品",
      //   paramKey: FilterParamKeys.platformType,
      //   controller: controllers[FilterParamKeys.platformType]!,
      // ),
      // todo spu列表 .商品去掉全部商品 默认自营商品
      CommonDropConfigModel(
        defaultTitle: "自营商品",
        paramKey: FilterParamKeys.platformType,
        controller: controllers[FilterParamKeys.platformType]!,
      ),
    ];
  }

  List<CommonDropConfigModel> _getSkuConfig() {
    return [
      CommonDropConfigModel(
        defaultTitle: "采购店数",
        paramKey: FilterParamKeys.salesVolume,
        controller: controllers[FilterParamKeys.salesVolume]!,
      ),
      // 店铺
      CommonDropConfigModel(
        defaultTitle: "全部",
        paramKey: FilterParamKeys.shopCodes,
        controller: controllers[FilterParamKeys.shopCodes]!,
      ),
    ];
  }

  List<CommonDropConfigModel> _getProviceConfig() {
    return [
      CommonDropConfigModel(
        defaultTitle: "全部商品",
        paramKey: FilterParamKeys.platformType,
        controller: controllers[FilterParamKeys.platformType]!,
      ),
      CommonDropConfigModel(
        defaultTitle: "全部",
        paramKey: FilterParamKeys.businessFormatType,
        controller: controllers[FilterParamKeys.businessFormatType]!,
      ),
    ];
  }

  void showDropPopupView(GlobalKey authKey, CommonDropConfigModel model, DropButtonController controller) async {
    switch (model.paramKey) {
      case FilterParamKeys.timeType:
        await _showDatePopup(authKey, controller);
        break;
      case FilterParamKeys.salesVolume:
        await _showSalePopup(authKey, controller);
        break;
      case FilterParamKeys.shopCodes:
        await _showShopPopup(authKey, controller);
        break;
      case FilterParamKeys.businessFormatType:
        await _showChainPopup(authKey, controller);
        break;
      case FilterParamKeys.platformType:
        await _showPlatformTypePopup(authKey, controller);
        break;
      case FilterParamKeys.proviceCode:
        await _showLocationPopup(authKey, controller);
        break;
    }

    controller.setIsOpen(false);
  }

  Future<void> _showDatePopup(GlobalKey authKey, DropButtonController controller) async {
    await showCommonFilterPopup(
      key: authKey,
      context: context,
      pageBuilder: (distance) {
        return CustomerSingleFilterPopup(
          models: [
            CustomerConditionModel(text: '今日', code: 1),
            CustomerConditionModel(text: '过去7天', code: 2),
            CustomerConditionModel(text: '过去14天', code: 3),
            CustomerConditionModel(text: '过去30天', code: 4),
          ],
          selectedCode: '${priceFilterModal.timeType}',
          selectAction: (value) {
            controller.setSelectText("${value.text}");
            priceFilterModal.timeType = value.code;
            onChangeBackFun();
          },
          distance: distance,
        );
      },
    );
  }

  Future<void> _showSalePopup(GlobalKey authKey, DropButtonController controller) async {
    await showCommonFilterPopup(
      key: authKey,
      context: context,
      pageBuilder: (distance) {
        return CustomerSingleFilterPopup(
          models: [
            CustomerConditionModel(text: '采购店数', code: 1),
            CustomerConditionModel(text: '采购数量', code: 2),
            CustomerConditionModel(text: '实付金额', code: 3),
          ],
          selectedCode: '${priceFilterModal.salesVolume}',
          selectAction: (value) {
            controller.setSelectText("${value.text}");
            priceFilterModal.salesVolume = value.code;
            onChangeBackFun();
          },
          distance: distance,
        );
      },
    );
  }

  Future<void> _showShopPopup(GlobalKey authKey, DropButtonController controller) async {
    await showCommonFilterPopup(
      key: authKey,
      context: context,
      pageBuilder: (distance) {
        return CustomerMultipleFilterPopup(
          models: widget.shopList!.map((shopModel) => CustomerConditionModel(text: '${shopModel.showName ?? '--'}', code: shopModel.shopCode ?? '')).toList(),
          selectedCodes: priceFilterModal.shopCodes,
          selectAction: (value) {
            controller.setSelectText(value.isNotEmpty ? "店铺" : null);
            priceFilterModal.shopCodes = value;
            onChangeBackFun();
          },
          distance: distance,
        );
      },
    );
  }

  Future<void> _showChainPopup(GlobalKey authKey, DropButtonController controller) async {
    await showCommonFilterPopup(
      key: authKey,
      context: context,
      pageBuilder: (distance) {
        return CustomerSingleFilterPopup(
          models: [
            CustomerConditionModel(text: '全部', code: 3),
            CustomerConditionModel(text: '连锁', code: 1),
            CustomerConditionModel(text: '非连锁', code: 2),
          ],
          selectedCode: '${priceFilterModal.businessFormatType}',
          selectAction: (value) {
            controller.setSelectText("${value.text}");
            priceFilterModal.businessFormatType = value.code;
            onChangeBackFun();
          },
          distance: distance,
        );
      },
    );
  }

  Future<void> _showPlatformTypePopup(GlobalKey authKey, DropButtonController controller) async {
    await showCommonFilterPopup(
      key: authKey,
      context: context,
      pageBuilder: (distance) {
        return CustomerSingleFilterPopup(
          models: platformTypeModels,
          selectedCode: '${priceFilterModal.platformType}',
          selectAction: (value) {
            controller.setSelectText("${value.text}");
            priceFilterModal.platformType = value.code;
            onChangeBackFun();
          },
          distance: distance,
        );
      },
    );
  }

  Future<void> _showLocationPopup(GlobalKey authKey, DropButtonController controller) async {
    if (locationSource == null) {
      await requestLocationData();
    }
    if (locationSource?.isEmpty ?? true) return;
    await showCommodityFilterPopup(
      context: context,
      pageBuilder: (distance) {
        return CommodityLocationFilterPopup(
          distance: distance,
          selectId: priceFilterModal.provinceCode,
          itemChanged: (name, param) {
            controller.setIsOpen(false);
            setState(() {
              controllers[FilterParamKeys.proviceCode]!.setSelectText(name);
              priceFilterModal.provinceCode = param['provinceCode'];
              onChangeBackFun();
            });
          },
          source: locationSource ?? [],
        );
      },
      key: authKey,
    );
  }

  requestLocationData() async {
    var result = await NetworkV2<CommodityFilterLocationData>(CommodityFilterLocationData()).requestDataV2(
      'skuRank/getProvince',
      method: RequestMethod.GET,
    );
    if (result.isSuccess == true) {
      setState(() => locationSource = result.getListData() ?? []);
      try {
        // todo spu列表 省份选择去掉全国
        if (locationSource != null && locationSource!.isNotEmpty) {
          if (widget.filterType == PriceFilterType.TypeEcspu) {
            // locationSource = locationSource!.sublist(1);
            // 删除 provinceCode 为 -1 全国的项
            locationSource!.removeWhere((location) => location.provinceCode == -1);
          }
          var locationData = locationSource!.firstWhere(
            (location) => location.provinceCode == priceFilterModal.provinceCode,
            orElse: () {
              // return locationSource![Random().nextInt(locationSource!.length)];

              var validLocations = locationSource!.where((location) => location.provinceCode != -1).toList();
              return validLocations[Random().nextInt(validLocations.length)];
            },
          );
          // 设置选择的省份
          if (locationData != null) {
            controllers[FilterParamKeys.proviceCode]?.setSelectText(locationData.provinceName);
            priceFilterModal.provinceCode = int.tryParse(locationData.provinceCode.toString()) ?? -1; // 确保 provinceCode 为 int 类型
            onChangeBackFun();
            setState(() {});
          }
        }
      } catch (e) {
        print('Location processing error: $e');
      }
    } else {
      XYYContainer.toastChannel.toast(result.errorMsg ?? '获取区域错误');
    }
  }

  void onChangeBackFun() {
    Map<String, dynamic> filterParams = {'timeType': priceFilterModal.timeType};
    if (priceFilterModal.provinceCode == -1) {
      //全国不传
      filterParams['provinceCode'] = null;
    } else {
      filterParams['provinceCode'] = priceFilterModal.provinceCode;
    }
    switch (widget.filterType) {
      case PriceFilterType.TypeEcspu:
        filterParams['salesVolume'] = priceFilterModal.salesVolume;
        filterParams['platformType'] = priceFilterModal.platformType;
        widget.onSelectChangedFun(filterParams);
        break;
      case PriceFilterType.TypeSku:
        filterParams['salesVolume'] = priceFilterModal.salesVolume;
        // 同时处理null、空字符串和纯空格的情况
        if (priceFilterModal.shopCodes?.trim().isNotEmpty ?? false) {
          filterParams['shopCodeStr'] = priceFilterModal.shopCodes!;
        }
        widget.onSelectChangedFun(filterParams);
        break;
      case PriceFilterType.TypeProvice:
        filterParams['platformType'] = priceFilterModal.platformType;
        if (priceFilterModal.businessFormatType == 3) {
          //全部不传
          filterParams['businessFormatType'] = null;
        } else {
          filterParams['businessFormatType'] = priceFilterModal.businessFormatType;
        }
        widget.onSelectChangedFun(filterParams);
        break;
    }
  }
}
