import 'package:flutter/material.dart';

class ProductCountFilterBar extends StatefulWidget {
  final List<int> initialCounts; // 外部传入的计数
  final Function(int)? onTapFun; // 点击事件回调

  ProductCountFilterBar({this.initialCounts = const [0, 0, 0], this.onTapFun});

  @override
  _ProductCountFilterBarState createState() => _ProductCountFilterBarState();
}

class _ProductCountFilterBarState extends State<ProductCountFilterBar> {
  int selectedIndex = 0; // 选中的索引
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<String> titles = ['全部', '价优', '价劣'];
    final tabs = List.generate(titles.length, (index) {
      return {
        'title': titles[index],
        'count': index < widget.initialCounts.length ? widget.initialCounts[index] : 0,
      };
    });
    return Container(
      height: 44,
      color: Colors.white,
      // padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween, // 均匀分布
        children: List.generate(tabs.length, (index) {
          return Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  selectedIndex = index; // 更新选中索引
                });
                if (widget.onTapFun != null) {
                  widget.onTapFun!(selectedIndex);
                }
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      '${tabs[index]['title']} (${tabs[index]['count']})',
                      style: TextStyle(
                        fontSize: 16,
                        color: selectedIndex == index ? Color(0xFF00B377) : Colors.black,
                        fontWeight: selectedIndex == index ? FontWeight.bold : FontWeight.normal,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
