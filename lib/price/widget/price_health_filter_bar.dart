import 'package:XyyBeanSproutsFlutter/common/button/dropdown_controller_button.dart';
import 'package:XyyBeanSproutsFlutter/common/popup_filter/common_popup_filter_base.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/data/customer_condition_model.dart';
import 'package:XyyBeanSproutsFlutter/customer/common/popup/customer_filter_popup.dart';

///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-19 05:21

import 'package:flutter/material.dart';

const List<Map<String, dynamic>> productFilterList = [
  {'name': '全部商品', 'platformType': 3},
  {'name': '自营商品', 'platformType': 1},
  {'name': 'POP商品', 'platformType': 2}
];

const List<Map<String, dynamic>> dateTypeOptions = [
  {'text': '今日', 'timeType': '1'},
  {'text': '过去7天', 'timeType': '2'},
  {'text': '过去14天', 'timeType': '3'},
  {'text': '过去30天', 'timeType': '4'}
];

class PriceHealthFilterBar extends StatefulWidget {
  final GlobalKey globalKey;
  final Function(Map<String, dynamic>) onSelectTypeChanged; // 回调函数

  PriceHealthFilterBar({
    required this.globalKey,
    required this.onSelectTypeChanged,
  });

  @override
  _PriceHealthFilterBarState createState() => _PriceHealthFilterBarState();
}

class _PriceHealthFilterBarState extends State<PriceHealthFilterBar> {
  late Map<String, dynamic> _selProduct; // 使用 late

  // 当前选中的日期类型
  late Map<String, dynamic> _selDateType;

  @override
  void initState() {
    super.initState();

    _selProduct = productFilterList[0];
    _selDateType = dateTypeOptions[0];
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            flex: 8,
            child: buildProductFilterButtons(),
          ),
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerRight, // 向右对齐
              child: buildDateDropdown(context),
            ),
          ),
        ],
      ),
    );
  }

  // 商品过滤按钮
  Widget buildProductFilterButtons() {
    return Row(
      children: productFilterList.map((item) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _selProduct = item; // 更新选中商品
            });
            widget.onSelectTypeChanged({'platformType': _selProduct['platformType'], 'timeType': _selDateType['timeType']});
          },
          child: Container(
            margin: const EdgeInsets.only(right: 10),
            padding: EdgeInsets.symmetric(horizontal: 14, vertical: 6),
            decoration: BoxDecoration(
              color: _selProduct['platformType'] == item['platformType'] ? Colors.green.withOpacity(0.1) : Color(0xFFF5F5F5),
              border: _selProduct['platformType'] == item['platformType'] ? Border.all(color: Color(0xFF00B377)) : null,
              borderRadius: BorderRadius.circular(18),
            ),
            child: Text(
              item['name'],
              style: TextStyle(color: _selProduct['platformType'] == item['platformType'] ? Color(0xFF00B377) : Color(0xFF666666), fontSize: 13),
            ),
          ),
        );
      }).toList(),
    );
  }

  // 日期下拉框
  Widget buildDateDropdown(BuildContext context) {
    return DropControllerButton(
      key: widget.globalKey,
      title: _selDateType["text"], // 显示第一个日期类型的文本
      selectedText: _selDateType["text"],
      selectedStyle: TextStyle(color: Color(0xFF00B377), fontSize: 14, fontWeight: FontWeight.w600),
      onPressed: (controller) async {
        await showCommonFilterPopup(
          key: widget.globalKey,
          context: context,
          pageBuilder: (distance) {
            return CustomerSingleFilterPopup(
              models: dateTypeOptions.map((e) {
                return CustomerConditionModel()
                  ..code = e["timeType"]
                  ..text = e["text"];
              }).toList(),
              selectedCode: _selDateType["timeType"],
              selectAction: (value) {
                setState(() {
                  _selDateType = dateTypeOptions.firstWhere((element) => element["timeType"] == value.code);
                });
                widget.onSelectTypeChanged({'platformType': _selProduct['platformType'], 'timeType': _selDateType['timeType']}); // 回调更新的日期类型数组
                controller.setSelectText(value.text ?? "今日");
              },
              distance: distance,
            );
          },
        );
        controller.setIsOpen(false);
      },
    );
  }
}
