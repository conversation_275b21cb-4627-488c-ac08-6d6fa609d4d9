///@Description(描述)     xxxx
///@author(作者)          zhang<PERSON>nhong
///@create(时间)          2025-01-19 16:52

import 'package:XyyBeanSproutsFlutter/price/data/provice_item_model.dart';
import 'package:flutter/material.dart';

class HomeProvincePriceRank extends StatelessWidget {
  final Function? onTapMore; // 点击事件回调
  final List<PriceProviceItemModel> provinces;

  HomeProvincePriceRank({required this.provinces, this.onTapMore});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9), // 背景颜色
      padding: EdgeInsets.all(10.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: 5),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '省份价格健康度排行',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                ),
                buildLookMore(),
              ],
            ),
          ),
          <PERSON><PERSON><PERSON><PERSON>(height: 10),
          Container(
            height: 150, // 设置高度以适应内容
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: provinces.length,
              itemBuilder: (context, index) {
                return buildProvinceCard(provinces[index], index + 1);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 查看再售商品
  Widget buildLookMore() {
    return GestureDetector(
      onTap: () {
        print('查看跟多');
        if (onTapMore != null) {
          onTapMore!();
        }
      },
      child: Row(
        children: [
          Text(
            "查看更多",
            style: TextStyle(color: Color(0xFF949498), fontSize: 12),
          ),
          Image.asset(
            'assets/images/commodity/commodity_rank_arrow.png',
            width: 12,
            height: 12,
          )
        ],
      ),
    );
  }

  Widget buildProvinceCard(PriceProviceItemModel provinceModel, int rank) {
    return Container(
      width: 230, // 设置每个卡片的宽度
      margin: EdgeInsets.only(right: 10),
      padding: EdgeInsets.all(10.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildHeaderInfo(provinceModel, rank),
          Divider(color: Color(0xFFF1F6F9), thickness: 1, height: 10),
          buildInfoItem('价格健康度', '${provinceModel.priceHealthScore ?? 0}%'),
          buildInfoItem('在售商品', '${provinceModel.onSaleProductCount}'),
          buildInfoItem('优质商品', '${provinceModel.priceAdvantageProductCount}'),
        ],
      ),
    );
  }

  Widget buildHeaderInfo(PriceProviceItemModel provinceModel, int? rank) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            buildRankTag(rank),
            SizedBox(width: 5),
            Text(
              '${provinceModel.province ?? '-'}',
              style: TextStyle(fontSize: 16, color: Colors.black, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        Container(
          padding: EdgeInsets.symmetric(horizontal: 5, vertical: 2),
          decoration: BoxDecoration(
            color: getRemarkColor(provinceModel.remark),
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            '${provinceModel.remark ?? '-'}',
            style: TextStyle(color: Colors.white, fontSize: 12),
          ),
        ),
      ],
    );
  }

  Color getRemarkColor(String? remark) {
    switch (remark) {
      case '太棒了':
        return const Color(0xFF03C261); // 绿色
      case '不错哟':
        return const Color(0xFFFF7F33); // 橙色
      case '太差了':
        return const Color(0xFFF23535); // 红色
      default:
        return const Color(0xFF999999); // 默认灰色
    }
  }

  Widget buildInfoItem(String leftText, String? value) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            leftText,
            style: TextStyle(color: Color(0xFF666666), fontSize: 14),
          ),
          Text(
            (value ?? '0'),
            style: TextStyle(color: Color(0xFF333333), fontSize: 14),
            textAlign: TextAlign.end,
          ),
        ],
      ),
    );
  }

  /// 排行标志图片
  Widget buildRankTag(int? rank) {
    if (rank == null) return SizedBox(); // 如果 rank 为 null，返回空的 SizedBox
    String url = "";
    if (rank <= 3) {
      switch (rank) {
        case 1:
          url = "assets/images/price/provice_rank_1.png";
          break;
        case 2:
          url = "assets/images/price/provice_rank_2.png";
          break;
        case 3:
          url = "assets/images/price/provice_rank_3.png";
          break;
      }
      return Image.asset(url, height: 26, width: 22);
    } else {
      return Stack(
        alignment: Alignment.center,
        children: [
          Image.asset("assets/images/price/provice_rank_other.png", height: 26, width: 22),
          Transform.translate(
            offset: Offset(0, 2),
            child: Text(
              '$rank',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: Color(0xFF999999),
              ),
            ),
          ),
        ],
      );
    }
  }
}
