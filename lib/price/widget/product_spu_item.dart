///@Description(描述)     xxxx
///@author(作者)          zhang<PERSON><PERSON>ong
///@create(时间)          2025-01-19 08:57

import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/price/data/price_advantage_model.dart';
import 'package:flutter/material.dart';

class ProductSpuItem extends StatefulWidget {
  final String? mImageHost;
  final PriceAdvantageItemModel? priceAdvantageItemModel;
  final int? rank;
  final Function(PriceAdvantageItemModel) onTapLookSale; // 点击事件回调

  ProductSpuItem({
    required this.priceAdvantageItemModel,
    this.rank,
    required this.onTapLookSale,
    this.mImageHost,
  });

  @override
  _ProductSpuItemState createState() => _ProductSpuItemState();
}

class _ProductSpuItemState extends State<ProductSpuItem> {
  int selectType = 1; // 1.动态及曝光 2.价格健康度

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: 10),
      color: Color(0xFFF5F5F5),
      child: Container(
        padding: const EdgeInsets.all(10.0),
        color: Colors.white,
        child: Column(
          children: [
            Row(
              children: [
                getProductImage(),
                getRightProductInfo(),
              ],
            ),
            Container(
              padding: EdgeInsets.symmetric(vertical: 10, horizontal: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  buildSelectButton('动销及曝光', 1),
                  SizedBox(width: 16), // 添加间距
                  buildSelectButton('价格健康度', 2),
                ],
              ),
            ),
            Container(
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Color(0xFFF9F9F9),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Column(
                  children: [
                    Visibility(
                      visible: selectType == 1,
                      child: buildDynamicSalesExposure(),
                    ),
                    Visibility(
                      visible: selectType == 2,
                      child: buildPriceHealth(),
                    ),
                  ],
                ))
          ],
        ),
      ),
    );
  }

  Widget getRightProductInfo() {
    return Expanded(
      child: Container(
        margin: EdgeInsets.only(left: 8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '${widget.priceAdvantageItemModel?.productName ?? '-'}/${widget.priceAdvantageItemModel?.spec ?? '-'}',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(height: 4),
            Text(
              '${widget.priceAdvantageItemModel?.manufacturer ?? '-'}',
              style: TextStyle(fontSize: 14, color: Color(0xFF666666)),
            ),
            SizedBox(height: 6),
            Column(
              children: [
                Row(
                  children: [
                    Text(
                      '¥',
                      style: TextStyle(color: Colors.red, fontSize: 11, fontWeight: FontWeight.w500),
                    ),
                    Text(
                      formatPriceRange(),
                      style: TextStyle(color: Colors.red, fontSize: 18, fontWeight: FontWeight.w500),
                    ),

                  ],
                ),
                // SizedBox(width: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end, // 使内容向右对齐
                  children: [
                    getMoreGoodsItem(),
                    SizedBox(width: 5),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                      decoration: BoxDecoration(
                        color: Color(0xFFE6F8F2),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        '${widget.priceAdvantageItemModel?.priceHealth ?? 0}%健康度',
                        style: TextStyle(color: Color(0xFF00B377), fontSize: 12),
                      ),
                    ),
                  ],
                )
              ],
            )
          ],
        ),
      ),
    );
  }

  String formatPriceRange() {
    final info = widget.priceAdvantageItemModel?.companyPriceInfo;
    final min = info?.minBasePriceRage;
    final max = info?.maxBasePriceRage;

    if (min == null || max == null) return '-';
    // 如果需要格式化价格，比如保留两位小数
    return '${min.toStringAsFixed(2)}-${max.toStringAsFixed(2)}';
  }

  /// 查看再售商品
  Widget getMoreGoodsItem() {
    return GestureDetector(
      onTap: () {
        if (widget.onTapLookSale != null) {
          widget.onTapLookSale(widget.priceAdvantageItemModel!);
        }
      },
      child: Row(
        children: [
          Text(
            "查看在售",
            style: TextStyle(color: Color(0xFF949498), fontSize: 12),
          ),
          Image.asset(
            'assets/images/commodity/commodity_rank_arrow.png',
            width: 12,
            height: 12,
          )
        ],
      ),
    );
  }

  /// 商品图片
  Widget getProductImage() {
    return Container(
      height: 80,
      width: 83,
      child: Stack(
        children: [
          Positioned(
            left: 13,
            top: 10,
            child: Container(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: ImageCatchWidget(
                  url: '${widget.mImageHost}${widget.priceAdvantageItemModel?.productImage}',
                  w: 70,
                  h: 70,
                ),
              ),
            ),
          ),
          // 排行标识
          Positioned(
            left: 0,
            top: 0,
            child: buildRankTag(),
          ),
        ],
      ),
    );
  }

  /// 生成按钮
  Widget buildSelectButton(String title, int type) {
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            selectType = type; // 设置为选中的按钮
          });
        },
        child: Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
          decoration: BoxDecoration(
            color: selectType == type ? Color(0xFFF2FBF8) : Color(0xFFF9F9F9),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: selectType == type ? Color(0xFF00B377) : Colors.transparent, // 根据选中状态设置边框颜色
              width: 1, // 边框宽度
            ),
          ),
          child: Text(
            title,
            style: TextStyle(
              color: selectType == type ? Color(0xFF00B377) : Color(0xFF777777),
              fontSize: 14,
            ),
          ),
        ),
      ),
    );
  }

  // 动销及曝光
  Widget buildDynamicSalesExposure() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: buildInfoItem('在售店铺：', widget.priceAdvantageItemModel?.productListingInfo?.storeCount),
            ),
            Expanded(
              child: buildInfoItem('已售数量：', widget.priceAdvantageItemModel?.sold),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          children: [
            // Expanded(
            //   child: buildInfoItem('覆盖省份：', formatProvinces()),
            // ),
            Expanded(
              flex: 1,
              child: buildInfoItem('采购店数：', widget.priceAdvantageItemModel?.purchaseStoreCount),
            ),
          ],
        ),
        SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: buildInfoItem('覆盖省份：', formatProvinces()),
            ),
          ],
        ),
      ],
    );
  }

  String formatProvinces() {
    final provinces = widget.priceAdvantageItemModel?.productListingInfo?.coveredProvinces;
    if (provinces == null || provinces.isEmpty) return '-';
    return provinces.join('、');
  }

  // 价格健康度
  Widget buildPriceHealth() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: buildInfoItem('在售商品：', widget.priceAdvantageItemModel?.companyPriceInfo?.onSaleProductCount),
            ),
            Expanded(
              child: buildInfoItem('竞争力高：', widget.priceAdvantageItemModel?.companyPriceInfo?.priceAdvantageProductCount),
            ),
          ],
        ),
      ],
    );
  }

  Widget buildRankTag() {
    if (widget.rank == null) return SizedBox(); // 如果 rank 为 null，返回空的 SizedBox
    int rank = widget.rank!;
    String url = "";
    if (rank <= 3) {
      switch (rank) {
        case 1:
          url = "assets/images/price/provice_rank_1.png";
          break;
        case 2:
          url = "assets/images/price/provice_rank_2.png";
          break;
        case 3:
          url = "assets/images/price/provice_rank_3.png";
          break;
      }
      return Image.asset(url, height: 26, width: 22);
    } else {
      return Stack(
        alignment: Alignment.center,
        children: [
          Image.asset("assets/images/price/provice_rank_other.png", height: 26, width: 22),
          Transform.translate(
            offset: Offset(0, 2),
            child: Text(
              '$rank',
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w700,
                color: Color(0xFF999999),
              ),
            ),
          ),
        ],
      );
    }
  }

  TextStyle itemStyle = TextStyle(color: Color(0xFF777777), fontSize: 14);
  TextStyle valueStyle = TextStyle(color: Color(0xFF222222), fontWeight: FontWeight.w500, fontSize: 14);
  Widget buildInfoItem(String label, dynamic? value) {
    return Row(
      // crossAxisAlignment: CrossAxisAlignment.start,  // 让文本从顶部对齐
      children: [
        SizedBox(
          width: 80,
          child: Text(label, style: itemStyle),
        ),
        Expanded(
          child: Text(
            value?.toString() ?? '-',
            style: valueStyle,
            overflow: TextOverflow.visible,
            softWrap: true, // 允许换行
            maxLines: null, // 取消行数限制
          ),
        ),
      ],
    );
  }
}
