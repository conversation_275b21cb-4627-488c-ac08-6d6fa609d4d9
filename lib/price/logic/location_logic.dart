import 'dart:math';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/goods/commodity/data/commodity_filter_location_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';

/// @Description     位置信息处理工具类
/// <AUTHOR>
/// @create          2025-01-23 12:19

class LocationLogic {
  /// 获取位置信息并返回 CommodityFilterLocationData
  static Future<CommodityFilterLocationData?> getLocationData() async {
    try {
      final location = await _getGPSLocation();
      if (_isValidLocation(location)) {
        return await getLocationWithGPS();
      } else {
        return await getRandomLocation();
      }
    } catch (e) {
      print('Location error: $e');
      return null;
    }
  }

  /// 根据GPS获取位置信息  (失败和异常需要继续请求 然后返回一个随机位置)
  static Future<CommodityFilterLocationData?> getLocationWithGPS() async {
    try {
      final location = await _getGPSLocation();
      final result = await NetworkV2<CommodityFilterLocationData>(
        CommodityFilterLocationData(),
      ).requestDataV2(
        '/price/get/province',
        method: RequestMethod.GET,
        parameters: location,
      );
      if (result.isSuccess == true) {
        return result.getData();
      } else {
        return await getRandomLocation();
      }
    } catch (e) {
      return await getRandomLocation();
    }
  }

  /// 搜索导航栏根据GPS获取位置信息 (失败和异常直接返回null 搜索栏会随机一个位置)
  // static Future<CommodityFilterLocationData?> getSearchBarLocationWithGPS() async {
  //   try {
  //     var location = await _getGPSLocation();
  //     if (!_isValidLocation(location)) {
  //       // 没有经纬度直接返回null
  //       return null;
  //     }
  //     final result = await NetworkV2<CommodityFilterLocationData>(
  //       CommodityFilterLocationData(),
  //     ).requestDataV2(
  //       '/price/get/province',
  //       method: RequestMethod.GET,
  //       parameters: location,
  //     );
  //     return result.isSuccess == true ? result.getData() : null;
  //   } catch (e) {
  //     print('GPS location error: $e');
  //     return null;
  //   }
  // }

  /// 随机获取一个CommodityFilterLocationData
  static Future<CommodityFilterLocationData?> getRandomLocation() async {
    try {
      final result = await NetworkV2<CommodityFilterLocationData>(
        CommodityFilterLocationData(),
      ).requestDataV2(
        'skuRank/getProvince',
        method: RequestMethod.GET,
      );
      if (result.isSuccess == true) {
        return _getRandomFromList(result.getListData());
      } else {
        _showError(result.errorMsg);
      }
    } catch (e) {
      print('Random location error: $e');
      _showError("获取区域错误");
    }
    return null;
  }

  /// 获取GPS位置
  static Future<Map<String, dynamic>> _getGPSLocation() async {

    return {
      // 定位获取失败 默认北京
      'latitude': '39.98426',
      'longitude': '116.495386',
    };
  }

  /// 检查位置信息是否有效
  static bool _isValidLocation(Map<String, dynamic> location) {
    return location['latitude']?.isEmpty == false && location['longitude']?.isEmpty == false;
  }

  /// 从列表中随机选择
  static CommodityFilterLocationData? _getRandomFromList(List<CommodityFilterLocationData>? list) {
    // if (list == null || list.length <= 1) return null;
    // final withoutFirst = list.sublist(1);
    // return withoutFirst[Random().nextInt(withoutFirst.length)];
    if (list != null && list.isNotEmpty) {
      var validLocations = list.where((location) => location.provinceCode != -1).toList();
      return validLocations[Random().nextInt(validLocations.length)];
    }
  }

  /// 显示错误信息
  static void _showError(String? message) {
    if (message?.isNotEmpty == true) {
      XYYContainer.toastChannel.toast(message!);
    }
  }
}
