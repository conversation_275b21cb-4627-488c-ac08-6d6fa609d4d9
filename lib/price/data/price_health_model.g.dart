// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_health_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PriceProviceItemModel _$PriceProviceItemModelFromJson(Map<String, dynamic> json) {
  return PriceProviceItemModel()
    ..province = json['province'] as String?
    ..onSaleProductCount = json['onSaleProductCount'] as int?
    ..priceAdvantageProductCount = json['priceAdvantageProductCount'] as int?
    ..priceHealthScore = (json['priceHealthScore'] as num?)?.toDouble()
    ..priceHealthScorePercent = (json['priceHealthScorePercent'] as num?)?.toDouble()
    ..remark = json['remark'] as String?;
}

Map<String, dynamic> _$PriceProviceItemModelToJson(PriceProviceItemModel instance) => <String, dynamic>{
      'province': instance.province,
      'onSaleProductCount': instance.onSaleProductCount,
      'priceAdvantageProductCount': instance.priceAdvantageProductCount,
      'priceHealthScore': instance.priceHealthScore,
      'priceHealthScorePercent': instance.priceHealthScorePercent,
      'remark': instance.remark,
    };

PriceShopModel _$PriceShopModelFromJson(Map<String, dynamic> json) => PriceShopModel()
  ..shopCode = json['shopCode'] as String?
  ..showName = json['showName'] as String?;

Map<String, dynamic> _$PriceShopModelToJson(PriceShopModel instance) => <String, dynamic>{
      'shopCode': instance.shopCode,
      'showName': instance.showName,
    };
