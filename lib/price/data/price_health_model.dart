///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-
///
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

import 'package:json_annotation/json_annotation.dart';
part 'price_health_model.g.dart';

// 省份商品价格健康度
@JsonSerializable()
class PriceProviceItemModel extends BaseModelV2<PriceProviceItemModel> {
  String? province;

  int? onSaleProductCount;

  int? priceAdvantageProductCount;

  double? priceHealthScore;

  double? priceHealthScorePercent;

  String? remark;

  PriceProviceItemModel();

  factory PriceProviceItemModel.fromJson(Map<String, dynamic> json) {
    return _$PriceProviceItemModelFromJson(json);
  }

  @override
  PriceProviceItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceProviceItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceProviceItemModelToJson(this);
  }
}

@JsonSerializable()
class PriceShopModel extends BaseModelV2<PriceShopModel> {
  String? shopCode;
  String? showName;

  PriceShopModel();

  factory PriceShopModel.fromJson(Map<String, dynamic> json) {
    return _$PriceShopModelFromJson(json);
  }

  @override
  PriceShopModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceShopModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceShopModelToJson(this);
  }
}
