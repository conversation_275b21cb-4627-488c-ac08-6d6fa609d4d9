///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-24 13:58

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'provice_item_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ProviceListItemModel _$ProviceListItemModelFromJson(Map<String, dynamic> json) => ProviceListItemModel()
  ..sold = json['sold'] as int?
  ..onSaleStoreCount = json['onSaleStoreCount'] as int?
  ..purchaseStoreCount = json['purchaseStoreCount'] as int?
  ..provinceProductStatisticsDTO =
      json['provinceProductStatisticsDTO'] == null ? null : PriceProviceItemModel.fromJson(json['provinceProductStatisticsDTO'] as Map<String, dynamic>)
  ..productListingInfo = json['productListingInfo'];

Map<String, dynamic> _$ProviceListItemModelToJson(ProviceListItemModel instance) => <String, dynamic>{
      'sold': instance.sold,
      'onSaleStoreCount': instance.onSaleStoreCount,
      'purchaseStoreCount': instance.purchaseStoreCount,
      'provinceProductStatisticsDTO': instance.provinceProductStatisticsDTO,
      'productListingInfo': instance.productListingInfo,
    };

PriceProviceItemModel _$PriceProviceItemModelFromJson(Map<String, dynamic> json) => PriceProviceItemModel()
  ..province = json['province'] as String?
  ..onSaleProductCount = json['onSaleProductCount'] as int?
  ..priceAdvantageProductCount = json['priceAdvantageProductCount'] as int?
  ..priceHealthScore = (json['priceHealthScore'] as num?)?.toDouble()
  ..priceHealthScorePercent = (json['priceHealthScorePercent'] as num?)?.toDouble()
  ..remark = json['remark'] as String?;

Map<String, dynamic> _$PriceProviceItemModelToJson(PriceProviceItemModel instance) => <String, dynamic>{
      'province': instance.province,
      'onSaleProductCount': instance.onSaleProductCount,
      'priceAdvantageProductCount': instance.priceAdvantageProductCount,
      'priceHealthScore': instance.priceHealthScore,
      'priceHealthScorePercent': instance.priceHealthScorePercent,
      'remark': instance.remark,
    };
