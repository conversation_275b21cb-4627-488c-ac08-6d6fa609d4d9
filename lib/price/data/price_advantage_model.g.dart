///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-24 15:57

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'price_advantage_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PriceAdvantageModel _$PriceAdvantageModelFromJson(Map<String, dynamic> json) => PriceAdvantageModel()
  ..total = json['total'] as int?
  ..list = (json['list'] as List<dynamic>?)?.map((e) => PriceAdvantageItemModel.fromJson(e as Map<String, dynamic>)).toList()
  ..pageNum = json['pageNum'] as int?
  ..pageSize = json['pageSize'] as int?
  ..size = json['size'] as int?
  ..startRow = json['startRow'] as int?
  ..endRow = json['endRow'] as int?
  ..pages = json['pages'] as int?
  ..prePage = json['prePage'] as int?
  ..nextPage = json['nextPage'] as int?
  ..isFirstPage = json['isFirstPage'] as bool?
  ..isLastPage = json['isLastPage'] as bool?
  ..hasPreviousPage = json['hasPreviousPage'] as bool?
  ..hasNextPage = json['hasNextPage'] as bool?
  ..navigatePages = json['navigatePages'] as int?
  ..navigatepageNums = (json['navigatepageNums'] as List<dynamic>?)?.map((e) => e as int).toList()
  ..navigateFirstPage = json['navigateFirstPage'] as int?
  ..navigateLastPage = json['navigateLastPage'] as int?;

Map<String, dynamic> _$PriceAdvantageModelToJson(PriceAdvantageModel instance) => <String, dynamic>{
      'total': instance.total,
      'list': instance.list,
      'pageNum': instance.pageNum,
      'pageSize': instance.pageSize,
      'size': instance.size,
      'startRow': instance.startRow,
      'endRow': instance.endRow,
      'pages': instance.pages,
      'prePage': instance.prePage,
      'nextPage': instance.nextPage,
      'isFirstPage': instance.isFirstPage,
      'isLastPage': instance.isLastPage,
      'hasPreviousPage': instance.hasPreviousPage,
      'hasNextPage': instance.hasNextPage,
      'navigatePages': instance.navigatePages,
      'navigatepageNums': instance.navigatepageNums,
      'navigateFirstPage': instance.navigateFirstPage,
      'navigateLastPage': instance.navigateLastPage,
    };

PriceAdvantageItemModel _$PriceAdvantageItemModelFromJson(Map<String, dynamic> json) => PriceAdvantageItemModel()
  ..id = json['id'] as int?
  ..productImage = json['productImage'] as String?
  ..productName = json['productName'] as String?
  ..spec = json['spec'] as String?
  ..manufacturer = json['manufacturer'] as String?
  ..priceHealth = (json['priceHealth'] as num?)?.toDouble()
  ..purchaseStoreCount = json['purchaseStoreCount'] as int?
  ..businessFormatType = json['businessFormatType'] as int?
  ..provinceCode = json['provinceCode'] as String?
  ..platformType = json['platformType'] as int?
  ..sold = json['sold'] as int?
  ..spuId = json['spuId'] as int?
  ..companyPriceInfo = json['companyPriceInfo'] == null ? null : CompanyPriceInfo.fromJson(json['companyPriceInfo'] as Map<String, dynamic>)
  ..productListingInfo = json['productListingInfo'] == null ? null : ProductListingInfo.fromJson(json['productListingInfo'] as Map<String, dynamic>)
  ..competitorPriceInfo = json['competitorPriceInfo'];

Map<String, dynamic> _$PriceAdvantageItemModelToJson(PriceAdvantageItemModel instance) => <String, dynamic>{
      'id': instance.id,
      'productImage': instance.productImage,
      'productName': instance.productName,
      'spec': instance.spec,
      'manufacturer': instance.manufacturer,
      'priceHealth': instance.priceHealth,
      'purchaseStoreCount': instance.purchaseStoreCount,
      'sold': instance.sold,
      'spuId': instance.spuId,
      'businessFormatType':instance.businessFormatType,
      'platformType':instance.platformType,
      'provinceCode':instance.provinceCode,
      'companyPriceInfo': instance.companyPriceInfo,
      'productListingInfo': instance.productListingInfo,
      'competitorPriceInfo': instance.competitorPriceInfo,
    };

CompanyPriceInfo _$CompanyPriceInfoFromJson(Map<String, dynamic> json) => CompanyPriceInfo()
  ..averagePrice = (json['averagePrice'] as num?)?.toDouble()
  ..onSaleProductCount = json['onSaleProductCount'] as int?
  ..priceAdvantageProductCount = json['priceAdvantageProductCount'] as int?
  ..minBasePriceRage = (json['minBasePriceRage'] as num?)?.toDouble()
  ..maxBasePriceRage = (json['maxBasePriceRage'] as num?)?.toDouble();

Map<String, dynamic> _$CompanyPriceInfoToJson(CompanyPriceInfo instance) => <String, dynamic>{
      'averagePrice': instance.averagePrice,
      'onSaleProductCount': instance.onSaleProductCount,
      'priceAdvantageProductCount': instance.priceAdvantageProductCount,
      'minBasePriceRage': instance.minBasePriceRage,
      'maxBasePriceRage': instance.maxBasePriceRage,
    };

ProductListingInfo _$ProductListingInfoFromJson(Map<String, dynamic> json) => ProductListingInfo()
  ..coveredProvinces = (json['coveredProvinces'] as List<dynamic>?)?.map((e) => e as String).toList()
  ..storeCount = json['storeCount'] as int?
  ..totalInventory = json['totalInventory'] as int?;

Map<String, dynamic> _$ProductListingInfoToJson(ProductListingInfo instance) => <String, dynamic>{
      'coveredProvinces': instance.coveredProvinces,
      'storeCount': instance.storeCount,
      'totalInventory': instance.totalInventory,
    };


PriceSpuModel _$PriceSpuModelFromJson(Map<String, dynamic> json) => PriceSpuModel(
      totalProductCount: json['totalProductCount'] as int?,
      priceAdvantageProductCount: json['priceAdvantageProductCount'] as int?,
      priceDisadvantageProductCount: json['priceDisadvantageProductCount'] as int?,
    );

Map<String, dynamic> _$PriceSpuModelToJson(PriceSpuModel instance) => <String, dynamic>{
      'totalProductCount': instance.totalProductCount,
      'priceAdvantageProductCount': instance.priceAdvantageProductCount,
      'priceDisadvantageProductCount': instance.priceDisadvantageProductCount,
    };
