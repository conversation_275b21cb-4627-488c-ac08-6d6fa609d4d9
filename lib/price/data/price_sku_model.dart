///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-24 15:55
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'price_sku_model.g.dart';

@JsonSerializable()
class PriceSkuModel extends BaseModelV2<PriceSkuModel> {
  int? total;
  List<PriceSkuItemModel>? list;
  int? pageNum;
  int? pageSize;
  int? size;
  int? startRow;
  int? endRow;
  int? pages;
  int? prePage;
  int? nextPage;
  bool? isFirstPage;
  bool? isLastPage;
  bool? hasPreviousPage;
  bool? hasNextPage;
  int? navigatePages;
  List<int>? navigatepageNums;
  int? navigateFirstPage;
  int? navigateLastPage;

  PriceSkuModel();

  factory PriceSkuModel.fromJson(Map<String, dynamic> json) {
    return _$PriceSkuModelFromJson(json);
  }

  @override
  PriceSkuModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceSkuModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceSkuModelToJson(this);
  }
}

@JsonSerializable()
class PriceSkuItemModel {
  int? id;
  String? productImage;
  String? productName;
  String? spec;
  String? manufacturer;
  int? purchaseStoreCount;
  int? sold;
  int? spuId;
  int? skuId;
  String? barcode;
  String? standardId;
  String? merchantName;
  double? minBasePriceRage;
  double? maxBasePriceRage;
  double? salePrice;
  int? priceLevel;
  double? priceRange;
  List<String>? optimizationDetailsDescription;
  ProductListingInfo? productListingInfo;

  PriceSkuItemModel();

  factory PriceSkuItemModel.fromJson(Map<String, dynamic> json) {
    return _$PriceSkuItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$PriceSkuItemModelToJson(this);
  }
}

@JsonSerializable()
class ProductListingInfo {
  List<String>? coveredProvinces;
  int? storeCount;
  int? totalInventory;

  ProductListingInfo();

  factory ProductListingInfo.fromJson(Map<String, dynamic> json) {
    return _$ProductListingInfoFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ProductListingInfoToJson(this);
  }
}

// 价格走势
@JsonSerializable()
class PriceTrendModel extends BaseModelV2<PriceTrendModel> {
  List<int>? date;
  double? price;

  PriceTrendModel();

  factory PriceTrendModel.fromJson(Map<String, dynamic> json) {
    return _$PriceTrendModelFromJson(json);
  }

  @override
  PriceTrendModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceTrendModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceTrendModelToJson(this);
  }
}
