///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-24 15:55
///
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

import 'package:json_annotation/json_annotation.dart';
part 'price_advantage_model.g.dart';

@JsonSerializable()
class PriceAdvantageModel extends BaseModelV2<PriceAdvantageModel> {
  int? total;
  List<PriceAdvantageItemModel>? list;
  int? pageNum;
  int? pageSize;
  int? size;
  int? startRow;
  int? endRow;
  int? pages;
  int? prePage;
  int? nextPage;
  bool? isFirstPage;
  bool? isLastPage;
  bool? hasPreviousPage;
  bool? hasNextPage;
  int? navigatePages;
  List<int>? navigatepageNums;
  int? navigateFirstPage;
  int? navigateLastPage;

  PriceAdvantageModel();

  factory PriceAdvantageModel.fromJson(Map<String, dynamic> json) {
    return _$PriceAdvantageModelFromJson(json);
  }

  @override
  PriceAdvantageModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceAdvantageModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceAdvantageModelToJson(this);
  }
}

@JsonSerializable()
class PriceAdvantageItemModel {
  int? id;
  String? productImage;
  String? productName;
  String? spec;
  String? manufacturer;
  double? priceHealth;
  int? purchaseStoreCount;
  int? businessFormatType;
  String? provinceCode;
  int? platformType;
  int? sold;
  int? spuId;
  CompanyPriceInfo? companyPriceInfo;
  ProductListingInfo? productListingInfo;
  dynamic competitorPriceInfo;

  PriceAdvantageItemModel();

  factory PriceAdvantageItemModel.fromJson(Map<String, dynamic> json) {
    return _$PriceAdvantageItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$PriceAdvantageItemModelToJson(this);
  }
}

@JsonSerializable()
class CompanyPriceInfo {
  double? averagePrice;
  int? onSaleProductCount;
  int? priceAdvantageProductCount;
  double? minBasePriceRage;
  double? maxBasePriceRage;

  CompanyPriceInfo();

  factory CompanyPriceInfo.fromJson(Map<String, dynamic> json) {
    return _$CompanyPriceInfoFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$CompanyPriceInfoToJson(this);
  }
}

@JsonSerializable()
class ProductListingInfo {
  List<String>? coveredProvinces;
  int? storeCount;
  int? totalInventory;

  ProductListingInfo();

  factory ProductListingInfo.fromJson(Map<String, dynamic> json) {
    return _$ProductListingInfoFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$ProductListingInfoToJson(this);
  }
}

@JsonSerializable()
class PriceSpuModel extends BaseModelV2<PriceSpuModel> {
  int? totalProductCount;
  int? priceAdvantageProductCount;
  int? priceDisadvantageProductCount;

  PriceSpuModel({
    this.totalProductCount,
    this.priceAdvantageProductCount,
    this.priceDisadvantageProductCount,
  });

  @override
  PriceSpuModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceSpuModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceSpuModelToJson(this);
  }
}
