///@Description(描述)     xxxx
///@author(作者)          zhangyinhong
///@create(时间)          2025-01-24 13:57

import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

import 'package:json_annotation/json_annotation.dart';

part 'provice_item_model.g.dart';

// 省份商品列表项模型
@JsonSerializable()
class ProviceListItemModel extends BaseModelV2<ProviceListItemModel> {
  int? sold;
  int? purchaseStoreCount;
  int? onSaleStoreCount;
  PriceProviceItemModel? provinceProductStatisticsDTO;
  dynamic productListingInfo;

  ProviceListItemModel();

  factory ProviceListItemModel.fromJson(Map<String, dynamic> json) {
    return _$ProviceListItemModelFromJson(json);
  }

  @override
  ProviceListItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$ProviceListItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$ProviceListItemModelToJson(this);
  }
}

// 省份商品价格健康度
@JsonSerializable()
class PriceProviceItemModel extends BaseModelV2<PriceProviceItemModel> {
  String? province;
  int? onSaleProductCount;
  int? priceAdvantageProductCount;
  double? priceHealthScore;
  double? priceHealthScorePercent;
  String? remark;

  PriceProviceItemModel();

  factory PriceProviceItemModel.fromJson(Map<String, dynamic> json) {
    return _$PriceProviceItemModelFromJson(json);
  }

  @override
  PriceProviceItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$PriceProviceItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PriceProviceItemModelToJson(this);
  }
}
