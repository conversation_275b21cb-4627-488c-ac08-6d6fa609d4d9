// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'pop_statistics_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

PopStatisticsIndexModel _$PopStatisticsIndexModelFromJson(
    Map<String, dynamic> json) {
  return PopStatisticsIndexModel()
    ..shopNum = json['shopNum']
    ..skuNum = json['skuNum']
    ..dataStatisticsResult = (json['dataStatisticsResult'] as List<dynamic>?)
        ?.map((e) => (e as List<dynamic>)
            .map((e) => PopStatisticsIndexEntryModel.fromJson(
                e as Map<String, dynamic>))
            .toList())
        .toList();
}

Map<String, dynamic> _$PopStatisticsIndexModelToJson(
        PopStatisticsIndexModel instance) =>
    <String, dynamic>{
      'shopNum': instance.shopNum,
      'skuNum': instance.skuNum,
      'dataStatisticsResult': instance.dataStatisticsResult,
    };

PopStatisticsIndexEntryModel _$PopStatisticsIndexEntryModelFromJson(
    Map<String, dynamic> json) {
  return PopStatisticsIndexEntryModel()
    ..rate = json['rate']
    ..indexValue = json['indexValue']
    ..index = json['index']
    ..type = json['type'];
}

Map<String, dynamic> _$PopStatisticsIndexEntryModelToJson(
        PopStatisticsIndexEntryModel instance) =>
    <String, dynamic>{
      'rate': instance.rate,
      'indexValue': instance.indexValue,
      'index': instance.index,
      'type': instance.type,
    };

PopDataMerchantListData _$PopDataMerchantListDataFromJson(
    Map<String, dynamic> json) {
  return PopDataMerchantListData()
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) =>
            PopDataMerchantListItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$PopDataMerchantListDataToJson(
        PopDataMerchantListData instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };

PopDataMerchantListItemData _$PopDataMerchantListItemDataFromJson(
    Map<String, dynamic> json) {
  return PopDataMerchantListItemData()
    ..orgId = json['orgId']
    ..shopCode = json['shopCode']
    ..shopName = json['shopName']
    ..pinTuanGmv = json['pinTuanGmv']
    ..pinTuanGmvLinkRatio = json['pinTuanGmvLinkRatio']
    ..realPayGmv = json['realPayGmv']
    ..realPayGmvLinkRatio = json['realPayGmvLinkRatio'];
}

Map<String, dynamic> _$PopDataMerchantListItemDataToJson(
        PopDataMerchantListItemData instance) =>
    <String, dynamic>{
      'orgId': instance.orgId,
      'shopCode': instance.shopCode,
      'shopName': instance.shopName,
      'pinTuanGmv': instance.pinTuanGmv,
      'pinTuanGmvLinkRatio': instance.pinTuanGmvLinkRatio,
      'realPayGmv': instance.realPayGmv,
      'realPayGmvLinkRatio': instance.realPayGmvLinkRatio,
    };
