import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'pop_statistics_data.g.dart';

@JsonSerializable()
class PopStatisticsIndexModel extends BaseModelV2<PopStatisticsIndexModel> {
  /// 关联店铺数
  dynamic shopNum;

  /// 关联SKU数
  dynamic skuNum;

  /// 统计数据
  List<List<PopStatisticsIndexEntryModel>>? dataStatisticsResult;

  PopStatisticsIndexModel();

  @override
  PopStatisticsIndexModel fromJsonMap(Map<String, dynamic> json) {
    return _$PopStatisticsIndexModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PopStatisticsIndexModelToJson(this);
  }
}

@JsonSerializable()
class PopStatisticsIndexEntryModel
    extends BaseModelV2<PopStatisticsIndexEntryModel> {
  /// 环比
  dynamic rate;

  /// 值
  dynamic indexValue;

  /// 标题
  dynamic index;

  /// 类型 1:金额 2:数量 3:费率
  dynamic type;

  /// 计算属性 1:上升 2:下降
  @JsonKey(ignore: true)
  int get toward {
    String rateStr = "$rate";
    double rateNumber = double.tryParse(rateStr) ?? 0;
    return rateNumber > 0 ? 1 : 2;
  }

  @JsonKey(ignore: true)
  String get rateNumber {
    String rateStr = "$rate";
    double rateNumber = double.tryParse(rateStr) ?? 0;
    if (rateNumber < 0) {
      rateNumber = rateNumber * -1;
    }
    return rateNumber.toStringAsFixed(2);
  }

  @JsonKey(ignore: true)
  String get value {
    String typeStr = "$type";
    String indexValueStr = "$indexValue";

    switch (typeStr) {
      case "1":
        return (double.tryParse(indexValueStr) ?? 0).toStringAsFixed(2);
      case "2":
        return (double.tryParse(indexValueStr) ?? 0).toStringAsFixed(0);
      case "3":
        return (double.tryParse(indexValueStr) ?? 0).toStringAsFixed(2);
      default:
        return indexValueStr;
    }
  }

  PopStatisticsIndexEntryModel();

  factory PopStatisticsIndexEntryModel.fromJson(Map<String, dynamic> json) {
    return _$PopStatisticsIndexEntryModelFromJson(json);
  }

  @override
  PopStatisticsIndexEntryModel fromJsonMap(Map<String, dynamic> json) {
    return _$PopStatisticsIndexEntryModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PopStatisticsIndexEntryModelToJson(this);
  }
}

@JsonSerializable()
class PopDataMerchantListData extends BaseModelV2<PopDataMerchantListData> {
  dynamic isLastPage;

  List<PopDataMerchantListItemData>? list;

  PopDataMerchantListData();

  @override
  PopDataMerchantListData fromJsonMap(Map<String, dynamic> json) {
    return _$PopDataMerchantListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PopDataMerchantListDataToJson(this);
  }
}

@JsonSerializable()
class PopDataMerchantListItemData
    extends BaseModelV2<PopDataMerchantListItemData> {
  dynamic orgId;

  dynamic shopCode;

  dynamic shopName;

  dynamic pinTuanGmv;

  dynamic pinTuanGmvLinkRatio;

  dynamic realPayGmv;

  dynamic realPayGmvLinkRatio;

  PopDataMerchantListItemData();

  factory PopDataMerchantListItemData.fromJson(Map<String, dynamic> json) {
    return _$PopDataMerchantListItemDataFromJson(json);
  }

  @override
  PopDataMerchantListItemData fromJsonMap(Map<String, dynamic> json) {
    return _$PopDataMerchantListItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PopDataMerchantListItemDataToJson(this);
  }
}
