import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/data/pop_statistics_data.dart';
import 'package:flutter/material.dart';

class PopDataStatisticsItem extends StatelessWidget {
  final List<PopStatisticsIndexEntryModel> data;

  PopDataStatisticsItem({this.data = const []});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(2),
      ),
      margin: EdgeInsets.only(left: 15, right: 15, top: 10),
      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
      child: Wrap(
        runSpacing: 20,
        children: this.generateEntrys(),
      ),
    );
  }

  List<Widget> generateEntrys() {
    return this.data.map((e) => PopDataStatisticsEntry(data: e)).toList();
  }
}

class PopDataStatisticsEntry extends StatelessWidget {
  final PopStatisticsIndexEntryModel data;

  PopDataStatisticsEntry({required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: (MediaQuery.of(context).size.width - 50) / 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('${data.index}',
              style: TextStyle(
                color: Color(0xFF676773),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              )),
          SizedBox(height: 3.5),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Visibility(
                visible: "${data.type}" == "1",
                child: Text('¥',
                    style: TextStyle(
                      color: Color(0xFF0D0E10),
                      fontSize: 11,
                      fontWeight: FontWeight.w700,
                    )),
              ),
              Text(
                  data.value +
                      ("${data.type}" == "3" || "${data.type}" == "4"
                          ? "%"
                          : ""),
                  style: TextStyle(
                    color: Color(0xFF0D0E10),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  )),
            ],
          ),
          Container(
            constraints: BoxConstraints(minHeight: 15),
            child: RichText(
                text: TextSpan(
              text: "${data.type}" == "4" ? "${data.rate}" : "环比",
              style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 11,
                fontWeight: FontWeight.w400,
              ),
              children: [
                TextSpan(
                    text: this.rateNumber,
                    style: TextStyle(
                      color: data.toward == 1
                          ? Color(0xFF00B377)
                          : Color(0xFFFF4741),
                      fontSize: 11,
                      fontWeight: FontWeight.w400,
                    )),
              ],
            )),
          )
        ],
      ),
    );
  }

  String get rateNumber {
    if ("${data.type}" == "4") {
      return "";
    }
    if (data.rateNumber == "0.00") {
      return "--";
    } else {
      return (data.toward == 1 ? '↑' : '↓') + data.rateNumber + '%';
    }
  }
}

class PopDataStatisticsRowItem extends StatelessWidget {
  final String title;
  final String content;
  final VoidCallback? clickAction;

  PopDataStatisticsRowItem({
    required this.title,
    required this.content,
    this.clickAction,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.clickAction,
      child: Container(
        padding: EdgeInsets.only(left: 15, top: 18, right: 15, bottom: 18),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(2),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              this.title,
              style: TextStyle(
                color: Color(0xFF292933),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            Text(
              this.content,
              textAlign: TextAlign.right,
              style: TextStyle(
                color: Color(0xFF292933),
                fontSize: 15,
                fontWeight: FontWeight.w500,
              ),
            ),
            Spacer(),
            Visibility(
              visible: this.clickAction != null,
              child: Image.asset(
                'assets/images/funnel/funnel_arrow_right.png',
                width: 13,
                height: 13,
              ),
            )
          ],
        ),
      ),
    );
  }
}
