import 'package:flutter/material.dart';

class PopMerchantStatisticsTimeOutItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(2),
      ),
      margin: EdgeInsets.only(left: 15, right: 15, top: 10),
      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('销售GMV',
              style: TextStyle(
                color: Color(0xFF676773),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              )),
          SizedBox(height: 3.5),
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text('0.10',
                  style: TextStyle(
                    color: Color(0xFF0D0E10),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  )),
              Text('%',
                  style: TextStyle(
                    color: Color(0xFF0D0E10),
                    fontSize: 11,
                    fontWeight: FontWeight.w700,
                  ))
            ],
          ),
          Text(
            '(超过48小时发货率)',
            style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 11,
                fontWeight: FontWeight.w400),
          )
        ],
      ),
    );
  }
}

class PopMerchantStatisticsCustomerNumberItem extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(2),
      ),
      margin: EdgeInsets.only(left: 15, right: 15, top: 10),
      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('下单客户数',
                  style: TextStyle(
                    color: Color(0xFF676773),
                    fontSize: 12,
                    fontWeight: FontWeight.w400,
                  )),
              SizedBox(height: 3.5),
              Text('7.432.00',
                  style: TextStyle(
                    color: Color(0xFF0D0E10),
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                  )),
              RichText(
                  text: TextSpan(
                text: "环比",
                style: TextStyle(
                  color: Color(0xFF9494A6),
                  fontSize: 11,
                  fontWeight: FontWeight.w400,
                ),
                children: [
                  TextSpan(
                      text: '↑12%',
                      style: TextStyle(
                        color: Color(0xFF00B377),
                        fontSize: 11,
                        fontWeight: FontWeight.w400,
                      )),
                  TextSpan(
                      text: '↓12%',
                      style: TextStyle(
                        color: Color(0xFFFF4741),
                        fontSize: 11,
                        fontWeight: FontWeight.w400,
                      ))
                ],
              ))
            ],
          ),
          Spacer(),
          Container(
            color: Color(0xFFDDDDDD),
            height: 45,
            width: 0.5,
          ),
          Spacer(),
          PopMerchantNumberEntry(),
          Spacer(),
          PopMerchantNumberEntry(),
          Spacer()
        ],
      ),
    );
  }
}

class PopMerchantNumberEntry extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '首单客户数:',
            style: TextStyle(
                color: Color(0xFF676773),
                fontSize: 12,
                fontWeight: FontWeight.w400),
          ),
          Text(
            '3,219',
            style: TextStyle(
                color: Color(0xFF0D0E10),
                fontSize: 14,
                fontWeight: FontWeight.w700),
          )
        ],
      ),
    );
  }
}
