import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/data/pop_statistics_data.dart';

class PopDataMerchantItem extends StatelessWidget {
  final PopDataMerchantListItemData data;

  PopDataMerchantItem({required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
          color: Color(0xFFFFFFFF), borderRadius: BorderRadius.circular(7)),
      margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(top: 1),
                child: Image.asset(
                  'assets/images/pop_data/pop_data_merchant.png',
                  width: 25,
                  height: 25,
                ),
              ),
              SizedBox(width: 5),
              Expanded(
                child: Text(
                  '${this.data.shopName}',
                  style: TextStyle(
                    color: Color(0xFF333333),
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 5),
                child: Image.asset(
                  'assets/images/pop_data/pop_data_arrow.png',
                  width: 11,
                  height: 11,
                ),
              )
            ],
          ),
          SizedBox(height: 12),
          Container(
            margin: EdgeInsets.only(
              left: 30,
            ),
            child: Row(
              children: [
                PopDataMerchantEntry(
                  title: "净采购总金额",
                  amount: "${data.realPayGmv}",
                  rate: data.realPayGmvLinkRatio,
                ),
                Container(
                  color: Color(0xFFDDDDDD),
                  height: 45,
                  width: 0.5,
                  margin: EdgeInsets.only(right: 30),
                ),
                PopDataMerchantEntry(
                  title: "拼团活动净采购金额",
                  amount: "${data.pinTuanGmv}",
                  rate: data.pinTuanGmvLinkRatio,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

class PopDataMerchantEntry extends StatelessWidget {
  final String title;
  final String amount;
  final dynamic rate;

  PopDataMerchantEntry({
    required this.title,
    required this.amount,
    required this.rate,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: (MediaQuery.of(context).size.width - 110) / 2,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(title,
              style: TextStyle(
                color: Color(0xFF676773),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              )),
          SizedBox(height: 3.5),
          RichText(
            text: TextSpan(
                text: "¥",
                style: TextStyle(
                  color: Color(0xFF0D0E10),
                  fontSize: 11,
                  fontWeight: FontWeight.w700,
                ),
                children: [
                  TextSpan(
                      text: amount,
                      style: TextStyle(
                        color: Color(0xFF0D0E10),
                        fontSize: 17,
                        fontWeight: FontWeight.w700,
                      )),
                ]),
          ),
          RichText(
              text: TextSpan(
            text: "环比",
            style: TextStyle(
              color: Color(0xFF9494A6),
              fontSize: 11,
              fontWeight: FontWeight.w400,
            ),
            children: [
              TextSpan(
                  text: this.rateNumber == "0.00"
                      ? "--"
                      : (this.toward == 1 ? '↑' : '↓') + this.rateNumber + '%',
                  style: TextStyle(
                    color: this.toward == 1
                        ? Color(0xFF00B377)
                        : Color(0xFFFF4741),
                    fontSize: 11,
                    fontWeight: FontWeight.w400,
                  )),
            ],
          ))
        ],
      ),
    );
  }

  int get toward {
    String rateStr = "$rate";
    double rateNumber = double.tryParse(rateStr) ?? 0;
    return rateNumber > 0 ? 1 : 2;
  }

  String get rateNumber {
    String rateStr = "$rate";
    double rateNumber = double.tryParse(rateStr) ?? 0;
    if (rateNumber < 0) {
      rateNumber = rateNumber * -1;
    }
    return rateNumber.toStringAsFixed(2);
  }
}
