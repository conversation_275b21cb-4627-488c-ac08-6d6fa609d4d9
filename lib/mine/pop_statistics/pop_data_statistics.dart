import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/data/pop_statistics_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/pop_statistics/widget/pop_data_statistics_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class PopDataStatisticsPage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return PopDataStatisticsPageState();
  }
}

class PopDataStatisticsPageState extends BaseState<PopDataStatisticsPage> {
  List<String> filterTitles = ['昨天', '今天', '本月', '上月'];

  ValueNotifier<BackgroundButtonState>? controller =
      ValueNotifier(BackgroundButtonState.selected);

  EasyRefreshController _refreshController = EasyRefreshController();

  PopStatisticsIndexModel? sourceData;

  bool requestSuccess = true;

  // 判断是否是M级账号
  bool isManagerLevel = false;

  // 选择的名称
  String name = "全部";

  Map<String, dynamic> params = {'period': 1};

  @override
  void initState() {
    _requestRoleType();
    refreshData();
    super.initState();
  }

  void _requestRoleType() {
    UserInfoUtil.isBDMOrGJRBDM().then((value) {
      setState(() {
        this.isManagerLevel = value;
      });
    });
  }

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    int itemCount = (this.sourceData?.dataStatisticsResult?.length ?? 0) + 1;
    return Container(
      color: Color(0xFFFFFFFF),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(top: 10, bottom: 10, left: 10),
            child: Wrap(
              runSpacing: 10,
              spacing: 10,
              runAlignment: WrapAlignment.start,
              alignment: WrapAlignment.start,
              children: getFilterItem(),
            ),
          ),
          Expanded(
            child: Container(
              color: Color(0xFFF7F7F8),
              child: EasyRefresh(
                onRefresh: () async {
                  await this.refreshData();
                },
                controller: _refreshController,
                child: ListView.builder(
                  itemCount: itemCount,
                  itemBuilder: (context, index) {
                    if (index == itemCount - 1) {
                      return Container(
                        margin: EdgeInsets.fromLTRB(15, 10, 15, 15),
                        child: PopDataStatisticsRowItem(
                          title: "关联店铺数：",
                          content: "${this.sourceData?.shopNum ?? 0}",
                          clickAction: () {
                            if (this.params['period'] == 1) {
                              showToast('pop店铺暂不支持查看当天数据');
                              return;
                            }
                            var url =
                                '/pop_data_merchant_list?isSearch=0&period=${params['period']}';
                            if (params.containsKey("groupId")) {
                              url += "&groupId=${params['groupId']}";
                            } else if (params.containsKey("searchUserId")) {
                              url += "&searchUserId=${params['searchUserId']}";
                            }
                            XYYContainer.open(url);
                          },
                        ),
                      );
                    }
                    return PopDataStatisticsItem(
                      data: this.sourceData?.dataStatisticsResult?[index] ?? [],
                    );
                  },
                ),
                emptyWidget: getEmptyWidget(),
              ),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> getFilterItem() {
    int index = 0;
    return this.filterTitles.map((e) {
      dynamic option = 1;
      switch (index) {
        case 0:
          option = 6;
          break;
        case 1:
          option = 1;
          break;
        case 2:
          option = 3;
          break;
        case 3:
          option = 8;
          break;
        default:
      }
      Widget button = Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(2)),
        clipBehavior: Clip.hardEdge,
        child: BackgroundStateButton(
          title: e,
          controller: index == 1 ? this.controller : null,
          panding: EdgeInsets.fromLTRB(17, 6, 17, 6),
          option: option,
          onPressed: (controller, option) {
            if (this.controller != controller) {
              this.controller?.value = BackgroundButtonState.normal;
              this.controller = controller;
            }
            this.params['period'] = option;
            this._refreshController.callRefresh();
          },
        ),
      );
      index += 1;
      return button;
    }).toList();
  }

  Widget? getEmptyWidget() {
    if (!this.requestSuccess && this.sourceData == null) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        this.refreshData();
      });
    }
    if (this.sourceData == null) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "POP数据统计";
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        Visibility(
          visible: this.isManagerLevel,
          child: TextButton(
            onPressed: () {
              XYYContainer.open(
                  'xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=false&isPop=true',
                  callback: (params) {
                try {
                  if (!(params?.containsKey("name") ?? false)) {
                    return;
                  }
                  setState(() {
                    this.name = params?["name"];
                  });
                  if (this.name == '全部') {
                    this.params.remove('groupId');
                    this.params.remove('searchUserId');
                  } else {
                    if (params?["isgroup"] == 'true') {
                      this.params.remove('searchUserId');
                      this.params['groupId'] = params?['id'];
                    } else {
                      this.params.remove('groupId');
                      this.params['searchUserId'] = params?['id'];
                    }
                  }
                  this._refreshController.callRefresh();
                } catch (e) {}
              });
            },
            child: Container(
              constraints: BoxConstraints(maxWidth: 90),
              margin: EdgeInsets.only(right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Expanded(
                    child: Text(
                      this.name,
                      textAlign: TextAlign.right,
                      style: TextStyle(
                          color: Color(0xFF00B377),
                          fontSize: 16,
                          fontWeight: FontWeight.w500),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 5),
                  Image.asset(
                    'assets/images/pop_data/pop_statistics_right.png',
                    width: 12,
                    height: 12,
                  ),
                ],
              ),
            ),
            style: ButtonStyle(
              overlayColor:
                  MaterialStateProperty.all<Color>(Colors.transparent),
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              minimumSize: MaterialStateProperty.all<Size>(Size.zero),
            ),
          ),
        )
      ],
    );
  }

  /// Request
  Future<void> refreshData() async {
    if (this.sourceData == null) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<PopStatisticsIndexModel>(PopStatisticsIndexModel())
            .requestDataV2(
      'pop/indexStatistics',
      parameters: this.params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      _refreshController.finishRefresh();
      this.requestSuccess = result.isSuccess == true;
      if (result.isSuccess == true) {
        PopStatisticsIndexModel? source = result.getData();
        if (source != null) {
          this.sourceData = source;
        }
      }
      setState(() {});
    }
  }
}
