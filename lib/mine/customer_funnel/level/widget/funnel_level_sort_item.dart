import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_sort_base_item.dart';
import 'package:flutter/material.dart';

class FunnelLevelSortItem extends FunnelSortBaseItem {
  FunnelLevelSortItem({
    required int index,
    bool isBottom = false,
    required String title,
    required String thisMonthNumber,
    required String lastMonthNumber,
    VoidCallback? clickAction,
  }) : super(
          index: index,
          title: title,
          isBottom: isBottom,
          clickAction: clickAction,
          content: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                FunnelContentSpan(
                  title: '本月S级客户数：',
                  content: thisMonthNumber + "家",
                ),
                SizedBox(height: 5),
                FunnelContentSpan(
                  title: '上月S级客户数：',
                  content: lastMonthNumber + "家",
                ),
              ],
            ),
          ),
        );
}
