import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_sort_base_item.dart';
import 'package:flutter/material.dart';

class FunnelPotentialSortItem extends FunnelSortBaseItem {
  FunnelPotentialSortItem({
    required int index,
    bool isBottom = false,
    required String title,
    required String potentialNumber,
    required dynamic isBd,
    bool contentHidden = false,
    VoidCallback? clickAction,
  }) : super(
          index: index,
          title: title,
          isBottom: isBottom,
          clickAction: clickAction,
          contentHidden: contentHidden,
          content: Visibility(
            visible: ("$isBd" == "1" || "$isBd" == "3"),
            child: Container(
              child: FunnelContentSpan(
                title: '本月S级潜力客户数：',
                content: potentialNumber + "家",
              ),
            ),
          ),
        );
}
