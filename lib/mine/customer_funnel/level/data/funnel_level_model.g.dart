// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'funnel_level_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunnelLevelDeptData _$FunnelLevelDeptDataFromJson(Map<String, dynamic> json) {
  return FunnelLevelDeptData()
    ..crmUserList = (json['crmUserList'] as List<dynamic>?)
        ?.map(
            (e) => FunnelLevelDeptItemData.fromJson(e as Map<String, dynamic>))
        .toList()
    ..thisMonthSCustomers = json['thisMonthSCustomers']
    ..potentialCustomers = json['potentialCustomers'];
}

Map<String, dynamic> _$FunnelLevelDeptDataToJson(
        FunnelLevelDeptData instance) =>
    <String, dynamic>{
      'crmUserList': instance.crmUserList,
      'thisMonthSCustomers': instance.thisMonthSCustomers,
      'potentialCustomers': instance.potentialCustomers,
    };

FunnelLevelDeptItemData _$FunnelLevelDeptItemDataFromJson(
    Map<String, dynamic> json) {
  return FunnelLevelDeptItemData()
    ..oaId = json['oaId']
    ..crmUserName = json['crmUserName']
    ..deptCode = json['deptCode']
    ..crmUserDepartment = json['crmUserDepartment']
    ..haveChild = json['haveChild']
    ..isBd = json['isBd']
    ..postOaIds = json['postOaIds']
    ..deptAndUserName = json['deptAndUserName']
    ..isPostDept = json['isPostDept']
    ..thisMonthSCustomers = json['thisMonthSCustomers']
    ..lastMonthSCustomers = json['lastMonthSCustomers']
    ..potentialCustomers = json['potentialCustomers'];
}

Map<String, dynamic> _$FunnelLevelDeptItemDataToJson(
        FunnelLevelDeptItemData instance) =>
    <String, dynamic>{
      'oaId': instance.oaId,
      'crmUserName': instance.crmUserName,
      'deptCode': instance.deptCode,
      'crmUserDepartment': instance.crmUserDepartment,
      'haveChild': instance.haveChild,
      'isBd': instance.isBd,
      'postOaIds': instance.postOaIds,
      'deptAndUserName': instance.deptAndUserName,
      'isPostDept': instance.isPostDept,
      'thisMonthSCustomers': instance.thisMonthSCustomers,
      'lastMonthSCustomers': instance.lastMonthSCustomers,
      'potentialCustomers': instance.potentialCustomers,
    };

FunnelLevelMerchantData _$FunnelLevelMerchantDataFromJson(
    Map<String, dynamic> json) {
  return FunnelLevelMerchantData()
    ..sLevelCustomers = json['sLevelCustomers']
    ..isLastPage = json['isLastPage']
    ..merchantDtoList = (json['merchantDtoList'] as List<dynamic>?)
        ?.map((e) =>
            FunnelLevelMerchantItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelLevelMerchantDataToJson(
        FunnelLevelMerchantData instance) =>
    <String, dynamic>{
      'sLevelCustomers': instance.sLevelCustomers,
      'isLastPage': instance.isLastPage,
      'merchantDtoList': instance.merchantDtoList,
    };

FunnelLevelMerchantItemData _$FunnelLevelMerchantItemDataFromJson(
    Map<String, dynamic> json) {
  return FunnelLevelMerchantItemData()
    ..merchantId = json['merchantId']
    ..merchantName = json['merchantName']
    ..totalOrderAmount = json['totalOrderAmount']
    ..orderNum = json['orderNum']
    ..lastOrderTime = json['lastOrderTime']
    ..levelDesc = json['levelDesc'];
}

Map<String, dynamic> _$FunnelLevelMerchantItemDataToJson(
        FunnelLevelMerchantItemData instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'totalOrderAmount': instance.totalOrderAmount,
      'orderNum': instance.orderNum,
      'lastOrderTime': instance.lastOrderTime,
      'levelDesc': instance.levelDesc,
    };

FunnelPotentialMerchantData _$FunnelPotentialMerchantDataFromJson(
    Map<String, dynamic> json) {
  return FunnelPotentialMerchantData()
    ..total = json['total']
    ..isLastPage = json['isLastPage']
    ..potentialDtos = (json['potentialDtos'] as List<dynamic>?)
        ?.map((e) =>
            FunnelPotentialMerchantItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelPotentialMerchantDataToJson(
        FunnelPotentialMerchantData instance) =>
    <String, dynamic>{
      'total': instance.total,
      'isLastPage': instance.isLastPage,
      'potentialDtos': instance.potentialDtos,
    };

FunnelPotentialMerchantItemData _$FunnelPotentialMerchantItemDataFromJson(
    Map<String, dynamic> json) {
  return FunnelPotentialMerchantItemData()
    ..merchantId = json['merchantId']
    ..merchantName = json['merchantName']
    ..totalOrderAmount = json['totalOrderAmount']
    ..potentialInfo = json['potentialInfo'];
}

Map<String, dynamic> _$FunnelPotentialMerchantItemDataToJson(
        FunnelPotentialMerchantItemData instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'totalOrderAmount': instance.totalOrderAmount,
      'potentialInfo': instance.potentialInfo,
    };
