import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/data/funnel_level_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/widget/funnel_potential_merchant_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelPotentialMerchantPage extends BasePage {
  final dynamic oaId;
  final dynamic isBd;
  final dynamic potentialNumber;

  FunnelPotentialMerchantPage({this.oaId, this.isBd, this.potentialNumber});

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelPotentialMerchantPageState();
  }
}

class FunnelPotentialMerchantPageState
    extends BaseState<FunnelPotentialMerchantPage> {
  EasyRefreshController _controller = EasyRefreshController();

  int page = 0;

  List<FunnelPotentialMerchantItemData> dataSource = [];
  dynamic potentialNumber;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(top: 10, bottom: 10),
            child: FunnelRowItem(
              title: "本月S级潜力客户数：",
              content: getPotentialNumber(),
              unit: "家",
            ),
          ),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              onLoad: () async {
                this.loadMoreData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelPotentialMerchantItemData model =
                      this.dataSource[index];
                  return Container(
                    child: FunnelPotentialMerchantItem(
                      title: '${model.merchantName ?? "--"}',
                      totalOrderAmount:
                          FunnelUtil.amountFlex(model.totalOrderAmount),
                      potentialInfo: '${model.potentialInfo}',
                      isTop: index == 0,
                      isBottom: index == this.dataSource.length - 1,
                      clickAction: () {
                        FunnelUtil.jumpMerchantInfo(model.merchantId);
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  String getPotentialNumber() {
    if (this.potentialNumber != null) {
      return "${this.potentialNumber ?? 0}";
    }
    return "${widget.potentialNumber ?? 0}";
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshData() {
    this.page = 0;
    this.requestListData();
  }

  void loadMoreData() {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {
      "pageNum": this.page,
      "pageSize": "10",
    };
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.isBd != null && widget.isBd != "null") {
      params["isBd"] = widget.isBd;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelPotentialMerchantData>(
            FunnelPotentialMerchantData())
        .requestDataV2(
      'funnerV2/potentialCustomers',
      parameters: params,
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    this._controller.finishLoad();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelPotentialMerchantData? data = result.getData();
        if (data != null) {
          this.potentialNumber = data.total;
          List<FunnelPotentialMerchantItemData> source =
              data.potentialDtos ?? [];
          if (this.page == 0) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          if (source.length != 10) {
            this._controller.finishLoad(noMore: true);
          }
          this.page += 1;
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "S级潜力客户";
  }
}
