import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/data/funnel_level_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/widget/funnel_level_sort_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelLevelPage extends BasePage {
  final dynamic oaId;
  final dynamic sLevelNumber;
  final dynamic deptCode;

  FunnelLevelPage({this.oaId, this.sLevelNumber, this.deptCode});

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelLevelPageState();
  }
}

class FunnelLevelPageState extends BaseState<FunnelLevelPage> {
  EasyRefreshController _controller = EasyRefreshController();

  List<FunnelLevelDeptItemData> dataSource = [];
  dynamic sLevelNumber;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.all(10),
            child: FunnelRowItem(
              title: "本月S级客户数：",
              content: getSLevelNumber(),
              unit: "家",
            ),
          ),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelLevelDeptItemData model = this.dataSource[index];
                  return FunnelLevelSortItem(
                    title: '${model.deptAndUserName ?? "--"}',
                    index: index,
                    isBottom: index == this.dataSource.length - 1,
                    lastMonthNumber: '${model.lastMonthSCustomers}',
                    thisMonthNumber: '${model.thisMonthSCustomers}',
                    clickAction: () {
                      bool haveChild = "${model.haveChild}" == "1";
                      dynamic oaId = model.oaId;
                      dynamic isBd = model.isBd;
                      dynamic deptCode = model.deptCode;
                      dynamic sLevelNumber = model.thisMonthSCustomers ?? 0;
                      if (haveChild) {
                        XYYContainer.open(
                            '/funnel_level_page?oaId=$oaId&deptCode=$deptCode&sLevelNumber=$sLevelNumber');
                      } else {
                        XYYContainer.open(
                            '/funnel_level_merchant_page?oaId=$oaId&isBd=$isBd&sLevelNumber=$sLevelNumber');
                      }
                    },
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          )
        ],
      ),
    );
  }

  String getSLevelNumber() {
    if (this.sLevelNumber != null) {
      return "${this.sLevelNumber}";
    }
    return "${widget.sLevelNumber ?? 0}";
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshData() async {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {"movesaleType": "0"};
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.deptCode != null && widget.deptCode != "null") {
      params["deptCode"] = widget.deptCode;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelLevelDeptData>(FunnelLevelDeptData())
        .requestDataV2(
      'funnerV2/sLevelStatistics',
      parameters: params,
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelLevelDeptData? data = result.getData();
        if (data != null) {
          this.sLevelNumber = data.thisMonthSCustomers;
          this.dataSource = data.crmUserList ?? [];
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "本月S级客户";
  }
}
