import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/data/funnel_level_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/level/widget/funnel_level_merchat_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelLevelMerchantPage extends BasePage {
  final dynamic oaId;
  final dynamic isBd;
  final dynamic sLevelNumber;

  FunnelLevelMerchantPage({this.oaId, this.isBd, this.sLevelNumber});

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelLevelMerchantPageState();
  }
}

class FunnelLevelMerchantPageState extends BaseState<FunnelLevelMerchantPage> {
  EasyRefreshController _controller = EasyRefreshController();

  dynamic sLevelNumber;

  int page = 0;

  List<FunnelLevelMerchantItemData> dataSource = [];

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(top: 10, bottom: 10),
            child: FunnelRowItem(
              title: "本月S级客户数：",
              content: getSLevelNumber(),
              unit: "家",
            ),
          ),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              onLoad: () async {
                this.loadMoreData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelLevelMerchantItemData model = this.dataSource[index];
                  return Container(
                    child: FunnelLevelMerchantItem(
                      title: '${model.merchantName ?? "--"}',
                      orderAmount:
                          FunnelUtil.amountFlex(model.totalOrderAmount),
                      orderNum: '${model.orderNum ?? 0}',
                      orderTime: '${model.lastOrderTime ?? "--"}',
                      isTop: index == 0,
                      isBottom: index == this.dataSource.length - 1,
                      clickAction: () {
                        FunnelUtil.jumpMerchantInfo(model.merchantId);
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  String getSLevelNumber() {
    if (this.sLevelNumber != null) {
      return "${this.sLevelNumber ?? 0}";
    }
    return "${widget.sLevelNumber ?? 0}";
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshData() {
    this.page = 0;
    this.requestListData();
  }

  void loadMoreData() {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {
      "pageNum": this.page,
      "pageSize": "10",
    };
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.isBd != null && widget.isBd != "null") {
      params["isBd"] = widget.isBd;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result =
        await NetworkV2<FunnelLevelMerchantData>(FunnelLevelMerchantData())
            .requestDataV2(
      'funnerV2/sLevelCustomers',
      parameters: params,
      contentType: RequestContentType.FORM,
      method: RequestMethod.GET,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    this._controller.finishLoad();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelLevelMerchantData? data = result.getData();
        if (data != null) {
          this.sLevelNumber = data.sLevelCustomers;
          List<FunnelLevelMerchantItemData> source = data.merchantDtoList ?? [];
          if (this.page == 0) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          if (source.length != 10) {
            this._controller.finishLoad(noMore: true);
          }
          this.page += 1;
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "本月S级客户";
  }
}
