import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/quality/data/funnel_quality_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/quality/widget/funnel_quality_merchant_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_column_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelQualityMerchantPage extends BasePage {
  final dynamic oaId;
  final dynamic isBd;
  final dynamic totalCustNum;

  FunnelQualityMerchantPage({
    this.oaId,
    this.isBd,
    this.totalCustNum,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelQualityMerchantPageState();
  }
}

class FunnelQualityMerchantPageState
    extends BaseState<FunnelQualityMerchantPage> {
  EasyRefreshController _controller = EasyRefreshController();

  int page = 0;

  List<FunnelQualityMerchantItemModel> dataSource = [];

  dynamic qualityRatio;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          this.getTopWidget(),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              onLoad: () async {
                this.loadMoreData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelQualityMerchantItemModel model = this.dataSource[index];
                  return Container(
                    child: FunnelQualityMerchantItem(
                      title: '${model.merchantName ?? "--"}',
                      isTop: index == 0,
                      isBottom: index == this.dataSource.length - 1,
                      orderAmount:
                          FunnelUtil.amountFlex(model.totalOrderAmount),
                      orderNum: "${model.orderNum ?? 0}",
                      orderTime: '${model.lastOrderTime ?? "--"}',
                      clickAction: () {
                        FunnelUtil.jumpMerchantInfo(model.merchantId);
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    double ratio = double.parse("${this.qualityRatio ?? 0}");
    return Container(
      padding: EdgeInsets.only(top: 10, bottom: 10),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: FunnelColumnItem(
              title: "质量客户数：",
              content: "${widget.totalCustNum}",
              unit: "家",
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            flex: 1,
            child: FunnelColumnItem(
              title: "质量客户/动销客户:",
              content: "${ratio.toStringAsFixed(2)}",
              unit: "%",
            ),
          ),
        ],
      ),
    );
  }

  void refreshData() {
    this.page = 0;
    this.requestListData();
  }

  void loadMoreData() {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {
      "pageNum": this.page,
      "pageSize": "10",
    };
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.isBd != null && widget.isBd != "null") {
      params["isBd"] = widget.isBd;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelQualityMerchantDataModel>(
            FunnelQualityMerchantDataModel())
        .requestDataV2(
      'customerFunnel/qualityCustomerList',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    this._controller.finishLoad();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelQualityMerchantDataModel? data = result.getData();
        if (data != null) {
          this.qualityRatio = data.qualityRatio;
          List<FunnelQualityMerchantItemModel> source = data.merchantList ?? [];
          if (this.page == 0) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          if (source.length != 10) {
            this._controller.finishLoad(noMore: true);
          }
          this.page += 1;
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "质量客户";
  }
}
