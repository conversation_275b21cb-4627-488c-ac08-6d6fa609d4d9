import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'funnel_quality_model.g.dart';

@JsonSerializable()
class FunnelQualitySalesDataModel extends BaseModelV2 {
  /// 累计客户数
  dynamic totalCustNum;

  /// 质量客户/动销客户占比
  dynamic qualityRatio;

  /// 员工列表
  List<FunnelQualitySalesItemModel>? crmUserList;

  FunnelQualitySalesDataModel();

  factory FunnelQualitySalesDataModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelQualitySalesDataModelFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelQualitySalesDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelQualitySalesDataModelToJson(this);
  }
}

@JsonSerializable()
class FunnelQualitySalesItemModel {
  /// 员工编码
  dynamic oaId;

  /// 员工名称
  dynamic crmUserName;

  /// 部门编码
  dynamic deptCode;

  /// 部门名称
  dynamic crmUserDepartment;

  /// 列表展示名称
  dynamic deptAndUserName;

  /// 客户数量
  dynamic customerNum;

  /// 当前BD质量客户/动销客户占比
  dynamic currQualityRatio;

  /// 是否有子节点：1有，0无
  dynamic haveChild;

  /// 1=bd，2管理层
  dynamic isBd;

  /// 是部门还是岗位 1：岗位 / 0：部门
  dynamic isPostDept;

  FunnelQualitySalesItemModel();

  factory FunnelQualitySalesItemModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelQualitySalesItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$FunnelQualitySalesItemModelToJson(this);
  }
}

@JsonSerializable()
class FunnelQualityMerchantDataModel extends BaseModelV2 {
  /// 累计客户数
  dynamic totalCustNum;

  /// 质量客户/动销客户占比
  dynamic qualityRatio;

  /// 员工列表
  List<FunnelQualityMerchantItemModel>? merchantList;

  FunnelQualityMerchantDataModel();

  factory FunnelQualityMerchantDataModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelQualityMerchantDataModelFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelQualityMerchantDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelQualityMerchantDataModelToJson(this);
  }
}

@JsonSerializable()
class FunnelQualityMerchantItemModel {
  /// 药店编码
  dynamic merchantId;

  /// 药店名称
  dynamic merchantName;

  /// 订单金额
  dynamic totalOrderAmount;

  /// 订单数量
  dynamic orderNum;

  /// 客户数量
  dynamic customerNum;

  /// 最近下单时间
  dynamic lastOrderTime;

  FunnelQualityMerchantItemModel();

  factory FunnelQualityMerchantItemModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelQualityMerchantItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$FunnelQualityMerchantItemModelToJson(this);
  }
}
