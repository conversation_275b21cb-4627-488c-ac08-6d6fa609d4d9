// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'funnel_quality_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunnelQualitySalesDataModel _$FunnelQualitySalesDataModelFromJson(
    Map<String, dynamic> json) {
  return FunnelQualitySalesDataModel()
    ..totalCustNum = json['totalCustNum']
    ..qualityRatio = json['qualityRatio']
    ..crmUserList = (json['crmUserList'] as List<dynamic>?)
        ?.map((e) =>
            FunnelQualitySalesItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelQualitySalesDataModelToJson(
        FunnelQualitySalesDataModel instance) =>
    <String, dynamic>{
      'totalCustNum': instance.totalCustNum,
      'qualityRatio': instance.qualityRatio,
      'crmUserList': instance.crmUserList,
    };

FunnelQualitySalesItemModel _$FunnelQualitySalesItemModelFromJson(
    Map<String, dynamic> json) {
  return FunnelQualitySalesItemModel()
    ..oaId = json['oaId']
    ..crmUserName = json['crmUserName']
    ..deptCode = json['deptCode']
    ..crmUserDepartment = json['crmUserDepartment']
    ..deptAndUserName = json['deptAndUserName']
    ..customerNum = json['customerNum']
    ..currQualityRatio = json['currQualityRatio']
    ..haveChild = json['haveChild']
    ..isBd = json['isBd']
    ..isPostDept = json['isPostDept'];
}

Map<String, dynamic> _$FunnelQualitySalesItemModelToJson(
        FunnelQualitySalesItemModel instance) =>
    <String, dynamic>{
      'oaId': instance.oaId,
      'crmUserName': instance.crmUserName,
      'deptCode': instance.deptCode,
      'crmUserDepartment': instance.crmUserDepartment,
      'deptAndUserName': instance.deptAndUserName,
      'customerNum': instance.customerNum,
      'currQualityRatio': instance.currQualityRatio,
      'haveChild': instance.haveChild,
      'isBd': instance.isBd,
      'isPostDept': instance.isPostDept,
    };

FunnelQualityMerchantDataModel _$FunnelQualityMerchantDataModelFromJson(
    Map<String, dynamic> json) {
  return FunnelQualityMerchantDataModel()
    ..totalCustNum = json['totalCustNum']
    ..qualityRatio = json['qualityRatio']
    ..merchantList = (json['merchantList'] as List<dynamic>?)
        ?.map((e) =>
            FunnelQualityMerchantItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelQualityMerchantDataModelToJson(
        FunnelQualityMerchantDataModel instance) =>
    <String, dynamic>{
      'totalCustNum': instance.totalCustNum,
      'qualityRatio': instance.qualityRatio,
      'merchantList': instance.merchantList,
    };

FunnelQualityMerchantItemModel _$FunnelQualityMerchantItemModelFromJson(
    Map<String, dynamic> json) {
  return FunnelQualityMerchantItemModel()
    ..merchantId = json['merchantId']
    ..merchantName = json['merchantName']
    ..totalOrderAmount = json['totalOrderAmount']
    ..orderNum = json['orderNum']
    ..customerNum = json['customerNum']
    ..lastOrderTime = json['lastOrderTime'];
}

Map<String, dynamic> _$FunnelQualityMerchantItemModelToJson(
        FunnelQualityMerchantItemModel instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'totalOrderAmount': instance.totalOrderAmount,
      'orderNum': instance.orderNum,
      'customerNum': instance.customerNum,
      'lastOrderTime': instance.lastOrderTime,
    };
