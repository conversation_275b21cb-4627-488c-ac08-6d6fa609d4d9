import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/quality/data/funnel_quality_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/quality/widget/funnel_quality_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_column_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelQualityPage extends BasePage {
  final dynamic oaId;
  final dynamic deptCode;
  final dynamic totalCustNum;

  FunnelQualityPage({
    this.oaId,
    this.deptCode,
    this.totalCustNum,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelQualityPageState();
  }
}

class FunnelQualityPageState extends BaseState<FunnelQualityPage> {
  EasyRefreshController _controller = EasyRefreshController();

  List<FunnelQualitySalesItemModel> dataSource = [];

  dynamic qualityRatio;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          this.getTopWidget(),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelQualitySalesItemModel model = this.dataSource[index];
                  return Container(
                    child: FunnelQualityItem(
                      title: '${model.deptAndUserName ?? "--"}',
                      index: index,
                      isBottom: index == this.dataSource.length - 1,
                      contentHidden: "${model.isPostDept}" == "1",
                      customerNum: "${model.customerNum ?? 0}",
                      rate: "${model.currQualityRatio ?? 0}",
                      clickAction: () {
                        bool haveChild = "${model.haveChild}" == "1";
                        dynamic oaId = model.oaId;
                        dynamic totalNum = model.customerNum ?? 0;
                        dynamic deptCode = model.deptCode;
                        if (haveChild) {
                          XYYContainer.open(
                              '/funnel_quality_page?oaId=$oaId&&totalCustNum=$totalNum&deptCode=$deptCode');
                        } else {
                          dynamic isBd = model.isBd;
                          XYYContainer.open(
                              '/funnel_quality_merchant_page?oaId=$oaId&isBd=$isBd&totalCustNum=$totalNum');
                        }
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    double ratio = double.parse("${this.qualityRatio ?? 0}");
    return Container(
      padding: EdgeInsets.all(10),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: FunnelColumnItem(
              title: "质量客户数：",
              content: "${widget.totalCustNum ?? 0}",
              unit: "家",
            ),
          ),
          SizedBox(width: 10),
          Expanded(
            flex: 1,
            child: FunnelColumnItem(
              title: "质量客户/动销客户:",
              content: "${ratio.toStringAsFixed(2)}",
              unit: "%",
            ),
          ),
        ],
      ),
    );
  }

  void refreshData() async {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {};
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.deptCode != null && widget.deptCode != "null") {
      params["deptCode"] = widget.deptCode;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelQualitySalesDataModel>(
            FunnelQualitySalesDataModel())
        .requestDataV2(
      'customerFunnel/qualityCustomer',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelQualitySalesDataModel? data = result.getData();
        if (data != null) {
          this.qualityRatio = data.qualityRatio;
          this.dataSource = data.crmUserList ?? [];
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "质量客户";
  }
}
