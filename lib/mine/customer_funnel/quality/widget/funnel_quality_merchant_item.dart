import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_merchant_base_item.dart';
import 'package:flutter/material.dart';

class FunnelQualityMerchantItem extends FunnelMerchantBaseItem {
  FunnelQualityMerchantItem({
    required String title,
    required String orderAmount,
    required String orderNum,
    required String orderTime,
    bool isTop = false,
    bool isBottom = false,
    required VoidCallback clickAction,
  }) : super(
          title: title,
          content: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: FunnelContentSpan(
                        title: '下单金额：',
                        content: orderAmount + "元",
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: FunnelContentSpan(
                        title: '订单数量：',
                        content: orderNum + "单",
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 10),
                FunnelContentSpan(
                  title: '下单时间（最近）：',
                  content: orderTime,
                ),
              ],
            ),
          ),
          isFirst: isTop,
          isBottom: isBottom,
          clickAction: clickAction,
        );
}
