import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_sort_base_item.dart';
import 'package:flutter/material.dart';

class FunnelQualityItem extends FunnelSortBaseItem {
  FunnelQualityItem({
    required int index,
    bool isBottom = false,
    required bool contentHidden,
    required String title,
    required String customerNum,
    required String rate,
    VoidCallback? clickAction,
  }) : super(
            index: index,
            title: title,
            isBottom: isBottom,
            clickAction: clickAction,
            contentHidden: contentHidden,
            content: Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "质量客户数：$customerNum家",
                    style: TextStyle(
                      color: Color(0xFF00B377),
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Sized<PERSON><PERSON>(height: 7),
                  FunnelContentSpan(
                    title: "质量客户/动销客户占比：",
                    content: rate + "%",
                  ),
                ],
              ),
            ));
}
