// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_funnel_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerFunnelListData _$CustomerFunnelListDataFromJson(
    Map<String, dynamic> json) {
  return CustomerFunnelListData()
    ..haveChild = json['haveChild']
    ..totalCustomer = json['totalCustomer'] == null
        ? null
        : CustomerFunnelItemData.fromJson(
            json['totalCustomer'] as Map<String, dynamic>)
    ..last3MonthsCustomer = json['last3MonthsCustomer'] == null
        ? null
        : CustomerFunnelItemData.fromJson(
            json['last3MonthsCustomer'] as Map<String, dynamic>)
    ..currentMonthsCustomer = json['currentMonthsCustomer'] == null
        ? null
        : CustomerFunnelItemData.fromJson(
            json['currentMonthsCustomer'] as Map<String, dynamic>)
    ..qualityCustomer = json['qualityCustomer'] == null
        ? null
        : CustomerFunnelItemData.fromJson(
            json['qualityCustomer'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CustomerFunnelListDataToJson(
        CustomerFunnelListData instance) =>
    <String, dynamic>{
      'haveChild': instance.haveChild,
      'totalCustomer': instance.totalCustomer,
      'last3MonthsCustomer': instance.last3MonthsCustomer,
      'currentMonthsCustomer': instance.currentMonthsCustomer,
      'qualityCustomer': instance.qualityCustomer,
    };

CustomerFunnelItemData _$CustomerFunnelItemDataFromJson(
    Map<String, dynamic> json) {
  return CustomerFunnelItemData()
    ..totalCustNum = json['totalCustNum']
    ..addCustNum = json['addCustNum']
    ..totalOrderAmount = json['totalOrderAmount']
    ..totalOrderNum = json['totalOrderNum'];
}

Map<String, dynamic> _$CustomerFunnelItemDataToJson(
        CustomerFunnelItemData instance) =>
    <String, dynamic>{
      'totalCustNum': instance.totalCustNum,
      'addCustNum': instance.addCustNum,
      'totalOrderAmount': instance.totalOrderAmount,
      'totalOrderNum': instance.totalOrderNum,
    };

CustomerFunnelData _$CustomerFunnelDataFromJson(Map<String, dynamic> json) {
  return CustomerFunnelData()
    ..totalPoiNum = json['totalPoiNum']
    ..registeredNoBDNum = json['registeredNoBDNum']
    ..newPois = json['newPois']
    ..accumulateCustomers = json['accumulateCustomers']
    ..registerCustomers = json['registerCustomers']
    ..purchaseCustomers = json['purchaseCustomers']
    ..sLevelCustomers = json['sLevelCustomers']
    ..haveChild = json['haveChild'];
}

Map<String, dynamic> _$CustomerFunnelDataToJson(CustomerFunnelData instance) =>
    <String, dynamic>{
      'totalPoiNum': instance.totalPoiNum,
      'registeredNoBDNum': instance.registeredNoBDNum,
      'newPois': instance.newPois,
      'accumulateCustomers': instance.accumulateCustomers,
      'registerCustomers':instance.registerCustomers,
      'purchaseCustomers': instance.purchaseCustomers,
      'sLevelCustomers': instance.sLevelCustomers,
      'haveChild': instance.haveChild,
    };

CustomerFunnelIndexCumulativeData _$CustomerFunnelIndexCumulativeDataFromJson(
    Map<String, dynamic> json) {
  return CustomerFunnelIndexCumulativeData()
    ..accumulateCustomers = json['accumulateCustomers']
    ..sLevelVo = json['sLevelVo'] == null
        ? null
        : CustomerFunnelIndexCumulativeLevelData.fromJson(
            json['sLevelVo'] as Map<String, dynamic>)
    ..aLevelVo = json['aLevelVo'] == null
        ? null
        : CustomerFunnelIndexCumulativeLevelData.fromJson(
            json['aLevelVo'] as Map<String, dynamic>)
    ..bLevelVo = json['bLevelVo'] == null
        ? null
        : CustomerFunnelIndexCumulativeLevelData.fromJson(
            json['bLevelVo'] as Map<String, dynamic>)
    ..cLevelVo = json['cLevelVo'] == null
        ? null
        : CustomerFunnelIndexCumulativeLevelData.fromJson(
            json['cLevelVo'] as Map<String, dynamic>)
    ..dLevelVo = json['dLevelVo'] == null
        ? null
        : CustomerFunnelIndexCumulativeLevelData.fromJson(
            json['dLevelVo'] as Map<String, dynamic>)
    ..levelVo = json['levelVo'] == null
        ? null
        : CustomerFunnelIndexCumulativeLevelData.fromJson(
            json['levelVo'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CustomerFunnelIndexCumulativeDataToJson(
        CustomerFunnelIndexCumulativeData instance) =>
    <String, dynamic>{
      'accumulateCustomers': instance.accumulateCustomers,
      'sLevelVo': instance.sLevelVo,
      'aLevelVo': instance.aLevelVo,
      'bLevelVo': instance.bLevelVo,
      'cLevelVo': instance.cLevelVo,
      'dLevelVo': instance.dLevelVo,
      'levelVo': instance.levelVo,
    };

CustomerFunnelIndexCumulativeLevelData
    _$CustomerFunnelIndexCumulativeLevelDataFromJson(
        Map<String, dynamic> json) {
  return CustomerFunnelIndexCumulativeLevelData()
    ..levelCustomers = json['levelCustomers']
    ..levelProportion = json['levelProportion'];
}

Map<String, dynamic> _$CustomerFunnelIndexCumulativeLevelDataToJson(
        CustomerFunnelIndexCumulativeLevelData instance) =>
    <String, dynamic>{
      'levelCustomers': instance.levelCustomers,
      'levelProportion': instance.levelProportion,
    };

CustomerSLevelModelData _$CustomerSLevelModelDataFromJson(
    Map<String, dynamic> json) {
  return CustomerSLevelModelData()
    ..sLevelCustomers = json['sLevelCustomers']
    ..potentialCustomers = json['potentialCustomers']
    ..sLevelAA = json['sLevelAA']
    ..sLevelAB = json['sLevelAB']
    ..sLevelBA = json['sLevelBA']
    ..sLevelBB = json['sLevelBB'];
}

Map<String, dynamic> _$CustomerSLevelModelDataToJson(
        CustomerSLevelModelData instance) =>
    <String, dynamic>{
      'sLevelCustomers': instance.sLevelCustomers,
      'potentialCustomers': instance.potentialCustomers,
      'sLevelAA': instance.sLevelAA,
      'sLevelAB': instance.sLevelAB,
      'sLevelBA': instance.sLevelBA,
      'sLevelBB': instance.sLevelBB,
    };

CustomerFunnelIndexPurchaseData _$CustomerFunnelIndexPurchaseDataFromJson(
    Map<String, dynamic> json) {
  return CustomerFunnelIndexPurchaseData()
    ..nonMoveSale = json['nonMoveSale'] == null
        ? null
        : CustomerFunnelIndexPurchaseItemData.fromJson(
            json['nonMoveSale'] as Map<String, dynamic>)
    ..moveSale = json['moveSale'] == null
        ? null
        : CustomerFunnelIndexPurchaseItemData.fromJson(
            json['moveSale'] as Map<String, dynamic>);
}

Map<String, dynamic> _$CustomerFunnelIndexPurchaseDataToJson(
        CustomerFunnelIndexPurchaseData instance) =>
    <String, dynamic>{
      'nonMoveSale': instance.nonMoveSale,
      'moveSale': instance.moveSale,
    };

CustomerFunnelIndexPurchaseItemData
    _$CustomerFunnelIndexPurchaseItemDataFromJson(Map<String, dynamic> json) {
  return CustomerFunnelIndexPurchaseItemData()
    ..purchaseCustomers = json['purchaseCustomers']
    ..sLevelCustomers = json['sLevelCustomers']
    ..aLevelCustomers = json['aLevelCustomers']
    ..bLevelCustomers = json['bLevelCustomers']
    ..cLevelCustomers = json['cLevelCustomers']
    ..dLevelCustomers = json['dLevelCustomers']
    ..levelCustomers = json['levelCustomers'];
}

Map<String, dynamic> _$CustomerFunnelIndexPurchaseItemDataToJson(
        CustomerFunnelIndexPurchaseItemData instance) =>
    <String, dynamic>{
      'purchaseCustomers': instance.purchaseCustomers,
      'sLevelCustomers': instance.sLevelCustomers,
      'aLevelCustomers': instance.aLevelCustomers,
      'bLevelCustomers': instance.bLevelCustomers,
      'cLevelCustomers': instance.cLevelCustomers,
      'dLevelCustomers': instance.dLevelCustomers,
      'levelCustomers': instance.levelCustomers,
    };
