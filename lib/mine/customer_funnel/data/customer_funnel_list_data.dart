
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_funnel_list_data.g.dart';

@JsonSerializable()
class CustomerFunnelListData extends BaseModelV2 {
  /// 是否有子节点
  dynamic? haveChild;

  /// 合作客户
  CustomerFunnelItemData? totalCustomer;

  /// 近3月动销客户
  CustomerFunnelItemData? last3MonthsCustomer;

  /// 本月动销客户
  CustomerFunnelItemData? currentMonthsCustomer;

  /// 质量客户
  CustomerFunnelItemData? qualityCustomer;

  CustomerFunnelListData();

  factory CustomerFunnelListData.fromJson(Map<String, dynamic> json) {
    return _$CustomerFunnelListDataFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerFunnelListDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerFunnelListDataToJson(this);
  }
}

/// 合作客户
@JsonSerializable()
class CustomerFunnelItemData {
  /// 累计客户数 / 客户数量
  dynamic totalCustNum;

  /// 本月新增客户数
  dynamic addCustNum;

  /// 订单金额
  dynamic totalOrderAmount;

  /// 订单数量
  dynamic totalOrderNum;

  CustomerFunnelItemData();

  factory CustomerFunnelItemData.fromJson(Map<String, dynamic> json) {
    return _$CustomerFunnelItemDataFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$CustomerFunnelItemDataToJson(this);
  }
}

@JsonSerializable()
class CustomerFunnelData extends BaseModelV2<CustomerFunnelData> {
  /// 客户大盘
  dynamic totalPoiNum;

  /// 已注册无人认领
  dynamic registeredNoBDNum;

  /// 新开业门店数
  dynamic newPois;

  /// 累计客户数
  dynamic accumulateCustomers;

  /// 累计客户数
  dynamic registerCustomers;

  /// 动销客户数
  dynamic purchaseCustomers;

  /// S级客户数
  dynamic sLevelCustomers;

  /// 是否有子节点 0-无；1-有
  dynamic haveChild;

  CustomerFunnelData();

  @override
  CustomerFunnelData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerFunnelDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerFunnelDataToJson(this);
  }
}

@JsonSerializable()
class CustomerFunnelIndexCumulativeData
    extends BaseModelV2<CustomerFunnelIndexCumulativeData> {
  /// 	累计客户数
  dynamic accumulateCustomers;
  CustomerFunnelIndexCumulativeLevelData? sLevelVo;
  CustomerFunnelIndexCumulativeLevelData? aLevelVo;
  CustomerFunnelIndexCumulativeLevelData? bLevelVo;
  CustomerFunnelIndexCumulativeLevelData? cLevelVo;
  CustomerFunnelIndexCumulativeLevelData? dLevelVo;
  CustomerFunnelIndexCumulativeLevelData? levelVo;

  CustomerFunnelIndexCumulativeData();

  @override
  CustomerFunnelIndexCumulativeData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerFunnelIndexCumulativeDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerFunnelIndexCumulativeDataToJson(this);
  }
}

@JsonSerializable()
class CustomerFunnelIndexCumulativeLevelData
    extends BaseModelV2<CustomerFunnelIndexCumulativeLevelData> {
  /// 客户等级数
  dynamic levelCustomers;

  /// 客户占比
  dynamic levelProportion;

  CustomerFunnelIndexCumulativeLevelData();

  factory CustomerFunnelIndexCumulativeLevelData.fromJson(
      Map<String, dynamic> json) {
    return _$CustomerFunnelIndexCumulativeLevelDataFromJson(json);
  }
  @override
  CustomerFunnelIndexCumulativeLevelData fromJsonMap(
      Map<String, dynamic> json) {
    return _$CustomerFunnelIndexCumulativeLevelDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerFunnelIndexCumulativeLevelDataToJson(this);
  }
}

@JsonSerializable()
class CustomerSLevelModelData extends BaseModelV2<CustomerSLevelModelData> {
  /// S级客户数
  dynamic sLevelCustomers;

  /// 本月潜力客户数
  dynamic potentialCustomers;

  /// s++
  dynamic sLevelAA;

  /// s+-
  dynamic sLevelAB;

  /// s-+
  dynamic sLevelBA;

  /// s--
  dynamic sLevelBB;

  @override
  CustomerSLevelModelData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerSLevelModelDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerSLevelModelDataToJson(this);
  }
}

@JsonSerializable()
class CustomerFunnelIndexPurchaseData
    extends BaseModelV2<CustomerFunnelIndexPurchaseData> {
  /// 未动销
  CustomerFunnelIndexPurchaseItemData? nonMoveSale;

  /// 动销
  CustomerFunnelIndexPurchaseItemData? moveSale;

  CustomerFunnelIndexPurchaseData();

  @override
  CustomerFunnelIndexPurchaseData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerFunnelIndexPurchaseDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerFunnelIndexPurchaseDataToJson(this);
  }
}

@JsonSerializable()
class CustomerFunnelIndexPurchaseItemData
    extends BaseModelV2<CustomerFunnelIndexPurchaseItemData> {
  /// 客户总数
  dynamic purchaseCustomers;

  /// S级别客户数
  dynamic sLevelCustomers;

  /// A级别客户数
  dynamic aLevelCustomers;

  /// B级别客户数
  dynamic bLevelCustomers;

  /// C级别客户数
  dynamic cLevelCustomers;

  /// D级别客户数
  dynamic dLevelCustomers;

  /// 新增
  dynamic levelCustomers;

  CustomerFunnelIndexPurchaseItemData();

  factory CustomerFunnelIndexPurchaseItemData.fromJson(
      Map<String, dynamic> json) {
    return _$CustomerFunnelIndexPurchaseItemDataFromJson(json);
  }

  @override
  CustomerFunnelIndexPurchaseItemData fromJsonMap(Map<String, dynamic> json) {
    return _$CustomerFunnelIndexPurchaseItemDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerFunnelIndexPurchaseItemDataToJson(this);
  }
}
