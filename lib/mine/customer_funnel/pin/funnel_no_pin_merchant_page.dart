import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/base/date_range_picker/date_range_picker.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_level_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_pin_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/widget/funnel_merchant_level_filter.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/widget/funnel_no_pin_merchant_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelNoPinMerchantPage extends BasePage {
  final dynamic oaId;
  final dynamic isBd;
  final dynamic nonMoveSaleTotalCustNum;

  FunnelNoPinMerchantPage({
    this.oaId,
    this.isBd,
    this.nonMoveSaleTotalCustNum,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelNoPinMerchantPageState();
  }
}

class FunnelNoPinMerchantPageState extends BaseState<FunnelNoPinMerchantPage> {
  EasyRefreshController _controller = EasyRefreshController();

  int page = 0;

  List<FunnelPinMerchantItemModel> dataSource = [];

  dynamic nonMovesaleRatio;
  dynamic moveSaleCount;

  List<FunnelLevelItemModel>? levelList;

  FunnelLevelItemModel? selectLevel;

  DateTime? startDate;
  DateTime? endDate;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          this.getTopWidget(),
          this.getFilterWidget(),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              onLoad: () async {
                this.loadMoreData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelPinMerchantItemModel model = this.dataSource[index];
                  return Container(
                    child: FunnelNoPinMerchantItem(
                      level: model.levelDesc?.toString() ?? "--",
                      title: '${model.merchantName ?? "--"}',
                      isTop: index == 0,
                      isBottom: index == this.dataSource.length - 1,
                      orderTime: "${model.lastOrderTime ?? "--"}",
                      orderNum: "${model.last3monthOrderNum ?? 0}",
                      orderAmount:
                          FunnelUtil.amountFlex(model.last3monthOrderAmount),
                      clickAction: () {
                        FunnelUtil.jumpMerchantInfo(model.merchantId);
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    double radio = double.parse("${this.nonMovesaleRatio ?? 0}");
    return Container(
      padding: EdgeInsets.only(bottom: 10, top: 10),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: FunnelRowItem(
              title: "未动销客户数：",
              content: "${moveSaleCount ?? 0}",
              unit: "家",
            ),
          ),
        ],
      ),
    );
  }

  void refreshData() {
    this.page = 0;
    this.requestPageData();
  }

  void loadMoreData() {
    this.requestPageData();
  }

  void requestPageData() {
    if (selectLevel != null) {
      requestListData();
    } else {
      requestLevelData();
    }
  }

  void requestLevelData() {
    showLoadingDialog();
    NetworkV2<FunnelLevelItemModel>(FunnelLevelItemModel())
        .requestDataV2("funnerV2/getLevelEnum")
        .then((value) {
      dismissLoadingDialog();
      if (mounted && value.isSuccess == true) {
        levelList = value.getListData();
        if (levelList?.isNotEmpty == true) {
          selectLevel = levelList!.first;
          this.page = 0;
          requestPageData();
          setState(() {});
        }
      }
    });
  }

  static String _fourDigits(int n) {
    int absN = n.abs();
    String sign = n < 0 ? "-" : "";
    if (absN >= 1000) return "$n";
    if (absN >= 100) return "${sign}0$absN";
    if (absN >= 10) return "${sign}00$absN";
    return "${sign}000$absN";
  }

  static String _twoDigits(int n) {
    if (n >= 10) return "$n";
    return "0$n";
  }

  String formatDate(DateTime date) {
    return "${_fourDigits(date.year)}-${_twoDigits(date.month)}-${_twoDigits(date.day)}";
  }

  void requestListData() async {
    Map<String, dynamic> params = {
      "pageNum": this.page,
      "pageSize": "10",
    };
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.isBd != null && widget.isBd != "null") {
      params["isBd"] = int.tryParse(widget.isBd);
    }

    String startDateStr = "";
    String endDateStr = "";
    if (startDate == null || endDate == null) {
      var end = DateTime.now();
      var start = DateTime(end.year, end.month, 1);
      startDateStr = formatDate(start);
      endDateStr = formatDate(end);
    } else {
      startDateStr = formatDate(startDate!);
      endDateStr = formatDate(endDate!);
    }

    params["startDate"] = startDateStr;
    params["endDate"] = endDateStr;

    if (selectLevel != null) {
      params["level"] = selectLevel!.code;
    }

    params["movesaleType"] = 0;

    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelPinMerchantData>(FunnelPinMerchantData())
        .requestDataV2(
      'funnerV2/purchaseCustomerList',
      parameters: params,
      contentType: RequestContentType.FORM,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    this._controller.finishLoad();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelPinMerchantData? data = result.getData();
        if (data != null) {
          this.nonMovesaleRatio = data.proportion;
          this.moveSaleCount = data.count;
          List<FunnelPinMerchantItemModel> source = data.merchantList ?? [];
          if (this.page == 0) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          if (source.length != 10) {
            this._controller.finishLoad(noMore: true);
          }
          this.page += 1;
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    if (startDate == null || endDate == null) {
      return "本月未动销客户";
    } else {
      return "${_twoDigits(startDate!.month)}.${_twoDigits(startDate!.day)}"
          "-${_twoDigits(endDate!.month)}.${_twoDigits(endDate!.day)}未动销客户";
    }
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        Visibility(
          visible: widget.isBd?.toString() != "3",
          child: GestureDetector(
            onTap: () {
              showDateFilter();
            },
            behavior: HitTestBehavior.opaque,
            child: Container(
              margin: EdgeInsets.only(top: 11, bottom: 11, right: 15, left: 15),
              alignment: Alignment.center,
              child: Image.asset(
                "assets/images/funnel/funnel_time_filter.png",
                width: 22,
                height: 22,
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget getFilterWidget() {
    return FunnelMerchantLevelFilterWidget((key) {
      selectLevel = key;
      refreshData();
    }, levelList ?? List.empty(), selectLevel ?? levelList?.first);
  }

  void showDateFilter() {
    DateRangePicker.showDateRangePicker(
            context, DateTime.now().add(Duration(days: -31)), DateTime.now())
        .then((value) {
      if (value != null) {
        startDate = value.start;
        endDate = value.end;
        refreshData();
      }
    });
  }
}
