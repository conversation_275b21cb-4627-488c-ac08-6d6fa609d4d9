import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_pin_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/widget/funnel_pin_nearly_sort_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelPinNearlyPage extends BasePage {
  /// 动销客户数
  final dynamic moveSaleTotalCustNum;

  final dynamic oaId;

  final dynamic deptCode;

  FunnelPinNearlyPage({
    this.oaId,
    this.deptCode,
    this.moveSaleTotalCustNum,
  });

  @override
  BaseState<StatefulWidget> initState() {
    return FUnnelPinNearlyPageState();
  }
}

class FUnnelPinNearlyPageState extends BaseState<FunnelPinNearlyPage> {
  EasyRefreshController _controller = EasyRefreshController();

  List<FunnelPinSalesItemModel> dataSource = [];

  /// 未动销客户数
  dynamic nonMoveSaleTotalCustNum;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        children: [
          this.getTopWidget(),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelPinSalesItemModel model = this.dataSource[index];
                  return Container(
                    child: FunnelPinNearSortItem(
                      title: "${model.deptAndUserName ?? "--"}",
                      index: index,
                      pinNum: "${model.totalCustNum ?? 0}",
                      contentHidden: "${model.isPostDept}" == "1",
                      clickAction: () {
                        bool haveChild = "${model.haveChild}" == "1";
                        dynamic oaId = model.oaId;
                        dynamic totalNum = model.totalCustNum ?? 0;
                        dynamic deptCode = model.deptCode;
                        if (haveChild) {
                          XYYContainer.open(
                              '/funnel_pin_nearly_page?oaId=$oaId&moveSaleTotalCustNum=$totalNum&deptCode=$deptCode');
                        } else {
                          dynamic isBd = model.isBd;
                          XYYContainer.open(
                              '/funnel_pin_nearly_merchant_page?oaId=$oaId&moveSaleTotalCustNum=$totalNum&isBd=$isBd');
                        }
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    return Container(
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          FunnelRowItem(
            title: "近3月内动销客户数：",
            content: "${widget.moveSaleTotalCustNum ?? 0}",
            unit: "家",
          ),
          SizedBox(height: 10),
          FunnelRowItem(
            title: "近3月未动销客户数：",
            content: "${this.nonMoveSaleTotalCustNum ?? 0}",
            unit: "家",
            clickAction: () {
              dynamic noMoveSaleTotalCustNum = this.nonMoveSaleTotalCustNum;
              XYYContainer.open(
                  '/funnel_no_pin_nearly_page?noMoveSaleTotalCustNum=$noMoveSaleTotalCustNum&oaId=${widget.oaId}&deptCode=${widget.deptCode}');
            },
          ),
        ],
      ),
    );
  }

  void refreshData() async {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {"movesaleType": "1"};
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.deptCode != null && widget.deptCode != "null") {
      params["deptCode"] = widget.deptCode;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result =
        await NetworkV2<FunnelPinSalesData>(FunnelPinSalesData()).requestDataV2(
      'customerFunnel/movesaleCustomerLast3month',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelPinSalesData? data = result.getData();
        if (data != null) {
          this.nonMoveSaleTotalCustNum = data.nonMoveSaleTotalCustNum;
          this.dataSource = data.crmUserList ?? [];
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "近3月动销客户";
  }
}
