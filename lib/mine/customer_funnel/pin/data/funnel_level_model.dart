import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'funnel_level_model.g.dart';

@JsonSerializable()
class FunnelLevelModel extends BaseModelV2<FunnelLevelModel> {

  List<FunnelLevelItemModel>? data;

  FunnelLevelModel();

  factory FunnelLevelModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelLevelModelFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelLevelModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelLevelModelToJson(this);
  }
}

@JsonSerializable()
class FunnelLevelItemModel extends BaseModelV2<FunnelLevelItemModel>{

  dynamic? code;
  String? desc;

  FunnelLevelItemModel();

  factory FunnelLevelItemModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelLevelItemModelFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelLevelItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelLevelItemModelToJson(this);
  }
}
