import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'funnel_pin_model.g.dart';

@JsonSerializable()
class FunnelPinSalesData extends BaseModelV2 {
  /// 动销客户数
  dynamic moveSaleTotalCustNum;

  /// 未动销客户数
  dynamic nonMoveSaleTotalCustNum;

  /// 动销率
  dynamic movesaleRatio;

  /// 动销类型：1动销, 0未动销
  dynamic movesaleType;

  /// 部门列表
  List<FunnelPinSalesItemModel>? crmUserList;

  FunnelPinSalesData();

  factory FunnelPinSalesData.fromJson(Map<String, dynamic> json) {
    return _$FunnelPinSalesDataFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelPinSalesDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelPinSalesDataToJson(this);
  }
}

@JsonSerializable()
class FunnelPinSalesItemModel {
  /// 员工编码
  dynamic oaId;

  /// 员工名称
  dynamic crmUserName;

  /// 部门编码
  dynamic deptCode;

  /// 部门名称
  dynamic crmUserDepartment;

  /// 列表展示名称
  dynamic deptAndUserName;

  /// 累计客户数
  dynamic totalCustNum;

  /// 本月新增客户数
  dynamic addCustNum;

  /// 动销率
  dynamic movesaleRatio;

  /// 订单金额
  dynamic totalOrderAmount;

  /// 订单数量
  dynamic orderNum;

  /// 是否有子节点：1有，0无
  dynamic haveChild;

  /// 1=bd，2管理层
  dynamic isBd;

  /// 是部门还是岗位 1：岗位 / 0：部门
  dynamic isPostDept;

  FunnelPinSalesItemModel();

  factory FunnelPinSalesItemModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelPinSalesItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$FunnelPinSalesItemModelToJson(this);
  }
}

@JsonSerializable()
class FunnelPinMerchantData extends BaseModelV2 {
  /// 近3月动销客户数
  dynamic moveSaleTotalCustNum;

  /// 近3月未动销客户数
  dynamic nonMoveSaleTotalCustNum;

  /// 本月未动销率
  dynamic nonMovesaleRatio;

  /// 本月动销率
  dynamic movesaleRatio;

  /// 员工编码
  dynamic oaId;

  /// 员工姓名
  dynamic crmUserName;

  /// 新动销客户数
  dynamic count;

  /// 新动销率
  dynamic proportion;

  /// 客户列表
  List<FunnelPinMerchantItemModel>? merchantList;

  FunnelPinMerchantData();

  factory FunnelPinMerchantData.fromJson(Map<String, dynamic> json) {
    return _$FunnelPinMerchantDataFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelPinMerchantDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelPinMerchantDataToJson(this);
  }
}

@JsonSerializable()
class FunnelPinMerchantItemModel {
  /// 药店编码
  dynamic merchantId;

  /// 药店名称
  dynamic merchantName;

  /// 订单金额
  dynamic totalOrderAmount;

  /// 订单数量
  dynamic orderNum;

  /// 最近下单时间
  dynamic lastOrderTime;

  /// 最近一单订单金额
  dynamic lastOrderAmount;

  /// 最近3月订单金额
  dynamic last3monthOrderAmount;

  /// 最近3月订单数量
  dynamic last3monthOrderNum;

  /// 未动销率
  dynamic nonMovesaleRatio;

  /// 是否有子节点：1有，0无
  dynamic haveChild;

  /// 客户等级
  dynamic levelDesc;

  FunnelPinMerchantItemModel();

  factory FunnelPinMerchantItemModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelPinMerchantItemModelFromJson(json);
  }

  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelPinMerchantItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$FunnelPinMerchantItemModelToJson(this);
  }
}
