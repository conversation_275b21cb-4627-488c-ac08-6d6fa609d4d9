// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'funnel_pin_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunnelPinSalesData _$FunnelPinSalesDataFromJson(Map<String, dynamic> json) {
  return FunnelPinSalesData()
    ..moveSaleTotalCustNum = json['moveSaleTotalCustNum']
    ..nonMoveSaleTotalCustNum = json['nonMoveSaleTotalCustNum']
    ..movesaleRatio = json['movesaleRatio']
    ..movesaleType = json['movesaleType']
    ..crmUserList = (json['crmUserList'] as List<dynamic>?)
        ?.map(
            (e) => FunnelPinSalesItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelPinSalesDataToJson(FunnelPinSalesData instance) =>
    <String, dynamic>{
      'moveSaleTotalCustNum': instance.moveSaleTotalCustNum,
      'nonMoveSaleTotalCustNum': instance.nonMoveSaleTotalCustNum,
      'movesaleRatio': instance.movesaleRatio,
      'movesaleType': instance.movesaleType,
      'crmUserList': instance.crmUserList,
    };

FunnelPinSalesItemModel _$FunnelPinSalesItemModelFromJson(
    Map<String, dynamic> json) {
  return FunnelPinSalesItemModel()
    ..oaId = json['oaId']
    ..crmUserName = json['crmUserName']
    ..deptCode = json['deptCode']
    ..crmUserDepartment = json['crmUserDepartment']
    ..deptAndUserName = json['deptAndUserName']
    ..totalCustNum = json['totalCustNum']
    ..addCustNum = json['addCustNum']
    ..movesaleRatio = json['movesaleRatio']
    ..totalOrderAmount = json['totalOrderAmount']
    ..orderNum = json['orderNum']
    ..haveChild = json['haveChild']
    ..isBd = json['isBd']
    ..isPostDept = json['isPostDept'];
}

Map<String, dynamic> _$FunnelPinSalesItemModelToJson(
        FunnelPinSalesItemModel instance) =>
    <String, dynamic>{
      'oaId': instance.oaId,
      'crmUserName': instance.crmUserName,
      'deptCode': instance.deptCode,
      'crmUserDepartment': instance.crmUserDepartment,
      'deptAndUserName': instance.deptAndUserName,
      'totalCustNum': instance.totalCustNum,
      'addCustNum': instance.addCustNum,
      'movesaleRatio': instance.movesaleRatio,
      'totalOrderAmount': instance.totalOrderAmount,
      'orderNum': instance.orderNum,
      'haveChild': instance.haveChild,
      'isBd': instance.isBd,
      'isPostDept': instance.isPostDept,
    };

FunnelPinMerchantData _$FunnelPinMerchantDataFromJson(
    Map<String, dynamic> json) {
  return FunnelPinMerchantData()
    ..moveSaleTotalCustNum = json['moveSaleTotalCustNum']
    ..nonMoveSaleTotalCustNum = json['nonMoveSaleTotalCustNum']
    ..nonMovesaleRatio = json['nonMovesaleRatio']
    ..movesaleRatio = json['movesaleRatio']
    ..oaId = json['oaId']
    ..crmUserName = json['crmUserName']
    ..count = json['count']
    ..proportion = json['proportion']
    ..merchantList = (json['merchantList'] as List<dynamic>?)
        ?.map((e) =>
            FunnelPinMerchantItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelPinMerchantDataToJson(
        FunnelPinMerchantData instance) =>
    <String, dynamic>{
      'moveSaleTotalCustNum': instance.moveSaleTotalCustNum,
      'nonMoveSaleTotalCustNum': instance.nonMoveSaleTotalCustNum,
      'nonMovesaleRatio': instance.nonMovesaleRatio,
      'movesaleRatio': instance.movesaleRatio,
      'oaId': instance.oaId,
      'crmUserName': instance.crmUserName,
      'count': instance.count,
      'proportion': instance.proportion,
      'merchantList': instance.merchantList,
    };

FunnelPinMerchantItemModel _$FunnelPinMerchantItemModelFromJson(
    Map<String, dynamic> json) {
  return FunnelPinMerchantItemModel()
    ..merchantId = json['merchantId']
    ..merchantName = json['merchantName']
    ..totalOrderAmount = json['totalOrderAmount']
    ..orderNum = json['orderNum']
    ..lastOrderTime = json['lastOrderTime']
    ..lastOrderAmount = json['lastOrderAmount']
    ..last3monthOrderAmount = json['last3monthOrderAmount']
    ..last3monthOrderNum = json['last3monthOrderNum']
    ..nonMovesaleRatio = json['nonMovesaleRatio']
    ..haveChild = json['haveChild']
    ..levelDesc = json['levelDesc'];
}

Map<String, dynamic> _$FunnelPinMerchantItemModelToJson(
        FunnelPinMerchantItemModel instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'totalOrderAmount': instance.totalOrderAmount,
      'orderNum': instance.orderNum,
      'lastOrderTime': instance.lastOrderTime,
      'lastOrderAmount': instance.lastOrderAmount,
      'last3monthOrderAmount': instance.last3monthOrderAmount,
      'last3monthOrderNum': instance.last3monthOrderNum,
      'nonMovesaleRatio': instance.nonMovesaleRatio,
      'haveChild': instance.haveChild,
      'levelDesc': instance.levelDesc,
    };
