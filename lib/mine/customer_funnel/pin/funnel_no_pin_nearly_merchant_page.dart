import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_pin_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/widget/funnel_no_pin_nearly_merchant_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelNoPinNearlyMerchantPage extends BasePage {
  final dynamic oaId;
  final dynamic isBd;
  final dynamic noMoveSaleTotalCustNum;
  FunnelNoPinNearlyMerchantPage({
    this.oaId,
    this.isBd,
    this.noMoveSaleTotalCustNum,
  });
  @override
  BaseState<StatefulWidget> initState() {
    return FunnelNoPinNearlyMerchantPageState();
  }
}

class FunnelNoPinNearlyMerchantPageState
    extends BaseState<FunnelNoPinNearlyMerchantPage> {
  EasyRefreshController _controller = EasyRefreshController();

  int page = 0;

  List<FunnelPinMerchantItemModel> dataSource = [];

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 10, top: 10),
            child: FunnelRowItem(
              title: "近3月未动销客户数：",
              content: "${widget.noMoveSaleTotalCustNum ?? 0}",
              unit: "家",
            ),
          ),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              onLoad: () async {
                this.loadMoreData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelPinMerchantItemModel model = this.dataSource[index];
                  return FunnelNoPinNearlyMerchantItem(
                    title: '${model.merchantName ?? "--"}',
                    orderAmount: FunnelUtil.amountFlex(model.lastOrderAmount),
                    orderTime: '${model.lastOrderTime}',
                    isTop: index == 0,
                    isBottom: index == this.dataSource.length,
                    clickAction: () {
                      FunnelUtil.jumpMerchantInfo(model.merchantId);
                    },
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  void refreshData() {
    this.page = 0;
    this.requestListData();
  }

  void loadMoreData() {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {
      "pageNum": this.page,
      "pageSize": "10",
    };
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.isBd != null && widget.isBd != "null") {
      params["isBd"] = widget.isBd;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelPinMerchantData>(FunnelPinMerchantData())
        .requestDataV2(
      'customerFunnel/nonMovesaleCustomerListLast3month',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    this._controller.finishLoad();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelPinMerchantData? data = result.getData();
        if (data != null) {
          List<FunnelPinMerchantItemModel> source = data.merchantList ?? [];
          if (this.page == 0) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          if (source.length != 10) {
            this._controller.finishLoad(noMore: true);
          }
          this.page += 1;
          setState(() {});
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "近3月未动销客户";
  }
}
