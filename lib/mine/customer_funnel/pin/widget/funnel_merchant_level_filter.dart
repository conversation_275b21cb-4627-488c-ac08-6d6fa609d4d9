import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_level_model.dart';
import 'package:flutter/material.dart';

class FunnelMerchantLevelFilterWidget extends StatefulWidget {
  final FilterCallback valueChanged;

  final List<FunnelLevelItemModel> itemKeys;

  final FunnelLevelItemModel? defaultSelectKey;

  FunnelMerchantLevelFilterWidget(
      this.valueChanged, this.itemKeys, this.defaultSelectKey);

  @override
  State<StatefulWidget> createState() {
    return FunnelMerchantLevelFilterState();
  }
}

class FunnelMerchantLevelFilterState
    extends State<FunnelMerchantLevelFilterWidget> {
  FunnelLevelItemModel? currentSelected;

  @override
  void initState() {
    super.initState();
    currentSelected = widget.defaultSelectKey;

  }

  @override
  Widget build(BuildContext context) {
    if (currentSelected == null && widget.itemKeys.length > 0) {
      currentSelected = widget.itemKeys.first;
    }
    return Container(
      height: 48,
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(4), topRight: Radius.circular(4)),
      ),
      padding: EdgeInsets.only(top: 10, bottom: 8),
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        itemCount: widget.itemKeys.length,
        padding: EdgeInsets.only(left: 15, right: 15),
        itemBuilder: (context, index) {
          return buildFilterItem(index);
        },
        separatorBuilder: (context, index) {
          return SizedBox(
            width: 10,
          );
        },
      ),
    );
  }

  Widget buildFilterItem(int index) {
    print("guan12 select$currentSelected,item:${widget.itemKeys[index]}");
    bool isSelected = widget.itemKeys[index] == currentSelected;
    return GestureDetector(
      onTap: () {
        if (currentSelected != null) {
          currentSelected = widget.itemKeys[index];
          setState(() {});
          widget.valueChanged(currentSelected!);
        }
      },
      child: Container(
        height: 30,
        padding: EdgeInsets.symmetric(horizontal: 16.5),
        decoration: BoxDecoration(
            color:
                isSelected ? const Color(0x1a00b377) : const Color(0xfff7f7f8),
            borderRadius: BorderRadius.circular(2)),
        alignment: Alignment.center,
        child: Text(
          widget.itemKeys[index].desc ?? "",
          style: TextStyle(
              color: isSelected
                  ? const Color(0xff00b377)
                  : const Color(0xff676773),
              fontSize: 12,
              fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400),
        ),
      ),
    );
  }
}

typedef FilterCallback = void Function(FunnelLevelItemModel key);
