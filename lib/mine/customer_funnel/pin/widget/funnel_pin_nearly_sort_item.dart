import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_sort_base_item.dart';
import 'package:flutter/material.dart';

class FunnelPinNearSortItem extends FunnelSortBaseItem {
  FunnelPinNearSortItem({
    required int index,
    bool isBottom = false,
    required bool contentHidden,
    required String title,
    required String pinNum,
    VoidCallback? clickAction,
  }) : super(
          index: index,
          title: title,
          isBottom: isBottom,
          clickAction: clickAction,
          contentHidden: contentHidden,
          content: Container(
            child: Text(
              "动销客户数：$pinNum家",
              style: TextStyle(
                color: Color(0xFF00B377),
                fontSize: 13,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
}
