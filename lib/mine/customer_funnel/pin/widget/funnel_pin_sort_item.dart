import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_sort_base_item.dart';
import 'package:flutter/material.dart';

class FunnelPinSortItem extends FunnelSortBaseItem {
  FunnelPinSortItem({
    required int index,
    bool isBottom = false,
    required bool contentHidden,
    required String title,
    required String pinNum,
    required String pinRate,
    required String orderNum,
    required String orderAmount,
    VoidCallback? clickAction,
  }) : super(
          index: index,
          title: title,
          isBottom: isBottom,
          clickAction: clickAction,
          contentHidden: contentHidden,
          content: Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      flex: 3,
                      child: Text(
                        "动销客户数：$pinNum家",
                        style: TextStyle(
                          color: Color(0xFF00B377),
                          fontSize: 13,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: FunnelContentSpan(
                        title: '动销率：',
                        content: pinRate + "%",
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                FunnelContentSpan(
                  title: '订单数量：',
                  content: orderNum + "单",
                ),
                SizedBox(height: 5),
                FunnelContentSpan(
                  title: '订单金额：',
                  content: orderAmount + "元",
                ),
              ],
            ),
          ),
        );
}
