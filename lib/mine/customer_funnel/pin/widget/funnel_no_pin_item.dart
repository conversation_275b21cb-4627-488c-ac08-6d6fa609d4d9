import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_base_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:flutter/material.dart';

class FunnelNoPinItem extends FunnelBaseItem {
  FunnelNoPinItem({
    required String title,
    required bool contentHidden,
    required String noPinNum,
    required String noPinRate,
    required VoidCallback clickAction,
    bool isTop = false,
    bool isBottom = false,
  }) : super(
          title: title,
          content: Container(
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: FunnelContentSpan(
                    title: '未动销客户数：',
                    content: noPinNum + '家',
                  ),
                )
              ],
            ),
          ),
          isTop: isTop,
          isBottom: isBottom,
          contentHidden: contentHidden,
          clickAction: clickAction,
        );
}
