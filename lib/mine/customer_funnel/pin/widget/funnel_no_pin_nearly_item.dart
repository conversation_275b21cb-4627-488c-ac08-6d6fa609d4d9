import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_base_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:flutter/material.dart';

class FunnelNoPinNearlyItem extends FunnelBaseItem {
  FunnelNoPinNearlyItem({
    required String title,
    required bool contentHidden,
    required String noPinNum,
    required VoidCallback clickAction,
    bool isTop = false,
    bool isBottom = false,
  }) : super(
          title: title,
          content: Container(
            child: FunnelContentSpan(
              title: '未动销客户数：',
              content: noPinNum,
            ),
          ),
          isTop: isTop,
          isBottom: isBottom,
          contentHidden: contentHidden,
          clickAction: clickAction,
        );
}
