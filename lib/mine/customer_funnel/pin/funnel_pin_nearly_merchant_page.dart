import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/data/funnel_pin_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/pin/widget/funnel_pin_nearly_merchant_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelPinNearlyMerchantPage extends BasePage {
  /// 动销客户数
  final dynamic moveSaleTotalCustNum;

  final dynamic oaId;

  final dynamic isBd;

  FunnelPinNearlyMerchantPage({
    this.moveSaleTotalCustNum,
    this.oaId,
    this.isBd,
  });
  @override
  BaseState<StatefulWidget> initState() {
    return FunnelPinNearlyMerchantPageState();
  }
}

class FunnelPinNearlyMerchantPageState
    extends BaseState<FunnelPinNearlyMerchantPage> {
  EasyRefreshController _controller = EasyRefreshController();

  int page = 0;

  List<FunnelPinMerchantItemModel> dataSource = [];

  dynamic crmUserName;

  /// 未动销客户数
  dynamic nonMoveSaleTotalCustNum;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          this.getTopWidget(),
          Expanded(
            child: EasyRefresh(
              controller: _controller,
              onRefresh: () async {
                this.refreshData();
              },
              onLoad: () async {
                this.loadMoreData();
              },
              child: ListView.builder(
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  FunnelPinMerchantItemModel model = this.dataSource[index];
                  return Container(
                    child: FunnelPinNearlyMerchantItem(
                      title: "${model.merchantName ?? "--"}",
                      orderAmount:
                          FunnelUtil.amountFlex(model.totalOrderAmount),
                      orderNum: "${model.orderNum ?? 0}",
                      orderTime: "${model.lastOrderTime ?? "--"}",
                      isTop: index == 0,
                      isBottom: index == this.dataSource.length - 1,
                      clickAction: () {
                        FunnelUtil.jumpMerchantInfo(model.merchantId);
                      },
                    ),
                  );
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          ),
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    return Container(
      margin: EdgeInsets.only(top: 10, bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible:
                (this.crmUserName != null && '${this.crmUserName}'.length > 0),
            child: Container(
              margin: EdgeInsets.only(bottom: 10),
              child: Text(
                '${this.crmUserName ?? "--"}',
                style: TextStyle(
                  color: Color(0xFF676773),
                  fontSize: 12,
                ),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: 10),
            child: FunnelRowItem(
              title: "近3月动销客户数：",
              content: "${widget.moveSaleTotalCustNum ?? 0}",
              unit: "家",
            ),
          ),
          Container(
            child: FunnelRowItem(
              title: "近3月动未销客户数：",
              content: "${this.nonMoveSaleTotalCustNum ?? 0}",
              unit: "家",
              clickAction: () {
                XYYContainer.open(
                    '/funnel_no_pin_nearly_merchant_page?oaId=${widget.oaId}&noMoveSaleTotalCustNum=${this.nonMoveSaleTotalCustNum ?? 0}&isBd=${widget.isBd}');
              },
            ),
          ),
        ],
      ),
    );
  }

  void refreshData() {
    this.page = 0;
    this.requestListData();
  }

  void loadMoreData() {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {
      "pageNum": this.page,
      "pageSize": "10",
    };
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.isBd != null && widget.isBd != 'null') {
      params["isBd"] = widget.isBd;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelPinMerchantData>(FunnelPinMerchantData())
        .requestDataV2(
      'customerFunnel/movesaleCustomerListLast3month',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    this._controller.finishLoad();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelPinMerchantData? data = result.getData();
        this.nonMoveSaleTotalCustNum = data?.nonMoveSaleTotalCustNum;
        this.crmUserName = data?.crmUserName;
        if (data != null) {
          List<FunnelPinMerchantItemModel> source = data.merchantList ?? [];
          if (this.page == 0) {
            this.dataSource = source;
          } else {
            this.dataSource.addAll(source);
          }
          if (source.length != 10) {
            this._controller.finishLoad(noMore: true);
          }
          this.page += 1;
        }
        setState(() {});
      }
    }
  }

  @override
  String getTitleName() {
    return '近3月动销客户';
  }
}
