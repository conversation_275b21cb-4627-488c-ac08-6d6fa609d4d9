import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'funnel_cumulative_model.g.dart';

@JsonSerializable()
class FunnelCumulativeSalesData extends BaseModelV2 {
  /// 累计客户
  dynamic totalCustNum;

  dynamic currMonthsAddCustNum;

  List<FunnelCumulativeSalesItemModel>? crmUserList;

  FunnelCumulativeSalesData();

  factory FunnelCumulativeSalesData.fromJson(Map<String, dynamic> json) {
    return _$FunnelCumulativeSalesDataFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelCumulativeSalesDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelCumulativeSalesDataToJson(this);
  }
}

@JsonSerializable()
class FunnelCumulativeSalesItemModel {
  /// 员工编码
  dynamic oaId;

  /// 员工名称
  dynamic crmUserName;

  /// 部门编码
  dynamic deptCode;

  /// 部门名称
  dynamic crmUserDepartment;

  /// 累计客户数
  dynamic totalCustNum;

  /// 列表展示名称
  dynamic deptAndUserName;

  /// 本月新增客户数
  dynamic addCustNum;

  /// 是否有子节点：1有，0无
  dynamic haveChild;

  /// 1=bd，2管理层
  dynamic isBd;

  /// 是部门还是岗位 1：岗位 / 0：部门
  dynamic isPostDept;

  FunnelCumulativeSalesItemModel();

  factory FunnelCumulativeSalesItemModel.fromJson(Map<String, dynamic> json) {
    return _$FunnelCumulativeSalesItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$FunnelCumulativeSalesItemModelToJson(this);
  }
}

@JsonSerializable()
class FunnelCumulativeMerchantData extends BaseModelV2 {
  /// 累计客户数
  dynamic totalCustNum;

  /// 本月新增客户数
  dynamic currMonthsAddCustNum;

  /// 客户列表
  List<FunnelCumulativeMerchantItemModel>? merchantList;

  FunnelCumulativeMerchantData();

  factory FunnelCumulativeMerchantData.fromJson(Map<String, dynamic> json) {
    return _$FunnelCumulativeMerchantDataFromJson(json);
  }

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$FunnelCumulativeMerchantDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$FunnelCumulativeMerchantDataToJson(this);
  }
}

@JsonSerializable()
class FunnelCumulativeMerchantItemModel {
  /// 药店编码
  dynamic merchantId;

  /// 药店名称
  dynamic merchantName;

  /// 注册时间
  dynamic registerTime;

  FunnelCumulativeMerchantItemModel();

  factory FunnelCumulativeMerchantItemModel.fromJson(
      Map<String, dynamic> json) {
    return _$FunnelCumulativeMerchantItemModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$FunnelCumulativeMerchantItemModelToJson(this);
  }
}
