// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'funnel_cumulative_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

FunnelCumulativeSalesData _$FunnelCumulativeSalesDataFromJson(
    Map<String, dynamic> json) {
  return FunnelCumulativeSalesData()
    ..totalCustNum = json['totalCustNum']
    ..currMonthsAddCustNum = json['currMonthsAddCustNum']
    ..crmUserList = (json['crmUserList'] as List<dynamic>?)
        ?.map((e) =>
            FunnelCumulativeSalesItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelCumulativeSalesDataToJson(
        FunnelCumulativeSalesData instance) =>
    <String, dynamic>{
      'totalCustNum': instance.totalCustNum,
      'currMonthsAddCustNum': instance.currMonthsAddCustNum,
      'crmUserList': instance.crmUserList,
    };

FunnelCumulativeSalesItemModel _$FunnelCumulativeSalesItemModelFromJson(
    Map<String, dynamic> json) {
  return FunnelCumulativeSalesItemModel()
    ..oaId = json['oaId']
    ..crmUserName = json['crmUserName']
    ..deptCode = json['deptCode']
    ..crmUserDepartment = json['crmUserDepartment']
    ..totalCustNum = json['totalCustNum']
    ..deptAndUserName = json['deptAndUserName']
    ..addCustNum = json['addCustNum']
    ..haveChild = json['haveChild']
    ..isBd = json['isBd']
    ..isPostDept = json['isPostDept'];
}

Map<String, dynamic> _$FunnelCumulativeSalesItemModelToJson(
        FunnelCumulativeSalesItemModel instance) =>
    <String, dynamic>{
      'oaId': instance.oaId,
      'crmUserName': instance.crmUserName,
      'deptCode': instance.deptCode,
      'crmUserDepartment': instance.crmUserDepartment,
      'totalCustNum': instance.totalCustNum,
      'deptAndUserName': instance.deptAndUserName,
      'addCustNum': instance.addCustNum,
      'haveChild': instance.haveChild,
      'isBd': instance.isBd,
      'isPostDept': instance.isPostDept,
    };

FunnelCumulativeMerchantData _$FunnelCumulativeMerchantDataFromJson(
    Map<String, dynamic> json) {
  return FunnelCumulativeMerchantData()
    ..totalCustNum = json['totalCustNum']
    ..currMonthsAddCustNum = json['currMonthsAddCustNum']
    ..merchantList = (json['merchantList'] as List<dynamic>?)
        ?.map((e) => FunnelCumulativeMerchantItemModel.fromJson(
            e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$FunnelCumulativeMerchantDataToJson(
        FunnelCumulativeMerchantData instance) =>
    <String, dynamic>{
      'totalCustNum': instance.totalCustNum,
      'currMonthsAddCustNum': instance.currMonthsAddCustNum,
      'merchantList': instance.merchantList,
    };

FunnelCumulativeMerchantItemModel _$FunnelCumulativeMerchantItemModelFromJson(
    Map<String, dynamic> json) {
  return FunnelCumulativeMerchantItemModel()
    ..merchantId = json['merchantId']
    ..merchantName = json['merchantName']
    ..registerTime = json['registerTime'];
}

Map<String, dynamic> _$FunnelCumulativeMerchantItemModelToJson(
        FunnelCumulativeMerchantItemModel instance) =>
    <String, dynamic>{
      'merchantId': instance.merchantId,
      'merchantName': instance.merchantName,
      'registerTime': instance.registerTime,
    };
