import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_sort_base_item.dart';
import 'package:flutter/material.dart';

class FunnelCumulativeItem extends FunnelSortBaseItem {
  FunnelCumulativeItem({
    required int index,
    bool isBottom = false,
    required bool contentHidden,
    required String title,
    required String cooperativeNum,
    required String newNum,
    VoidCallback? clickAction,
  }) : super(
          index: index,
          title: title,
          isBottom: isBottom,
          clickAction: clickAction,
          contentHidden: contentHidden,
          content: Container(
            child: Row(
              children: [
                Expanded(
                  flex: 1,
                  child: Text(
                    "累计客户：$cooperativeNum家",
                    style: TextStyle(
                      color: Color(0xFF00B377),
                      fontSize: 13,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    "本月新增：$newNum家",
                    style: TextStyle(
                      color: Color(0xFF676773),
                      fontSize: 13,
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
}
