import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_content_span.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_filter_base_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_merchant_base_item.dart';
import 'package:flutter/material.dart';

class FunnelCumulativeMerchantFilterItem extends FunnelFilterBaseItem {
  FunnelCumulativeMerchantFilterItem({required ValueChanged changeOption})
      : super(
          configList: [
            FunnelFilterConfigData(title: "全部", option: "1"),
            FunnelFilterConfigData(title: "本月新增", option: "2"),
          ],
          changeOption: changeOption,
        );
}

class FunnelCumulativeMerchantItem extends FunnelMerchantBaseItem {
  FunnelCumulativeMerchantItem({
    required String title,
    required String registerTime,
    bool isBottom = false,
    required VoidCallback clickAction,
  }) : super(
          title: title,
          content: Container(
            child: FunnelContentS<PERSON>(
              title: '注册时间：',
              content: registerTime,
            ),
          ),
          isBottom: isBottom,
          clickAction: clickAction,
        );
}
