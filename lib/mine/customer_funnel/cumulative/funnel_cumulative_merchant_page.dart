import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/cumulative/data/funnel_cumulative_model.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/cumulative/widget/funnel_cumulative_merchant_filter_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/utils/funnel_util.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/funnel_row_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class FunnelCumulativeMerchantPage extends BasePage {
  final dynamic totalNum;
  final dynamic oaId;
  final dynamic isBd;

  FunnelCumulativeMerchantPage({this.oaId, this.isBd, this.totalNum});

  @override
  BaseState<StatefulWidget> initState() {
    return FunnelCumulativeMerchantPageState();
  }
}

class FunnelCumulativeMerchantPageState
    extends BaseState<FunnelCumulativeMerchantPage> {
  EasyRefreshController _controller = EasyRefreshController();

  List<FunnelCumulativeMerchantItemModel> dataSource = [];

  /// 1累计，2本月新增
  dynamic calculationType = "1";

  /// 分页参数
  int page = 0;

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshData();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          this.getTopWidget(),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 10, right: 10),
              child: EasyRefresh(
                controller: _controller,
                onRefresh: () async {
                  this.refreshData();
                },
                onLoad: () async {
                  this.loadMoreData();
                },
                child: ListView.builder(
                  itemCount: this.dataSource.length,
                  itemBuilder: (context, index) {
                    FunnelCumulativeMerchantItemModel model =
                        this.dataSource[index];
                    return Container(
                      child: FunnelCumulativeMerchantItem(
                        title: "${model.merchantName}",
                        registerTime: "${model.registerTime}",
                        isBottom: index == this.dataSource.length - 1,
                        clickAction: () {
                          FunnelUtil.jumpMerchantInfo(model.merchantId);
                        },
                      ),
                    );
                  },
                ),
                emptyWidget: this.getEmptyWidget(),
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  Widget getTopWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          margin: EdgeInsets.all(10),
          child: FunnelRowItem(
            title: "累计客户数：",
            content: "${widget.totalNum ?? 0}",
            unit: "家",
          ),
        ),
        Container(
          margin: EdgeInsets.only(left: 10, right: 10),
          constraints:
              BoxConstraints(minWidth: MediaQuery.of(context).size.width),
          decoration: BoxDecoration(
            color: Color(0xFFFFFFFF),
            borderRadius: BorderRadius.vertical(
              top: Radius.circular(4),
            ),
          ),
          child: FunnelCumulativeMerchantFilterItem(
            changeOption: (option) {
              this.calculationType = option;
              this._controller.callRefresh();
            },
          ),
        ),
      ],
    );
  }

  void refreshData() {
    this.page = 0;
    this.requestListData();
  }

  void loadMoreData() {
    this.requestListData();
  }

  void requestListData() async {
    Map<String, dynamic> params = {
      "calculationType": this.calculationType,
      "pageNum": this.page,
      "pageSize": 10,
    };
    if (widget.oaId != null && widget.oaId != "null") {
      params["oaId"] = widget.oaId;
    }
    if (widget.isBd != null && widget.isBd != "null") {
      params["isBd"] = widget.isBd;
    }
    if (this.dataSource.length == 0) {
      this.showLoadingDialog();
    }
    var result = await NetworkV2<FunnelCumulativeMerchantData>(
            FunnelCumulativeMerchantData())
        .requestDataV2(
      'customerFunnel/customerList',
      parameters: params,
      contentType: RequestContentType.JSON,
      method: RequestMethod.POST,
    );
    this.dismissLoadingDialog();
    this._controller.finishRefresh();
    this._controller.finishLoad();
    if (result.isSuccess == true) {
      if (mounted) {
        FunnelCumulativeMerchantData? data = result.getData();
        if (data != null) {
          if (this.page == 0) {
            this.dataSource = data.merchantList ?? [];
          } else {
            this.dataSource.addAll(data.merchantList ?? []);
          }
          this.page += 1;
          setState(() {});
          if ((data.merchantList ?? []).length != 10) {
            this._controller.finishLoad(noMore: true);
          }
        }
      }
    }
  }

  @override
  String getTitleName() {
    return "累计客户";
  }
}
