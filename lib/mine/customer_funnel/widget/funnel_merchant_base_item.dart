import 'package:flutter/material.dart';

class FunnelMerchantBaseItem extends StatelessWidget {
  final String title;
  final Widget content;
  final VoidCallback clickAction;
  final bool isFirst;
  final bool isBottom;

  FunnelMerchantBaseItem({
    required this.title,
    required this.content,
    required this.clickAction,
    this.isFirst = false,
    this.isBottom = false,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.clickAction,
      child: Container(
        padding: EdgeInsets.only(left: 15, right: 15, bottom: 15),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: this.getContentRadius(),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Divider(
              color: Color(0xFFF5F5F5),
              height: 1,
            ),
            SizedBox(height: 10),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Text(
                    this.title,
                    style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(top: 5, left: 25),
                  child: Image.asset(
                    'assets/images/funnel/funnel_arrow_right.png',
                    width: 13,
                    height: 13,
                  ),
                ),
              ],
            ),
            Container(
              padding: EdgeInsets.only(top: 10),
              child: this.content,
            )
          ],
        ),
      ),
    );
  }

  BorderRadius getContentRadius() {
    return BorderRadius.only(
      topLeft: this.isFirst ? Radius.circular(4) : Radius.zero,
      topRight: this.isFirst ? Radius.circular(4) : Radius.zero,
      bottomLeft: this.isBottom ? Radius.circular(4) : Radius.zero,
      bottomRight: this.isBottom ? Radius.circular(4) : Radius.zero,
    );
  }
}
