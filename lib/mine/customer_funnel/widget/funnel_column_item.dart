import 'package:flutter/material.dart';

class FunnelColumnItem extends StatelessWidget {
  final String title;
  final String content;
  final String unit;

  FunnelColumnItem({
    required this.title,
    required this.content,
    this.unit = "家",
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(4),
      ),
      padding: EdgeInsets.only(top: 15, left: 15, right: 15, bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            this.title,
            style: TextStyle(
              color: Color(0xFF676773),
              fontSize: 14,
              fontWeight: FontWeight.w500,
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 4),
            child: Row(
              children: [
                Text(
                  this.content,
                  style: TextStyle(
                    color: Color(0xFF292933),
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(width: 8),
                Text(
                  this.unit,
                  style: TextStyle(
                    color: Color(0xFF9494A6),
                    fontSize: 11,
                    fontWeight: FontWeight.normal,
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}
