import 'package:flutter/material.dart';

class FunnelRowItem extends StatelessWidget {
  final String title;
  final String content;
  final String unit;
  final VoidCallback? clickAction;

  FunnelRowItem({
    required this.title,
    required this.content,
    this.unit = "家",
    this.clickAction,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.clickAction,
      child: Container(
        padding: EdgeInsets.only(left: 15, top: 18, right: 15, bottom: 18),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Text(
              this.title,
              style: TextStyle(
                color: Color(0xFF676773),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            this.getContentContainer(
              Text(
                this.content,
                textAlign: TextAlign.right,
                style: TextStyle(
                  color: Color(0xFF292933),
                  fontSize: this.hasAction ? 14 : 20,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Visibility(visible: !this.hasAction, child: SizedBox(width: 8)),
            Text(
              this.unit,
              style: TextStyle(
                color: this.hasAction ? Color(0xFF292933) : Color(0xFF9494A6),
                fontSize: this.hasAction ? 14 : 11,
                fontWeight:
                    this.hasAction ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
            Visibility(
              visible: this.hasAction,
              child: Expanded(
                child: Container(
                  alignment: Alignment.centerRight,
                  child: Image.asset(
                    'assets/images/funnel/funnel_arrow_right.png',
                    width: 13,
                    height: 13,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  bool get hasAction {
    return this.clickAction != null;
  }

  Widget getContentContainer(Widget child) {
    if (this.hasAction) {
      return Container(
        child: child,
      );
    } else {
      return Expanded(child: child);
    }
  }
}
