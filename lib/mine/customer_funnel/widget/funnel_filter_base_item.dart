import 'package:flutter/material.dart';

class FunnelFilterBaseItem extends StatefulWidget {
  final List<FunnelFilterConfigData> configList;
  final String? defaultOption;
  final ValueChanged<String> changeOption;

  FunnelFilterBaseItem({
    required this.configList,
    this.defaultOption,
    required this.changeOption,
  }) : assert(configList.length != 0, '选项配置不能为空');

  @override
  State<StatefulWidget> createState() {
    return FunnelFilterBaseItemState();
  }
}

class FunnelFilterBaseItemState extends State<FunnelFilterBaseItem> {
  late String selectOption;

  @override
  void initState() {
    if (widget.defaultOption != null) {
      this.selectOption = widget.defaultOption!;
    } else {
      this.selectOption = widget.configList.first.option;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 10),
      child: Wrap(
        spacing: 10,
        children: this.getItemList(),
      ),
    );
  }

  List<Widget> getItemList() {
    return widget.configList
        .map((e) => FunnelFilterBlockItem(
              title: e.title,
              isSelected: e.option == this.selectOption,
              clickAction: () {
                if (this.selectOption != e.option) {
                  this.selectOption = e.option;
                  setState(() {});
                  widget.changeOption(e.option);
                }
              },
            ))
        .toList();
  }
}

class FunnelFilterConfigData {
  String title;
  String option;

  FunnelFilterConfigData({required this.title, required this.option});
}

class FunnelFilterBlockItem extends StatelessWidget {
  final String title;
  final bool isSelected;
  final VoidCallback clickAction;

  FunnelFilterBlockItem({
    required this.title,
    this.isSelected = false,
    required this.clickAction,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.clickAction,
      child: Container(
        padding: EdgeInsets.only(top: 7, left: 10, bottom: 7, right: 10),
        decoration: BoxDecoration(
          color: this.isSelected ? Color(0x1900B377) : Color(0xFFF7F7F8),
          borderRadius: BorderRadius.circular(2),
        ),
        constraints: BoxConstraints(minWidth: 57),
        child: Text(
          this.title,
          style: TextStyle(
            color: this.isSelected ? Color(0xFF00B377) : Color(0xFF676773),
            fontSize: 12,
            fontWeight: FontWeight.normal,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }
}
