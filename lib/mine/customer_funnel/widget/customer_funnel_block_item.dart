
import 'package:flutter/material.dart';

class CustomerFunnelBlockItem extends StatelessWidget {
  final List<Color> colorBolcks;
  final String title;
  final String content;
  final String unit;

  CustomerFunnelBlockItem({
    required this.title,
    required this.content,
    this.unit = "家",
    this.colorBolcks = const [],
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        border: Border.all(width: 0.5, color: Color(0xFFCCCCCC)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: getTopWidgetList(),
          ),
          Visibility(
            visible: this.colorBolcks.length > 0,
            child: getTitleWidget(),
          ),
          Visibility(
            visible: this.colorBolcks.length > 0,
            child: RichText(
              text: TextSpan(
                  text: this.content,
                  style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 20,
                      fontWeight: FontWeight.w500),
                  children: [
                    TextSpan(
                      text: " ${this.unit}",
                      style: TextStyle(
                          color: Color(0xFF9494A6),
                          fontSize: 11,
                          fontWeight: FontWeight.w400),
                    )
                  ]),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> getTopWidgetList() {
    List<Widget> list = [];
    if (this.colorBolcks.length > 0) {
      list.addAll(this.colorBolcks.map((e) => Container(
            color: e,
            width: 7,
            height: 7,
            margin: EdgeInsets.only(right: 3),
          )));
    } else {
      list.add(getTitleWidget());
    }
    list.add(Spacer());
    list.add(Image.asset('assets/images/funnel/funnel_arrow_right.png',
        width: 15, height: 15));
    return list;
  }

  Widget getTitleWidget() {
    return Text(
      this.title,
      style: TextStyle(
          color: Color(0xFF676773), fontSize: 13, fontWeight: FontWeight.w500),
    );
  }
}
