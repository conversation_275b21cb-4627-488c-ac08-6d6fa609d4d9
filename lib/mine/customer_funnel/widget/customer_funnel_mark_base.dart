import 'package:flutter/material.dart';

class CustomerFunnelMarkBase extends StatelessWidget {
  final Widget content;

  CustomerFunnelMarkBase({required this.content});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: Color(0xFFFFFFFF),
      ),
      child: Stack(
        children: [
          this.content,
          Positioned(
            top: 0,
            left: 0,
            width: 12,
            height: 15.5,
            child: Image.asset(
              'assets/images/funnel/funnel_left_top_icon.png',
            ),
          ),
        ],
      ),
    );
  }
}
