import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/data/customer_funnel_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_block_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_mark_base.dart';
import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart' as flcharts;

class CustomerFunnelPinItem extends StatelessWidget {
  final ValueChanged<bool> pinItemCallback;
  final CustomerFunnelIndexPurchaseData? data;

  CustomerFunnelPinItem({Key? key, required this.pinItemCallback, this.data})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: CustomerFunnelMarkBase(
        content: Container(
          padding: EdgeInsets.fromLTRB(15, 10, 15, 20),
          child: Column(
            children: [
              Row(
                children: [
                  Text("本月动销客户",
                      style: TextStyle(
                          color: Color(0xFF292933),
                          fontSize: 16,
                          fontWeight: FontWeight.w500)),
                  Spacer(),
                  Image.asset(
                    "assets/images/funnel/funnel_tips_icon.png",
                    width: 12,
                    height: 12,
                  ),
                  SizedBox(width: 5),
                  Text('漏斗数据以客户上月等级为准',
                      style: TextStyle(
                          color: Color(0xFF676773),
                          fontSize: 12,
                          fontWeight: FontWeight.w400))
                ],
              ),
              Divider(height: 20.5, thickness: 0.5, color: Color(0xFFEEEEEE)),
              Container(
                height: 180,
                padding: EdgeInsets.only(top: 30),
                child: flcharts.BarChart(this.getBarChartData()),
              ),
              SizedBox(height: 13),
              Row(
                children: [
                  Expanded(
                    child: GestureDetector(
                        onTap: () {
                          this.pinItemCallback(true);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFunnelBlockItem(
                          title: "动销客户数:",
                          content:
                              "${this.data?.moveSale?.purchaseCustomers ?? 0}",
                          colorBolcks: [
                            Color(0xFFF94F4F),
                            Color(0xFFFFAA60),
                            Color(0xFF0579FF),
                            Color(0xFFE869FE),
                            Color(0xFF00B377),
                            Color(0xFF4FCDFF),
                          ],
                        )),
                  ),
                  SizedBox(width: 15),
                  Expanded(
                    child: GestureDetector(
                        onTap: () {
                          this.pinItemCallback(false);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFunnelBlockItem(
                          title: "未动销客户数:",
                          content:
                              "${this.data?.nonMoveSale?.purchaseCustomers ?? 0}",
                          colorBolcks: [
                            Color(0xFFEAEAED),
                          ],
                        )),
                  ),
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  flcharts.BarChartData getBarChartData() {
    return flcharts.BarChartData(
      alignment: flcharts.BarChartAlignment.spaceAround,
      barGroups: [
        flcharts.BarChartGroupData(
          x: 0,
          barsSpace: 0,
          showingTooltipIndicators: [0, 1],
          barRods: [
            getChartRodData(
                this.data?.moveSale?.sLevelCustomers, Color(0xFF00B377)),
            getChartRodData(
                this.data?.nonMoveSale?.sLevelCustomers, Color(0xFFEAEAED)),
          ],
        ),
        flcharts.BarChartGroupData(
          x: 1,
          barsSpace: 0,
          showingTooltipIndicators: [0, 1],
          barRods: [
            getChartRodData(
                this.data?.moveSale?.aLevelCustomers, Color(0xFFFFC760)),
            getChartRodData(
                this.data?.nonMoveSale?.aLevelCustomers, Color(0xFFEAEAED)),
          ],
        ),
        flcharts.BarChartGroupData(
          x: 2,
          barsSpace: 0,
          showingTooltipIndicators: [0, 1],
          barRods: [
            getChartRodData(
                this.data?.moveSale?.bLevelCustomers, Color(0xFF0579FF)),
            getChartRodData(
                this.data?.nonMoveSale?.bLevelCustomers, Color(0xFFEAEAED)),
          ],
        ),
        flcharts.BarChartGroupData(
          x: 3,
          barsSpace: 0,
          showingTooltipIndicators: [0, 1],
          barRods: [
            getChartRodData(
                this.data?.moveSale?.cLevelCustomers, Color(0xFFE869FE)),
            getChartRodData(
                this.data?.nonMoveSale?.cLevelCustomers, Color(0xFFEAEAED)),
          ],
        ),
        flcharts.BarChartGroupData(
          x: 4,
          barsSpace: 0,
          showingTooltipIndicators: [0, 1],
          barRods: [
            getChartRodData(
                this.data?.moveSale?.dLevelCustomers, Color(0xFFF94F4F)),
            getChartRodData(
                this.data?.nonMoveSale?.dLevelCustomers, Color(0xFFEAEAED)),
          ],
        ),
        flcharts.BarChartGroupData(
          x: 5,
          barsSpace: 0,
          showingTooltipIndicators: [0, 1],
          barRods: [
            getChartRodData(
                this.data?.moveSale?.levelCustomers, Color(0xFF4FCDFF)),
            getChartRodData(
                this.data?.nonMoveSale?.levelCustomers, Color(0xFFEAEAED)),
          ],
        ),
      ],
      titlesData: this.getTitleData(),
      barTouchData: flcharts.BarTouchData(
        handleBuiltInTouches: false,
        touchTooltipData: flcharts.BarTouchTooltipData(
          tooltipBgColor: Colors.transparent,
          tooltipPadding: EdgeInsets.zero,
          tooltipMargin: 0,
          direction: flcharts.TooltipDirection.top,
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            return flcharts.BarTooltipItem(
              "${rod.y.toInt()}",
              TextStyle(
                color: rodIndex == 0 ? Color(0xFF9494A6) : Color(0xFF292933),
                fontWeight: FontWeight.w500,
                fontSize: 12,
              ),
            );
          },
        ),
      ),
      borderData: flcharts.FlBorderData(
        show: true,
        border: Border(
          bottom: BorderSide(
            width: 0.5,
            color: Color(0xFFDDDDDD),
          ),
        ),
      ),
    );
  }

  flcharts.BarChartRodData getChartRodData(dynamic y, Color color) {
    double value = 0;
    if (y is int) {
      value = y * 1.0;
    }
    if (y is String) {
      value = double.tryParse(y) ?? 0;
    }
    if (y is double) {
      value = y;
    }
    return flcharts.BarChartRodData(
      y: value,
      width: 18,
      colors: [color],
      borderRadius: BorderRadius.zero,
    );
  }

  flcharts.FlTitlesData getTitleData() {
    return flcharts.FlTitlesData(
      leftTitles: flcharts.SideTitles(showTitles: false),
      bottomTitles: flcharts.SideTitles(
        showTitles: true,
        getTitles: (index) {
          switch (index.toInt()) {
            case 0:
              return "S级";
            case 1:
              return "A级";
            case 2:
              return "B级";
            case 3:
              return "C级";
            case 4:
              return "D级";
            case 5:
              return "新增";
            default:
              return "";
          }
        },
        getTextStyles: (_, value) {
          return TextStyle(
            color: Color(0xFF292933),
            fontSize: 12,
          );
        },
      ),
    );
  }
}
