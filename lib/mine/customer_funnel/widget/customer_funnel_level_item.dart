import 'dart:math';

import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/data/customer_funnel_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_block_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_mark_base.dart';
import 'package:flutter/material.dart';

class CustomerFunnelLevelItem extends StatelessWidget {
  final ValueChanged<bool> itemClickCallback;

  final CustomerSLevelModelData? data;

  CustomerFunnelLevelItem({
    Key? key,
    required this.itemClickCallback,
    this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: CustomerFunnelMarkBase(
        content: Container(
          padding: EdgeInsets.fromLTRB(15, 10, 15, 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text("本月S级客户",
                  style: TextStyle(
                      color: Color(0xFF292933),
                      fontSize: 16,
                      fontWeight: FontWeight.w500)),
              Divider(height: 20.5, thickness: 0.5, color: Color(0xFFEEEEEE)),
              _CustomerFunnelPinProgressItem(
                  title: "S(++)客户数:",
                  number: this.getIntForNumber(this.data?.sLevelAA),
                  total: this.getTotalNumber(),
                  color: Color(0xFF00B377)),
              _CustomerFunnelPinProgressItem(
                  title: "S(+-)客户数:",
                  number: this.getIntForNumber(this.data?.sLevelAB),
                  total: this.getTotalNumber(),
                  color: Color(0xFF33C292)),
              _CustomerFunnelPinProgressItem(
                  title: "S(-+)客户数:",
                  number: this.getIntForNumber(this.data?.sLevelBA),
                  total: this.getTotalNumber(),
                  color: Color(0xFF66D1AD)),
              _CustomerFunnelPinProgressItem(
                  title: "S(--)客户数:",
                  number: this.getIntForNumber(this.data?.sLevelBB),
                  total: this.getTotalNumber(),
                  color: Color(0xFF99E1C9)),
              _CustomerFunnelPinProgressItem(
                  title: "潜在客户数:",
                  number: this.getIntForNumber(this.data?.potentialCustomers),
                  total: this.getTotalNumber(),
                  color: Color(0xFFFDF2BF)),
              SizedBox(height: 10),
              IntrinsicHeight(
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          this.itemClickCallback(false);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFunnelBlockItem(
                          title: "S级客户数:",
                          content: "${this.data?.sLevelCustomers ?? 0}",
                          colorBolcks: [
                            Color(0xFF00B377),
                            Color(0xFF33C292),
                            Color(0xFF66D1AD),
                            Color(0xFF99E1C9)
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 15),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          this.itemClickCallback(true);
                        },
                        behavior: HitTestBehavior.opaque,
                        child: CustomerFunnelBlockItem(
                          title: "S级潜在客户",
                          content: "${this.data?.potentialCustomers ?? 0}",
                          colorBolcks: [
                            Color(0xFFFDF2BF),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  int getIntForNumber(dynamic number) {
    if (number is int) {
      return number;
    }
    if (number is double) {
      return number.toInt();
    }
    if (number is String) {
      return int.tryParse(number, radix: 0) ?? 0;
    }
    return 0;
  }

  int getTotalNumber() {
    int maxNumber = this.getIntForNumber(this.data?.sLevelAA);
    int abNumber = this.getIntForNumber(this.data?.sLevelAB);
    if (maxNumber < abNumber) {
      maxNumber = abNumber;
    }
    int baNumber = this.getIntForNumber(this.data?.sLevelBA);
    if (maxNumber < baNumber) {
      maxNumber = baNumber;
    }
    int bbNumber = this.getIntForNumber(this.data?.sLevelBB);
    if (maxNumber < bbNumber) {
      maxNumber = bbNumber;
    }
    int pNumber = this.getIntForNumber(this.data?.potentialCustomers);
    if (maxNumber < pNumber) {
      maxNumber = pNumber;
    }
    return max(maxNumber, 1);
  }
}

class _CustomerFunnelPinProgressItem extends StatelessWidget {
  final String title;
  final int number;
  final int total;
  final Color color;

  _CustomerFunnelPinProgressItem({
    required this.title,
    required this.number,
    required this.total,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      child: Row(
        children: [
          Container(
            constraints: BoxConstraints(minWidth: 75),
            child: Text(
              this.title,
              style: TextStyle(color: Color(0xFF292933), fontSize: 12),
            ),
          ),
          Expanded(
            child: getProgress(),
          ),
          Container(
            width: 80,
            child: Text(
              "${this.number}",
              style: TextStyle(
                  color: Color(0xFF292933),
                  fontSize: 12,
                  fontWeight: FontWeight.w500),
              textAlign: TextAlign.right,
            ),
          )
        ],
      ),
    );
  }

  Widget getProgress() {
    return Container(
      height: 18,
      child: LinearProgressIndicator(
        backgroundColor: Colors.transparent,
        valueColor: AlwaysStoppedAnimation(this.color),
        value: this.number / this.total,
      ),
    );
  }
}
