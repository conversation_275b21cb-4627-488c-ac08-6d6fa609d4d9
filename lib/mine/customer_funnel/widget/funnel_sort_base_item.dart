import 'package:flutter/material.dart';

class FunnelSortBaseItem extends StatelessWidget {
  final int index;
  final bool isBottom;
  final bool contentHidden;
  final String title;
  final Widget content;
  final VoidCallback? clickAction;

  FunnelSortBaseItem({
    required this.index,
    this.isBottom = false,
    this.contentHidden = false,
    required this.title,
    required this.content,
    this.clickAction,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.clickAction,
      child: Container(
        margin: EdgeInsets.only(left: 10, right: 10),
        padding: EdgeInsets.only(left: 15, right: 15, top: 15),
        decoration: BoxDecoration(
            color: Color(0xFFFFFFFF), borderRadius: this.getContentRadius()),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                this.getIndexWidget(),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        this.title,
                        style: TextStyle(
                          color: Color(0xFF292933),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Visibility(
                        visible: !this.contentHidden,
                        child: SizedBox(height: 7),
                      ),
                      Visibility(
                        visible: !this.contentHidden,
                        child: this.content,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(top: 1),
                  child: Image.asset(
                    'assets/images/funnel/funnel_arrow_right.png',
                    width: 13,
                    height: 13,
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Visibility(
              visible: !this.isBottom,
              child: Divider(
                color: Color(0xFFF5F5F5),
                height: 1,
              ),
            )
          ],
        ),
      ),
    );
  }

  /// 排名widget
  Widget getIndexWidget() {
    switch (this.index) {
      case 0:
        return Container(
          margin: EdgeInsets.only(right: 15),
          child: Image.asset(
            'assets/images/funnel/funnel_sort_first.png',
            width: 25,
            height: 25,
          ),
        );
      case 1:
        return Container(
          margin: EdgeInsets.only(right: 15),
          child: Image.asset(
            'assets/images/funnel/funnel_sort_second.png',
            width: 25,
            height: 25,
          ),
        );
      case 2:
        return Container(
          margin: EdgeInsets.only(right: 15),
          child: Image.asset(
            'assets/images/funnel/funnel_sort_third.png',
            width: 25,
            height: 25,
          ),
        );
      default:
        int sortNum = this.index + 1;
        return Container(
          margin: EdgeInsets.only(right: 15),
          width: 25,
          height: 25,
          alignment: Alignment.center,
          child: Text(
            "$sortNum".padLeft(2, '0'),
            style: TextStyle(
              color: Color(0xFF9494A6),
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
    }
  }

  BorderRadius getContentRadius() {
    return BorderRadius.only(
      topLeft: this.index == 0 ? Radius.circular(4) : Radius.zero,
      topRight: this.index == 0 ? Radius.circular(4) : Radius.zero,
      bottomLeft: this.isBottom ? Radius.circular(4) : Radius.zero,
      bottomRight: this.isBottom ? Radius.circular(4) : Radius.zero,
    );
  }
}
