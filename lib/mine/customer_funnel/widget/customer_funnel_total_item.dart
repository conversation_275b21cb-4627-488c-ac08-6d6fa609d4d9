import 'dart:math';

import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/data/customer_funnel_list_data.dart';
import 'package:flutter/material.dart';

class CustomerFunnelTotalItem extends StatelessWidget {
  final ValueChanged<int> tapItem;
  final CustomerFunnelData? indexData;

  CustomerFunnelTotalItem({
    Key? key,
    required this.tapItem,
    this.indexData,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            this.tapItem(0);
          },
          behavior: HitTestBehavior.opaque,
          child: _CustomerFunnelTotalItem(
            title: getItemtitle(0),
            content: "${this.indexData?.totalPoiNum ?? 0}家",
            color: getItemLeftBackgroundColor(0),
            index: 0,
          ),
        ),
        _CustomerFunnelTotalItem(
          title: getItemtitle(1),
          content: "${this.indexData?.registerCustomers ?? 0}家",
          color: getItemLeftBackgroundColor(1),
          index: 1,
        ),
        GestureDetector(
          onTap: () {
            this.tapItem(1);
          },
          behavior: HitTestBehavior.opaque,
          child: _CustomerFunnelTotalItem(
            title: getItemtitle(2),
            content: "${this.indexData?.accumulateCustomers ?? 0}家",
            color: getItemLeftBackgroundColor(2),
            index: 2,
          ),
        ),
        GestureDetector(
          onTap: () {
            this.tapItem(2);
          },
          behavior: HitTestBehavior.opaque,
          child: _CustomerFunnelTotalItem(
            title: getItemtitle(3),
            content: "${this.indexData?.purchaseCustomers ?? 0}家",
            color: getItemLeftBackgroundColor(3),
            index: 3,
          ),
        ),
        GestureDetector(
          onTap: () {
            this.tapItem(3);
          },
          behavior: HitTestBehavior.opaque,
          child: _CustomerFunnelTotalItem(
            title: getItemtitle(4),
            content: "${this.indexData?.sLevelCustomers ?? 0}家",
            color: getItemLeftBackgroundColor(4),
            index: 4,
          ),
        ),
      ],
    );
  }

  String getItemtitle(int index) {
    switch (index) {
      case 0:
        return "客户大盘";
      case 1:
        return "注册客户";
      case 2:
        return "累计客户";
      case 3:
        return "动销客户";
      case 4:
        return "S级客户";
      default:
        return "";
    }
  }

  Color getItemLeftBackgroundColor(int index) {
    switch (index) {
      case 0:
        return Color(0xFF00B377);
      case 1:
        return Color(0xFF31BF8F);
      case 2:
        return Color(0xFF61CBA7);
      case 3:
        return Color(0xFF91D9C0);
      default:
        return Color(0xFF00B377);
    }
  }
}

class _CustomerFunnelTotalItem extends StatelessWidget {
  final String title;
  final String content;
  final Color color;
  final int index;

  _CustomerFunnelTotalItem(
      {required this.title,
      required this.content,
      required this.color,
      required this.index});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF1F1F1),
      height: 60,
      padding: EdgeInsets.fromLTRB(10, 5, 10, 0),
      child: CustomPaint(
        painter: _CustomerFunnelTotalPainter(
          index: index,
          painterColor: color,
        ),
        child: Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.only(left: 10),
                child: Text(
                  title,
                  style: TextStyle(
                    color: Color(0xFFFFFFFF),
                    fontWeight: FontWeight.w500,
                    fontSize: 17,
                  ),
                ),
              ),
              Container(
                padding: EdgeInsets.only(right: 10),
                child: Text(
                  content,
                  style: TextStyle(
                    color: Color(0xFF292626),
                    fontWeight: FontWeight.w500,
                    fontSize: 17,
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class _CustomerFunnelTotalPainter extends CustomPainter {
  final int index;
  final Color painterColor;
  final double spaceWidth;

  _CustomerFunnelTotalPainter({
    this.index = 0,
    this.spaceWidth = 5,
    this.painterColor = const Color(0xFF80FFC1),
  });

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.strokeJoin = StrokeJoin.round;
    paint.strokeWidth = 10;
    paint.style = PaintingStyle.stroke;

    var anchorWidth = paint.strokeWidth / 2;

    var width = size.width;
    var height = size.height;
    var difference = height / tan(pi / 2.65);

    var offsetSpace =
        (paint.strokeWidth * sqrt(2)) * (index + 1) + difference * 2;

    /// left
    paint.color = this.painterColor;
    var leftPath = Path();
    leftPath.moveTo(anchorWidth, anchorWidth);
    leftPath.lineTo(
        width -
            difference * (index + 1) -
            anchorWidth -
            offsetSpace -
            spaceWidth,
        anchorWidth);
    leftPath.lineTo(
        width -
            difference * (index + 2) -
            anchorWidth -
            offsetSpace -
            spaceWidth,
        height - anchorWidth);
    leftPath.lineTo(anchorWidth, size.height - anchorWidth);
    leftPath.close();
    canvas.drawPath(leftPath, paint);

    /// leftCenter
    paint.style = PaintingStyle.fill;
    canvas.drawPath(leftPath, paint);

    /// right
    paint.color = Color(0xFFFFFFFF);
    paint.style = PaintingStyle.stroke;
    var rightPath = Path();
    rightPath.moveTo(
        width -
            difference * (index + 1) +
            anchorWidth -
            offsetSpace +
            spaceWidth,
        anchorWidth);
    rightPath.lineTo(size.width - anchorWidth, anchorWidth);
    rightPath.lineTo(size.width - anchorWidth, size.height - anchorWidth);
    rightPath.lineTo(
        width -
            difference * (index + 2) +
            anchorWidth -
            offsetSpace +
            spaceWidth,
        height - anchorWidth);
    rightPath.close();
    canvas.drawPath(rightPath, paint);

    /// rightCenter
    paint.style = PaintingStyle.fill;
    canvas.drawPath(rightPath, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
