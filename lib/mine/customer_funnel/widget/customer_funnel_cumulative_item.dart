import 'dart:math';

import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/data/customer_funnel_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/customer_funnel/widget/customer_funnel_mark_base.dart';
import 'package:fl_chart/fl_chart.dart' as flcharts;
import 'package:flutter/material.dart';

class CustomerFunnelCumulativeItem extends StatelessWidget {
  final CustomerFunnelIndexCumulativeData? data;
  final VoidCallback? actionCallback;
  CustomerFunnelCumulativeItem({Key? key, this.actionCallback, this.data})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: CustomerFunnelMarkBase(
        content: Container(
          padding: EdgeInsets.fromLTRB(15, 10, 15, 20),
          child: Column(
            children: [
              Row(
                children: [
                  Text("累计客户",
                      style: TextStyle(
                          color: Color(0xFF292933),
                          fontSize: 16,
                          fontWeight: FontWeight.w500)),
                  Spacer(),
                  Image.asset(
                    "assets/images/funnel/funnel_tips_icon.png",
                    width: 12,
                    height: 12,
                  ),
                  SizedBox(width: 5),
                  Text('漏斗数据以客户上月等级为准',
                      style: TextStyle(
                          color: Color(0xFF676773),
                          fontSize: 12,
                          fontWeight: FontWeight.w400))
                ],
              ),
              Divider(height: 20.5, thickness: 0.5, color: Color(0xFFEEEEEE)),
              _CustomerFunnelCumulativePie(data: data),
              SizedBox(height: 15),
              GestureDetector(
                onTap: this.actionCallback,
                behavior: HitTestBehavior.opaque,
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Color(0xFFCCCCCC),
                      width: 0.5,
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  height: 30,
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: Row(
                    children: [
                      Text(
                        '累计客户数：${this.data?.accumulateCustomers ?? 0}家',
                        style: TextStyle(
                            color: Color(0xFF292933),
                            fontSize: 13,
                            fontWeight: FontWeight.w500),
                      ),
                      Spacer(),
                      Image.asset(
                        'assets/images/funnel/funnel_arrow_right.png',
                        width: 15,
                        height: 15,
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class _CustomerFunnelCumulativePie extends StatefulWidget {
  final CustomerFunnelIndexCumulativeData? data;

  _CustomerFunnelCumulativePie({this.data});

  @override
  State<StatefulWidget> createState() {
    return _CustomerFunnelCumulativePieState();
  }
}

class _CustomerFunnelCumulativePieState
    extends State<_CustomerFunnelCumulativePie> {
  int touchIndex = -1;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Container(
          width: (MediaQuery.of(context).size.width - 210) / 2,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    this.touchIndex = 5;
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: _CustomerFunnelPieAroundItem(
                  color: Color(0xFF00B377),
                  title: "S级:",
                  numberText: this
                      .getCountForNumber(widget.data?.sLevelVo?.levelCustomers),
                  content: this.getCustomerPercent(
                      widget.data?.sLevelVo?.levelProportion),
                  isSelected: this.touchIndex == 5,
                  isNormal: this.touchIndex == -1,
                ),
              ),
              SizedBox(height: 6),
              GestureDetector(
                onTap: () {
                  setState(() {
                    this.touchIndex = 4;
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: _CustomerFunnelPieAroundItem(
                  color: Color(0xFF0579FF),
                  title: "B级:",
                  numberText: this
                      .getCountForNumber(widget.data?.bLevelVo?.levelCustomers),
                  content: this.getCustomerPercent(
                      widget.data?.bLevelVo?.levelProportion),
                  isSelected: this.touchIndex == 4,
                  isNormal: this.touchIndex == -1,
                ),
              ),
              SizedBox(height: 6),
              GestureDetector(
                onTap: () {
                  setState(() {
                    this.touchIndex = 3;
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: _CustomerFunnelPieAroundItem(
                  color: Color(0xFFF94F4F),
                  title: "D级:",
                  numberText: this
                      .getCountForNumber(widget.data?.dLevelVo?.levelCustomers),
                  content: this.getCustomerPercent(
                      widget.data?.dLevelVo?.levelProportion),
                  isSelected: this.touchIndex == 3,
                  isNormal: this.touchIndex == -1,
                ),
              ),
            ],
          ),
        ),
        Stack(
          alignment: AlignmentDirectional.center,
          children: [
            Container(
              height: 160,
              width: 160,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(80),
                color: Color(0x33D8D8D8),
              ),
            ),
            Container(
              height: 160,
              width: 160,
              child: Transform.rotate(
                angle: -pi / 2,
                child: flcharts.PieChart(
                  flcharts.PieChartData(
                    pieTouchData: flcharts.PieTouchData(
                      touchCallback: (pieTouchRes) {
                        setState(() {
                          this.touchIndex =
                              pieTouchRes.touchedSection?.touchedSectionIndex ??
                                  -1;
                        });
                      },
                    ),
                    borderData: flcharts.FlBorderData(show: false),
                    sectionsSpace: 0,
                    centerSpaceRadius: 30,
                    sections: getFLPieListData(),
                  ),
                ),
              ),
            ),
          ],
        ),
        Container(
          width: (MediaQuery.of(context).size.width - 210) / 2,
          padding: EdgeInsets.only(left: 5),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              GestureDetector(
                onTap: () {
                  setState(() {
                    this.touchIndex = 0;
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: _CustomerFunnelPieAroundItem(
                  color: Color(0xFFFFC760),
                  title: "A级:",
                  numberText: this
                      .getCountForNumber(widget.data?.aLevelVo?.levelCustomers),
                  content: this.getCustomerPercent(
                      widget.data?.aLevelVo?.levelProportion),
                  isSelected: this.touchIndex == 0,
                  isNormal: this.touchIndex == -1,
                ),
              ),
              SizedBox(height: 6),
              GestureDetector(
                onTap: () {
                  setState(() {
                    this.touchIndex = 1;
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: _CustomerFunnelPieAroundItem(
                  color: Color(0xFFE869FE),
                  title: "C级:",
                  numberText: this
                      .getCountForNumber(widget.data?.cLevelVo?.levelCustomers),
                  content: this.getCustomerPercent(
                      widget.data?.cLevelVo?.levelProportion),
                  isSelected: this.touchIndex == 1,
                  isNormal: this.touchIndex == -1,
                ),
              ),
              SizedBox(height: 6),
              GestureDetector(
                onTap: () {
                  setState(() {
                    this.touchIndex = 2;
                  });
                },
                behavior: HitTestBehavior.opaque,
                child: _CustomerFunnelPieAroundItem(
                  color: Color(0xFF4FCDFF),
                  title: "新增:",
                  numberText: this
                      .getCountForNumber(widget.data?.levelVo?.levelCustomers),
                  content: this.getCustomerPercent(
                      widget.data?.levelVo?.levelProportion),
                  isSelected: this.touchIndex == 2,
                  isNormal: this.touchIndex == -1,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 转换客户数量
  String getCountForNumber(dynamic number) {
    int integer = this.getCustomerCount(number);

    if (integer > 10000) {
      double source = integer / 10000.0;
      return source.toStringAsFixed(2) + "w";
    }
    return "$integer";
  }

  int getCustomerCount(dynamic number) {
    int integer = 0;
    if (number is String) {
      integer = int.tryParse(number, radix: 0) ?? 0;
    }
    if (number is double) {
      integer = number.toInt();
    }
    if (number is int) {
      integer = number;
    }
    return integer;
  }

  // 转换客户百分比
  String getCustomerPercent(dynamic percent) {
    int source = getCustomerIntPercent(percent);

    return "${(source * 0.01).toStringAsFixed(2)}%";
  }

  int getCustomerIntPercent(dynamic percent) {
    if (percent is int) {
      return percent;
    }
    if (percent is double) {
      return percent.toInt();
    }
    if (percent is String) {
      return int.tryParse(percent, radix: 0) ?? 0;
    }
    return 0;
  }

  List<flcharts.PieChartSectionData> getFLPieListData() {
    return [
      // A级
      flcharts.PieChartSectionData(
        color: Color(0xFFFFC760),
        value: (widget.data?.aLevelVo?.levelCustomers ?? 0) * 1.0,
        radius: touchIndex == 0 ? 45 : 40,
        showTitle: false,
      ),
      // C级
      flcharts.PieChartSectionData(
        color: Color(0xFFE869FE),
        value: (widget.data?.cLevelVo?.levelCustomers ?? 0) * 1.0,
        radius: touchIndex == 1 ? 45 : 40,
        showTitle: false,
      ),
      // 新增
      flcharts.PieChartSectionData(
        color: Color(0xFF4FCDFF),
        value: (widget.data?.levelVo?.levelCustomers ?? 0) * 1.0,
        radius: touchIndex == 2 ? 45 : 40,
        showTitle: false,
      ),
      // D级
      flcharts.PieChartSectionData(
        color: Color(0xFFF94F4F),
        value: (widget.data?.dLevelVo?.levelCustomers ?? 0) * 1.0,
        radius: touchIndex == 3 ? 45 : 40,
        showTitle: false,
      ),
      // B级
      flcharts.PieChartSectionData(
        color: Color(0xFF0579FF),
        value: (widget.data?.bLevelVo?.levelCustomers ?? 0) * 1.0,
        radius: touchIndex == 4 ? 45 : 40,
        showTitle: false,
      ),
      // S级
      flcharts.PieChartSectionData(
        color: Color(0xFF00B377),
        value: (widget.data?.sLevelVo?.levelCustomers ?? 0) * 1.0,
        radius: touchIndex == 5 ? 45 : 40,
        showTitle: false,
      ),
    ];
  }
}

class _CustomerFunnelPieAroundItem extends StatelessWidget {
  final Color color;
  final String title;
  final String numberText;
  final String content;
  final bool isSelected;
  final bool isNormal;

  _CustomerFunnelPieAroundItem({
    required this.color,
    required this.title,
    required this.numberText,
    required this.content,
    this.isSelected = false,
    this.isNormal = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 7,
            height: 7,
            margin: EdgeInsets.only(top: 7.5, right: 7),
            decoration: BoxDecoration(
              color: (this.isSelected || this.isNormal)
                  ? this.color
                  : this.color.withOpacity(0.3),
              borderRadius: BorderRadius.circular(3.5),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  this.title,
                  style: TextStyle(
                    color: this.getTextColor(),
                    fontWeight: this.getTextFontWeight(),
                    fontSize: 14,
                  ),
                ),
                Text(
                  this.numberText,
                  style: TextStyle(
                    color: this.getTextColor(),
                    fontWeight: this.getTextFontWeight(),
                    fontSize: 14,
                  ),
                ),
                Text(
                  "(${this.content})",
                  style: TextStyle(
                    color: this.getTextColor(),
                    fontWeight: this.getTextFontWeight(),
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  Color getTextColor() {
    if (this.isSelected || this.isNormal) {
      return Color(0xFF333333);
    }
    return Color(0xFF9494A6);
  }

  FontWeight getTextFontWeight() {
    if (this.isSelected) {
      return FontWeight.w500;
    }
    return FontWeight.w400;
  }
}
