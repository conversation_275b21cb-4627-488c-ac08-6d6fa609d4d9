import 'package:flutter/material.dart';

class FunnelBaseItem extends StatelessWidget {
  final bool isTop;
  final bool isBottom;
  final bool contentHidden;
  final String title;
  final Widget content;
  final VoidCallback? clickAction;

  FunnelBaseItem({
    this.isTop = false,
    this.isBottom = false,
    this.contentHidden = false,
    required this.title,
    required this.content,
    this.clickAction,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.clickAction,
      child: Container(
        margin: EdgeInsets.only(left: 10, right: 10),
        padding: EdgeInsets.only(left: 15, right: 15, top: 15),
        decoration: BoxDecoration(
            color: Color(0xFFFFFFFF), borderRadius: this.getContentRadius()),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        this.title,
                        style: TextStyle(
                          color: Color(0xFF292933),
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Visibility(
                        visible: !this.contentHidden,
                        child: SizedBox(height: 7),
                      ),
                      Visibility(
                        visible: !this.contentHidden,
                        child: this.content,
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(top: 1),
                  child: Image.asset(
                    'assets/images/funnel/funnel_arrow_right.png',
                    width: 13,
                    height: 13,
                  ),
                ),
              ],
            ),
            SizedBox(height: 10),
            Visibility(
              visible: !this.isBottom,
              child: Divider(
                color: Color(0xFFF5F5F5),
                height: 1,
              ),
            )
          ],
        ),
      ),
    );
  }

  BorderRadius getContentRadius() {
    return BorderRadius.only(
      topLeft: this.isTop ? Radius.circular(4) : Radius.zero,
      topRight: this.isTop ? Radius.circular(4) : Radius.zero,
      bottomLeft: this.isBottom ? Radius.circular(4) : Radius.zero,
      bottomRight: this.isBottom ? Radius.circular(4) : Radius.zero,
    );
  }
}
