import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/mine/personal_info/bean/personal_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import './personal_data_item.dart';

class PersonPage extends StatefulWidget {
  @override
  _PersonalPageState createState() => _PersonalPageState();
}

class _PersonalPageState extends State<PersonPage> {
  PersonalDataBean userShowData = PersonalDataBean();

  dynamic reviceValue;

  @override
  void initState() {
    super.initState();
    getUserShowData();
  }

  void getUserShowData() async {
    /*RequestResult responseModel =
    await RequestHelper.request('findUserInfo', method: RequestMethod.GET);
    this.reviceValue = responseModel.responseObject;
    this._loadFinished();*/
    var result = await Network<PersonalDataBean>(PersonalDataBean())
        .requestData('findUserInfo',
            method: RequestMethod.GET, showErrorToast: false, parameters: {});
    print("guan:$result");
    if (result.isSuccess == true && mounted) {
      setState(() {
        this.userShowData = result;
      });
    }
  }

  Widget userInfoWidget() {
    List<Widget> info = [];
    info.add(PersonItem(title: "姓名", content: userShowData.realName));
    info.add(PersonItem(title: "账号", content: userShowData.name));
    info.add(PersonItem(title: "部门", content: userShowData.department));
    return Container(
      color: CupertinoColors.white,
      margin: EdgeInsets.only(top: 15, bottom: 15),
      child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: info),
    );
  }

  Widget userLinkWidget() {
    List<Widget> info = [];
    info.add(PersonItem(title: "手机号", content: userShowData.phone));
    info.add(PersonItem(
        title: "邮箱",
        content: userShowData.email!.isNotEmpty ? userShowData.email : '未设置'));
    return Container(
      color: CupertinoColors.white,
      margin: EdgeInsets.only(top: 0, bottom: 15),
      child: Column(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: info),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CommonTitleBar(
        "个人资料",
      ),
      body: Column(
        children: [
          userInfoWidget(),
          userLinkWidget(),
        ],
      ),
      backgroundColor: Color(0xFFF5F5F5),
    );
  }

// request(String s, {RequestMethod method}) {}
}

class _PersionData {
  final String? userName;
  final String? account;
  final String? department;
  final String? mobile;
  final String? email;

  _PersionData(
      this.userName, this.account, this.department, this.mobile, this.email);

  _PersionData.fromJson(Map<dynamic, dynamic> json)
      : userName = json['realName'],
        account = json['name'],
        department = json['department'],
        mobile = json['phone'],
        email = json['email'];

  Map<String, String?> toJson() => <String, String?>{
        'userName': userName,
        'account': account,
        'department': department,
        'mobile': mobile,
        'email': email
      };
}
