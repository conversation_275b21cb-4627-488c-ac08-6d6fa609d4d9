import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'personal_data.g.dart';

@JsonSerializable()
class PersonalDataBean extends BaseModel<PersonalDataBean> {
  String? realName = '';
  String? name = '';
  String? department = '';
  String? phone = '';
  String? email = '';

  PersonalDataBean();


  @override
  String toString() {
    return 'PersonalDataBean{realName: $realName, name: $name, department: $department, phone: $phone, email: $email}';
  }

  @override
  PersonalDataBean fromJsonMap(Map<String, dynamic>? json) {
    return PersonalDataBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PersonalDataBeanToJson(this);
  }

  factory PersonalDataBean.fromJson(Map<String, dynamic> json) =>
      _$PersonalDataBeanFromJson(json);
}
