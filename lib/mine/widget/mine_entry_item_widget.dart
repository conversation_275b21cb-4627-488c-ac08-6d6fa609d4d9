import 'package:XYYContainer/XYYContainer.dart';
import 'package:flutter/material.dart';

class MineEntryItemWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Container(
        padding: EdgeInsets.only(left: 10, right: 10),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          children: [
            _MineItem(
              iconPath: "assets/images/mine/mine_services_icon.png",
              title: "联系客服",
              aciton: () {
                XYYContainer.bridgeCall('event_track',
                    parameters: {"action_type": "mc-mine-callQA"});
                // 跳转联系客服  message页面改造后处理
                Navigator.of(context).pushNamed("/message_root_page?index=1");
              },
            ),
            Divider(color: Color(0xFFF5F5F5), height: 0.5),
            _MineItem(
              iconPath: "assets/images/mine/mine_setting_icon.png",
              title: "更多设置",
              aciton: () {
                XYYContainer.bridgeCall('event_track',
                    parameters: {"action_type": "mc-mine-install"});
                String router = "xyy://crm-app.ybm100.com/mine/setting";
                XYYContainer.open(router);
              },
            ),
          ],
        ),
      ),
    );
  }
}

class _MineItem extends StatelessWidget {
  final String iconPath;
  final String title;
  final VoidCallback aciton;

  _MineItem({
    required this.iconPath,
    required this.title,
    required this.aciton,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: this.aciton,
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: 55,
        child: Row(
          children: [
            Image.asset(
              this.iconPath,
              width: 24,
              height: 24,
            ),
            SizedBox(width: 10),
            Text(
              this.title,
              style: TextStyle(
                color: Color(0xFF676773),
                fontSize: 14,
              ),
            ),
            Spacer(),
            Image.asset(
              'assets/images/mine/mine_item_arrow.png',
              width: 12,
              height: 12,
            ),
          ],
        ),
      ),
    );
  }
}
