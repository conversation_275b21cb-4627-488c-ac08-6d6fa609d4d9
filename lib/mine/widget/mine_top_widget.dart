
import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/search_bar/message_icon_widget.dart';
import 'package:flutter/material.dart';

class MineTopWidget extends StatelessWidget {
  final String? name;
  final String? department;

  final ValueNotifier<int> controller = ValueNotifier(1);
  MineTopWidget({this.name, this.department});
  @override
  Widget build(BuildContext context) {
    double baseHeight = 156 * MediaQuery.of(context).size.width / 375;
    initMianButton();
    return ValueListenableBuilder<int>(
      valueListenable: controller,
      builder: (context, value, child) {
        return Container(
          child: Container(
            height: baseHeight,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage('assets/images/mine/mine_top_base.png'),
                fit: BoxFit.cover,
              ),
              color: Color(0xFFF7F7F8),
            ),
            padding: EdgeInsets.only(bottom: 20),
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              onTap: () {
                XYYContainer.bridgeCall('event_track',
                    parameters: {"action_type": "mc-mine-persondata"});
                Navigator.of(context).pushNamed("/personal_data");
              },
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  SizedBox(width: 15),
                  Image.asset(
                    'assets/images/mine/mine_head_icon.png',
                    width: 55,
                    height: 55,
                  ),
                  SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        children: [
                          Text(
                            name ?? "--",
                            style: TextStyle(
                              color: Color(0xFF333333),
                              fontSize: 21,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          SizedBox(width: 8),
                          Image.asset(
                            'assets/images/home/<USER>',
                            width: 4,
                            height: 8,
                          ),
                          SizedBox(width: 8),
                          value == 2
                              ? TextButton(
                                  onPressed: () async {
                                    XYYContainer.storageChannel.put('mianType_shop', 'shop',space:'douya_shop');
                                    Navigator.pushNamedAndRemoveUntil(context, '/main', (route) => false);
                                    
                                  },
                                  child: checkBtn(),
                                  style: ButtonStyle(
                                    overlayColor:
                                        MaterialStateProperty.all<Color>(
                                            Colors.transparent),
                                    padding:
                                        MaterialStateProperty.all<EdgeInsets>(
                                            EdgeInsets.only(right: 10)),
                                    minimumSize:
                                        MaterialStateProperty.all<Size>(
                                            Size.zero),
                                    tapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                  ))
                              : SizedBox()
                        ],
                      ),
                      SizedBox(height: 1),
                      Text(
                        department ?? "--",
                        style: TextStyle(
                          color: Color(0xFF999999),
                          fontSize: 14,
                        ),
                      ),
                      SizedBox(height: 5),
                    ],
                  ),
                  Spacer(),
                  Container(
                    padding: EdgeInsets.only(bottom: 46),
                    child: MessageIconWidget(),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  initMianButton() async {
    try {
      var result = await XYYContainer.requestChannel.request(
          '/mop/account/query',
          method: RequestMethod.GET,
          contentType: RequestContentType.FORM,
          parameters: {});
      var data = json.decode(result);
      if (data['status'] == 'success' &&
          data['data']['permissionLevelCode'] == 2) {
        controller.value = 2;
        return;
      }
      controller.value = 1;
    } catch (e) {
      controller.value = 1;
    }
  }

    Widget checkBtn() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Transform.translate(
          offset: Offset(0, 1.5),
          child: Icon(
            Icons.swap_horiz,
            color: Colors.black,
            size: 16,
          ),
        ),
        Text(
          '切换商家运营版',
          style: TextStyle(
              color: Colors.black,
              fontSize: 12
          ),
        ),
      ],
    );
  }
}
