// import 'package:XyyBeanSproutsFlutter/mine/data/mine_menu_model.dart';
import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:XyyBeanSproutsFlutter/mine/data/mine_menu_model.dart';
import 'package:flutter/material.dart';

class MineBlockMenuWidget extends StatelessWidget {
  final String title;
  final List<MineMenuModel> itemModels;
  final ValueChanged<MineMenuModel> itemClick;
  final String imageHost;

  MineBlockMenuWidget(
      {required this.title,
      required this.itemModels,
      required this.itemClick,
      required this.imageHost});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF7F7F8),
      padding: EdgeInsets.fromLTRB(10, 0, 10, 10),
      width: double.infinity,
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.all(15),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: TextStyle(
                color: Color(0xFF333333),
                fontSize: 15,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 18),
            Container(
              padding: EdgeInsets.only(left: 3, right: 3),
              child: Wrap(
                runSpacing: 23,
                children: this.menuItems(context),
              ),
            )
          ],
        ),
      ),
    );
  }

  List<Widget> menuItems(BuildContext context) {
    return this
        .itemModels
        .map((e) => GestureDetector(
              onTap: () {
                this.itemClick(e);
              },
              behavior: HitTestBehavior.opaque,
              child: SizedBox(
                width: (MediaQuery.of(context).size.width - 20 - 36) / 4,
                child: MineBlockMenuItem(
                  model: e,
                  imageHost: imageHost,
                ),
              ),
            ))
        .toList();
  }
}

class MineBlockMenuItem extends StatelessWidget {
  final MineMenuModel model;
  final String imageHost;

  MineBlockMenuItem({required this.model, required this.imageHost});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          ImageWidget(
            url: imageHost + model.image.toString(),
            w: 30,
            h: 30,
            defImagePath: 'assets/images/home/<USER>',
          ),
          SizedBox(height: 9),
          Text(
            model.name,
            style: TextStyle(
              color: Color(0xFF333333),
              fontSize: 12,
            ),
            overflow: TextOverflow.ellipsis,
          )
        ],
      ),
    );
  }
}
