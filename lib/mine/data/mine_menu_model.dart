import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'mine_menu_model.g.dart';

@JsonSerializable()
class MineMenuModel extends BaseModelV2<MineMenuModel> {
  dynamic name; // 名称
  dynamic icon; // 图标相对路径
  dynamic actionType; // 点击操作类型：1：直接跳转，2：pdc h5跳转，3：其他h5跳转，4：荷叶h5跳转
  dynamic jumpUrl; // 跳转链接，如果是pdc或者荷叶的h5需要拼接host
  dynamic platform; // 展示平台 0：全平台，1：Android，2：iOS
  dynamic sectionId; // 展示在第几栏，目前仅支持0、1
  dynamic typeID; // 唯一id
  dynamic image; // 展示的icon

  MineMenuModel();

  factory MineMenuModel.fromJson(Map<String, dynamic> json) =>
      _$MineMenuModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MineMenuModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MineMenuModelToJson(this);
  }
}

@JsonSerializable()
class MineMenuParentModel extends BaseModelV2<MineMenuParentModel> {
  List<MineMenuModel>? rows;

  MineMenuParentModel();

  factory MineMenuParentModel.fromJson(Map<String, dynamic> json) =>
      _$MineMenuParentModelFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MineMenuParentModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MineMenuParentModelToJson(this);
  }
}
