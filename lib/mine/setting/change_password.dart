import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:flutter/material.dart';

class ChangePassword extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CommonTitleBar("忘记密码",),
        body: Container(
          alignment: Alignment.topLeft,
          padding: EdgeInsets.only(top: 200, left: 10, right: 10),
          color: Color(0xFFF9F9F9),
          width: double.infinity,
          height: double.infinity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                '温馨提示',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 18, color: const Color(0xffff0000)),
              ),
              SizedBox(height: 20, width: 10),
              Text(
                '因豆芽的用户的账号和密码由AD主数据部门维护，密码修改请登录公司OA系统进行修改。',
                textAlign: TextAlign.center,
                softWrap: true,
                style: TextStyle(fontSize: 14, color: const Color(0xFF8E8E8E)),
              ),
              SizedBox(height: 20),
              Text(
                'OA修改密码步骤流程：登录OA系统-->个人设置-->个人应用设置-->密码和账号管理',
                textAlign: TextAlign.center,
                softWrap: true,
                style: TextStyle(fontSize: 14, color: const Color(0xFF8E8E8E)),
              )
            ],
          ),
        ));
  }
}
