import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'coupons_detail_data.g.dart';

@JsonSerializable()
class CouponsDetailHeaderModel extends BaseModelV2<CouponsDetailHeaderModel> {
  /// 未认领数量
  dynamic unclaimedCount;

  /// 未使用数量
  dynamic unusedCount;

  /// 已使用数量
  dynamic usedCount;

  /// 优惠券信息
  CouponsListDataModel? couponVo;

  @override
  CouponsDetailHeaderModel fromJsonMap(Map<String, dynamic> json) {
    return _$CouponsDetailHeaderModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CouponsDetailHeaderModelToJson(this);
  }
}

@JsonSerializable()
class CouponsDetailPageModel extends BaseModelV2<CouponsDetailPageModel> {
  dynamic isLastPage;

  List<CouponsDetailMerchantData>? list;

  @override
  CouponsDetailPageModel fromJsonMap(Map<String, dynamic> json) {
    return _$CouponsDetailPageModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CouponsDetailPageModelToJson(this);
  }
}

@JsonSerializable()
class CouponsDetailMerchantData extends BaseModelV2<CouponsDetailMerchantData> {
  dynamic merchantId;
  dynamic customerId;
  dynamic merchantName;
  dynamic merchantAddress;

  CouponsDetailMerchantData();

  factory CouponsDetailMerchantData.fromJson(Map<String, dynamic> json) {
    return _$CouponsDetailMerchantDataFromJson(json);
  }

  @override
  CouponsDetailMerchantData fromJsonMap(Map<String, dynamic> json) {
    return _$CouponsDetailMerchantDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CouponsDetailMerchantDataToJson(this);
  }
}
