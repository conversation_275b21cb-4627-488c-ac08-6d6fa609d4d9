import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_list_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'coupons_group_data.g.dart';

@JsonSerializable()
class CouponsGroupData extends BaseModelV2<CouponsGroupData> {
  /// 负责人；oaID
  dynamic adminId;

  /// 部门名称/销售名称
  dynamic crmUserDepartment;

  /// 部门编码
  dynamic groupId;

  /// 是否为叶子结点
  dynamic isLeafNode;

  CouponsGroupData();

  factory CouponsGroupData.fromJson(Map<String, dynamic> json) {
    return _$CouponsGroupDataFromJson(json);
  }

  @override
  CouponsGroupData fromJsonMap(Map<String, dynamic> json) {
    return _$CouponsGroupDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CouponsGroupDataToJson(this);
  }
}
