// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'coupons_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CouponsListDataPageModel _$CouponsListDataPageModelFromJson(
    Map<String, dynamic> json) {
  return CouponsListDataPageModel()
    ..isLastPage = json['isLastPage']
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) => CouponsListDataModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$CouponsListDataPageModelToJson(
        CouponsListDataPageModel instance) =>
    <String, dynamic>{
      'isLastPage': instance.isLastPage,
      'list': instance.list,
    };

CouponsListDataModel _$CouponsListDataModelFromJson(Map<String, dynamic> json) {
  return CouponsListDataModel()
    ..couponId = json['couponId']
    ..couponTypeId = json['couponTypeId']
    ..couponTypeDesc = json['couponTypeDesc']
    ..couponName = json['couponName']
    ..couponDocument = json['couponDocument']
    ..couponRange = json['couponRange']
    ..couponValue = json['couponValue']
    ..couponDiscount = json['couponDiscount']
    ..maxSubValue = json['maxSubValue']
    ..couponCondition = json['couponCondition']
    ..couponDate = json['couponDate']
    ..status = json['status'];
}

Map<String, dynamic> _$CouponsListDataModelToJson(
        CouponsListDataModel instance) =>
    <String, dynamic>{
      'couponId': instance.couponId,
      'couponTypeId': instance.couponTypeId,
      'couponTypeDesc': instance.couponTypeDesc,
      'couponName': instance.couponName,
      'couponDocument': instance.couponDocument,
      'couponRange': instance.couponRange,
      'couponValue': instance.couponValue,
      'couponDiscount': instance.couponDiscount,
      'maxSubValue': instance.maxSubValue,
      'couponCondition': instance.couponCondition,
      'couponDate': instance.couponDate,
      'status': instance.status,
    };
