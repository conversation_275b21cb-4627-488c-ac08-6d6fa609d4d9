import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'coupons_list_data.g.dart';

@JsonSerializable()
class CouponsListDataPageModel extends BaseModelV2<CouponsListDataPageModel> {
  dynamic isLastPage;

  List<CouponsListDataModel>? list;

  @override
  CouponsListDataPageModel fromJsonMap(Map<String, dynamic> json) {
    return _$CouponsListDataPageModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CouponsListDataPageModelToJson(this);
  }
}

@JsonSerializable()
class CouponsListDataModel extends BaseModelV2<CouponsListDataModel> {
  /// 券id
  dynamic couponId;

  /// 券类型id
  dynamic couponTypeId;

  /// 类型描述
  dynamic couponTypeDesc;

  /// 券名称
  dynamic couponName;

  /// 文案
  dynamic couponDocument;

  /// 使用范围
  dynamic couponRange;

  /// 券数值
  dynamic couponValue;

  /// 折扣值
  dynamic couponDiscount;

  /// 最高减 金额
  dynamic maxSubValue;

  /// 券使用条件
  dynamic couponCondition;

  /// 券时间？
  dynamic couponDate;

  /// 状态 1-未使用，2-已使用，3-已过期
  dynamic status;

  CouponsListDataModel();

  /// 是否是金额的优惠券
  bool get isPriceCoupons {
    return this.couponDiscount == null ||
        this.couponDiscount?.toString() == "0";
  }

  /// 获取面额
  String get faceValue {
    return this.isPriceCoupons
        ? "${this.couponValue ?? '0'}"
        : "${this.couponDiscount ?? '-'}";
  }

  factory CouponsListDataModel.fromJson(Map<String, dynamic> json) {
    return _$CouponsListDataModelFromJson(json);
  }

  @override
  CouponsListDataModel fromJsonMap(Map<String, dynamic> json) {
    return _$CouponsListDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CouponsListDataModelToJson(this);
  }
}
