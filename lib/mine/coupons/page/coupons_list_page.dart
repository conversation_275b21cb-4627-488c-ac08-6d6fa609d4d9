import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_search_item.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_list_data.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/widget/coupons_list_item.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class CouponsListPage extends BasePage {
  final dynamic searchUserId;

  CouponsListPage({this.searchUserId});

  @override
  BaseState<StatefulWidget> initState() {
    return CouponsListPageSate();
  }
}

class CouponsListPageSate extends BaseState<CouponsListPage> {
  int page = 1;

  String? searchKeyWord;

  EasyRefreshController _controller = EasyRefreshController();

  List<CouponsListDataModel> dataSource = [];

  bool isLastPage = true;

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void initState() {
    this.refreshList();
    super.initState();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        color: Color(0xFFF7F7F8),
        child: Column(
          children: [
            CommonSearchItem(
              hintText: "请输入优惠券名称/券面文案",
              onTap: () {
                // 搜索埋点
                track('mc-mine-couponSearch');
              },
              onSubmitted: (keyword) {
                var searchWord = keyword.trim();
                if (searchWord.isEmpty) {
                  this.searchKeyWord = null;
                } else {
                  this.searchKeyWord = searchWord;
                }
                _controller.callRefresh();
              },
              onClear: () {
                this.searchKeyWord = null;
                _controller.callRefresh();
              },
            ),
            Expanded(
              child: EasyRefresh(
                onRefresh: refreshList,
                onLoad: this.isLastPage ? null : loadMoreList,
                controller: _controller,
                emptyWidget: this.getEmptyView(),
                child: ListView.builder(
                  itemCount: this.dataSource.length,
                  itemBuilder: (context, index) {
                    CouponsListDataModel model = this.dataSource[index];
                    return CouponsListItem(
                      model: model,
                      clickAction: () {
                        var router =
                            '/coupons_detail?couponId=${model.couponId}';
                        if (widget.searchUserId != null) {
                          router += "&searchUserId=${widget.searchUserId}";
                        }
                        XYYContainer.open(router);
                      },
                    );
                  },
                ),
              ),
            )
          ],
        ),
      ),
    );
  }

  Future<void> refreshList() async {
    this.page = 1;
    await this.requestList();
    return;
  }

  Future<void> loadMoreList() async {
    await this.requestList();
    return;
  }

  Future<void> requestList() async {
    Map<String, dynamic> param = {
      'offset': this.page,
      'limit': "20",
    };
    if (widget.searchUserId != null) {
      param['searchUserId'] = widget.searchUserId;
    }
    if (this.searchKeyWord != null && this.searchKeyWord?.isNotEmpty == true) {
      param['searchKeyWord'] = this.searchKeyWord;
    }
    if (this.dataSource.length == 0) {
      showLoadingDialog();
    }
    var result =
        await NetworkV2<CouponsListDataPageModel>(CouponsListDataPageModel())
            .requestDataV2(
      'coupon/list',
      parameters: param,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var data = result.getData();
        if (this.page == 1) {
          this.dataSource = data?.list ?? [];
        } else {
          this.dataSource.addAll(data?.list ?? []);
        }
        this.isLastPage = data?.isLastPage == true;
        this.page += 1;
        setState(() {});
      }
    }
    return;
  }

  Widget? getEmptyView() {
    return (this.dataSource.length == 0)
        ? PageStateWidget(state: PageState.Empty)
        : null;
  }

  @override
  String getTitleName() {
    return "优惠券";
  }
}
