import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/widget/group_item_v1.dart';
import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_group_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/group_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/global_cache_manager.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

/// 简单版本的组织结构筛选
class CouponGroupSelectPage extends BasePage {
  final dynamic groupId;

  CouponGroupSelectPage({this.groupId});

  @override
  BaseState<StatefulWidget> initState() {
    return CouponGroupSelectPageState();
  }
}

class CouponGroupSelectPageState extends BaseState<CouponGroupSelectPage> {
  List<CouponsGroupData>? currentGroupList;

  PageState pageState = PageState.Empty;
  EasyRefreshController _refreshController = EasyRefreshController();

  @override
  void initState() {
    super.initState();
    showLoadingDialog();
    requestGroupDataList();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xfff7f7f8),
      child: buildListWidget(),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _refreshController.dispose();
  }

  Widget buildListWidget() {
    return Container(
      padding: EdgeInsets.all(10),
      child: EasyRefresh(
        controller: _refreshController,
        onRefresh: () async {
          return await requestGroupDataList();
        },
        onLoad: null,
        child: ListView.builder(
          itemCount: this.currentGroupList?.length ?? 0,
          itemBuilder: (BuildContext context, int index) {
            CouponsGroupData? model = this.currentGroupList?[index];
            if (model == null) {
              return Container();
            }
            return GestureDetector(
              onTap: () {
                handleItemClick(model);
              },
              behavior: HitTestBehavior.opaque,
              child: GroupItemV1(
                name: model.crmUserDepartment?.toString() ?? "--",
                isDepartment: model.groupId != null,
                isFirst: index == 0,
                isLast: index == (this.currentGroupList?.length ?? 0) - 1,
              ),
            );
          },
        ),
        emptyWidget: this.getEmptyWidget(),
      ),
    );
  }

  /// 空页面
  Widget? getEmptyWidget() {
    if (currentGroupList?.isNotEmpty == true) {
      return null;
    }
    switch (pageState) {
      case PageState.Error:
        return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
          showLoadingDialog();
          requestGroupDataList();
        });
      case PageState.Empty:
        return PageStateWidget.pageEmpty(
          PageState.Empty,
        );
      default:
        return null;
    }
  }

  @override
  String getTitleName() {
    return "优惠券";
  }

  Future<void> requestGroupDataList() async {
    var params = {};
    if (widget.groupId != null) {
      params["groupId"] = widget.groupId;
    }
    var result = await NetworkV2<CouponsGroupData>(CouponsGroupData())
        .requestDataV2("/newGetTree/list",
            parameters: params, method: RequestMethod.GET);
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess == true) {
        var listData = result.getListData();
        currentGroupList = listData;
        if (listData == null || listData.isEmpty) {
          pageState = PageState.Empty;
        } else {
          pageState = PageState.Normal;
        }
      } else {
        pageState = PageState.Error;
      }
      setState(() {});
    }
  }

  void handleItemClick(CouponsGroupData model) {
    if (model.isLeafNode?.toString() == "true") {
      // 跳转优惠券列表
      Navigator.of(context)
          .pushNamed("/coupons_list?searchUserId=${model.adminId}");
    } else {
      // 跳转部门
      Navigator.of(context).pushNamed("/coupon_group_select_page",
          arguments: {"groupId": model.groupId});
    }
  }
}
