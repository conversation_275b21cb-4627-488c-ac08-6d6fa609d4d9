import 'package:XyyBeanSproutsFlutter/mine/coupons/data/coupons_detail_data.dart';
import 'package:flutter/material.dart';

class CouponsDetailMerchantItem extends StatelessWidget {
  final CouponsDetailMerchantData model;

  CouponsDetailMerchantItem({required this.model});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      padding: EdgeInsets.fromLTRB(10, 15, 10, 0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.asset(
            'assets/images/mine/coupons_detail_merchant_icon.png',
            width: 44,
            height: 44,
          ),
          SizedBox(width: 10),
          Expanded(
            child: Column(
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${this.model.merchantName ?? '-'}',
                            style: TextStyle(
                              color: Color(0xFF333333),
                              fontSize: 16,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                          SizedBox(height: 3),
                          Text(
                            '${this.model.merchantAddress ?? '1'}',
                            style: TextStyle(
                              color: Color(0xFF9494A6),
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ),
                    SizedBox(width: 12),
                    Image.asset(
                      'assets/images/funnel/funnel_arrow_right.png',
                      width: 13,
                      height: 13,
                    )
                  ],
                ),
                SizedBox(height: 15),
                Divider(color: Color(0xFFF5F5F5), height: 0.5, thickness: 1),
              ],
            ),
          )
        ],
      ),
    );
  }
}
