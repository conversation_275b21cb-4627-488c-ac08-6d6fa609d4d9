import 'package:flutter/material.dart';

class GroupItemV1 extends StatelessWidget {
  final bool isFirst;
  final bool isLast;

  final String? name;
  final bool isDepartment;

  GroupItemV1(
      {this.isFirst = false,
      this.isLast = false,
      this.isDepartment = false,
      this.name});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      decoration:
      BoxDecoration(color: Colors.white, borderRadius: getBorderRadius()),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            isDepartment
                ? "assets/images/schedule/schedule_statistics_item_group.png"
                : "assets/images/schedule/schedule_statistics_item_user.png",
            width: 22,
            height: 22,
          ),
          SizedBox(
            width: 11,
          ),
          Text(
            name??"--",
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(
                color: const Color(0xff292933),
                fontSize: 16,
                fontWeight: FontWeight.w600),
          ),
          Expanded(child: Container()),
          Image.asset(
            "assets/images/schedule/schedule_statistics_item_arrow.png",
            width: 12.5,
            height: 12.5,
          ),
        ],
      ),
    );
  }

  BorderRadius getBorderRadius() {
    if (isFirst == true) {
      return BorderRadius.only(
          topLeft: Radius.circular(4), topRight: Radius.circular(4));
    }
    if (isLast == true) {
      return BorderRadius.only(
          bottomLeft: Radius.circular(4), bottomRight: Radius.circular(4));
    }
    return BorderRadius.zero;
  }
}
