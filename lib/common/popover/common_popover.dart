import 'package:flutter/material.dart';

Future<T?> showRightTipsPopover<T extends Object?>({
  required BuildContext context,
  required GlobalKey anchorKey,
  required Widget content,
  BorderRadius contentRadus = const BorderRadius.all(Radius.circular(2)),
  Color backgroundColor = const Color(0xCC000000),
}) {
  dynamic renderBox = anchorKey.currentContext?.findRenderObject()!;
  double renderWidth = renderBox.size.width;
  double renderHeight = renderBox.size.height;
  Offset offset = renderBox.localToGlobal(Offset(renderWidth, renderHeight));
  return showGeneralDialog(
    context: context,
    barrierColor: Colors.transparent,
    // 遮罩颜色
    barrierLabel: "",
    barrierDismissible: true,
    // 点击遮罩是否关闭对话框
    transitionDuration: const Duration(milliseconds: 200),
    // 对话框打开/关闭的动画时长
    pageBuilder: (
      // 构建对话框内部UI
      BuildContext ctx,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      double screenWidth = MediaQuery.of(ctx).size.width;
      return Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            top: offset.dy - 0.5,
            right: screenWidth - offset.dx,
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: contentRadus, color: backgroundColor),
              constraints: BoxConstraints(maxWidth: screenWidth - 30),
              child: content,
            ),
          ),
        ],
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ),
        child: child,
      );
    },
  );
}

Future<T?> showTipsPopover<T extends Object?>({
  required BuildContext context,
  required GlobalKey anchorKey,
  required Widget content,
  BorderRadius contentRadus = const BorderRadius.all(Radius.circular(2)),
  Color backgroundColor = const Color(0xCC000000),
  Size triangleSize = const Size(10, 5),
}) {
  dynamic renderBox = anchorKey.currentContext?.findRenderObject()!;
  double renderWidth = renderBox.size.width;
  double renderHeight = renderBox.size.height;
  Offset offset = renderBox.localToGlobal(Offset(0, renderHeight));
  return showGeneralDialog(
    context: context,
    barrierColor: Colors.transparent,
    // 遮罩颜色
    barrierLabel: "",
    barrierDismissible: true,
    // 点击遮罩是否关闭对话框
    transitionDuration: const Duration(milliseconds: 200),
    // 对话框打开/关闭的动画时长
    pageBuilder: (
      // 构建对话框内部UI
      BuildContext ctx,
      Animation animation,
      Animation secondaryAnimation,
    ) {
      double screenWidth = MediaQuery.of(ctx).size.width;
      return Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            left: offset.dx + renderWidth / 2,
            top: offset.dy,
            child:
                _CommonTriangleView(color: backgroundColor, size: triangleSize),
          ),
          Positioned(
            top: offset.dy + triangleSize.height - 0.5,
            child: Container(
              decoration: BoxDecoration(
                  borderRadius: contentRadus, color: backgroundColor),
              constraints: BoxConstraints(maxWidth: screenWidth - 30),
              padding: EdgeInsets.all(10),
              child: content,
            ),
          ),
        ],
      );
    },
    transitionBuilder: (context, animation, secondaryAnimation, child) {
      return FadeTransition(
        opacity: CurvedAnimation(
          parent: animation,
          curve: Curves.easeOut,
        ),
        child: child,
      );
    },
  );
}

class _CommonTriangleView extends StatelessWidget {
  final Color color;
  final Size size;

  _CommonTriangleView({
    required this.color,
    required this.size,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CommonTrianglePainter(color: this.color, size: this.size),
      child: Container(
        width: size.width,
        height: size.height,
      ),
    );
  }
}

class _CommonTrianglePainter extends CustomPainter {
  final Color color;
  final Size size;

  _CommonTrianglePainter({
    required this.color,
    required this.size,
  });

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.color = this.color;
    paint.style = PaintingStyle.fill;

    Path path = Path()
      ..moveTo(size.width / 2, 0)
      ..lineTo(size.width, size.height)
      ..lineTo(0, size.height);

    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
