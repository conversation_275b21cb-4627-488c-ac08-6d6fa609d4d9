import 'package:flutter/material.dart';

class VoiceToolBar extends StatefulWidget {
  final int selectValue;

  final ValueChanged<int> toolBarActions;

  VoiceToolBar({required this.toolBarActions, this.selectValue = 0});

  @override
  State<StatefulWidget> createState() {
    return VoiceToolBarState();
  }
}

class VoiceToolBarState extends State<VoiceToolBar> {
  int selectIndex = 0;

  @override
  void initState() {
    selectIndex = widget.selectValue;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        border: Border(
          top: BorderSide(
            width: 0.5,
            color: Color(0xFFE1E1E5),
          ),
        ),
      ),
      width: MediaQuery.of(context).size.width,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Container(
            child: TextButton(
              onPressed: () {
                setState(() {
                  selectIndex = 0;
                });
                widget.toolBarActions(selectIndex);
              },
              style: ButtonStyle(
                textStyle:
                    MaterialStateProperty.all<TextStyle>(voiceButtonStyle()),
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Row(
                children: [
                  Image.asset(
                    textIcon(),
                    width: 20,
                    height: 20,
                  ),
                  Text(
                    '文字',
                    style: textButtonStyle(),
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 10, bottom: 10),
            width: 0.5,
            color: Color(0xFFE1E1E5),
          ),
          Container(
            child: TextButton(
              onPressed: () {
                setState(() {
                  selectIndex = 1;
                });
                widget.toolBarActions(selectIndex);
              },
              style: ButtonStyle(
                textStyle:
                    MaterialStateProperty.all<TextStyle>(voiceButtonStyle()),
                overlayColor:
                    MaterialStateProperty.all<Color>(Colors.transparent),
                padding: MaterialStateProperty.all<EdgeInsets>(EdgeInsets.zero),
                minimumSize: MaterialStateProperty.all<Size>(Size.zero),
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
              ),
              child: Row(
                children: [
                  Image.asset(
                    voiceIcon(),
                    width: 20,
                    height: 20,
                  ),
                  Text(
                    '语音',
                    style: voiceButtonStyle(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  String textIcon() {
    return selectIndex == 0
        ? "assets/images/visit/icon_visit_text_normal.png"
        : "assets/images/visit/icon_visit_text_enable.png";
  }

  String voiceIcon() {
    return selectIndex == 1
        ? "assets/images/visit/icon_visit_voice_normal.png"
        : "assets/images/visit/icon_visit_voice_enable.png";
  }

  TextStyle textButtonStyle() {
    return TextStyle(
      fontSize: 18,
      color: selectIndex == 0 ? Color(0xFF00B377) : Color(0xFF333333),
    );
  }

  TextStyle voiceButtonStyle() {
    return TextStyle(
      fontSize: 18,
      color: selectIndex == 1 ? Color(0xFF00B377) : Color(0xFF333333),
    );
  }
}
