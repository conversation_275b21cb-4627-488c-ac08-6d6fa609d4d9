import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/animation_image/animation_image.dart';
import 'package:flutter/material.dart';
import 'package:keyboard_actions/keyboard_actions.dart';

class VoiceKeyboard extends StatefulWidget
    with KeyboardCustomPanelMixin<String>
    implements PreferredSizeWidget {
  final ValueNotifier<String> controller;
  const VoiceKeyboard({required this.controller});

  @override
  State<StatefulWidget> createState() {
    return VoiceKeyboardState();
  }

  @override
  ValueNotifier<String> get notifier => controller;

  @override
  Size get preferredSize => Size.fromHeight(250);
}

class VoiceKeyboardState extends State<VoiceKeyboard> {
  List<String> imagePaths = [];

  bool isRecord = false;

  @override
  void initState() {
    super.initState();
    for (var i = 0; i < 20; i++) {
      imagePaths.add(
          'assets/images/visit/voice/voice_animation_' + i.toString() + '.png');
    }
  }

  @override
  void dispose() {
    /// 销毁时调用一次 防止未结束一直录制
    XYYContainer.bridgeCall('voice_input', parameters: {'action_type': 'stop'});
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    MediaQueryData mediaQuery = MediaQuery.of(context);

    return Container(
      color: Color(0xFFFFFFFF),
      width: mediaQuery.size.width,
      child: ListView(
        /// 因为键盘打开动画的问题 造成绘制越界问题，此处添加ListView处理
        physics: NeverScrollableScrollPhysics(),
        children: [
          Container(
            constraints: BoxConstraints(
              minHeight: 80,
            ),
            child: Visibility(
              visible: this.isRecord,
              child: AnimationImages(
                imagePaths: imagePaths,
                width: mediaQuery.size.width - 80,
                height: 80,
              ),
            ),
          ),
          GestureDetector(
            onLongPress: () {
              recordStart();
            },
            onLongPressUp: () {
              recordEnd();
            },
            onLongPressEnd: (_) {
              recordEnd();
            },
            child: Container(
              child: Image.asset(
                recordImage(),
                width: 60,
                height: 60,
              ),
            ),
          ),
          Center(
            child: Text(
              '按住说话',
              style: TextStyle(
                color: Color(0xFF666666),
                fontSize: 15,
              ),
            ),
          )
        ],
      ),
    );
  }

  recordStart() {
    setState(() {
      this.isRecord = true;
    });
    XYYContainer.bridgeCall('voice_input', parameters: {'action_type': 'start'})
        .then((value) {
      if (value is String) {
        if (value.isNotEmpty) {
          widget.updateValue(value);
        }
      }
    });
  }

  recordEnd() {
    setState(() {
      this.isRecord = false;
    });
    XYYContainer.bridgeCall('voice_input', parameters: {'action_type': 'stop'})
        .then((value) {
      if (value is String) {
        if (value.isNotEmpty) {
          widget.updateValue(value);
        }
      }
    });
  }

  String recordImage() {
    return isRecord
        ? 'assets/images/visit/icon_visit_record_pressed.png'
        : 'assets/images/visit/icon_visit_record_normal.png';
  }
}
