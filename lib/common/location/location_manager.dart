import 'dart:io';

import 'package:XyyBeanSproutsFlutter/customer/map/utils/ybm_customer_map_controller.dart';
import 'package:flutter_baidu_mapapi_search/flutter_baidu_mapapi_search.dart';
import 'package:flutter_bmflocation/flutter_bmflocation.dart';

typedef LocationChangeCallBack = void Function(BaiduLocation location);
typedef BMFHeadingResultCallback = void Function(BaiduHeading result);
typedef BMFGeoCodeSearchCallback = void Function(
    BMFGeoCodeSearchResult result, BMFSearchErrorCode errorCode);

class LocationManager {
  LocationManager._privateConstructor() {
    /// 设置隐私协议
    this._locationPlugin.setAgreePrivacy(true);

    /// 设置iOSkey
    if (Platform.isIOS) {
      this._locationPlugin.authAK("RKuRzPikLuTINnEM9uRpihvaAxMoSdmO");
    }

    /// 设置定位参数
    this._locationPlugin.prepareLoc(
          YBMCustomerMapLocationOptions.initAndroidOptions().getMap(),
          YBMCustomerMapLocationOptions.initIOSOptions().getMap(),
        );

    /// 朝向回调
    this._locationPlugin.updateHeadingCallback(
      callback: (result) {
        this._headingCallbacks.values.forEach((element) {
          element(result);
        });
      },
    );

    /// 定位回调
    this._locationPlugin.seriesLocationCallback(
      callback: (result) {
        this._locationChangeCallbacks.values.forEach((element) {
          element(result);
        });
      },
    );
  }

  static final LocationManager _instance =
      LocationManager._privateConstructor();

  factory LocationManager() {
    return _instance;
  }

  static LocationManager get instance {
    return _instance;
  }

  final LocationFlutterPlugin _locationPlugin = LocationFlutterPlugin();

  final BMFGeocodeSearch _geocodeSearch = BMFGeocodeSearch();

  final Map<Object, LocationChangeCallBack> _locationChangeCallbacks = {};

  final Map<Object, BMFHeadingResultCallback> _headingCallbacks = {};

  /// 设置定位回调
  void setLocationCallback(Object observer, LocationChangeCallBack callBack) {
    _locationChangeCallbacks[observer] = callBack;
  }

  /// 移除定位回调
  void removeLocationCallback(Object observer) {
    _locationChangeCallbacks.remove(observer);
    if (_locationChangeCallbacks.length <= 0) {
      this._locationPlugin.stopLocation();
    }
  }

  void startLocation() {
    _locationPlugin.startLocation();
  }

  /// 设置朝向回调
  void setUpdateHeadingCallback(
      Object observer, BMFHeadingResultCallback callback) {
    _headingCallbacks[observer] = callback;
  }

  void startUpdatingHeading() {
    _locationPlugin.startUpdatingHeading();
  }

  void removeUpdatingHeading(Object observer) {
    _headingCallbacks.remove(observer);
    if (_headingCallbacks.length <= 0) {
      this._locationPlugin.stopUpdatingHeading();
    }
  }

  /// 检索地理位置
  void searchLocation(String locationName, BMFGeoCodeSearchCallback callback) {
    /// 设置地理位置检索回调
    _geocodeSearch.onGetGeoCodeSearchResult(callback: callback);

    /// 调用正地址位置检索
    _geocodeSearch
        .geoCodeSearch(BMFGeoCodeSearchOption(address: locationName, city: ""));
  }
}
