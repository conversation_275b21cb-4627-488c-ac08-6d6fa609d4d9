import 'package:flutter/material.dart';

enum TriggerTextButtonState {
  normal,
  selected,
}

class TriggerTextStateButton extends StatefulWidget {
  final String title;
  final ValueChanged<ValueNotifier<TriggerTextButtonState>> onPressed;
  final TextStyle textStyle;
  final TextStyle selectStyle;
  final ValueNotifier<TriggerTextButtonState>? controller;

  TriggerTextStateButton({
    required this.title,
    required this.onPressed,
    this.textStyle = const TextStyle(
      color: Color(0xFF676773),
      fontSize: 14,
      fontWeight: FontWeight.normal,
    ),
    this.selectStyle = const TextStyle(
      color: Color(0xFF00B377),
      fontSize: 14,
      fontWeight: FontWeight.w500,
    ),
    this.controller,
  });

  @override
  State<StatefulWidget> createState() {
    return _TriggerTextStateButtonState();
  }
}

class _TriggerTextStateButtonState extends State<TriggerTextStateButton> {
  late ValueNotifier<TriggerTextButtonState> controller;
  @override
  void initState() {
    if (widget.controller != null) {
      this.controller = widget.controller!;
    } else {
      this.controller = ValueNotifier(TriggerTextButtonState.normal);
    }
    super.initState();
  }

  @override
  void dispose() {
    this.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (this.controller.value == TriggerTextButtonState.selected) {
          this.controller.value = TriggerTextButtonState.normal;
        } else {
          this.controller.value = TriggerTextButtonState.selected;
        }
        widget.onPressed(this.controller);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        child: ValueListenableBuilder(
          valueListenable: this.controller,
          builder:
              (BuildContext context, TriggerTextButtonState value, Widget? child) {
            return Text(
              widget.title,
              style: value == TriggerTextButtonState.selected
                  ? widget.selectStyle
                  : widget.textStyle,
            );
          },
        ),
      ),
    );
  }
}
