import 'package:flutter/material.dart';

enum TextButtonState {
  normal,
  selected,
}

class TextStateButton extends StatefulWidget {
  final String title;
  final ValueChanged<ValueNotifier<TextButtonState>> onPressed;
  final TextStyle textStyle;
  final TextStyle selectStyle;
  final ValueNotifier<TextButtonState>? controller;

  TextStateButton({
    required this.title,
    required this.onPressed,
    this.textStyle = const TextStyle(
      color: Color(0xFF676773),
      fontSize: 14,
      fontWeight: FontWeight.normal,
    ),
    this.selectStyle = const TextStyle(
      color: Color(0xFF00B377),
      fontSize: 14,
      fontWeight: FontWeight.w500,
    ),
    this.controller,
  });

  @override
  State<StatefulWidget> createState() {
    return _TextStateButtonState();
  }
}

class _TextStateButtonState extends State<TextStateButton> {
  late ValueNotifier<TextButtonState> controller;
  @override
  void initState() {
    if (widget.controller != null) {
      this.controller = widget.controller!;
    } else {
      this.controller = ValueNotifier(TextButtonState.normal);
    }
    super.initState();
  }

  @override
  void dispose() {
    this.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this.controller.value = TextButtonState.selected;
        widget.onPressed(this.controller);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        child: ValueListenableBuilder(
          valueListenable: this.controller,
          builder:
              (BuildContext context, TextButtonState value, Widget? child) {
            return Text(
              widget.title,
              style: value == TextButtonState.selected
                  ? widget.selectStyle
                  : widget.textStyle,
            );
          },
        ),
      ),
    );
  }
}
