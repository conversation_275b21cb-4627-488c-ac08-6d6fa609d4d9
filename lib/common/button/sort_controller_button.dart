import 'package:flutter/material.dart';
import 'dart:math' as math;

enum SortControllerButtonState {
  // 正常
  normal,
  // 降序
  desc,
  // 升序
  asc,
}

typedef SortButtonChangeState = void Function(
  dynamic key,
  ValueNotifier<SortControllerButtonState> controller,
);

class SortControllerButton extends StatefulWidget {
  final String name;
  final dynamic sortKey;
  final SortControllerButtonState defaultState;
  final ValueNotifier<SortControllerButtonState>? controller;
  final SortButtonChangeState onPressed;

  SortControllerButton({
    required this.name,
    required this.sortKey,
    required this.onPressed,
    this.defaultState = SortControllerButtonState.normal,
    this.controller,
  });

  @override
  State<StatefulWidget> createState() {
    return _SortControllerButtonState();
  }
}

class _SortControllerButtonState extends State<SortControllerButton> {
  late ValueNotifier<SortControllerButtonState> controller;

  @override
  void initState() {
    if (widget.controller == null) {
      this.controller = ValueNotifier(SortControllerButtonState.normal);
    } else {
      this.controller = widget.controller!;
    }
    super.initState();
  }

  @override
  void dispose() {
    this.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (this.controller.value != SortControllerButtonState.desc) {
          this.controller.value = SortControllerButtonState.desc;
        } else {
          this.controller.value = SortControllerButtonState.asc;
        }
        widget.onPressed(widget.sortKey, this.controller);
      },
      child: Container(
        child: ValueListenableBuilder(
          valueListenable: this.controller,
          builder: (BuildContext context, SortControllerButtonState value,
              Widget? child) {
            return Row(
              children: [
                Text(
                  widget.name,
                  style: TextStyle(
                    color: getTextColor(value),
                    fontSize: 14,
                    fontWeight: getTextWidght(value),
                  ),
                ),
                SizedBox(width: 5),
                getIconImage(value),
              ],
            );
          },
        ),
      ),
    );
  }

  Color getTextColor(SortControllerButtonState value) {
    return value == SortControllerButtonState.normal
        ? Color(0xFF676773)
        : Color(0xFF00B377);
  }

  FontWeight getTextWidght(SortControllerButtonState value) {
    return value == SortControllerButtonState.normal
        ? FontWeight.normal
        : FontWeight.w500;
  }

  Widget getIconImage(SortControllerButtonState value) {
    String topImagePath = value == SortControllerButtonState.asc
        ? 'assets/images/commodity/sort_controller_button_selected.png'
        : 'assets/images/commodity/sort_controller_button_normal.png';
    String bottomImagePath = value == SortControllerButtonState.desc
        ? 'assets/images/commodity/sort_controller_button_selected.png'
        : 'assets/images/commodity/sort_controller_button_normal.png';

    Widget topImage = Transform.rotate(
      angle: math.pi,
      child: Image.asset(
        topImagePath,
        width: 6,
        height: 3,
      ),
    );
    Widget bottomImage = Image.asset(
      bottomImagePath,
      width: 6,
      height: 3,
    );
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        topImage,
        SizedBox(height: 1),
        bottomImage,
      ],
    );
  }
}
