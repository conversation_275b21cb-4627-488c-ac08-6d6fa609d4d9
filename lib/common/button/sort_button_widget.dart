import 'package:flutter/material.dart';

enum SortButtonWidgetType {
  // 正常
  normal,
  // 降序
  desc,
  // 升序
  asce
}

// 排序widget
class SortButtonWidget extends StatefulWidget {
  const SortButtonWidget({
    // 文字内容
    this.title,
    // 是否选中
    this.isSelected = false,
    // 选中文字颜色
    this.selectedColor = const Color(0xffFF292933),
    // 非选中文字颜色
    this.unSelectedColor = const Color(0xff676773),
    // 正常状态图片
    this.normalImage = const Image(
        image: AssetImage("assets/images/order/client_sort_normal.png"),
        width: 5,
        height: 9),
    // 升序状态图片
    this.asceImage = const Image(
        image: AssetImage("assets/images/order/client_sort_asec.png"),
        width: 5,
        height: 9),
    // 降序状态图片
    this.descImage = const Image(
        image: AssetImage("assets/images/order/client_sort_decs.png"),
        width: 5,
        height: 9),
    // 按钮点击事件
    this.onPressed,
    // 按钮排序状态
    this.type = SortButtonWidgetType.normal,
    // 是否需要图片
    this.isNeedImage = true,
  });
  final String? title;
  final Color selectedColor;
  final Color unSelectedColor;
  final bool isSelected;
  final Image normalImage;
  final Image descImage;
  final Image asceImage;
  final VoidCallback? onPressed;
  final SortButtonWidgetType type;
  final bool isNeedImage;
  @override
  _SortButtonWidgetState createState() => _SortButtonWidgetState();
}

class _SortButtonWidgetState extends State<SortButtonWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: GestureDetector(
        onTap: widget.onPressed,
        child: _contentWidget(),
      ),
    );
  }

  Widget _contentWidget() {
    if (widget.isNeedImage) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _sortText(),
          SizedBox(width: 4),
          _sortImage(),
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _sortText(),
        ],
      );
    }
  }

  Text _sortText() {
    return Text(
      widget.title??"",
      style: TextStyle(
        color:
            widget.isSelected ? widget.selectedColor : widget.unSelectedColor,
        fontSize: 14.0,
        fontWeight: widget.isSelected ? FontWeight.w600 : FontWeight.w300,
        inherit: false,
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  Image _sortImage() {
    switch (widget.type) {
      case SortButtonWidgetType.normal:
        return widget.normalImage;
        break;
      case SortButtonWidgetType.asce:
        return widget.asceImage;
        break;
      case SortButtonWidgetType.desc:
        return widget.descImage;
        break;
      default:
        return widget.normalImage;
        break;
    }
  }
}
