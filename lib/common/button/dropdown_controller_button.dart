import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class DropControllerButton extends StatefulWidget {
  DropControllerButton({
    Key? key,
    required this.title,
    this.selectedText,
    this.controller,
    required this.onPressed,
    this.normalStyle = const TextStyle(color: Color(0xFF292933), fontSize: 14),
    this.selectedStyle =
        const TextStyle(color: Color(0xFF00B377), fontSize: 14),
    this.normalImage = const Image(
        image: AssetImage("assets/images/commodity/commodity_normal_drop.png"),
        width: 7,
        height: 4),
    this.selectImage = const Image(
        image: AssetImage("assets/images/commodity/commodity_select_drop.png"),
        width: 7,
        height: 4),
    this.maxTextWidth = 75.0,
    this.isShowImage = true,
  }) : super(key: key);

  final ValueChanged<DropButtonController> onPressed;
  final String title;
  final String? selectedText;
  final TextStyle normalStyle;
  final TextStyle selectedStyle;
  final Image normalImage;
  final Image selectImage;
  final DropButtonController? controller;
  final double maxTextWidth;
  final bool isShowImage;

  @override
  State<StatefulWidget> createState() {
    return _DropControllerButtonState();
  }
}

class _DropControllerButtonState extends State<DropControllerButton> {
  late DropButtonController controller;

  @override
  void initState() {
    if (widget.controller == null) {
      controller = DropButtonController(
        model: DropButtonModel(
          normalText: widget.title,
          selectText: widget.selectedText,
        ),
      );
    } else {
      controller = widget.controller!;
    }
    super.initState();
  }

  @override
  void dispose() {
    this.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        if (controller._model.isOpen != true) {
          widget.onPressed(controller);
        }
        controller.setIsOpen(!controller._model.isOpen);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: 40,
        alignment: Alignment.center,
        child: ValueListenableBuilder(
          valueListenable: this.controller,
          builder:
              (BuildContext context, DropButtonModel model, Widget? child) {
            return Container(
              alignment: Alignment.center,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    padding: EdgeInsets.only(right: 4),
                    constraints: BoxConstraints(
                      maxWidth: this.widget.maxTextWidth,
                    ),
                    child: Text(
                      model.showText,
                      style: model.isSelected || model.isOpen
                          ? widget.selectedStyle
                          : widget.normalStyle,
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                    ),
                  ),
                  imageWidget(),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  Widget imageWidget() {
    if (!this.widget.isShowImage) {
      return Container();
    }
    if (controller._model.isOpen) {
      return Transform.rotate(
        angle: math.pi,
        child: widget.selectImage,
      );
    } else if (controller._model.isSelected) {
      return widget.selectImage;
    }
    return widget.normalImage;
  }
}

class DropButtonModel {
  String normalText;
  String? selectText;
  bool isOpen;
  DropButtonModel({
    this.normalText = "",
    this.selectText,
    this.isOpen = false,
  });

  bool get isSelected => this.selectText != null;

  String get showText {
    if (this.selectText != null) {
      return this.selectText!;
    }
    return this.normalText;
  }
}

class DropButtonController extends ChangeNotifier
    implements ValueListenable<DropButtonModel> {
  DropButtonModel _model = DropButtonModel();

  DropButtonController({required DropButtonModel model}) {
    _model = model;
  }

  @override
  DropButtonModel get value => _model;

  void setSelectText(String? selectText) {
    _model.selectText = selectText;
    notifyListeners();
  }

  void setIsOpen(bool isOpen) {
    _model.isOpen = isOpen;
    notifyListeners();
  }
}
