import 'package:flutter/material.dart';

enum BackgroundButtonState {
  normal,
  selected,
}

typedef BackgroundButtonPressed = void Function(
    ValueNotifier<BackgroundButtonState> controller, dynamic option);

class BackgroundStateButton extends StatefulWidget {
  final String title;
  final BackgroundButtonPressed onPressed;
  final TextStyle textStyle;
  final TextStyle selectStyle;
  final Color normalColor;
  final Color selectColor;
  final BoxDecoration? normalDecoration;
  final BoxDecoration? selectDecoration;
  final ValueNotifier<BackgroundButtonState>? controller;
  final EdgeInsets panding;
  final Alignment? alignment;
  final dynamic option;

  BackgroundStateButton({
    required this.title,
    required this.onPressed,
    this.textStyle = const TextStyle(
      color: Color(0xFF676773),
      fontSize: 14,
      fontWeight: FontWeight.normal,
    ),
    this.selectStyle = const TextStyle(
      color: Color(0xFF00B377),
      fontSize: 14,
      fontWeight: FontWeight.w500,
    ),
    this.normalColor = const Color(0xFFF7F7F8),
    this.selectColor = const Color(0xFFE5F7F1),
    this.normalDecoration,
    this.selectDecoration,
    this.panding = EdgeInsets.zero,
    this.controller,
    this.alignment,
    this.option,
  });

  @override
  State<StatefulWidget> createState() {
    return _BackgroundStateButtonState();
  }
}

class _BackgroundStateButtonState extends State<BackgroundStateButton> {
  late ValueNotifier<BackgroundButtonState> controller;
  @override
  void initState() {
    if (widget.controller != null) {
      this.controller = widget.controller!;
    } else {
      this.controller = ValueNotifier(BackgroundButtonState.normal);
    }
    super.initState();
  }

  @override
  void dispose() {
    this.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        this.controller.value = BackgroundButtonState.selected;
        widget.onPressed(this.controller, widget.option);
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        child: ValueListenableBuilder(
          valueListenable: this.controller,
          builder: (BuildContext context, BackgroundButtonState value,
              Widget? child) {
            return Container(
              decoration: this.getDecoration(value),
              alignment: widget.alignment,
              padding: widget.panding,
              child: Text(
                widget.title,
                style: value == BackgroundButtonState.selected
                    ? widget.selectStyle
                    : widget.textStyle,
              ),
            );
          },
        ),
      ),
    );
  }

  BoxDecoration? getDecoration(BackgroundButtonState value) {
    if (widget.normalDecoration != null && widget.selectDecoration != null) {
      return value == BackgroundButtonState.selected
          ? widget.selectDecoration
          : widget.normalDecoration;
    } else {
      return BoxDecoration(
        color: value == BackgroundButtonState.selected
            ? widget.selectColor
            : widget.normalColor,
      );
    }
  }
}
