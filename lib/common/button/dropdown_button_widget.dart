import 'package:flutter/material.dart';

enum DropButtonWidgetType {
  // 正常
  normal,
  // 下拉
  desc,
  // 默认选中
  asce
}

// 排序widget
class DropButtonWidget extends StatefulWidget {
  const DropButtonWidget({
    // 文字内容
    this.title,
    // 是否选中
    this.isSelected = false,
    // 选中文字颜色
    this.selectedColor = const Color(0xFF292933),
    // 非选中文字颜色
    this.unSelectedColor = const Color(0xFF676773),
    // 正常状态图片
    this.normalImage = const Image(
        image: AssetImage("assets/images/task/task_pull_icon.png"),
        width: 15,
        height: 19),
    // 升序状态图片
    this.asceImage = const Image(
        image: AssetImage("assets/images/task/task_up_icon.png"),
        width: 15,
        height: 19),
    // 下拉状态图片
    this.descImage = const Image(
        image: AssetImage("assets/images/task/task_up_icon.png"),
        width: 15,
        height:19),
    // 按钮点击事件
    this.onPressed,
    // 按钮排序状态
    this.type = DropButtonWidgetType.normal,
    // 是否需要图片
    this.isNeedImage = true,
  });
  final String? title;
  final Color selectedColor;
  final Color unSelectedColor;
  final bool isSelected;
  final Image normalImage;
  final Image descImage;
  final Image asceImage;
  final VoidCallback? onPressed;
  final DropButtonWidgetType type;
  final bool isNeedImage;
  @override
  _DropButtonWidgetState createState() => _DropButtonWidgetState();
}

class _DropButtonWidgetState extends State<DropButtonWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: GestureDetector(
        onTap: widget.onPressed,
        child: _contentWidget(),
      ),
    );
  }

  Widget _contentWidget() {
    if (widget.isNeedImage) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _sortText(),
          SizedBox(width: 4),
          Container(
            padding: EdgeInsets.only(top: 2),
            child: _sortImage(),
            ),
          
        ],
      );
    } else {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _sortText(),
        ],
      );
    }
  }

  Text _sortText() {
    return Text(
      widget.title??"",
      style: TextStyle(
        color:
            widget.isSelected ? widget.selectedColor : widget.unSelectedColor,
        fontSize: 14.0,
        fontWeight: widget.isSelected ? FontWeight.w500 : FontWeight.w300,
        inherit: false,
      ),
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  Image _sortImage() {
    switch (widget.type) {
      case DropButtonWidgetType.normal:
        return widget.normalImage;
        break;
      case DropButtonWidgetType.asce:
        return widget.asceImage;
        break;
      case DropButtonWidgetType.desc:
        return widget.descImage;
        break;
      default:
        return widget.normalImage;
        break;
    }
  }
}