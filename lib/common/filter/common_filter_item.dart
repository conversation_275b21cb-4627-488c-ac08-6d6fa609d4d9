import 'package:flutter/material.dart';

class CommonFilterItemEntry {
  String text;
  String code;

  CommonFilterItemEntry({required this.text, required this.code});
}

// ignore: must_be_immutable
class CommonFilterItem extends StatefulWidget {
  final Widget titleWidget;
  final List<CommonFilterItemEntry> contents;

  /// 当前已选中的id
  List<String> selectedIds;

  /// 默认选中
  final String? defaultId;

  /// 是否支持多选
  final bool isAllowMultipleSelection;

  /// 是否支持多选情况下与默认选中互斥
  final bool isMutexByDefault;

  /// 是否可不选中
  final bool isAllowClean;

  final ValueChanged<List<String>> changeSelected;

  CommonFilterItem({
    required this.titleWidget,
    this.contents = const [],
    this.selectedIds = const [],
    this.defaultId,
    this.isAllowMultipleSelection = false,
    this.isMutexByDefault = false,
    this.isAllowClean = true,
    required this.changeSelected,
  });

  @override
  State<StatefulWidget> createState() {
    return CommonFilterItemState();
  }
}

class CommonFilterItemState extends State<CommonFilterItem> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void setState(fn) {
    super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    /// 设置初始值
    if (widget.selectedIds.length == 0 && widget.defaultId != null) {
      widget.selectedIds = [widget.defaultId!];
    }
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 10),
            child: widget.titleWidget,
          ),
          Container(
            child: Wrap(
              spacing: 10,
              runSpacing: 10,
              children: this.optionItems(),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> optionItems() {
    return widget.contents
        .map(
          (e) => CommonFilterSelectItem(
            content: e.text,
            id: e.code,
            isSelected: widget.selectedIds.contains(e.code),
            selectCall: (String? value) {
              if (value != null) {
                this.selectedAction(value);
              }
            },
            unSelectCall: (String? value) {
              if (value != null) {
                this.unSelectedAction(value);
              }
            },
          ),
        )
        .toList();
  }

  void selectedAction(String code) {
    /// 是否允许多选
    if (widget.isAllowMultipleSelection) {
      /// 默认选项互斥
      if (widget.isMutexByDefault && code == widget.defaultId) {
        widget.selectedIds = [code];
      } else {
        if (widget.selectedIds.contains(widget.defaultId)) {
          widget.selectedIds.remove(widget.defaultId);
        }
        widget.selectedIds.add(code);
      }
    } else {
      widget.selectedIds = [code];
    }
    this.callChange();
    setState(() {});
  }

  void unSelectedAction(String code) {
    if (widget.selectedIds.contains(code)) {
      if (widget.isAllowClean) {
        widget.selectedIds.remove(code);
      } else {
        if (code != widget.defaultId) {
          widget.selectedIds.remove(code);
          if (widget.selectedIds.length == 0 && widget.defaultId != null) {
            widget.selectedIds = [widget.defaultId!];
          }
        }
      }
    }
    this.callChange();
    setState(() {});
  }

  void callChange() {
    widget.changeSelected(widget.selectedIds);
  }
}

class CommonFilterSelectItem extends StatelessWidget {
  final String? content;
  final String? id;
  final bool isSelected;
  final ValueChanged<String?> selectCall;
  final ValueChanged<String?> unSelectCall;

  CommonFilterSelectItem({
    this.content,
    this.id,
    this.isSelected = false,
    required this.selectCall,
    required this.unSelectCall,
  });

  @override
  Widget build(Object context) {
    return GestureDetector(
      onTap: () {
        if (this.isSelected) {
          this.unSelectCall(this.id);
        } else {
          this.selectCall(this.id);
        }
      },
      child: Container(
        padding: EdgeInsets.fromLTRB(12, 5, 12, 5),
        decoration: BoxDecoration(
          color: this.isSelected ? Color(0xFFFFFFFF) : Color(0xFFF0F0F0),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: this.isSelected ? Color(0xFF35C561) : Color(0xFFF0F0F0),
            width: 1,
          ),
        ),
        constraints: BoxConstraints(
          minWidth: 63,
          minHeight: 30,
        ),
        child: Text(
          this.content ?? "",
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 13,
            height: 1.1,
            fontWeight: this.isSelected ? FontWeight.w500 : FontWeight.normal,
            color: this.isSelected ? Color(0xFF35C561) : Color(0xFF666666),
          ),
          strutStyle: StrutStyle(leading: 0),
        ),
      ),
    );
  }
}
