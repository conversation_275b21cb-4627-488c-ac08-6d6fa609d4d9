import 'package:flutter/material.dart';

class CommonSelectItem extends StatelessWidget {
  final Widget titleWidget;
  final Widget contentWidget;
  final VoidCallback? clickAction;
  CommonSelectItem({
    required this.titleWidget,
    required this.contentWidget,
    this.clickAction,
  });
  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(minHeight: 50),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              this.titleWidget,
              Expanded(
                child: GestureDetector(
                  behavior: HitTestBehavior.opaque,
                  onTap: this.clickAction,
                  child: Container(
                    margin: EdgeInsets.only(right: 5),
                    child: this.contentWidget,
                  ),
                ),
              ),
              GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: this.clickAction,
                child: Image.asset(
                  'assets/images/base/common_filter_right_icon.png',
                  width: 7,
                  height: 12,
                ),
              ),
            ],
          ),
          Divider(
            height: 0.5,
            color: Color(0xFFE1E1E5),
          )
        ],
      ),
    );
  }
}
