import 'package:XyyBeanSproutsFlutter/customer/private/data/ybm_customer_private_model.dart';
import 'package:flutter/cupertino.dart';

import 'common_alert_dialog.dart';

/// 检测资质弹框
class CheckLicenseDialog {

  void showCheckLicenseDialog(
    BuildContext context,
    int licenseValidateMust,
    int licenseValidateIssue,
    bool isFreeze, {
    VoidCallback? continueVisitCallback,
  }) {
    List<CommonAlertAction> actions = [];
    CommonAlertAction cancelAction = CommonAlertAction(
      title: "取消拜访",
    );
    CommonAlertAction continueActionGray = CommonAlertAction(
      title: "继续拜访",
      style: CommonAlertActionStyle.cancle,
      onPressed: continueVisitCallback,
    );
    CommonAlertAction continueActionGreen = CommonAlertAction(
      title: "继续拜访",
      onPressed: continueVisitCallback,
    );
    String dialogContent = "";
    var licenseStatus = getLicenseValidateStatus(licenseValidateMust, licenseValidateIssue);
    if (isFreeze) {
      dialogContent = '该客户为已冻结状态客户，拜访已冻结客户将会判定为无效拜访。';
      actions.add(continueActionGray);
      actions.add(cancelAction);
    } else {
      switch (licenseStatus) {
        //必填临期
        case LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_ADVENT:
          dialogContent = '客户部分资质临近过期，为保证客户顺利采购，请先更新客户资质。';
          actions.add(continueActionGreen);
          break;
        //非必临期
        case LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_ADVENT:
          dialogContent = '客户部分资质临近过期，为保证客户顺利采购，请先更新客户资质。';
          actions.add(continueActionGreen);
          break;
        //必填过期
        case LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_EXPIRE:
          dialogContent = '客户必填资质已经过期，请先更新客户资质，拜访必填资质过期客户将被判定为无效拜访。';
          actions.add(continueActionGray);
          actions.add(cancelAction);
          break;
        //非必过期
        case LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_EXPIRE:
          dialogContent = '客户非必填资质已经过期，会导致部分商品订单卡单。为保证客户顺利采购，请先更新客户资质。';
          actions.add(continueActionGreen);
          break;
      }
    }
    if (dialogContent == "" && continueVisitCallback != null) {
        continueVisitCallback();
        return;
    }

    showCommonAlert(
      context: context,
      title: "提示",
      content: dialogContent,
      actions: actions,
      barrierDismissible: true,
    );
  }

  /// 查看当前资质状态
  int getLicenseValidateStatus(int licenseValidateMust, int licenseValidateIssue) {
    if (licenseValidateMust == LICENSE_VALIDATE_STATUS_EXPIRE) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_EXPIRE;
    } else if(licenseValidateIssue == LICENSE_VALIDATE_STATUS_EXPIRE){
      return LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_EXPIRE;
    } else if (licenseValidateMust == LICENSE_VALIDATE_STATUS_ADVENT) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_REQUIRED_ADVENT;
    } else if (licenseValidateIssue == LICENSE_VALIDATE_STATUS_ADVENT) {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NO_REQUIRED_ADVENT;
    } else {
      return LICENSE_VALIDATE_STATUS_COMPOSE_NORMAL;
    }
  }
}
