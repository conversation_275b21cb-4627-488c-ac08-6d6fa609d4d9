import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class WidgetLoadingController extends ValueNotifier<bool> {
  WidgetLoadingController(bool value) : super(value);
}

class WidgetLoading extends StatefulWidget {
  final Widget child;

  final WidgetLoadingController? loadingController;

  WidgetLoading({
    required this.child,
    this.loadingController,
  });

  @override
  State<StatefulWidget> createState() {
    return _WidgetLoadingState();
  }
}

class _WidgetLoadingState extends State<WidgetLoading> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.center,
      children: [
        widget.child,
        ValueListenableBuilder<bool>(
          valueListenable: widget.loadingController ?? ValueNotifier(false),
          builder: (context, value, child) {
            return Visibility(
              visible: value,
              child: SpinKitFadingCircle(),
            );
          },
        )
      ],
    );
  }
}

class SpinKitFadingCircle extends StatefulWidget {
  const SpinKitFadingCircle({
    Key? key,
    this.color = Colors.white,
    this.size = 30.0,
    this.duration = const Duration(milliseconds: 1200),
    this.controller,
  }) : super(key: key);

  final Color? color;
  final double size;
  final Duration duration;
  final AnimationController? controller;

  @override
  _SpinKitFadingCircleState createState() => _SpinKitFadingCircleState();
}

class _SpinKitFadingCircleState extends State<SpinKitFadingCircle>
    with SingleTickerProviderStateMixin {
  final List<double> delays = [
    .0,
    -1.1,
    -1.0,
    -0.9,
    -0.8,
    -0.7,
    -0.6,
    -0.5,
    -0.4,
    -0.3,
    -0.2,
    -0.1
  ];
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();

    _controller = (widget.controller ??
        AnimationController(vsync: this, duration: widget.duration))
      ..repeat();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Color(0x99000000),
        borderRadius: BorderRadius.circular(10),
      ),
      child: Center(
        child: SizedBox.fromSize(
          size: Size.square(widget.size),
          child: Stack(
            children: List.generate(12, (i) {
              final _position = widget.size * .5;
              return Positioned.fill(
                left: _position,
                top: _position,
                child: Transform(
                  transform: Matrix4.rotationZ(30.0 * i * 0.0174533),
                  child: Align(
                    alignment: Alignment.center,
                    child: FadeTransition(
                      opacity:
                          DelayTween(begin: 0.0, end: 1.0, delay: delays[i])
                              .animate(_controller),
                      child: SizedBox.fromSize(
                          size: Size.square(widget.size * 0.15),
                          child: _itemBuilder(i)),
                    ),
                  ),
                ),
              );
            }),
          ),
        ),
      ),
    );
  }

  Widget _itemBuilder(int index) => DecoratedBox(
      decoration: BoxDecoration(color: widget.color, shape: BoxShape.circle));
}
