import 'package:flutter/material.dart';

class PopupToolBar extends StatelessWidget {
  final String? middleTitle;
  final TextStyle? middleStyle;
  final String? leftTitle;
  final TextStyle leftStyle;
  final String? rightTitle;
  final TextStyle rightStyle;
  final VoidCallback? leftAction;
  final VoidCallback? rightAction;

  PopupToolBar({
    this.middleTitle,
    this.middleStyle = const TextStyle(
      color: Color(0xFF999999),
      fontSize: 14,
      fontWeight: FontWeight.w500,
    ),
    this.leftTitle,
    this.leftStyle = const TextStyle(
      color: Color(0xFF333333),
      fontSize: 15,
      fontWeight: FontWeight.w500,
    ),
    this.leftAction,
    this.rightTitle,
    this.rightStyle = const TextStyle(
      color: Color(0xFF00B377),
      fontSize: 15,
      fontWeight: FontWeight.w500,
    ),
    this.rightAction,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFF),
      height: 50,
      padding: EdgeInsets.fromLTRB(10, 0, 15, 0),
      child: Row(
        children: [
          GestureDetector(
            child: Text(this.leftTitle ?? "取消", style: this.leftStyle),
            onTap: () {
              if (this.leftAction != null) {
                this.leftAction!();
              }
            },
          ),
          Spacer(
            flex: 1,
          ),
          Text(this.middleTitle ?? "", style: this.middleStyle),
          Spacer(
            flex: 1,
          ),
          GestureDetector(
            child: Text(this.rightTitle ?? "确定", style: this.rightStyle),
            onTap: () {
              if (this.rightAction != null) {
                this.rightAction!();
              }
            },
          ),
        ],
      ),
    );
  }
}
