import 'package:flutter/material.dart';

class AnimationImages extends StatefulWidget {
  final List<String> imagePaths;
  final int durationMillisecond;
  final double width;
  final double height;

  AnimationImages({
    required this.imagePaths,
    this.durationMillisecond = 500,
    required this.width,
    required this.height,
  }) : assert(imagePaths.length > 0);

  @override
  State<StatefulWidget> createState() {
    return _AnimationImagesState();
  }
}

class _AnimationImagesState extends State<AnimationImages>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<int> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: Duration(
        milliseconds: widget.durationMillisecond,
      ),
    );
    Future.delayed(Duration(milliseconds: 200), () {
      if (mounted) {
        _controller.repeat();
      }
    });
    _animation = IntTween(begin: 0, end: widget.imagePaths.length - 1)
        .animate(_controller);
  }

  @override
  void dispose() {
    _controller.stop();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (BuildContext context, Widget? child) {
        String imagePath = widget.imagePaths[_animation.value];
        return Image.asset(
          imagePath,
          width: widget.width,
          height: widget.height,
        );
      },
    );
  }
}
