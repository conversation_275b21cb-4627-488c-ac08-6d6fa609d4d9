import 'package:XyyBeanSproutsFlutter/goods/widgets/easy_popup.dart';
import 'package:flutter/material.dart';

typedef CommonFilterBuilder = EasyPopupChild Function(
  double topDistance,
);

typedef CommonFilterItemChanged = void Function(
  String name,
  Map<String, dynamic> params,
);

Future showCommonFilterPopup({
  required BuildContext context,
  required CommonFilterBuilder pageBuilder,
  GlobalKey? key,
}) {
  double topDistance = 0;
  if (key != null) {
    RenderBox? renderBox = key.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      Offset offset = renderBox.localToGlobal(Offset(0, renderBox.size.height));
      topDistance = offset.dy.ceil() * 1.0;
    }
  }
  return EasyPopup.show(
    context,
    pageBuilder(topDistance),
    offsetLT: Offset(0, topDistance),
    outsideTouchCancelable: true,
    duration: Duration(milliseconds: 300),
  );
}

// Bottom Button
mixin CommonFilterPopupBottomButton<T extends StatefulWidget> on State<T> {
  void resetAction();
  void determineAction();

  Widget bottomButton() {
    return Container(
      height: 50,
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: resetAction,
              behavior: HitTestBehavior.opaque,
              child: Column(
                children: [
                  Divider(
                    color: Color(0xFFF7F7F8),
                    height: 0.5,
                    thickness: 0.5,
                  ),
                  Expanded(
                    child: Container(
                      color: Color(0xFFFFFFFF),
                      child: Center(
                        child: Text(
                          '重置',
                          style: TextStyle(
                            color: Color(0xFF292933),
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            child: GestureDetector(
              onTap: determineAction,
              behavior: HitTestBehavior.opaque,
              child: Container(
                color: Color(0xFF00B377),
                child: Center(
                  child: Text(
                    '确定',
                    style: TextStyle(
                      color: Color(0xFFFFFFFF),
                      fontSize: 16,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

@optionalTypeArgs
mixin CommonFilterPopupAnimation<T extends StatefulWidget>
    on SingleTickerProviderStateMixin<T> {
  late Animation<Offset> animation;
  late AnimationController controller;

  @override
  void dispose() {
    this.controller.dispose();
    super.dispose();
  }

  void initalAnimation() {
    this.controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
      reverseDuration: Duration(milliseconds: 300),
    );

    // 动画差值器
    CurvedAnimation curve =
        CurvedAnimation(parent: this.controller, curve: Curves.easeInOut);

    this.animation =
        Tween<Offset>(begin: Offset(0, -1), end: Offset.zero).animate(curve);
    this.controller.forward();
  }
}

abstract class CommonFilterPopupBase extends StatefulWidget
    with EasyPopupChild {
  late final VoidCallback _actionForDismiss;

  final double distance;

  @mustCallSuper
  CommonFilterPopupBase({required this.distance});

  @override
  CommonFilterPopupBaseState createState();

  @override
  dismiss() {
    this._actionForDismiss();
  }
}

abstract class CommonFilterPopupBaseState<T extends CommonFilterPopupBase>
    extends State<T>
    with SingleTickerProviderStateMixin, CommonFilterPopupAnimation {
  @override
  void initState() {
    this.initalAnimation();
    widget._actionForDismiss = () {
      this.controller.reverse();
    };
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(top: widget.distance),
      child: ClipRect(
        child: SlideTransition(
          position: this.animation,
          child: buildContent(),
        ),
      ),
    );
  }

  void closePopup() {
    EasyPopup.pop(context);
  }

  Widget buildContent();
}
