import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/utils/qt_analytics_util.dart';

/// 带有QT埋点功能的基础页面类
/// 自动处理页面的进入和离开埋点
abstract class BasePageWithAnalytics extends StatefulWidget {
  const BasePageWithAnalytics({Key? key}) : super(key: key);
  
  /// 获取页面名称，子类必须实现
  String getPageName();
  
  /// 获取页面属性，子类可以重写
  Map<String, dynamic>? getPageProperties() => null;
  
  /// 是否跳过页面统计，子类可以重写
  bool shouldSkipPageTracking() => false;
}

/// 带有QT埋点功能的基础页面状态类
abstract class BasePageWithAnalyticsState<T extends BasePageWithAnalytics> extends State<T> {
  
  @override
  void initState() {
    super.initState();
    
    // 如果不跳过页面统计，则进行页面开始埋点
    if (!widget.shouldSkipPageTracking()) {
      WidgetsBinding.instance?.addPostFrameCallback((_) {
        _trackPageStart();
      });
    }
  }
  
  @override
  void dispose() {
    // 如果不跳过页面统计，则进行页面结束埋点
    if (!widget.shouldSkipPageTracking()) {
      _trackPageEnd();
    }
    super.dispose();
  }
  
  /// 页面开始埋点
  void _trackPageStart() {
    String pageName = widget.getPageName();
    
    // 页面开始统计
    QTAnalyticsUtil.trackPageStart(pageName);
    
    // 页面访问埋点
    QTAnalyticsUtil.trackPageView(pageName, extra: widget.getPageProperties());
    
    // 设置页面属性
    Map<String, dynamic>? properties = widget.getPageProperties();
    if (properties != null) {
      QTAnalyticsUtil.setPageProperty(pageName, properties);
    }
  }
  
  /// 页面结束埋点
  void _trackPageEnd() {
    String pageName = widget.getPageName();
    QTAnalyticsUtil.trackPageEnd(pageName);
  }
  
  /// 手动触发按钮点击埋点
  void trackButtonClick(String buttonName, {Map<String, dynamic>? extra}) {
    QTAnalyticsUtil.trackButtonClick(buttonName, widget.getPageName(), extra: extra);
  }
  
  /// 手动触发自定义事件埋点
  void trackCustomEvent(String event, {Map<String, dynamic>? properties}) {
    QTAnalyticsUtil.trackEventWithPage(event, widget.getPageName(), properties: properties);
  }
}

/// 使用示例的页面类
class ExamplePageWithAnalytics extends BasePageWithAnalytics {
  const ExamplePageWithAnalytics({Key? key}) : super(key: key);
  
  @override
  String getPageName() => "example_page";
  
  @override
  Map<String, dynamic>? getPageProperties() => {
    'page_type': 'example',
    'version': '1.0',
  };
  
  @override
  _ExamplePageWithAnalyticsState createState() => _ExamplePageWithAnalyticsState();
}

class _ExamplePageWithAnalyticsState extends BasePageWithAnalyticsState<ExamplePageWithAnalytics> {
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('示例页面'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text('这是一个带有QT埋点的示例页面'),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // 按钮点击埋点
                trackButtonClick('test_button', extra: {
                  'button_type': 'primary',
                });
              },
              child: Text('测试按钮'),
            ),
            SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // 自定义事件埋点
                trackCustomEvent('custom_action', properties: {
                  'action_type': 'test',
                  'timestamp': DateTime.now().millisecondsSinceEpoch,
                });
              },
              child: Text('自定义事件'),
            ),
          ],
        ),
      ),
    );
  }
}
