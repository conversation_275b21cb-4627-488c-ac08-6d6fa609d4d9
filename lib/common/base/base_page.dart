import 'dart:io';

import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:flutter/material.dart';

import '../../main.dart';
import 'base_function.dart';
import 'navigator_manager.dart';

abstract class BasePage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return initState();
  }

  BaseState initState();
}

abstract class BaseState<T extends StatefulWidget> extends State<T>
    with BaseFunction, WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  bool _onResumed = false; //页面展示标记
  bool _onPause = false; //页面暂停标记

  bool isSubPage() {
    return false;
  }

  @override
  void initState() {
    //工具类初始化
    initBaseCommon(this);
    if (!isSubPage()) {
      //页面栈注册
      NavigatorManager().addWidget(this);
    }
    //生命周期监听
    WidgetsBinding.instance?.addObserver(this);
    onCreate();
    super.initState();
    log('initState');
  }

  @override
  void deactivate() {
    log('deactivate');
    if (!isSubPage()) {
      //说明是被覆盖了
      if (NavigatorManager().isSecondTop(this)) {
        if (!_onPause) {
          onPause();
          _onPause = true;
        } else {
          onResume();
          _onPause = false;
        }
      } else if (NavigatorManager().isTopPage(this)) {
        if (!_onPause) {
          onPause();
        }
      }
    }
    super.deactivate();
  }

  @override
  Widget build(BuildContext context) {
    if (needKeepAlive()) {
      super.build(context);
    }
    if (!isSubPage()) {
      if (!_onResumed) {
        //说明是 初次加载
        if (NavigatorManager().isTopPage(this)) {
          _onResumed = true;
          onResume();
        }
      }
    }
    // print("guan,statusPadding:$statusPadding");
    if (Platform.isAndroid && statusPadding > 0) {
      return Padding(
        padding: EdgeInsets.only(top: statusPadding.toDouble()),
        child: Scaffold(
          appBar: getTitleBar(context),
          body: getBaseView(context),
          bottomNavigationBar: getBottomNavigationBar(context),
          backgroundColor: Colors.white,
        ),
      );
    } else {
      return Scaffold(
        appBar: getTitleBar(context),
        body: getBaseView(context),
        extendBodyBehindAppBar: isExtendBodyBehindAppBar(),
        bottomNavigationBar: getBottomNavigationBar(context),
        backgroundColor: Colors.white,
        resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      );
    }
  }

  bool get resizeToAvoidBottomInset => true;

  bool isExtendBodyBehindAppBar() {
    return false;
  }

  @override
  void didUpdateWidget(T oldWidget) {
    super.didUpdateWidget(oldWidget);
    compatTranslucentStatusBar();
  }

  @override
  void dispose() {
    log('dispose');
    onDestroy();
    WidgetsBinding.instance?.removeObserver(this);
    _onResumed = false;
    _onPause = false;

    if (!isSubPage()) {
      //把改页面 从 页面列表中 去除
      NavigatorManager().removeWidget(this);
      dismissLoadingDialog();
    }
    super.dispose();
  }

  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(getTitleName());
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    log('didChangeAppLifecycleState');
    if (!isSubPage()) {
      //此处可以拓展 是不是从前台回到后台
      if (state == AppLifecycleState.resumed) {
        //on resume
        if (NavigatorManager().isTopPage(this)) {
          onForeground();
          onResume();
        }
      } else if (state == AppLifecycleState.paused) {
        //on pause
        if (NavigatorManager().isTopPage(this)) {
          onBackground();
          onPause();
        }
      }
    }
    super.didChangeAppLifecycleState(state);
  }

  @override
  void didChangeDependencies() {
    log("didChangeDependencies");
    super.didChangeDependencies();
  }

  void onHideBySecondWidget() {
    if (NavigatorManager().isSecondTop(this)) {
      onPause();
    }
  }

  /// 页面标题名
  String getTitleName();

  /// 是否需要当前页面Push页面后状态保持
  bool needKeepAlive() => false;

  @override
  bool get wantKeepAlive => needKeepAlive();
}
