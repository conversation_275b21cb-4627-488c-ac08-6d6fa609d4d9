import 'package:XyyBeanSproutsFlutter/common/dialog/popup_toolbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

typedef PickerEnumerator<T> = String Function(T value);

Future<T?> showStringValuePickerView<T>({
  required BuildContext context,
  required List<T> source,
  String? defaultValue,
  required PickerEnumerator<T> sourceBuilder,
  ValueChanged<T>? selectedAction,
  String? middleTitle,
}) {
  return showCupertinoModalPopup(
    context: context,
    builder: (BuildContext context) {
      return Container(
        height: 266 + MediaQuery.of(context).viewInsets.bottom,
        child: StringValuePickerView<T>(
          source: source,
          defaultValue: defaultValue,
          sourceBuilder: sourceBuilder,
          selectedAction: selectedAction,
          middleTitle: middleTitle,
        ),
      );
    },
  );
}

class StringValuePickerView<T> extends StatefulWidget {
  final String? middleTitle;
  final List<T> source;
  final TextStyle? textStyle;
  final String? defaultValue;
  final PickerEnumerator<T> sourceBuilder;
  final ValueChanged<T>? selectedAction;

  StringValuePickerView({
    this.middleTitle,
    required this.source,
    this.textStyle = const TextStyle(
      color: Color(0xFF292933),
      fontSize: 18,
      fontWeight: FontWeight.normal,
    ),
    required this.sourceBuilder,
    this.defaultValue,
    this.selectedAction,
  }) : assert(source.length > 0);

  @override
  State<StatefulWidget> createState() {
    return _StringValuePickerViewState<T>();
  }
}

class _StringValuePickerViewState<T> extends State<StringValuePickerView<T>> {
  late List<String> stringSource;

  int selectValue = 0;

  @override
  void initState() {
    this.builderSource();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      child: Column(
        children: [
          PopupToolBar(
            middleTitle: widget.middleTitle,
            leftAction: () {
              Navigator.of(context).pop();
            },
            rightAction: () {
              if (widget.source.length > this.selectValue) {
                T currentValue = widget.source[selectValue];
                if (widget.selectedAction != null) {
                  widget.selectedAction!(currentValue);
                }
                Navigator.of(context).pop<T>(currentValue);
              } else {
                Navigator.of(context).pop();
              }
            },
          ),
          Expanded(
            child: CupertinoPicker(
              itemExtent: 40,
              scrollController: FixedExtentScrollController(
                initialItem: widget.defaultValue != null
                    ? this.stringSource.indexOf(widget.defaultValue!)
                    : 0,
              ),
              onSelectedItemChanged: (value) {
                this.selectValue = value;
              },
              children: this
                  .stringSource
                  .map((text) => Center(
                        child: Text(
                          text,
                          style: widget.textStyle,
                          textAlign: TextAlign.center,
                        ),
                      ))
                  .toList(),
              backgroundColor: Color(0xFFFFFFFF),
              selectionOverlay: CupertinoPickerDefaultSelectionOverlay(
                background: Colors.transparent,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void builderSource() {
    this.stringSource =
        widget.source.map<String>((e) => widget.sourceBuilder(e)).toList();
  }
}
