import 'package:XyyBeanSproutsFlutter/common/dialog/popup_toolbar.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

Future<String?> showTimeMSValuePickerView({
  required BuildContext context,
  String? title,
  String? defaultValue,
  ValueChanged<String>? selectedAction,
}) {
  return showCupertinoModalPopup(
    context: context,
    builder: (BuildContext context) {
      return Container(
        height: 266 + MediaQuery.of(context).viewInsets.bottom,
        child: TimeMSPickerView(
          title: title,
          defaultValue: defaultValue,
          selectedAction: selectedAction,
        ),
      );
    },
  );
}

class TimeMSPickerView extends StatefulWidget {
  final String? title;
  final String? defaultValue;
  final ValueChanged<String>? selectedAction;

  TimeMSPickerView({this.title, this.defaultValue, this.selectedAction});

  @override
  State<StatefulWidget> createState() {
    return TimeMSPickerViewState();
  }
}

class TimeMSPickerViewState extends State<TimeMSPickerView> {
  List<String> minuteSource = [];
  List<String> secondSource = [];

  String minuteValue = "0";
  String secondValue = "0";

  @override
  void initState() {
    super.initState();
    for (int i = 0; i <= 200; i++) {
      String number = i.toString();
      minuteSource.add(number);
      if (i < 60) {
        secondSource.add(number);
      }
    }

    if (widget.defaultValue != null) {
      int value = int.tryParse(widget.defaultValue ?? "0") ?? 0;
      this.minuteValue = (value ~/ 60).toString();
      this.secondValue = (value % 60).toString();
    }
  }

  String getSelectValue() {
    int minute = int.tryParse(this.minuteValue) ?? 0;
    int second = int.tryParse(this.secondValue) ?? 0;
    int value = minute * 60 + second;
    return value.toString();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      child: Column(
        children: [
          PopupToolBar(
            middleTitle: widget.title,
            leftAction: () {
              Navigator.of(context).pop();
            },
            rightAction: () {
              String selectValue = this.getSelectValue();
              if (widget.selectedAction != null) {
                widget.selectedAction!(selectValue);
              }
              Navigator.of(context).pop(selectValue);
            },
          ),
          Container(
            padding: EdgeInsets.only(left: 50, right: 50),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  flex: 1,
                  child: Container(
                    height: 216,
                    child: CupertinoPicker(
                      itemExtent: 40,
                      scrollController: FixedExtentScrollController(
                        initialItem: widget.defaultValue != null
                            ? this.minuteSource.indexOf(this.minuteValue)
                            : 0,
                      ),
                      onSelectedItemChanged: (value) {
                        this.minuteValue = this.minuteSource[value];
                      },
                      children: this
                          .minuteSource
                          .map((text) => Center(
                                child: Text(
                                  text,
                                  style: TextStyle(
                                    color: Color(0xFF292933),
                                    fontSize: 18,
                                    fontWeight: FontWeight.normal,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ))
                          .toList(),
                      looping: true,
                      backgroundColor: Color(0xFFFFFFFF),
                      selectionOverlay: CupertinoPickerDefaultSelectionOverlay(
                        background: Colors.transparent,
                      ),
                    ),
                  ),
                ),
                Text(
                  '分',
                  style: TextStyle(
                    color: Color(0xFF292933),
                    fontSize: 18,
                    fontWeight: FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
                Expanded(
                  flex: 1,
                  child: Container(
                    height: 216,
                    child: CupertinoPicker(
                      itemExtent: 40,
                      scrollController: FixedExtentScrollController(
                        initialItem: widget.defaultValue != null
                            ? this.secondSource.indexOf(this.secondValue)
                            : 0,
                      ),
                      onSelectedItemChanged: (value) {
                        this.secondValue = this.secondSource[value];
                      },
                      children: this
                          .secondSource
                          .map((text) => Center(
                                child: Text(
                                  text,
                                  style: TextStyle(
                                    color: Color(0xFF292933),
                                    fontSize: 18,
                                    fontWeight: FontWeight.normal,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ))
                          .toList(),
                      looping: true,
                      backgroundColor: Color(0xFFFFFFFF),
                      selectionOverlay: CupertinoPickerDefaultSelectionOverlay(
                        background: Colors.transparent,
                      ),
                    ),
                  ),
                ),
                Text(
                  '秒',
                  style: TextStyle(
                    color: Color(0xFF292933),
                    fontSize: 18,
                    fontWeight: FontWeight.normal,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
