import 'dart:collection';

import 'package:flutter/material.dart';

import 'base_page.dart';

///这个管理类，只是标记 当前 按照顺序放入和移除栈名称，并不是页面跳转后退 的功能，
///只是方便 推算、表示生命周期方法
class NavigatorManager {
  List<String> _activityStack = <String>[];

  NavigatorManager._internal();

  static NavigatorManager _singleton = new NavigatorManager._internal();

  //工厂模式
  factory NavigatorManager() => _singleton;
  void addWidget(BaseState widgetName) {
    _activityStack.add(widgetName.getWidgetName());
  }

  void removeWidget(BaseState widgetName) {
    _activityStack.remove(widgetName.getWidgetName());
  }

  bool isTopPage(BaseState widgetName) {
    if (_activityStack == null) {
      return false;
    }
    try {
      return widgetName.getWidgetName() ==
          _activityStack[_activityStack.length - 1];
    } catch (exception) {
      return false;
    }
  }

  bool isSecondTop(BaseState widgetName) {
    if (_activityStack == null) {
      return false;
    }
    try {
      return widgetName.getWidgetName() ==
          _activityStack[_activityStack.length - 2];
    } catch (exception) {
      return false;
    }
  }
}

class CustomNavigatorManager extends NavigatorObserver {
  HashSet<NavigatorEventCallback> _callbackList = HashSet.identity();

  void registerCallback(NavigatorEventCallback? callback) {
    if (callback != null) {
      _callbackList.add(callback);
    }
  }

  void unregisterCallback(NavigatorEventCallback? callback) {
    _callbackList.remove(callback);
  }

  void clearCallbackList() {
    _callbackList.clear();
  }

  @override
  void didPush(Route route, Route? previousRoute) {
    dispatchEvent(NavigatorEvent.Push, route, previousRoute);
    printRouteInfo(route, "didPush");
    printRouteInfo(previousRoute, "previousRoute didPush");
  }

  @override
  void didPop(Route route, Route? previousRoute) {
    dispatchEvent(NavigatorEvent.Pop, route, previousRoute);
    printRouteInfo(route, "didPop");
    printRouteInfo(previousRoute, "previousRoute didPop");
  }

  @override
  void didRemove(Route<dynamic> route, Route<dynamic>? previousRoute) {
    dispatchEvent(NavigatorEvent.Remove, route, previousRoute);
    printRouteInfo(route, "didRemove");
    printRouteInfo(previousRoute, "previousRoute didRemove");
  }

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    dispatchEvent(NavigatorEvent.Replace, newRoute, oldRoute);
    printRouteInfo(newRoute, "didReplace");
    printRouteInfo(oldRoute, "previousRoute didReplace");
  }

  printRouteInfo(Route? route, String extra) {
    if (route != null) {
      print("guan $extra,isFirst:${route.isFirst},isActive:${route.isActive},"
          "isCurrent:${route.isCurrent},hasActiveRouteBelow:"
          "${route.hasActiveRouteBelow},settings:${route.settings}");
    }
  }

  void dispatchEvent(NavigatorEvent event, Route? newRoute, Route? oldRoute) {
    _callbackList.forEach((element) {
      element(event, newRoute, oldRoute);
    });
  }
}

typedef NavigatorEventCallback = void Function(
    NavigatorEvent event, Route? newRoute, Route? oldRoute);

enum NavigatorEvent { Pop, Push, Replace, Remove }
