import 'package:flutter/material.dart';

class PageStateWidget extends StatefulWidget {
  final PageStateWidgetState _pageStateWidgetState = PageStateWidgetState();

  final PageState? state;
  final VoidCallback? errorClick;

  PageStateWidget({this.state, this.errorClick});

  static PageStateWidget pageEmpty(PageState pageState,
      {String? imagePath,
      String? buttonText,
      String? stateText,
      int? backgroundColor,
      VoidCallback? errorClick}) {
    PageStateWidget emptyView = PageStateWidget();
    emptyView._pageStateWidgetState.pageState = pageState;
    emptyView._pageStateWidgetState.imagePath = imagePath;
    emptyView._pageStateWidgetState.stateText = stateText;
    emptyView._pageStateWidgetState.backgroundColor = backgroundColor;
    emptyView._pageStateWidgetState.errorClick = errorClick;
    emptyView._pageStateWidgetState.buttonText = buttonText;
    return emptyView;
  }

  void setPageState(PageState pageState,
      {String? imagePath,
      String? buttonText,
      String? stateText,
      int? backgroundColor,
      VoidCallback? errorClick}) {
    _pageStateWidgetState.setPageState(pageState,
        imagePath: imagePath,
        buttonText: buttonText,
        stateText: stateText,
        backgroundColor: backgroundColor,
        errorClick: errorClick);
  }

  @override
  State<StatefulWidget> createState() {
    return _pageStateWidgetState;
  }
}

class PageStateWidgetState extends State<PageStateWidget> {
  final String _defaultErrorImagePath = 'assets/images/base/state_error.png';
  final String _defaultEmptyImagePath = 'assets/images/base/state_empty.png';
  final String _defaultButtonText = '点击刷新页面';

  VoidCallback? errorClick;
  String? imagePath;
  String? buttonText;
  String? stateText;
  int? backgroundColor;
  PageState pageState = PageState.Normal;

  @override
  void initState() {
    super.initState();
    if (widget.state != null) {
      pageState = widget.state!;
      errorClick = widget.errorClick;
    }
  }

  void setPageState(PageState pageState,
      {String? imagePath,
      String? buttonText,
      String? stateText,
      int? backgroundColor,
      VoidCallback? errorClick}) {
    if (mounted) {
      setState(() {
        this.pageState = pageState;
        this.imagePath = imagePath;
        this.buttonText = buttonText;
        this.errorClick = errorClick;
        this.stateText = stateText;
        this.backgroundColor = backgroundColor;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    //如果是loading状态，需要单独设置，与其他页面不同
    if (pageState == PageState.Loading) {
      return _getLoadingWidget();
    }
    if (stateText == null) {
      switch (pageState) {
        case PageState.Empty:
          stateText = "暂无数据～";
          break;
        case PageState.Error:
          stateText = "网络不给力，重新加载看看～";
          break;
        default:
          stateText = "";
          break;
      }
    }
    return Visibility(
        visible: PageState.Normal != pageState,
        child: Container(
          //错误页面中心可以自己调整
          color:
              backgroundColor == null ? Colors.white : Color(backgroundColor!),
          width: double.infinity,
          height: double.infinity,
          child: Center(
            child: InkWell(
              onTap: errorClick,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Image(
                    image: AssetImage(_getImagePath()!),
                    width: 190,
                    height: 145,
                  ),
                  SizedBox(
                    width: 10,
                    height: 7,
                  ),
                  Text(
                    stateText!,
                    style: TextStyle(fontSize: 14, color: Color(0xFF9494A6)),
                  ),
                  Visibility(
                    visible: pageState == PageState.Error || errorClick != null,
                    child: Container(
                      margin: EdgeInsets.only(top: 15),
                      padding: EdgeInsets.fromLTRB(25, 7, 25, 7),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(25)),
                          color: Color(0XFF00b377)),
                      child: Text(_getButtonText()!,
                          style: TextStyle(
                              fontWeight: FontWeight.normal,
                              fontSize: 16,
                              color: Colors.white)),
                    ),
                  )
                ],
              ),
            ),
          ),
        ));
  }

  Widget _getLoadingWidget() {
    return Container(
      //错误页面中心可以自己调整
      color: Colors.white,
      width: double.infinity,
      height: double.infinity,
      child: Center(
        child:
            // 圆形进度条
            new CircularProgressIndicator(
          strokeWidth: 4.0,
          backgroundColor: Colors.white,
          // value: 0.2,
          valueColor: new AlwaysStoppedAnimation<Color>(Color(0x3072f6)),
        ),
      ),
    );
  }

  String? _getImagePath() {
    if (imagePath != null && imagePath!.isNotEmpty) {
      return imagePath;
    } else if (pageState == PageState.Empty) {
      return _defaultEmptyImagePath;
    } else {
      return _defaultErrorImagePath;
    }
  }

  String? _getButtonText() {
    if (buttonText != null && buttonText!.isNotEmpty) {
      return buttonText;
    } else {
      return _defaultButtonText;
    }
  }
}

enum PageState { Normal, Error, Empty, Loading }
