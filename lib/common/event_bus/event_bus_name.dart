part of 'event_bus.dart';

/// 客户相关 EventBus的名称
class CustomerEventBusName {
  /*
  药帮忙私海客户打开筛选页面 名称
  */
  static const String YBM_PRIVATE_FILTER = 'ybm_private_filter';
  /*
  药帮忙公海客户打开筛选页面 名称
  */
  static const String YBM_PUBLIC_FILTER = 'ybm_public_filter';

  /*
  药帮忙公海认领客户成功 名称
  */
  static const String YBM_PUBLIC_RECEIVE = 'ybm_public_receive';

  /*
  药帮忙地图模式下认领成功通知
  */
  static const String YBM_MAP_REVICE = 'ybm_map_revice';

  /*
  药帮忙公海客户打开筛选页面 名称
  */
  static const String HY_PRIVATE_FILTER = 'hy_private_filter';

  /*
  荷叶公海认领
  */
  static const String HY_PUBLIC_RECEIVE = 'hy_public_receive';

  /*
  荷叶私海分配成功
  */
  static const String HY_PRIVATE_ALLOC = 'hy_private_alloc';

  /*
  跳转药帮忙客户私海 (附带单据筛选状态参数) - 首页使用
  资质临期-1 资质过期-2
  */

  static const String YBM_PRIVATE_SHOW = 'ybm_private_show';

  /*
  切换药帮忙 公私海tab
  参数 index 0-私海 1-公海
  */
  static const String YBM_CUSTOMER_TAB_CHANGE = 'ybm_customer_tab_change';
}

/// 消息相关的 通知名称
class MessageEventBusName {
  /*
  刷新消息数量
  */
  static const String REFRESH_MESSAGE_COUNT = 'refresh_message_count';
}

/// Tab页面相关通知 名称
class MainTabBarEventBusName {
  /*
  切换Tab页面 参数附带 index
  */
  static const String CHANGE_TAB_INDEX = 'change_tab_index';

  /*
  打开首页及子页面
 */
  static const String OPEN_MAIN = 'open_main';

  static const String CHANGE_FILTER_POI_REGISTER = 'change_filter_poi_register';
}
