part 'event_bus_name.dart';

/// 回调方法
typedef void EventCallback(arg);

/// 混入 观察者
mixin EventBusObserver {
  final EventBus eventBus = EventBus();
}

class EventBus {
  /// 构造函数
  EventBus._internal();

  /// 单例
  static EventBus _singleton = EventBus._internal();

  /// 工厂构造函数
  factory EventBus() => _singleton;

  /// 保存订阅队列
  Map<EventBusObserver, List<_EventBusCacheModel>> _observerMap = new Map();

  /// 添加订阅
  void addListener({
    required EventBusObserver observer,
    required Object eventName,
    required EventCallback callback,
  }) {
    List<_EventBusCacheModel> list = _observerMap[observer] ?? [];
    bool hasEvent =
        list.indexWhere((element) => element.eventName == eventName) != -1;
    // 已存在相同订阅则不处理
    if (hasEvent) return;
    list.add(_EventBusCacheModel(eventName, callback));
    _observerMap[observer] = list;
  }

  /// 移除订阅
  void removeListener({
    required EventBusObserver observer,
    Object? eventName,
  }) {
    if (eventName != null) {
      List<_EventBusCacheModel> list = _observerMap[observer] ?? [];
      // 不存在订阅 不处理
      if (list.length == 0) return;
      list.removeWhere((element) => element.eventName == eventName);
      _observerMap[observer] = list;
    } else {
      _observerMap.remove(observer);
    }
  }

  /// 发送订阅消息
  void sendMessage(Object eventName, {arg}) {
    _observerMap.forEach((key, value) {
      int index = value.indexWhere((element) => element.eventName == eventName);
      if (index != -1) {
        value[index].callback(arg);
      }
    });
  }
}

class _EventBusCacheModel {
  Object eventName;
  EventCallback callback;

  _EventBusCacheModel(this.eventName, this.callback);
}
