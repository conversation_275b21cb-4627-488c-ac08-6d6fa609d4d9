import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:XYYContainer/XYYContainer.dart';

class CommonTitleBar extends AppBar {
  CommonTitleBar(String title,
      {LeftButtonType leftType = LeftButtonType.back,
      Widget? leftButton,
      double? leadingWidth,
      VoidCallback? onLeftPressed,
      List<Widget>? rightButtons,
      double elevation = 0,
      PreferredSizeWidget? bottom,
      double toolbarHeight = 44.0,
      Widget? titleWidget,
      Color? backgroundColor,
      Color? leftButtonColor,
      Color? titleTextColor})
      : super(
            leading: leftType != LeftButtonType.custom
                ? LeftButton(
                    leftBtnType: leftType,
                    onPressed: onLeftPressed,
                    leftButtonColor: leftButtonColor,
                  )
                : leftButton,
            leadingWidth: leadingWidth,
            actions: rightButtons,
            title: titleWidget == null
                ? Text(
                    title,
                    style: TextStyle(
                      color: titleTextColor ?? Colors.black,
                      fontSize: 17,
                      fontWeight: FontWeight.w600,
                    ),
                  )
                : titleWidget,
            backgroundColor:
                backgroundColor == null ? Colors.white : backgroundColor,
            elevation: elevation,
            centerTitle: true,
            toolbarHeight: toolbarHeight,
            bottom: bottom,
            titleSpacing: 0,
            toolbarTextStyle: TextTheme(
              headline6: TextStyle(
                color: Colors.black,
              ),
            ).bodyText2,
            titleTextStyle: TextTheme(
              headline6: TextStyle(
                color: Colors.black,
              ),
            ).headline6,
            systemOverlayStyle: SystemUiOverlayStyle.dark);
}

//标题栏左侧按钮样式
enum LeftButtonType { back, close, none, custom }

class LeftButton extends StatefulWidget {
  const LeftButton(
      {Key? key, this.leftButtonColor, this.leftBtnType, this.onPressed})
      : super(key: key);

  final LeftButtonType? leftBtnType;
  final VoidCallback? onPressed;
  final Color? leftButtonColor;

  @override
  State<StatefulWidget> createState() {
    return _IconButtonState(leftBtnType: leftBtnType, onPressed: onPressed);
  }
}

class _IconButtonState extends State<LeftButton> {
  final LeftButtonType? leftBtnType;
  final VoidCallback? onPressed;

  _IconButtonState({this.leftBtnType, this.onPressed});

  @override
  Widget build(BuildContext context) {
    var iconRes = getIconRes();
    return Visibility(
      visible: leftBtnType != LeftButtonType.none,
      child: IconButton(
        icon: ImageIcon(
          AssetImage(iconRes),
          color: widget.leftButtonColor ?? Colors.black,
          size: 22,
        ),
        onPressed: () {
          if (onPressed != null) {
            onPressed!();
          } else if (Navigator.canPop(context)) {
            Navigator.maybePop(context);
          } else {
            XYYContainer.close(context);
          }
        },
        highlightColor: Colors.transparent,
        splashColor: Colors.transparent,
      ),
    );
  }

  String getIconRes() {
    String iconRes;
    switch (leftBtnType) {
      case LeftButtonType.close:
        iconRes = 'assets/images/titlebar/icon_close.png';
        break;
      case LeftButtonType.back:
      default:
        iconRes = 'assets/images/titlebar/icon_back.png';
        break;
    }
    return iconRes;
  }
}
