import 'package:XYYContainer/XYYContainer.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';

void showCopyActionSheet(
  BuildContext context, {
  required String message,
}) {
  showCupertinoModalPopup<void>(
    context: context,
    builder: (BuildContext context) {
      return CupertinoActionSheet(
        message: Text(message),
        actions: [
          CupertinoActionSheetAction(
            onPressed: () {
              Clipboard.setData(
                ClipboardData(text: message),
              );
              Navigator.of(context).pop();
              XYYContainer.toastChannel.toast('复制成功');
            },
            child: Text('复制'),
          ),
        ],
        cancelButton: CupertinoActionSheetAction(
          onPressed: () {
            Navigator.of(context).pop();
          },
          child: Text('取消'),
        ),
      );
    },
  );
}
