import 'package:flutter/material.dart';

//封装图片加载控件，增加图片加载失败时加载默认图片
class ImageWidget extends StatefulWidget {
  ImageWidget(
      {required this.url,
      this.w,
      this.h,
      this.fit = BoxFit.fill,
      this.defImagePath = "assets/images/base/icon_default_image.png"});

  final String url;
  final double? w;
  final double? h;
  final String defImagePath;
  final BoxFit fit;

  @override
  State<StatefulWidget> createState() {
    return _StateImageWidget();
  }
}

class _StateImageWidget extends State<ImageWidget> {
  late Image _image;
  late Image _defaultImage;

  bool loadFinished = false;

  @override
  void initState() {
    super.initState();

    _defaultImage = Image.asset(
      widget.defImagePath,
      fit: widget.fit,
      width: widget.w,
      height: widget.h,
    );
  }

  @override
  Widget build(BuildContext context) {
    _image = Image.network(
      widget.url,
      fit: widget.fit,
      width: widget.w,
      height: widget.h,
      errorBuilder: (
        BuildContext context,
        Object error,
        StackTrace? stackTrace,
      ) {
        return _defaultImage;
      },
    );
    return _image;
  }
}
