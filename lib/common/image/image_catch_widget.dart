import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

//封装图片加载控件，增加图片加载失败时加载默认图片
class ImageCatchWidget extends StatelessWidget {
  ImageCatchWidget(
      {required this.url,
      this.w,
      this.h,
      this.fit = BoxFit.fill,
      this.defImagePath = "assets/images/base/icon_default_image.png"});

  final String? url;
  final double? w;
  final double? h;
  final String defImagePath;
  final BoxFit fit;

  @override
  Widget build(BuildContext context) {
    var _defaultImage = Image.asset(
      defImagePath,
      fit: fit,
      width: w,
      height: h,
    );
    var _image = CachedNetworkImage(
      imageUrl: url!,
      width: w,
      height: h,
      fit: fit,
      placeholder: (context, url) => _defaultImage,
      errorWidget: (context, url, error) => _defaultImage,
    );
    return _image;
  }
}
