import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:flutter/widgets.dart';

class GalleryItem {
  final String id;
  final String imageUrl;

  GalleryItem({
    required this.id,
    required this.imageUrl,
  });
}

class GalleryItemThumbnail extends StatelessWidget {
  final GalleryItem galleryItem;
  final GestureTapCallback onTap;

  GalleryItemThumbnail({required this.galleryItem, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: GestureDetector(
        onTap: this.onTap,
        child: Hero(
          tag: this.galleryItem.id,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: ImageWidget(
              url: '${this.galleryItem.imageUrl}',
              fit: BoxFit.cover,
              defImagePath: "assets/images/order/icon_load_failed.png",
            ),
          ),
        ),
      ),
    );
  }
}
