import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';

class GalleryPhotoView extends StatefulWidget {
  final List<dynamic> images;
  final int index;

  GalleryPhotoView({required this.images, this.index = 0});

  @override
  State<StatefulWidget> createState() {
    return GalleryPhotoViewState();
  }
}

class GalleryPhotoViewState extends State<GalleryPhotoView> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
      },
      child: Container(
        color: Colors.black,
        child: Stack(
          children: [
            PhotoViewGallery.builder(
              itemCount: widget.images.length,
              backgroundDecoration: null,
              scrollPhysics: const BouncingScrollPhysics(),
              enableRotation: false,
              pageController: PageController(initialPage: widget.index),
              builder: _buildItem,
            ),
            Positioned(
              right: 10,
              top: MediaQuery.of(context).padding.top,
              child: IconButton(
                icon: Icon(
                  Icons.close,
                  size: 30,
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            )
          ],
        ),
      ),
    );
  }

  PhotoViewGalleryPageOptions _buildItem(BuildContext context, int index) {
    final String imageUrl = widget.images[index];
    return PhotoViewGalleryPageOptions(
      imageProvider: NetworkImage('$imageUrl'),
      initialScale: PhotoViewComputedScale.contained,
      minScale: PhotoViewComputedScale.contained,
      maxScale: PhotoViewComputedScale.covered * 1.5,
      heroAttributes: PhotoViewHeroAttributes(tag: "tag$index"),
    );
  }
}
