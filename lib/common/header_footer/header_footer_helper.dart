import 'package:flutter_easyrefresh/easy_refresh.dart';

class HeaderFooterHelp {
  ClassicalHeader getHeader() {
    return ClassicalHeader(
        refreshReadyText: "松手刷新",
        refreshText: "下拉刷新",
        refreshingText: "正在刷新数据中",
        refreshedText: "刷新完毕",
        refreshFailedText: "刷新失败",
        noMoreText: "没有更多数据了",
        infoText: "更新于 %T");
  }

  ClassicalFooter getFooter() {
    return ClassicalFooter(
      loadText: "开始上拉加载",
      loadReadyText: "松手加载",
      loadingText: "正在加载中",
      loadedText: "加载完成",
      loadFailedText: "加载失败",
      noMoreText: "没有更多数据了",
      infoText: "更新于 %T",
      enableInfiniteLoad: false,
      overScroll: false,
      safeArea: false,
    );
  }
}
