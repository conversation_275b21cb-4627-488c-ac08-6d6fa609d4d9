import 'dart:async';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class HomeTaskWidget extends StatelessWidget {
  final List<HomeTaskCardItemModel>? itemModels;

  HomeTaskWidget(this.itemModels);

  List<HomeTaskCardItemModel> get listModel => this.itemModels ?? [];

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 10),
      child: Column(
        children: this.getItems(),
      ),
    );
  }

  Widget titleWidget() {
    return Row(
      children: [
        Text(
          '待办任务',
          style: TextStyle(
            color: Color(0xFF383841),
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        Spacer(),
        GestureDetector(
          onTap: this.jumpAllTaskPage,
          behavior: HitTestBehavior.opaque,
          child: Row(
            children: [
              Text(
                '查看全部',
                style: TextStyle(
                  color: Color(0xFF9494A6),
                  fontSize: 13,
                ),
              ),
              SizedBox(width: 4),
              Image.asset(
                'assets/images/home/<USER>',
                width: 4.5,
                height: 7,
              ),
            ],
          ),
        ),
      ],
    );
  }

  List<Widget> getItems() {
    if (this.listModel.length <= 0) {
      return [this.titleWidget(), HomeTaskDefaultWidget()];
    } else {
      List<Widget> list = [this.titleWidget(), SizedBox(height: 10)];
      this.listModel.forEach((element) {
        list.add(HomeTaskItem(element));
      });
      return list;
    }
  }

  void jumpAllTaskPage() async {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-taskMore"});
    var roleType = await UserInfoUtil.roleType();
    int index = (roleType == "2" || roleType == "0") ? 0 : 1;
    XYYContainer.open('/GoodsManagement?selectedTab=$index&roleType=$roleType');
  }
}

class HomeTaskItem extends StatefulWidget {
  final HomeTaskCardItemModel model;

  HomeTaskItem(this.model);

  @override
  State<StatefulWidget> createState() {
    return HomeTaskItemState();
  }
}

class HomeTaskItemState extends State<HomeTaskItem>
    with AutomaticKeepAliveClientMixin {
  late ValueNotifier<int> controller = ValueNotifier(0);
  late Timer _timer;

  // 保证定时状态
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    int countdown = int.tryParse("${widget.model.countdown}") ?? 0;
    this.controller.value = countdown;

    _timer = Timer.periodic(Duration(seconds: 1), (t) {
      if (this.controller.value > 0) {
        this.controller.value = this.controller.value - 1;
      }
      if (this.controller.value <= 0) {
        t.cancel();
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    if (this._timer.isActive) {
      this._timer.cancel();
    }
    this.controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(10),
      margin: EdgeInsets.only(bottom: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            'assets/images/home/<USER>',
            width: 40,
            height: 40,
          ),
          SizedBox(width: 10),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${widget.model.theme ?? '-'}',
                style: TextStyle(
                  color: Color(0xFF191919),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              SizedBox(height: 3),
              Text(
                "任务类型：${widget.model.typeName ?? '-'}",
                style: TextStyle(color: Color(0xFF676773), fontSize: 11),
              ),
              SizedBox(height: 2),
              ValueListenableBuilder<int>(
                valueListenable: this.controller,
                builder: (context, value, child) {
                  String day = "${value ~/ 86400}".padLeft(2, '0');
                  String hours = "${value % 86400 ~/ 3600}".padLeft(2, '0');
                  String mintues = "${value % 3600 ~/ 60}".padLeft(2, '0');
                  String seconds = "${value % 60}".padLeft(2, '0');
                  return HomeTaskTimeWidget(
                    day: day,
                    hours: hours,
                    minutes: mintues,
                    seconds: seconds,
                  );
                },
              )
            ],
          ),
          Spacer(),
          GestureDetector(
            onTap: () {
              XYYContainer.bridgeCall('event_track',
                  parameters: {"action_type": "mc-homepage-taskDetail"});
              /// 跳转任务详情
              Navigator.of(context).pushNamed('/TaskDetailPage', arguments: {
                "taskId": widget.model.id,
              }).then((value) => {});
            },
            child: Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: Color(0xFF00B377),
                  width: 0.5,
                ),
                borderRadius: BorderRadius.circular(13),
              ),
              width: 60,
              height: 26,
              child: Center(
                child: Text(
                  '去看看',
                  style: TextStyle(color: Color(0xFF00B377), fontSize: 12),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class HomeTaskTimeWidget extends StatelessWidget {
  final String day;
  final String hours;
  final String minutes;
  final String seconds;

  HomeTaskTimeWidget({
    this.day = "0",
    this.hours = "0",
    this.minutes = "0",
    this.seconds = "0",
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        children: [
          Text(
            '任务仅剩：',
            style: TextStyle(color: Color(0xFF676773), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.day,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            '天',
            style: TextStyle(color: Color(0xFF191919), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.hours,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            ':',
            style: TextStyle(color: Color(0xFF191919), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.minutes,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            ':',
            style: TextStyle(color: Color(0xFF191919), fontSize: 11),
          ),
          Container(
            decoration: BoxDecoration(
              color: Color(0xFFF5F8FA),
              borderRadius: BorderRadius.circular(1),
            ),
            padding: EdgeInsets.all(1),
            child: Text(
              this.seconds,
              style: TextStyle(
                color: Color(0xFF191919),
                fontSize: 10,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HomeTaskDefaultWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: SizedBox(
          height: 250,
          child: PageStateWidget.pageEmpty(
            PageState.Empty,
            stateText: "当前没有待办任务",
            backgroundColor: 0xFFF1F6F9,
          )),
    );
  }
}
