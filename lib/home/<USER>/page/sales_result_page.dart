import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/data/sales_result_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/widget/sales_result_filter_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/widget/sales_result_item.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/widget/sales_result_total_item.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';

class SalesResultPage extends BasePage {
  final String period;

  SalesResultPage({this.period = '1'});

  @override
  BaseState<StatefulWidget> initState() {
    return SalesResultPageState();
  }
}

class SalesResultPageState extends BaseState<SalesResultPage>
    with HomeORGChooseManagerObserver {
  List<SalesResultModel> dataSource = [];

  EasyRefreshController _controller = EasyRefreshController();

  String period = "1";

  String chain = "-1";

  @override
  void initState() {
    this.period = widget.period;
    this.refreshData();
    super.initState();
  }

  @override
  void dispose() {
    this._controller.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      child: Column(
        children: [
          SalesResultFilterWidget(
            defaultPeriod: this.period,
            defaultChain: this.chain,
            changePeriod: (period) {
              this.period = period;
              this._controller.callRefresh();
            },
            changeChain: (chain) {
              this.chain = chain;
              this._controller.callRefresh();
            },
          ),
          Expanded(
            child: EasyRefresh(
              onRefresh: this.refreshData,
              controller: this._controller,
              child: ListView.builder(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewPadding.bottom),
                itemCount: this.dataSource.length,
                itemBuilder: (context, index) {
                  var source = this.dataSource[index];
                  if (index == 0) {
                    return SalesResultTotalItem(model: source,period: this.period,chain:this.chain);
                  }
                  return SalesResultItem(model: source,period: this.period,chain:this.chain);
                },
              ),
              emptyWidget: this.getEmptyWidget(),
            ),
          )
        ],
      ),
    );
  }

  Widget? getEmptyWidget() {
    if (this.dataSource.length == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  @override
  String getTitleName() {
    return "业绩数据";
  }

  Future<void> refreshData() async {
    /// period: 1.今日 2.本周 3.本月 4.全年 5.自定义 6.昨天 7.上周 8.上月
    /// queryType: 1:数据总览 2:控销 3:优选 4:自营 5:POP 6:甄选
    List<Future<SalesResultModel?>> list = [
      SalesResultRequest.requestSalesData(
          paramModel: this.chooseManager.paramModel ?? HomeORGParamModel(),
          period: this.period,
          drugstoreType: this.chain,
          queryType: 1),
      SalesResultRequest.requestSalesData(
          paramModel: this.chooseManager.paramModel ?? HomeORGParamModel(),
          period: this.period,
          drugstoreType: this.chain,
          queryType: 2),
      SalesResultRequest.requestSalesData(
          paramModel: this.chooseManager.paramModel ?? HomeORGParamModel(),
          period: this.period,
          drugstoreType: this.chain,
          queryType: 3),
      SalesResultRequest.requestSalesData(
          paramModel: this.chooseManager.paramModel ?? HomeORGParamModel(),
          period: this.period,
          drugstoreType: this.chain,
          queryType: 6),
      SalesResultRequest.requestSalesData(
          paramModel: this.chooseManager.paramModel ?? HomeORGParamModel(),
          period: this.period,
          drugstoreType: this.chain,
          queryType: 4),
      SalesResultRequest.requestSalesData(
          paramModel: this.chooseManager.paramModel ?? HomeORGParamModel(),
          period: this.period,
          drugstoreType: this.chain,
          queryType: 5),
    ];

    try {
      showLoadingDialog();
      var result = await Future.wait(list);
      print(result);
      this.dataSource = [];
      result.forEach((element) {
        if (element != null) {
          this.dataSource.add(element);
        }
      });
      dismissLoadingDialog();
      setState(() {});
    } catch (e) {
      dismissLoadingDialog();
    }
  }
}
