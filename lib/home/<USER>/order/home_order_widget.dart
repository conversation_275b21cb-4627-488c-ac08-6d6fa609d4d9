import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class HomeOrderWidget extends StatelessWidget
    with HomeORGChooseManagerObserver {
  final HomeOrderDataModel? orderModel;

  HomeORGParamModel? get orgParamModel => this.chooseManager.paramModel;

  final List<String> titles = ['今日订单', '今日退单', '未支付订单', '卡单订单'];
  final List<String> imagePahs = [
    'assets/images/home/<USER>',
    'assets/images/home/<USER>',
    'assets/images/home/<USER>',
    'assets/images/home/<USER>',
  ];

  List<String> get contents => [
        '${this.orderModel?.orderCount?.showNum ?? 0}', // 今日订单数
        '${this.orderModel?.refundOrderCount?.showNum ?? 0}', // 今日退单数
        '${this.orderModel?.notPayOrderCount?.showNum ?? 0}', // 未支付
        '${this.orderModel?.exceptionOrderCount?.showNum ?? 0}', // 卡单数
      ];

  HomeOrderWidget({this.orderModel});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFF1F6F9),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.fromLTRB(10, 12, 10, 12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  '订单',
                  style: TextStyle(
                    color: Color(0xFF383841),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Spacer(),
                GestureDetector(
                  onTap: jumpToOrder,
                  behavior: HitTestBehavior.opaque,
                  child: Row(
                    children: [
                      Text(
                        '查看更多订单',
                        style: TextStyle(
                          color: Color(0xFF9494A6),
                          fontSize: 12,
                        ),
                      ),
                      SizedBox(width: 4),
                      Image.asset(
                        'assets/images/home/<USER>',
                        width: 3.5,
                        height: 6,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Divider(
              color: Color(0xFFEEEEEE),
              height: 20,
              thickness: 0.5,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: this.items(context),
            ),
          ],
        ),
      ),
    );
  }

  List<Widget> items(BuildContext context) {
    return this
        .titles
        .mapIndexed(
          (index, element) => GestureDetector(
            onTap: () {
              this.jumpForItemOrder(index);
            },
            behavior: HitTestBehavior.opaque,
            child: HomeOrderItemWidget(
              title: element,
              imagePath: this.imagePahs[index],
              count: this.contents[index],
            ),
          ),
        )
        .toList();
  }

  String getOrderManagerPageRouter() {
    var router = '/order_manage_page?pageType=1&dateType=1';
    var id = this.orgParamModel?.executerId;
    if (id != null) {
      var isGroup = this.orgParamModel?.isGroup == true ? 'true' : 'false';
      router += '&id=$id&isGroup=$isGroup';
      router += '&areaName=${this.orgParamModel?.name}';
    }
    router = Uri.encodeFull(router);
    return router;
  }

  /// 跳带状态的订单
  void jumpForItemOrder(int index) {
    String actionType = "";
    var router = this.getOrderManagerPageRouter();
    if (index == 0 && this.orderModel?.orderCount?.statusList != null) {
      // 今日订单
      router += '&statusList=${this.orderModel?.orderCount?.statusList}';
      actionType = "mc-homepage-orderPaid";
    }
    if (index == 1) {
      // 退款单
      router = router.replaceAll('pageType=1', 'pageType=3');
      actionType = "mc-homepage-orderRefund";
    }
    if (index == 2 && this.orderModel?.notPayOrderCount?.statusList != null) {
      // 未支付订单
      router += '&statusList=${this.orderModel?.notPayOrderCount?.statusList}';
      if (this.orderModel?.notPayOrderCount?.startCreateTime != null &&
          this.orderModel?.notPayOrderCount?.endCreateTime != null) {
        String sTime = "${this.orderModel?.notPayOrderCount?.startCreateTime}";
        String eTime = "${this.orderModel?.notPayOrderCount?.endCreateTime}";

        DateTime strTime =
            DateTime.fromMillisecondsSinceEpoch(int.parse(sTime));
        DateTime endTime =
            DateTime.fromMillisecondsSinceEpoch(int.parse(eTime));

        String startValueTime = formatDate(
          strTime,
          [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ":", "00"],
        );

        String endValueTime = formatDate(
          endTime,
          [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ":", "00"],
        );

        router +=
            '&startCreateTime=$startValueTime&endCreateTime=$endValueTime&sceneType=5';
      }
      actionType = "mc-homepage-orderUnpaid";
    }

    if (index == 3 &&
        this.orderModel?.exceptionOrderCount?.exceptionTypeList != null) {
      // 卡单
      router +=
          '&exceptionTypeList=${this.orderModel?.exceptionOrderCount?.exceptionTypeList}';
      // 订单状态
      if (this.orderModel?.exceptionOrderCount?.statusList != null) {
        router +=
            '&statusList=${this.orderModel?.exceptionOrderCount?.statusList}';
      }
      // 订单日期
      if (this.orderModel?.exceptionOrderCount?.startCreateTime != null &&
          this.orderModel?.exceptionOrderCount?.endCreateTime != null) {
        String sTime =
            "${this.orderModel?.exceptionOrderCount?.startCreateTime}";
        String eTime = "${this.orderModel?.exceptionOrderCount?.endCreateTime}";

        DateTime strTime =
            DateTime.fromMillisecondsSinceEpoch(int.parse(sTime));
        DateTime endTime =
            DateTime.fromMillisecondsSinceEpoch(int.parse(eTime));

        String startValueTime = formatDate(
          strTime,
          [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ":", "00"],
        );

        String endValueTime = formatDate(
          endTime,
          [yyyy, '-', mm, '-', dd, ' ', HH, ':', nn, ":", "00"],
        );

        router +=
            '&startCreateTime=$startValueTime&endCreateTime=$endValueTime&sceneType=5';
      }
      actionType = "mc-homepage-orderStuck";
    }
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": actionType});
    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  /// 跳订单
  void jumpToOrder() {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-orderMore"});
    var router = this.getOrderManagerPageRouter();
    XYYContainer.open(router);
  }
}

class HomeOrderItemWidget extends StatelessWidget {
  final String imagePath;
  final String title;
  final String count;

  HomeOrderItemWidget({
    required this.imagePath,
    required this.title,
    required this.count,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Image.asset(
            imagePath,
            width: 19,
            height: 19,
          ),
          SizedBox(height: 2),
          Text(
            this.title,
            style: TextStyle(
              color: Color(0xFF676773),
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            '(${this.count})',
            style: TextStyle(
              color: Color(0xFF00B377),
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          )
        ],
      ),
    );
  }
}
