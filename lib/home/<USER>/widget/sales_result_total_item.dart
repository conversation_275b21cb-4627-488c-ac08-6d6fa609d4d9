import 'dart:math' as math;
import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/data/sales_result_model.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class SalesResultTotalItem extends StatelessWidget {
  final SalesResultModel model;
  final String? period;
  final String? chain;

  SalesResultTotalItem({required this.model, this.period, this.chain});

  SalesResultEntryModel? get topModel {
    if ((model.dataStatisticsResult?.length ?? 0) > 0) {
      return model.dataStatisticsResult?[0];
    }
    return null;
  }

  List<SalesResultEntryModel>? get items {
    if ((model.dataStatisticsResult?.length ?? 0) > 1) {
      return model.dataStatisticsResult?.sublist(1);
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        clipBehavior: Clip.hardEdge,
        child: Column(
          children: [
            topWidget(),
            bottomWidget(),
          ],
        ),
      ),
    );
  }

  Widget topWidget() {
    return Container(
      decoration: BoxDecoration(
          image: DecorationImage(
        image: AssetImage('assets/images/home/<USER>'),
        fit: BoxFit.cover,
      )),
      padding: EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: this.topModel?.viewOrderFlag == true
                ? () {
                    // TODO: 跳转订单
                    String router = "/statistics_order_page?"
                        "period=${period}"
                        "&drugstoreType=${chain}"
                        "&targetId=${topModel?.targetId}"
                        "&gmvType=${topModel?.gmvType}"
                        "&total=${topModel?.showContent}";
                    XYYContainer.open(router);
                  }
                : null,
            child: Row(
              children: [
                Text(
                  '${this.model.title}',
                  style: TextStyle(
                    color: Color(0xFFB3E8D6),
                    fontSize: 14,
                  ),
                ),
                Visibility(
                  visible: this.topModel?.viewOrderFlag == true,
                  child: Image.asset(
                    'assets/images/home/<USER>',
                    width: 12,
                    height: 12,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(height: 10),
          Text(
            '${this.topModel?.showContent ?? '--'}',
            style: TextStyle(
              color: Color(0xFFFFFFFF),
              fontSize: 30,
              fontWeight: FontWeight.w700,
            ),
          ),
          SizedBox(height: 10),
          getRoteWidget(),
        ],
      ),
    );
  }

  Widget getRoteWidget() {
    return Row(
      children: [
        Text(
          '${this.topModel?.rateTitle ?? ''}',
          style: TextStyle(
            fontSize: 12,
            color: Color(0xFFB3E9D7),
          ),
        ),
        Text(
          this.topModel?.getRatio().replaceAll('+', '').replaceAll('-', '') ??
              "-",
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color: Color(0xFFFFFFFF),
          ),
        ),
        SizedBox(width: 5),
        Visibility(
          visible: this.topModel?.isShowRatioToward() == true,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(6),
              color: Color(0xFFFFFFFF),
            ),
            width: 12,
            height: 12,
            child: Center(
              child: Transform.rotate(
                angle: math.pi,
                child: Image.asset(
                  this.topModel?.isRatioUp() == true
                      ? 'assets/images/home/<USER>'
                      : 'assets/images/home/<USER>',
                  width: 6,
                  height: 7,
                ),
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget bottomWidget() {
    return Container(
      child: IntrinsicHeight(
        child: Row(
          children: getBottomItems(),
        ),
      ),
    );
  }

  List<Widget> getBottomItems() {
    List<Widget> list = [];
    this.items?.forEachIndexed((index, element) {
      if (index != 0) {
        list.add(Container(
          color: Color(0xFFF5F5F5),
          width: 1,
          margin: EdgeInsets.only(top: 15, bottom: 15),
        ));
      }
      list.add(SalesResultTotalBottomItem(
        model: element,
        period: period,
        chain: chain,
      ));
    });
    return list;
  }
}

class SalesResultTotalBottomItem extends StatelessWidget {
  final SalesResultEntryModel model;
  final String? period;
  final String? chain;

  SalesResultTotalBottomItem({required this.model, this.period, this.chain});

  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        padding: EdgeInsets.only(top: 15, bottom: 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween, // 使用 spaceBetween 让内容分散对齐
          children: [
            GestureDetector(
              onTap: model.viewOrderFlag == true
                  ? () {
                      // TODO: 跳转订单
                      String router = "/statistics_order_page?"
                          "period=${period}"
                          "&drugstoreType=${chain}"
                          "&targetId=${model.targetId}"
                          "&gmvType=${model.gmvType}"
                          "&total=${model.showContent}";
                      XYYContainer.open(router);
                    }
                  : null,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      '${model.showTitleName ?? "--"}',
                      style: TextStyle(color: Color(0xFF676773), fontSize: 12,),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Visibility(
                    visible: model.viewOrderFlag == true,
                    child: Image.asset(
                      'assets/images/home/<USER>',
                      width: 12,
                      height: 12,
                    ),
                  ),
                ],
              ),
            ),
            // SizedBox(height: 9),
            // Text(
            //   '${model.showContent}',
            //   style: TextStyle(
            //     color: Color(0xFF0D0E10),
            //     fontSize: 15,
            //     fontWeight: FontWeight.w500,
            //   ),
            // ),
            // SizedBox(height: 5),
            // getRoteWidget(),
            Column(
              children: [
                SizedBox(height: 8),
                Text(
                  '${model.showContent}',
                  style: TextStyle(
                    color: Color(0xFF0D0E10),
                    fontSize: 15,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                SizedBox(height: 5),
                getRoteWidget(),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget getRoteWidget() {
    return IntrinsicHeight(
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Flexible(
            child: Text(
              '${model.rateTitle} ',
              style: TextStyle(color: Color(0xFF676773), fontSize: 11),
            ),
          ),
          Flexible(
            child: Text(
              model.getRatio(),
              style: TextStyle(
                fontSize: 11,
                fontWeight: FontWeight.w600,
                color: model.isRatioUp() ? Color(0xFF00B377) : Color(0xFFFF2121),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
