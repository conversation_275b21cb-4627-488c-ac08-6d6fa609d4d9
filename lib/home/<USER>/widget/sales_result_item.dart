import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/dotted_line/dotted_line.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/data/sales_result_model.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class SalesResultItem extends StatelessWidget {
  final SalesResultModel model;
  final String? period;
  final String? chain;

  SalesResultItem({required this.model, this.period, this.chain});

  SalesResultEntryModel? get topModel {
    if ((model.dataStatisticsResult?.length ?? 0) > 0) {
      return model.dataStatisticsResult?[0];
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Container(
          color: Color(0xFFFFFFFF),
          child: Column(
            children: getItems(),
          ),
        ),
      ),
    );
  }

  List<Widget> getItems() {
    List<Widget> list = [];
    int count = this.model.dataStatisticsResult?.length ?? 0;
    if (count > 0) {
      list.add(getHeadWidget());
    }
    if (count > 1) {
      list.add(getNumberTitle());
    }

    this.model.dataStatisticsResult?.sublist(1).forEachIndexed((index, element) {
      if (index != 0) {
        list.add(Divider(
          color: Color(0xFFF5F5F5),
          height: 0.5,
          thickness: 0.5,
          indent: 15,
          endIndent: 15,
        ));
      }
      list.add(SalesResultContentItem(
        model: element,
        period: period,
        chain: chain,
      ));
    });
    return list;
  }

  Widget getHeadWidget() {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 20, 15, 10),
      decoration: BoxDecoration(
          image: DecorationImage(
        image: AssetImage('assets/images/home/<USER>'),
        fit: BoxFit.cover,
      )),
      child: Column(
        children: [
          Row(
            children: [
              Text(
                '${model.title}',
                style: TextStyle(
                  color: Color(0xFF000000),
                  fontSize: 16,
                  fontWeight: FontWeight.w900,
                ),
              ),
              SizedBox(width: 5),
              GestureDetector(
                onTap: this.topModel?.viewOrderFlag == true
                    ? () {
                        String router = "/statistics_order_page?"
                            "period=$period"
                            "&drugstoreType=$chain"
                            "&targetId=${topModel?.targetId}"
                            "&gmvType=${topModel?.gmvType}"
                            "&total=${topModel?.showContent}";
                        XYYContainer.open(router);
                      }
                    : null,
                child: Text(
                  '${this.topModel?.showTitleName ?? '--'}',
                  style: TextStyle(color: Color(0xFF676773), fontSize: 15),
                ),
              ),
              Visibility(
                visible: this.topModel?.viewOrderFlag == true,
                child: Image.asset(
                  'assets/images/home/<USER>',
                  width: 12,
                  height: 12,
                ),
              ),
              Spacer(),
              Text(
                '${this.topModel?.rateTitle ?? ''}',
                style: TextStyle(color: Color(0xFF676773), fontSize: 15),
              ),
              SizedBox(width: 4),
              Image.asset(
                this.topModel?.isRatioUp() == true ? 'assets/images/home/<USER>' : 'assets/images/home/<USER>',
                width: 14,
                height: 14,
              )
            ],
          ),
          SizedBox(height: 10),
          Row(
            children: [
              Text(
                '${this.topModel?.showContent ?? '--'}',
                style: TextStyle(
                  color: Color(0xFF0D0E10),
                  fontSize: 21,
                  fontWeight: FontWeight.w700,
                ),
              ),
              Spacer(),
              Text(
                '${this.topModel?.getRatio() ?? '--'}',
                style: TextStyle(
                  color: this.topModel?.isRatioUp() == true ? Color(0xFF00B377) : Color(0xFFFF2121),
                  fontSize: 21,
                  fontWeight: FontWeight.w700,
                ),
              )
            ],
          ),
        ],
      ),
    );
  }

  Widget getNumberTitle() {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 5, 15, 0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 3,
                child: Text(
                  '指标',
                  style: TextStyle(color: Color(0xFF676773), fontSize: 12),
                  textAlign: TextAlign.left,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '${model.timeString}',
                  style: TextStyle(color: Color(0xFF676773), fontSize: 12),
                  textAlign: TextAlign.right,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  '${model.rateTitle}',
                  style: TextStyle(color: Color(0xFF676773), fontSize: 12),
                  textAlign: TextAlign.right,
                ),
              )
            ],
          ),
          SizedBox(height: 10),
          DottedLine(color: Color(0xFFDFDFDF)),
        ],
      ),
    );
  }
}

class SalesResultContentItem extends StatelessWidget {
  final SalesResultEntryModel model;
  final String? period;
  final String? chain;

  SalesResultContentItem({required this.model, this.period, this.chain});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 10, 15, 10),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: GestureDetector(
              onTap: model.viewOrderFlag == true
                  ? () {
                      // TODO: 跳转订单
                      String router = "/statistics_order_page?"
                          "period=${period}"
                          "&drugstoreType=${chain}"
                          "&targetId=${model.targetId}"
                          "&gmvType=${model.gmvType}"
                          "&total=${model.showContent}";
                      XYYContainer.open(router);
                    }
                  : null,
              child: Row(
                children: [
                  Text(
                    '${model.showTitleName ?? "--"}',
                    style: TextStyle(color: Color(0xFF0D0E10), fontSize: 13),
                    textAlign: TextAlign.left,
                  ),
                  Visibility(
                    visible: model.viewOrderFlag == true,
                    child: Image.asset(
                      'assets/images/home/<USER>',
                      width: 12,
                      height: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${model.showContent ?? "--"}',
              style: TextStyle(color: Color(0xFF0D0E10), fontSize: 14),
              textAlign: TextAlign.right,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              '${model.getRatio()}',
              style: TextStyle(
                color: this.model.isRatioUp() == true ? Color(0xFF00B377) : Color(0xFFFF2121),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.right,
            ),
          )
        ],
      ),
    );
  }
}
