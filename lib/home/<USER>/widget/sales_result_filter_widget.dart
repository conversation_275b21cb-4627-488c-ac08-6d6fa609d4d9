import 'package:XyyBeanSproutsFlutter/common/button/background_state_button.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tab_controller.dart';
import 'package:XyyBeanSproutsFlutter/common/tabs/custom_tabs.dart';
import 'package:XyyBeanSproutsFlutter/goods/widgets/tab_custom_indicator.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class SalesResultFilterWidget extends StatefulWidget {
  final ValueChanged<String> changePeriod;
  final ValueChanged<String> changeChain;
  final String defaultPeriod;
  final String defaultChain;

  SalesResultFilterWidget({
    this.defaultPeriod = "1",
    this.defaultChain = "-1",
    required this.changePeriod,
    required this.changeChain,
  });

  @override
  State<StatefulWidget> createState() {
    return SalesResultFilterWidgetState();
  }
}

class SalesResultFilterWidgetState extends State<SalesResultFilterWidget>
    with SingleTickerProviderStateMixin {
  final List<_SalesResultFilterModel> models = [
    _SalesResultFilterModel(titile: "昨天", period: "6"),
    _SalesResultFilterModel(titile: "今天", period: "1"),
    _SalesResultFilterModel(titile: "本周", period: "2"),
    _SalesResultFilterModel(titile: "本月", period: "3"),
    _SalesResultFilterModel(titile: "上周", period: "7"),
    _SalesResultFilterModel(titile: "上月", period: "8"),
    _SalesResultFilterModel(titile: "本年", period: "4"),
  ];

  final List<_SalesResultFilterModel> chainModels = [
    _SalesResultFilterModel(titile: "全部", period: "-1"),
    _SalesResultFilterModel(titile: "连锁", period: "1"),
    _SalesResultFilterModel(titile: "非连锁", period: "2"),
  ];

  ValueNotifier<BackgroundButtonState> _filterController =
      ValueNotifier(BackgroundButtonState.selected);

  late CustomTabController _controller;

  @override
  void initState() {
    super.initState();
    int initialIndex = 0;
    this.models.forEachIndexed((index, element) {
      if (element.period == widget.defaultPeriod) {
        initialIndex = index;
      }
    });
    this._controller = CustomTabController(
      length: this.models.length,
      initialIndex: initialIndex,
      vsync: this,
      animationDuration: Duration.zero,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Color(0xFFFFFFFF),
      child: Column(
        children: [
          SizedBox(
            height: 44,
            child: CustomTabBar(
              controller: this._controller,
              isScrollable: true,
              indicatorWeight: 4,
              indicator: TabCustomIndicator(
                wantWidth: 28,
              ),
              indicatorColor: Color(0xFF00B377),
              indicatorSize: CustomTabBarIndicatorSize.label,
              unselectedLabelColor: Color(0xFF676773),
              unselectedLabelStyle: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w400,
              ),
              labelColor: Color(0xFF292933),
              labelStyle: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              labelPadding: EdgeInsets.only(left: 15, right: 15),
              padding: EdgeInsets.only(left: 8, right: 8),
              onTap: (index) {
                widget.changePeriod(this.models[index].period);
                _filterController =
                    ValueNotifier(BackgroundButtonState.selected);
                setState(() {});
              },
              tabs: tabs(),
            ),
          ),
          Visibility(
            visible: _controller.index != 6,
            child:
                Divider(color: Color(0xFFEEEEEE), height: 0.5, thickness: 0.5),
          ),
          Visibility(
            visible: true,
            child: Padding(
              padding: EdgeInsets.fromLTRB(5, 10, 5, 10),
              child: Row(
                children: filterItems(),
              ),
            ),
          )
        ],
      ),
    );
  }

  List<Widget> tabs() {
    return this
        .models
        .map((e) => Container(
              height: 28,
              child: Tab(
                text: e.titile,
              ),
            ))
        .toList();
  }

  List<Widget> filterItems() {
    return this
        .chainModels
        .map((e) => Expanded(
              child: Container(
                padding: EdgeInsets.only(left: 5, right: 5),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(2),
                  child: SizedBox(
                    height: 30,
                    child: BackgroundStateButton(
                      title: e.titile,
                      option: e.period,
                      alignment: Alignment.center,
                      selectDecoration: BoxDecoration(
                        color: Color(0xFFFFFFFF),
                        borderRadius: BorderRadius.circular(15),
                        border:
                            Border.all(color: Color(0xFF00B377), width: 0.5),
                      ),
                      normalDecoration: BoxDecoration(
                        color: Color(0xFFF0F0F0),
                        borderRadius: BorderRadius.circular(15),
                      ),
                      controller: e.period == widget.defaultChain
                          ? this._filterController
                          : null,
                      onPressed: (controller, chain) {
                        if (controller == this._filterController) return;
                        this._filterController.value =
                            BackgroundButtonState.normal;
                        this._filterController = controller;
                        widget.changeChain(chain);
                      },
                    ),
                  ),
                ),
              ),
            ))
        .toList();
  }
}

class _SalesResultFilterModel {
  final String titile;
  final String period;

  _SalesResultFilterModel({required this.titile, required this.period});
}
