import 'package:flutter/material.dart';

class HomeSearchBox extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(left: 10),
      height: 34,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(17),
        border: Border.all(color: Color(0xFF00B377), width: 1),
        color: Color(0xFFFFFFFF),
      ),
      child: Row(
        children: [
          Image.asset(
            'assets/images/home/<USER>',
            width: 22,
            height: 22,
          ),
          Text(
            '搜索客户',
            style: TextStyle(fontSize: 13, color: Color(0xFF9494A6)),
          )
        ],
      ),
    );
  }
}
