import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class HomeCustomerWidget extends StatelessWidget
    with HomeORGChooseManagerObserver {
  /// 活跃客户数据
  final HomeCustomerCardData? activityModel;

  /// 客户漏斗数据
  final HomeCustomerCardData? funnelModel;

  /// 选择的组织结构 模型
  HomeORGParamModel? get orgParamModel => this.chooseManager.paramModel;

  HomeCustomerWidget({this.activityModel, this.funnelModel});

  final HomeCustomerConfig activity = HomeCustomerConfig(
    assetImage: 'assets/images/home/<USER>',
    title: '活跃用户',
    moreTitle: '查看用户详情',
    moreImage: 'assets/images/home/<USER>',
    rowTopTitle: '今日下单(家)',
    rowBottomTitle: '今日登录(家)',
  );

  final HomeCustomerConfig funnel = HomeCustomerConfig(
    assetImage: 'assets/images/home/<USER>',
    title: '客户漏斗',
    moreTitle: '查看漏斗详情',
    moreImage: 'assets/images/home/<USER>',
    rowTopTitle: '本月动销',
    rowBottomTitle: '本月新开',
  );

  @override
  Widget build(BuildContext context) {
    bool activeFlag = Permission.isPermission('customerActiveIndex');
    bool funnelFlag = Permission.isPermission('customerFunnelIndex');
    return Container(
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: IntrinsicHeight(
        child: Row(
          children: [
            activeFlag ? Expanded(
              child: GestureDetector(
                onTap: () {
                  /// 跳活跃客户列表
                  var router = '/active_customer_today_page';
                  if (this.orgParamModel?.searchUserId != null) {
                    router +=
                        '?searchUserId=${this.orgParamModel?.searchUserId}';
                  } else if (this.orgParamModel?.groupId != null) {
                    router += '?groupId=${this.orgParamModel?.groupId}';
                  }
                  XYYContainer.bridgeCall('event_track',
                      parameters: {"action_type": "mc-homepage-clientActive"});
                  XYYContainer.open(router);
                },
                behavior: HitTestBehavior.opaque,
                child: HomeCustomerItem(
                  model: activity,
                  titleColor: Color(0xFF854502),
                  topContent: "${this.activityModel?.todayPlaceOrders ?? '-'}",
                  bottomContent: "${this.activityModel?.todayLogins ?? '-'}",
                ),
              ),
            ) : SizedBox(),
            (activeFlag && funnelFlag) ? SizedBox(width: 10) : SizedBox(),
            funnelFlag ? Expanded(
                child: GestureDetector(
              onTap: () async {
                XYYContainer.bridgeCall('event_track',
                    parameters: {"action_type": "mc-homepage-clientFunnel"});

                /// 跳客户漏斗
                var sysUserId = await UserInfoUtil.sysUserId();
                XYYContainer.open("/customer_funnel?oaId=$sysUserId");
              },
              behavior: HitTestBehavior.opaque,
              child: HomeCustomerItem(
                model: funnel,
                titleColor: Color(0xFF320784),
                moreColor: Color(0xFF9D7CDD),
                topContent: "${this.funnelModel?.moveSaleCustomers ?? '-'}",
                bottomContent: "${this.funnelModel?.newCustomers ?? '-'}",
              ),
            )) : SizedBox(),
          ],
        ),
      ),
    );
  }
}

class HomeCustomerConfig {
  final String assetImage;
  final String title;
  final String moreTitle;
  final String moreImage;
  final String rowTopTitle;
  final String rowBottomTitle;

  HomeCustomerConfig({
    required this.assetImage,
    required this.title,
    required this.moreTitle,
    required this.moreImage,
    required this.rowTopTitle,
    required this.rowBottomTitle,
  });
}

class HomeCustomerItem extends StatelessWidget {
  final HomeCustomerConfig model;

  final Color titleColor;

  final Color moreColor;

  final String? topContent;
  final String? bottomContent;

  HomeCustomerItem({
    required this.model,
    this.titleColor = const Color(0xFF320784),
    this.moreColor = const Color(0xFFCA842E),
    this.topContent,
    this.bottomContent,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(model.assetImage),
          fit: BoxFit.cover,
        ),
      ),
      child: Container(
        margin: EdgeInsets.fromLTRB(6, 10, 6, 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(left: 4),
              child: Text(
                model.title,
                style: TextStyle(
                  color: titleColor,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            SizedBox(height: 5),
            Container(
              padding: EdgeInsets.only(left: 4),
              child: Row(
                children: [
                  Text(
                    model.moreTitle,
                    style: TextStyle(
                      color: moreColor,
                      fontSize: 10,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(width: 5),
                  Image.asset(
                    model.moreImage,
                    width: 10,
                    height: 10,
                  )
                ],
              ),
            ),
            SizedBox(height: 5),
            ClipRRect(
              borderRadius: BorderRadius.circular(4),
              child: Container(
                decoration: BoxDecoration(
                  color: Color(0xFFFFFFFF),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      padding: EdgeInsets.only(
                          left: 6, right: 6, top: 10, bottom: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            model.rowTopTitle,
                            style: TextStyle(
                              color: Color(0xFF676773),
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '${this.topContent ?? "--"}',
                            style: TextStyle(
                              color: titleColor,
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    ),
                    Divider(
                      height: 0.5,
                      color: Color(0xFFEEEEEE),
                      indent: 6,
                      endIndent: 11,
                    ),
                    Container(
                      padding: EdgeInsets.only(
                          left: 6, right: 6, top: 10, bottom: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Text(
                            model.rowBottomTitle,
                            style: TextStyle(
                              color: Color(0xFF676773),
                              fontSize: 12,
                            ),
                          ),
                          Text(
                            '${this.bottomContent ?? "--"}',
                            style: TextStyle(
                              color: titleColor,
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                            ),
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
