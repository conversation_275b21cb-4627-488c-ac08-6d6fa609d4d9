import 'package:flutter/material.dart';

/// 混入 缓存
mixin HomeORGChooseManagerObserver {
  final HomeORGChooseManager chooseManager = HomeORGChooseManager();
}

/// 用来存储首页选择的 组织结构
class HomeORGChooseManager {
  /// 构造函数
  HomeORGChooseManager._internal();

  /// 单例
  static HomeORGChooseManager _singleton = HomeORGChooseManager._internal();

  /// 工厂构造函数
  factory HomeORGChooseManager() => _singleton;

  /// 首页选择组织结构后进行缓存
  HomeORGParamModel? paramModel;
}

class HomeORGParamModel {
  dynamic searchUserId;

  dynamic groupId;

  /// 选中的组织节点是否是当前登录用户
  bool selectedScopeMatchLoginUser = true;

  // 展示用
  String? name;

  HomeORGParamModel();

  Map<String, dynamic> toMap() {
    if (searchUserId != null) {
      return {'searchUserId': searchUserId};
    }
    if (groupId != null) {
      return {'groupId': groupId};
    }
    return {};
  }

  String? get executerId {
    if (searchUserId != null) {
      return searchUserId;
    }
    if (groupId != null) {
      return groupId;
    }
    return null;
  }

  bool get isGroup {
    if (groupId != null) {
      return true;
    }
    return false;
  }
}

class HomeORGChooseNotifier extends ValueNotifier<HomeORGParamModel> {
  HomeORGChooseNotifier(value) : super(value);

  void refreshData() {
    this.notifyListeners();
  }
}
