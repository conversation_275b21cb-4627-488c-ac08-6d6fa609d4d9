import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_root_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:json_annotation/json_annotation.dart';

part 'home_sales_item_model.g.dart';

@JsonSerializable()
class HomeSalesItemModel extends BaseModelV2<HomeSalesItemModel> {
  /// 标题
  @JsonKey(defaultValue: "-")
  dynamic title = "-";

  /// 描述
  @JsonKey(defaultValue: "-")
  dynamic describe = "-";

  /// 完成值
  dynamic completionValue;

  /// 下单客户数
  dynamic customerNum;

  /// 目标
  dynamic targetValue;

  /// 目标进度
  dynamic progress;

  /// 完成值同比
  @JsonKey(defaultValue: "-")
  dynamic linkRatio = "-";

  /// 下单客户数同比
  dynamic customerNumRatio = "-";

  /// 是否显示团队业绩入口
  bool isShowTeamPerformanceEntry = false;

  bool isShowCustomerNumRatio() {
    return customerNumRatio != null && customerNumRatio != "-";
  }

  /// 获取下单客户数同比字符串
  String getCustomerNumRatio() {
    if (customerNumRatio != null &&
        customerNumRatio != '-' &&
        customerNumRatio != '--') {
      return "$customerNumRatio%".replaceAll('-', '');
    }
    return "-";
  }

  /// 判断下单客户数是否是 同比上升
  bool isCustomerNumRatioUp() {
    double? ratio = double.tryParse("$customerNumRatio");
    if (ratio != null) {
      return ratio >= 0;
    }
    return false;
  }

  /// 判断下单客户数是否展示 同比朝向
  bool isShowCustomerNumRatioToward() {
    double? ratio = double.tryParse("$customerNumRatio");
    return ratio != null && ratio != 0;
  }

  /// 获取同比字符串
  String getRatio() {
    if (linkRatio != null && linkRatio != '-' && linkRatio != '--') {
      return "$linkRatio%".replaceAll('-', '');
    }
    return "-";
  }

  /// 判断是否是 同比上升
  bool isRatioUp() {
    double? ratio = double.tryParse("$linkRatio");
    if (ratio != null) {
      return ratio >= 0;
    }
    return false;
  }

  /// 判断是否展示 同比朝向
  bool isShowRatioToward() {
    double? ratio = double.tryParse("$linkRatio");
    return ratio != null && ratio != 0;
  }

  /// 获取完成进度
  double getFinishedProress() {
    double? finished = double.tryParse("$progress");
    double finishedProgress = (finished ?? 0) / 100;
    return finishedProgress > 1 ? 1.0 : finishedProgress;
  }

  HomeSalesItemModel();

  @override
  HomeSalesItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$HomeSalesItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomeSalesItemModelToJson(this);
  }
}

class HomeSalesRequest {
  /// 请求首页销售数据
  /// period: 1.今日 2.本周 3.本月 4.全年 5.自定义 6.昨天 7.上周 8.上月
  /// queryType: 1:数据总览 2:自营 3:POP 4:控销 5:优选
  static Future<BaseRootModel<HomeSalesItemModel>> requestSalesData({
    required HomeORGParamModel paramModel,
    required int period,
    required int queryType,
  }) {
    var params = paramModel.toMap();
    params['queryType'] = queryType;
    params['period'] = period;
    return NetworkV2<HomeSalesItemModel>(HomeSalesItemModel()).requestDataV2(
      'index/indexTargetProgress',
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
  }
}
