import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'home_item_models.g.dart';

@JsonSerializable()
class HomeOrderDataModel extends BaseModelV2<HomeOrderDataModel> {
  /// 今日订单数
  HomeOrderItemModel? orderCount;

  /// 今日退单数
  HomeOrderItemModel? refundOrderCount;

  /// 未支付订单数
  HomeOrderItemModel? notPayOrderCount;

  /// 卡单数据
  HomeOrderItemModel? exceptionOrderCount;

  @override
  HomeOrderDataModel fromJsonMap(Map<String, dynamic> json) {
    return _$HomeOrderDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomeOrderDataModelToJson(this);
  }
}

@JsonSerializable()
class HomeOrderItemModel extends BaseModelV2<HomeOrderItemModel> {
  /// 展示 数量
  dynamic showNum;

  /// 附带的订单筛选状态 ， 跳转订单时 需要传进二级页面
  dynamic statusList;

  /// 附带的卡单筛选状态 ， 跳转订单时 需要传进二级页面
  dynamic exceptionTypeList;

  /// 未支付 卡单订单附带的时间筛选参数
  dynamic startCreateTime;

  dynamic endCreateTime;

  HomeOrderItemModel();

  factory HomeOrderItemModel.fromJson(Map<String, dynamic> json) {
    return _$HomeOrderItemModelFromJson(json);
  }

  @override
  HomeOrderItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$HomeOrderItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomeOrderItemModelToJson(this);
  }
}

@JsonSerializable()
class HomeCustomerCardData extends BaseModelV2<HomeCustomerCardData> {
  /// 本月动销客户数
  dynamic moveSaleCustomers;

  /// 本月新开客户数
  dynamic newCustomers;

  /// 今日登录客户数
  dynamic todayLogins;

  /// 今日下单客户数
  dynamic todayPlaceOrders;

  @override
  HomeCustomerCardData fromJsonMap(Map<String, dynamic> json) {
    return _$HomeCustomerCardDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomeCustomerCardDataToJson(this);
  }
}

@JsonSerializable()
class HomeVisitCardDataModel extends BaseModelV2<HomeVisitCardDataModel> {
  /// 今日上门拜访
  dynamic doorVisits;

  /// 今日有效上门拜访
  dynamic validDoorVisits;

  /// 本月有效拜访
  dynamic thisMonthValidVisits;

  /// 本月未拜访
  dynamic thisMonthNonVisits;

  /// 1-BD;2-BDM
  dynamic isBd;

  @override
  HomeVisitCardDataModel fromJsonMap(Map<String, dynamic> json) {
    return _$HomeVisitCardDataModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomeVisitCardDataModelToJson(this);
  }
}

@JsonSerializable()
class HomeLicenceCardModel extends BaseModelV2<HomeLicenceCardModel> {
  /// 草稿单
  dynamic draft;

  /// 一审驳回
  dynamic firstRejection;

  /// 临期
  dynamic nearExpiration;

  /// 过期
  dynamic expiration;

  @override
  HomeLicenceCardModel fromJsonMap(Map<String, dynamic> json) {
    return _$HomeLicenceCardModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomeLicenceCardModelToJson(this);
  }
}

@JsonSerializable()
class HomeTaskCardItemModel extends BaseModelV2<HomeTaskCardItemModel> {
  /// 主题
  dynamic theme;

  /// 任务类型编码
  dynamic type;

  /// 任务类型名称
  dynamic typeName;

  /// 倒计时
  dynamic countdown;

  /// 结束时间
  dynamic endTime;

  /// 任务状态
  dynamic status;

  /// 任务编号
  dynamic id;

  @override
  HomeTaskCardItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$HomeTaskCardItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomeTaskCardItemModelToJson(this);
  }
}

@JsonSerializable()
class HomePriceItemModel extends BaseModelV2<HomePriceItemModel> {
  int? id;
  double? productHealth; //商品健康度
  double? productHealthWeek; //上周商品健康度
  double? productHealthWeekPercentage; //同比上周商品健康度
  int? onSaleProductCount; //在售商品数
  int? onSaleProductCountWeek; //上周在售商品数
  double? onSaleProductCountWeekPercentage; //同比上周在售商品数
  int? priceAdvantageProductCount; //价优商品数
  int? priceAdvantageProductCountWeek; //上周价优商品数
  double? priceAdvantageProductCountWeekPercentage; //同比上周价优商品数
  int? priceDisadvantageProductCount; //价劣商品数
  int? priceDisadvantageProductCountWeek; //上周价劣商品数
  double? priceDisadvantageProductCountWeekPercentage; //同比上周价劣商品数
  int? updateTime; //更新时间的字段

  HomePriceItemModel();

  factory HomePriceItemModel.fromJson(Map<String, dynamic> json) {
    return _$HomePriceItemModelFromJson(json);
  }

  @override
  HomePriceItemModel fromJsonMap(Map<String, dynamic> json) {
    return _$HomePriceItemModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$HomePriceItemModelToJson(this);
  }
}