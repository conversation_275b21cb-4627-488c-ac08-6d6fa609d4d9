import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_progress_new.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class HomePriceWidget extends StatelessWidget {
  final HomePriceItemModel? priceModel;
  HomePriceWidget({required this.priceModel});

  @override
  Widget build(BuildContext context) {
    // TODO: implement build
    return Container(
      color: Color(0xFFF1F6F9),
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        padding: EdgeInsets.fromLTRB(10, 12, 10, 12),
        child: Column(
          children: [
            Row(
              children: [
                Text(
                  '今日价格健康度',
                  style: TextStyle(
                    color: Color(0xFF383841),
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Spacer(),
                GestureDetector(
                  onTap: toPriceHealthHomePage,
                  behavior: HitTestBehavior.opaque,
                  child: Row(
                    children: [
                      Text(
                        '查看更多',
                        style: TextStyle(
                          color: Color(0xFF9494A6),
                          fontSize: 12,
                        ),
                      ),
                      SizedBox(width: 4),
                      Image.asset(
                        'assets/images/home/<USER>',
                        width: 3.5,
                        height: 6,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Divider(
              color: Color(0xFFEEEEEE),
              height: 20,
              thickness: 0.5,
            ),
            Container(
              child: Row(
                children: [
                  SizedBox(width: 10),
                  HomeSalesProgressNew(
                    progressBackGroundColor: Color.fromRGBO(229, 229, 229, 1.0),
                    w: 90,
                    h: 65,
                    progress: (priceModel?.productHealth ?? 0)/100,
                    progressText: '${priceModel?.productHealth ?? 0}',
                    progressColor: Color(0xFF09B475),
                    textWidgets: [
                      Text(
                        '同比上周',
                        style: TextStyle(
                          color: Color.fromRGBO(181, 181, 181, 1.0),
                          fontSize: 10,
                        ),
                      ),
                      SizedBox(width: 2),
                      Text(
                        '${priceModel?.productHealthWeekPercentage ?? 0}%',
                        style: TextStyle(
                          color: Color.fromRGBO(215, 8, 8, 1.0),
                          fontSize: 10,
                        ),
                      )
                    ],
                  ),
                  SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        IntrinsicHeight(
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    '在售商品数',
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  SizedBox(height: 10),
                                  Text(
                                    '${priceModel?.onSaleProductCount ?? 0}',
                                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                  ),
                                  SizedBox(height: 10),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Text(
                                    '价优商品数',
                                    style: TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                  SizedBox(height: 8),
                                  Text(
                                    '${priceModel?.priceAdvantageProductCount ?? 0}',
                                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                                  ),
                                  SizedBox(height: 8),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '${formatTimestamp(priceModel?.updateTime ?? 0)} 更新',
                          style: TextStyle(fontSize: 12, color: Color.fromRGBO(165, 165, 165, 1.0)),
                        )
                      ],
                    ),
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  String formatTimestamp(int timestamp) {
    if (timestamp == null || timestamp == 0) {
      return '-';
    }
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
    return DateFormat('yyyy-MM-dd HH:mm:ss').format(dateTime);
  }

  /// 去价格健康度主页
  void toPriceHealthHomePage() {
    XYYContainer.open("/price_health_home_page");
  }
}
