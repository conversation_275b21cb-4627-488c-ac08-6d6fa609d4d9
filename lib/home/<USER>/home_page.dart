import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/drag/draggable_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_user_level_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/customer/home_customer_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/footer/home_list_footer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/license/home_license_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/order/home_order_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/price/home_price_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_data_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/search_bar/home_org_choose.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/search_bar/home_search_box.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/search_bar/message_icon_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/task/home_task_widget.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/visit/home_visit_widget.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/price/logic/location_logic.dart';
import 'package:XyyBeanSproutsFlutter/utils/buildLocaRequest.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/share/share_data_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/share/share_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/qt_analytics_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/qt_test_helper.dart';


class HomePage extends BasePage {
  @override
  BaseState<StatefulWidget> initState() {
    return HomePageState();
  }
}

class HomePageState extends BaseState<HomePage> with HomeORGChooseManagerObserver {
  // 是否展示组织结构筛选
  ValueNotifier<bool> showOrgController = ValueNotifier(false);

  // 组织结构选择控制器
  ValueNotifier<String> orgController = ValueNotifier("全国");

  // 刷新控制器
  EasyRefreshController _easyRefreshController = EasyRefreshController();

  // 组织选择 控制器
  HomeORGChooseNotifier _chooseOrgController = HomeORGChooseNotifier(HomeORGParamModel());

  /// 数据源
  /// 订单数据
  HomeOrderDataModel? orderModel;

  /// 活跃客户数据
  HomeCustomerCardData? activityModel;

  /// 客户漏斗数据
  HomeCustomerCardData? funnelModel;

  /// 拜访数据
  HomeVisitCardDataModel? visitModel;

  /// 资质数据
  HomeLicenceCardModel? licenceModel;

  /// 任务数据
  List<HomeTaskCardItemModel>? taskModel;

  final dragController = DragController();

  @override
  bool needKeepAlive() {
    return true;
  }

  @override
  void initState() {
    /// 异步处理下 保证 orgController已赋值给 各item
    Future.delayed(Duration.zero, () {
      this.refreshData();
    });

    this.getRoleType();
    Future.delayed(Duration(milliseconds: 1000), () {
       BuildLocaRequest.locaRequest();
    });
   
    super.initState();
  }

  /// 获取用户级别
  void getRoleType() async {
    bool isBDM = await UserInfoUtil.isBDMOrGJRBDM();
    this.showOrgController.value = isBDM;
  }

  @override
  void dispose() {
    this._chooseOrgController.dispose();
    this._easyRefreshController.dispose();
    super.dispose();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Stack(
      children: [
        Container(
          color: Color(0xFFF1F6F9),
          child: EasyRefresh(
            controller: this._easyRefreshController,
            onLoad: this.hasTaskItem ? this.onLoad : null,
            onRefresh: () async {
              this.refreshData();
            },
            footer: this.hasTaskItem ? HomeListFooter() : null,
            bottomBouncing: true,
            child: ListView.builder(
              itemCount: 6,
              cacheExtent: 9999, // 设置一个较大数值， 使底部定时item能直接创建
              itemBuilder: (context, index) {
                if (index == 0) {
                  return HomeSalesDataWidget(orgController: _chooseOrgController);
                }
                if (index == 1 && Permission.isPermission('orderAllMould')) {
                  return HomeOrderWidget(orderModel: this.orderModel);
                }
                if (index == 3) {
                  return HomeCustomerWidget(
                    activityModel: this.activityModel,
                    funnelModel: this.funnelModel,
                  );
                }
                if (index == 4 && Permission.isPermission('visitTodayIndex')) {
                  return HomeVisitWidget(this.visitModel);
                }
                if (index == 5) {
                  return HomeLicenseWidget(this.licenceModel);
                }
                if (index == 6) {
                  return HomeTaskWidget(this.taskModel);
                }
                return Container();
              },
            ),
          ),
        ),
        DraggableWidget(
          initialPosition: AnchoringPosition.bottomRight,
          dragAnimationScale: 1,
          bottomMargin: (MediaQuery.of(context).size.width) * 0.4,
          intialVisibility: true,
          horizontalSpace: 0,
          shadowBorderRadius: 50,
          usedAppBar: true,
          child: GestureDetector(
            onTap: () {
              track("mc-homepage-charticon");
              requestQueryLevel();
            },
            child: Container(
              height: 64,
              width: 64,
              child: Image.asset('assets/images/home/<USER>'),
            ),
          ),
          dragController: dragController,
        ),
        // QT埋点测试按钮
        Positioned(
          bottom: 100,
          right: 20,
          child: Column(
            children: [
              // QT测试按钮
              FloatingActionButton(
                heroTag: "qt_test",
                backgroundColor: Colors.green,
                onPressed: () {
                  // 运行QT埋点测试
                  QTTestHelper.runAllTests();
                  showToast("QT埋点测试已运行，请查看控制台日志");

                  // 同时触发一个页面埋点测试
                  QTAnalyticsUtil.trackButtonClick('qt_test_button', 'home_page', extra: {
                    'test_time': DateTime.now().toString(),
                    'test_type': 'quick_test',
                  });
                },
                child: Icon(Icons.analytics, color: Colors.white),
              ),
              SizedBox(height: 10),
              // QT演示页面按钮
              FloatingActionButton(
                heroTag: "qt_demo",
                backgroundColor: Colors.blue,
                onPressed: () {
                  // 跳转到QT演示页面
                  Navigator.pushNamed(context, '/qt_analytics_demo');

                  // 埋点记录跳转行为
                  QTAnalyticsUtil.trackButtonClick('qt_demo_button', 'home_page', extra: {
                    'navigation_target': 'qt_analytics_demo',
                  });
                },
                child: Icon(Icons.science, color: Colors.white),
              ),
            ],
          ),
        ),
      ],
    );
  }

  bool get hasTaskItem {
    if (this.taskModel == null) {
      return false;
    }
    if (this.taskModel!.length == 0) {
      return false;
    }
    return true;
  }

  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return CommonTitleBar(
      this.getTitleName(),
      backgroundColor: Color(0xFFF1F6F9),
      leftType: LeftButtonType.none,
      leftButton: null,
      leadingWidth: 0,
      titleWidget: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            this.leftOrgButton(),
            Expanded(child: this.searchBox()),
            this.messageIcon(),
          ],
        ),
      ),
    );
  }

  // 组件 - 左侧组织结构选择
  Widget leftOrgButton() {
    return ValueListenableBuilder<bool>(
      valueListenable: this.showOrgController,
      builder: (context, value, child) {
        return Container(
          padding: EdgeInsets.only(left: value ? 0 : 10),
          child: Visibility(
            visible: value,
            child: GestureDetector(
              onTap: this.selectOrgAction,
              behavior: HitTestBehavior.opaque,
              child: HomeOrgChoose(controller: this.orgController),
            ),
          ),
        );
      },
    );
  }

  // 组件 - 客户搜索框
  Widget searchBox() {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: this.jumpSearchCustomer,
      child: HomeSearchBox(),
    );
  }

  Widget messageIcon() {
    return MessageIconWidget();
  }

  @override
  String getTitleName() {
    return "首页";
  }

  /// 事件
  /// 选择组织结构
  void selectOrgAction() {
    track("mc-homepage-org");
    XYYContainer.open('xyy://crm-app.ybm100.com/executor?needSetResult=true&canChooseDepartment=true',
        callback: (params) async {
      try {
        if (!(params?.containsKey("name") ?? false)) {
          return;
        }
        String? name = params?["name"];
        if (name == '全部') {
          var userInfo = await UserInfoUtil.getUserInfo();
          this.orgController.value = userInfo?.realName ?? '全国';
          HomeORGParamModel paramModel = HomeORGParamModel();
          paramModel.name = userInfo?.realName ?? '全国';
          paramModel.selectedScopeMatchLoginUser =
              params?['isgroup'] == 'true' && (params?['id'] == '' || params?['id'] == null);
          this._chooseOrgController.value = paramModel;

          /// 更新单例缓存
          this.chooseManager.paramModel = paramModel;
        } else {
          this.orgController.value = name ?? '未知';
          HomeORGParamModel paramModel = HomeORGParamModel();
          paramModel.selectedScopeMatchLoginUser =
              params?['isgroup'] == 'true' && (params?['id'] == '' || params?['id'] == null);
          if (params?["isgroup"] == 'true') {
            paramModel.groupId = params?['id'];
            paramModel.searchUserId = null;
          } else {
            paramModel.groupId = null;
            paramModel.searchUserId = params?['id'];
          }

          /// 缓存展示用名称
          paramModel.name = name ?? '--';

          this._chooseOrgController.value = paramModel;

          /// 更新单例缓存
          this.chooseManager.paramModel = paramModel;
        }

        /// 销售数据 通过 orgController控制请求， 此处请求其他模块数据
        this.mergeOtherRequest();
      } catch (e) {}
    });
  }

  void jumpSearchCustomer() {
    track("mc-homepage-searchClient");
    XYYContainer.open('/ybm_search_history?searchType=0');
    // XYYContainer.open('/customer_search_tab_page?roleCode=1&selectIndex=0');
  }

  Future<void> onLoad() async {
    this._easyRefreshController.finishLoad(noMore: true);
    return;
  }

  void refreshData() {
    /// 数据统计模块请求 各Widget自己负责
    /// 首页其他数据 在页面上请求 并刷新页面
    /// 通知子页面刷新
    this._chooseOrgController.refreshData();
    this.mergeOtherRequest();
  }

  /// 除销售数据模块 其他请求的合并
  void mergeOtherRequest() async {
    //M级排行榜弹窗
    this.requestRankPopupData();
    // 资质、任务请求较慢 单独处理
    this.requestLicence();
    this.requestTask();
    List<Future<dynamic>> list = [
      this.requestOrderData(),
      this.requestCustomerCardData(true),
      this.requestCustomerCardData(false),
      this.requestVisitCardData(),
    ];
    try {
      showLoadingDialog();
      var resultList = await Future.wait(list);
      dismissLoadingDialog();

      this.orderModel = resultList[0];
      this.activityModel = resultList[1];
      this.funnelModel = resultList[2];
      this.visitModel = resultList[3];
    } catch (e) {
      dismissLoadingDialog();
    }
    if (mounted) {
      setState(() {});
    }
  }

  // 目前资质请求比较慢 单独处理
  void requestLicence() async {
    var licenceResult = await this.requestLicenceCardData();
    this.licenceModel = licenceResult;
    if (mounted) {
      setState(() {});
    }
  }

  // 目前任务请求比较慢 单独处理
  void requestTask() async {
    var taskResult = await this.requestTaskCardData();
    this.taskModel = taskResult;
    if (mounted) {
      setState(() {});
    }
  }

  /// 请求订单数据
  Future<HomeOrderDataModel?> requestOrderData() async {
    var result = await NetworkV2<HomeOrderDataModel>(HomeOrderDataModel()).requestDataV2(
      'index/orderCount',
      parameters: this._chooseOrgController.value.toMap(),
    );
    if (result.isSuccess == true) {
      var data = result.getData();
      if (data != null) {
        return data;
      }
    }
    return null;
  }

  /// 请求客户卡片数据 活跃、漏斗
  Future<HomeCustomerCardData?> requestCustomerCardData(bool isActivity) async {
    var result = await NetworkV2<HomeCustomerCardData>(HomeCustomerCardData()).requestDataV2(
      isActivity ? 'active/card' : 'funnerV2/card',
      parameters: this._chooseOrgController.value.toMap(),
    );
    if (result.isSuccess == true) {
      var data = result.getData();
      if (data != null) {
        return data;
      }
    }
    return null;
  }

  /// 请求拜访数据卡片
  Future<HomeVisitCardDataModel?> requestVisitCardData() async {
    var result = await NetworkV2<HomeVisitCardDataModel>(HomeVisitCardDataModel()).requestDataV2(
      'visit/card',
      parameters: this._chooseOrgController.value.toMap(),
    );
    if (result.isSuccess == true) {
      var data = result.getData();
      if (data != null) {
        return data;
      }
    }
    return null;
  }

  /// 请求资质卡片
  Future<HomeLicenceCardModel?> requestLicenceCardData() async {
    var result = await NetworkV2<HomeLicenceCardModel>(HomeLicenceCardModel()).requestDataV2(
      'index/licenseInfo',
      parameters: this._chooseOrgController.value.toMap(),
    );
    if (result.isSuccess == true) {
      var data = result.getData();
      if (data != null) {
        return data;
      }
    }
    return null;
  }

  /// 请求待办任务卡片
  Future<List<HomeTaskCardItemModel>?> requestTaskCardData() async {
    var result = await NetworkV2<HomeTaskCardItemModel>(HomeTaskCardItemModel()).requestDataV2(
      'index/waitHandleTask',
      parameters: this._chooseOrgController.value.toMap(),
    );
    if (result.isSuccess == true) {
      var data = result.getListData();
      if (data != null) {
        return data;
      }
    }
    return null;
  }

  ///职位查询
  void requestQueryLevel() async{
    showLoadingDialog();
    var result = await NetworkV2<UserLevelModel>(UserLevelModel()).requestDataV2(
      'group/gmv/post/query',
      method: RequestMethod.GET
    );
    dismissLoadingDialog();
    if (result.isSuccess == true) {
      var userLevel = '${result.getData()?.postType?? USER_TYPE_M}';
      var rankUserType = userLevel == USER_TYPE_M? USER_TYPE_BD: userLevel;
      XYYContainer.open('/team_performance_rank_list_page?userLevel=$userLevel&rankUserType=$rankUserType');
    }
  }

  /// 请求M级排行榜弹窗
  Future<void> requestRankPopupData() async {
    var userInfo = await UserInfoUtil.getUserInfo();
    var result = await NetworkV2<RankPopupDataModel>(RankPopupDataModel())
        .requestDataV2('group/gmv/rankPopup',
        method: RequestMethod.GET);
    RankPopupDataModel? dataModel = RankPopupDataModel();
    dataModel = result.getData();
    if (result.isSuccess == true && dataModel?.showPopup == true) {
    //   // 弹出分享Dialog
      showDialog(
        context: this.context,
        barrierDismissible: false,//点击背后蒙层是否关闭弹框，默认为 true
        barrierColor : Colors.black.withOpacity(0.8),//弹框透明度0.8
        builder: (BuildContext context) {
          return Padding(
                padding: EdgeInsets.only(left: 17, right: 17),
                child: ShareContentPage(
                    dataModel: dataModel!,
                    userInfo: userInfo!
                ),
              );
        },
      );
    }
  }

}
