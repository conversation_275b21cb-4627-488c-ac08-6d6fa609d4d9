import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:flutter/material.dart';

class HomeLicenseWidget extends StatelessWidget
    with HomeORGChooseManagerObserver {
  final HomeLicenceCardModel? model;

  HomeLicenseWidget(this.model);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "资质信息",
            style: TextStyle(
              color: Color(0xFF383841),
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 10),
          IntrinsicHeight(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      this.jumpToSubPageAction(0);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: HomeLicenseItem(
                      iconImage: 'assets/images/home/<USER>',
                      title: '草稿单据',
                      content: "${this.model?.draft ?? 0}",
                    ),
                  ),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      this.jumpToSubPageAction(1);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: HomeLicenseItem(
                      iconImage: 'assets/images/home/<USER>',
                      title: '审核驳回',
                      content: "${this.model?.firstRejection ?? 0}",
                    ),
                  ),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      this.jumpToSubPageAction(2);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: HomeLicenseItem(
                      iconImage: 'assets/images/home/<USER>',
                      title: '资质过期',
                      content: "${this.model?.expiration ?? 0}",
                    ),
                  ),
                ),
                SizedBox(width: 5),
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      this.jumpToSubPageAction(3);
                    },
                    behavior: HitTestBehavior.opaque,
                    child: HomeLicenseItem(
                      iconImage: 'assets/images/home/<USER>',
                      title: '资质临期',
                      content: "${this.model?.nearExpiration ?? 0}",
                    ),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }

  void jumpToSubPageAction(int index) async {
    String actionType = "";
    switch (index) {
      case 0: //草稿单据
        actionType = "mc-homepage-licenseDraft";
        break;
      case 1: //审核驳回
        actionType = "mc-homepage-licenseReject";
        break;
      case 2: //资质过期
        actionType = "mc-homepage-licenseExpired";
        break;
      case 3: //资质临期
        actionType = "mc-homepage-licenseClosetoexpired";
        break;
    }
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": actionType});

    var hasYBMRole = await UserAuthManager.hasYBMRole();
    if (!hasYBMRole) {
      XYYContainer.toastChannel.toast('暂无此权限');
      return;
    }
    switch (index) {
      case 0:
      case 1:
        // 跳资质管理
        // 0-草稿 10-一审驳回
        var statusType = index == 0 ? "0" : "10";
        var router = "/licence_manager_page?tabCode=1&statusType=$statusType";
        XYYContainer.open(router);
        break;
      case 2:
      case 3:
        // 跳私海客户列表
        // tab 切到客户页面
        EventBus().sendMessage(MainTabBarEventBusName.CHANGE_TAB_INDEX, arg: 1);
        Future.delayed(Duration(milliseconds: 100), () {
          EventBus().sendMessage(CustomerEventBusName.YBM_CUSTOMER_TAB_CHANGE,
              arg: 0);
        });
        // 私海列表带的参数
        // 资质临期-1 资质过期-2
        Future.delayed(Duration(milliseconds: 110), () {
          var licenseStatus = (index == 2) ? 12 : 11;
          Map<String, dynamic> params = {'licenseStatus': licenseStatus};
          var chooseMap = this.chooseManager.paramModel?.toMap() ?? {};
          params.addAll(chooseMap);
          if (this.chooseManager.paramModel?.name != null &&
              chooseMap.isNotEmpty) {
            params['name'] = this.chooseManager.paramModel?.name;
          }
          params['merchantStatus'] = 1;
          EventBus()
              .sendMessage(CustomerEventBusName.YBM_PRIVATE_SHOW, arg: params);
        });
        break;
      default:
    }
  }
}

class HomeLicenseItem extends StatelessWidget {
  final String iconImage;
  final String title;
  final String content;

  HomeLicenseItem({
    required this.iconImage,
    required this.title,
    required this.content,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Color(0xFFFFFFFF),
        borderRadius: BorderRadius.circular(8),
      ),
      padding: EdgeInsets.all(10),
      child: Column(
        children: [
          Image.asset(
            this.iconImage,
            width: 40,
            height: 40,
          ),
          Text(
            title,
            style: TextStyle(
              color: Color(0xFF676773),
              fontSize: 13,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            '($content)',
            style: TextStyle(
              color: Color(0xFF00B377),
              fontSize: 11,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }
}
