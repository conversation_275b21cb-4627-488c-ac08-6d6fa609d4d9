import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_item_models.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

class HomeVisitWidget extends StatelessWidget
    with HomeORGChooseManagerObserver {
  final HomeVisitCardDataModel? visitModel;

  HomeORGParamModel? get orgParamModel => this.chooseManager.paramModel;

  HomeVisitWidget(this.visitModel);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
      child: Container(
        padding: EdgeInsets.fromLTRB(10, 12, 10, 12),
        decoration: BoxDecoration(
          color: Color(0xFFFFFFFF),
          borderRadius: BorderRadius.circular(8),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '拜访',
              style: TextStyle(
                color: Color(0xFF383841),
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(height: 10),
            IntrinsicHeight(
              child: Row(
                mainAxisSize: MainAxisSize.max,
                children: [
                  Expanded(
                    child: GestureDetector(
                      onTap: jumpTodayVisit,
                      behavior: HitTestBehavior.opaque,
                      child: Container(
                        decoration: BoxDecoration(
                          color: Color(0xFFFAFBFC),
                          borderRadius: BorderRadius.circular(6),
                        ),
                        child: Column(
                          children: [
                            Container(
                              padding: EdgeInsets.all(10),
                              child: Row(
                                children: [
                                  Image.asset(
                                    'assets/images/home/<USER>',
                                    width: 30,
                                    height: 30,
                                  ),
                                  Text(
                                    '今日拜访',
                                    style: TextStyle(
                                      color: Color(0xFF383841),
                                      fontSize: 12,
                                    ),
                                  ),
                                  Spacer(),
                                  Image.asset(
                                    'assets/images/home/<USER>',
                                    width: 14,
                                    height: 14,
                                  ),
                                ],
                              ),
                            ),
                            Divider(
                              color: Color(0xFFEEEEEE),
                              height: 0.5,
                            ),
                            Container(
                              padding: EdgeInsets.all(10),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: this.getLeftBottomItem(
                                        content:
                                            '${this.visitModel?.doorVisits ?? '-'}',
                                        title: '今日上门'),
                                  ),
                                  Container(
                                    height: 25,
                                    width: 0.5,
                                    color: Color(0xFFEEEEEE),
                                  ),
                                  Expanded(
                                    child: this.getLeftBottomItem(
                                        content:
                                            '${this.visitModel?.validDoorVisits ?? '-'}',
                                        title: '有效上门'),
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(width: 5),
                  Expanded(
                    child: Container(
                      child: Column(
                        children: [
                          Expanded(
                            child: GestureDetector(
                              onTap: jumpMonthValid,
                              behavior: HitTestBehavior.opaque,
                              child: this.getRightItem(
                                iconImage:
                                    'assets/images/home/<USER>',
                                title: '本月有效拜访',
                                content:
                                    '${this.visitModel?.thisMonthValidVisits ?? '-'}',
                              ),
                            ),
                          ),
                          SizedBox(height: 5),
                          Expanded(
                            child: GestureDetector(
                              onTap: jumpMonthNone,
                              behavior: HitTestBehavior.opaque,
                              child: this.getRightItem(
                                iconImage:
                                    'assets/images/home/<USER>',
                                title: '本月未拜访',
                                content:
                                    '${this.visitModel?.thisMonthNonVisits ?? '-'}',
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget getLeftBottomItem({required String content, required String title}) {
    return Column(
      children: [
        Text(
          content,
          style: TextStyle(
            color: Color(0xFF0D0E10),
            fontSize: 13,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          title,
          style: TextStyle(
            color: Color(0xFF383841),
            fontSize: 11,
          ),
        )
      ],
    );
  }

  Widget getRightItem({
    required String iconImage,
    required String title,
    required String content,
  }) {
    return Container(
      padding: EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Color(0xFFFAFBFC),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Image.asset(
            iconImage,
            width: 30,
            height: 30,
          ),
          SizedBox(width: 5),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: Color(0xFF383841),
                  fontSize: 12,
                ),
              ),
              Text(
                content,
                style: TextStyle(
                  color: Color(0xFF0D0E10),
                  fontSize: 13,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Spacer(),
          Image.asset(
            'assets/images/home/<USER>',
            width: 14,
            height: 14,
          ),
        ],
      ),
    );
  }

  /// 跳转今日拜访
  void jumpTodayVisit() async {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-visitDay"});
    var isBD = '${this.visitModel?.isBd}' == "1";
    String router = '';
    if (isBD) {
      router = '/schedule_analysis_bd_today_page?';
    } else {
      router = "/schedule_analysis_bdm_page?isToday=true&";
      if (this.orgParamModel?.name == null) {
        var userInfo = await UserInfoUtil.getUserInfo();
        router += 'name=${userInfo?.realName ?? '全国'}&';
      } else {
        router += 'name=${this.orgParamModel?.name}&';
      }
    }
    if (this.orgParamModel?.searchUserId != null) {
      router += 'searchUserId=${this.orgParamModel?.searchUserId}';
    } else if (this.orgParamModel?.groupId != null) {
      router += 'groupId=${this.orgParamModel?.groupId}';
    }

    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  /// 跳转本月有效拜访
  void jumpMonthValid() async {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-visitMonth"});
    var isBD = '${this.visitModel?.isBd}' == "1";
    String router = '';
    if (isBD) {
      router = '/schedule_analysis_bd_page?';
    } else {
      router = "/schedule_analysis_bdm_page?";
      if (this.orgParamModel?.name == null) {
        var userInfo = await UserInfoUtil.getUserInfo();
        router += 'name=${userInfo?.realName ?? '全国'}&';
      } else {
        router += 'name=${this.orgParamModel?.name}&';
      }
    }
    if (this.orgParamModel?.searchUserId != null) {
      router += 'searchUserId=${this.orgParamModel?.searchUserId}';
    } else if (this.orgParamModel?.groupId != null) {
      router += 'groupId=${this.orgParamModel?.groupId}';
    }

    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }

  /// 跳转本月未拜访
  void jumpMonthNone() async {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-unvisited"});
    var isBD = '${this.visitModel?.isBd}' == "1";
    String router = '';
    if (isBD) {
      router = '/schedule_no_visit_customer_bd_page?';
    } else {
      router = "/schedule_no_visit_customer_bdm_page?";
      if (this.orgParamModel?.name == null) {
        var userInfo = await UserInfoUtil.getUserInfo();
        router += 'name=${userInfo?.realName ?? '全国'}&';
      } else {
        router += 'name=${this.orgParamModel?.name}&';
      }
    }
    if (this.orgParamModel?.searchUserId != null) {
      router += 'searchUserId=${this.orgParamModel?.searchUserId}';
    } else if (this.orgParamModel?.groupId != null) {
      router += 'groupId=${this.orgParamModel?.groupId}';
    }

    router = Uri.encodeFull(router);
    XYYContainer.open(router);
  }
}
