import 'package:XYYContainer/XYYContainer.dart';
import 'dart:collection';

import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:flutter/material.dart';
import 'share_helper.dart';
import 'share_data_model.dart';
import 'share_select_page.dart';

class ShareContentPage extends StatefulWidget {
  final RankPopupDataModel dataModel;
  final UserInfoData userInfo;
  const ShareContentPage(
      {Key? key, required this.dataModel, required this.userInfo})
      : super(key: key);

  @override
  State<ShareContentPage> createState() => _ShareContentPageState();
}

class _ShareContentPageState extends State<ShareContentPage> {
  GlobalKey repaintWidgetKey = GlobalKey(); // 绘图key值
  bool isButton1Selected = true;
  int selectedType = 1; //默认选中BD=1，KA=2  电销榜单 3
  int postType = 0;
  var lastPopTime = DateTime.now().subtract(Duration(seconds: 2));

  List<String> rankImageUrls = [
    'assets/images/share/share_dept1.png',
    'assets/images/share/share_dept2.png',
    'assets/images/share/share_dept3.png',
    'assets/images/share/share_dept4.png',
  ];

  @override
  void initState() {
    postType = widget.dataModel.postType;
    if (postType != 0) {
      this.selectedType = postType;
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double containerHeight = MediaQuery.of(context).size.height - 170;
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Column(
        children: [
          Container(
            height: containerHeight, // 弹框整体高度
            decoration: BoxDecoration(
              // color: Colors.blue,
              borderRadius: BorderRadius.circular(9),
            ),
            child: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  if (postType == 0) ...[
                    buildHeaderButtons(),
                    SizedBox(
                      height: 10,
                    ),
                  ],
                  buildRepaintBoundary(),
                ],
              ),
            ),
          ),
          SizedBox(
            height: 20,
          ),
          Container(
            // height: 90,
            // color: Colors.blue,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                buildMaterialButton("关闭", onPressed: () {
                  Navigator.pop(context);
                }),
                buildMaterialButton("分享", hasGradient: true, onPressed: () {
                  track('mc-homepage-chartsshow');
                  //share_plus 分享
                  // ShareHelper().onSharePlusShare(repaintWidgetKey);
                  intervalClick(2);
                }),
              ],
            ),
          )
        ],
      ),
    );
  }

  // 埋点
  void track(String actionType, {Map<String, String>? extras}) {
    var hashMap = HashMap<String, String>();
    hashMap['action_type'] = actionType;
    if (extras != null && extras.isNotEmpty) {
      hashMap.addAll(extras);
    }
    XYYContainer.bridgeCall('event_track', parameters: hashMap);
  }

  //按钮：关闭、分享
  Widget buildMaterialButton(String title,
      {bool hasGradient = false, VoidCallback? onPressed}) {
    final double width = (MediaQuery.of(context).size.width - 80) / 2;
    final double height = 5 / 16 * width;
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(height / 2),
        border: !hasGradient
            ? Border.all(
                color: Colors.white,
                width: 1.0,
              )
            : null,
        gradient: hasGradient
            ? const LinearGradient(
                colors: [Color(0xFFFB9C02), Color(0xFFFB6302)], // 渐变的颜色数组
                begin: Alignment.centerLeft, // 渐变的起点位置
                end: Alignment.centerRight, // 渐变的终点位置
                stops: [0.0, 1.0], // 渐变颜色的分布位置，范围是0.0到1.0
              )
            : null,
      ),
      child: TextButton(
        onPressed: onPressed,
        child: Text(
          title,
          style: TextStyle(color: Colors.white, fontSize: 18),
        ),
      ),
    );
  }

  /// 防重复提交
  void intervalClick(int needTime) {
    // 防重复提交
    if (DateTime.now().difference(lastPopTime) > Duration(seconds: needTime)) {
      // print(lastPopTime);
      lastPopTime = DateTime.now();
      // print("允许点击");
      ShareHelper().onSharePlusShare(repaintWidgetKey).then((path) {
        if (path!.isNotEmpty) {
          showModalBottomSheet(
            context: this.context,
            isScrollControlled: true, //弹窗是否可以滑动
            builder: (BuildContext context) {
              return ShareSelectPage(path: path);
            },
          );
        }
      });
    } else {
      // lastPopTime = DateTime.now(); //如果不注释这行,则强制用户一定要间隔2s后才能成功点击. 而不是以上一次点击成功的时间开始计算.
      // print("请勿重复点击！");
    }
  }

  //整个截图部分
  Widget buildRepaintBoundary() {
    double width = MediaQuery.of(context).size.width - 34;
    return ClipRRect(
      borderRadius: BorderRadius.all(
        Radius.circular(13),
      ),
      child: RepaintBoundary(
        key: repaintWidgetKey,
        child: Column(
          children: [
            Image.asset(
              getHeaderImage(selectedType),
              width: width,
              height: 114 / 375 * width,
              fit: BoxFit.cover,
            ),
            Container(
              color: Color(0xFFFB8702),
              child: Column(
                children: <Widget>[
                  buildContentView(context), //排名数据
                  if (widget.dataModel.encourageText != null &&
                      widget.dataModel.encourageText.isNotEmpty)
                    buildEncourageText(), //底部激励文本
                  buildFooterView(), //底部白色内容
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getHeaderImage(int type) {
    switch (type) {
      case 1:
        return 'assets/images/share/share_headerBD.png';
      case 2:
        return 'assets/images/share/share_headerKA.png';
      case 3:
        return 'assets/images/share/share_headerDX.png';
      default:
        return 'assets/images/share/share_headerBD.png';//暂时没有默认图片 就用BD
    }
  }

  //头部按钮：BD榜、KA榜 电销榜
  Widget buildHeaderButtons() {
    return Container(
      padding: const EdgeInsets.all(2),
      decoration: BoxDecoration(
        color: const Color(0xFFFFEDD1),
        borderRadius: BorderRadius.circular(12),
      ),
      height: 45,
      child: Row(
        children: [
          Expanded(
            child: customButton("BD榜", 1),
          ),
          Expanded(
            child: customButton("KA榜", 2),
          ),
          Expanded(
            child: customButton("电销榜", 3),
          ),
        ],
      ),
    );
  }

  //按钮样式
  Widget customButton(String title, int type) {
    bool selected = (selectedType == type);
    return Container(
      decoration: BoxDecoration(
        color: const Color(0xFFFFEDD1),
        borderRadius: BorderRadius.circular(8),
        image: selected
            ? const DecorationImage(
                image: AssetImage('assets/images/share/share_bdka.png'),
                fit: BoxFit.cover,
              )
            : null,
      ),
      child: TextButton(
        onPressed: () {
          setState(() {
            selectedType = type;
            isButton1Selected = selected;
          });
        },
        child: Text(
          title,
          style: TextStyle(
              color: selected ? Colors.white : const Color(0xFFFC6105),
              fontSize: 18),
        ),
      ),
    );
  }

  //排名数据
  Widget buildContentView(BuildContext context) {
    List<RankListModel>? rankList;
    // if (selectedType == 1) {
    //   //1 BD
    //   rankList = widget.dataModel.bdRankList;
    // } else {
    //   //2 KA
    //   rankList = widget.dataModel.kaRankList;
    // }

    final Map<int, List<RankListModel>?> rankListMap = {
      1: widget.dataModel.bdRankList,   // BD
      2: widget.dataModel.kaRankList,   // KA
      3: widget.dataModel.telemarketingRankList,   // 电销
    };
    rankList = rankListMap[selectedType] ?? [];

    if (rankList.isEmpty == true) {
      // return Text('暂无数据');
      return Center(heightFactor: 4, child: Text('暂无数据',style: TextStyle(color: Colors.white)));
    } else if ((rankList.length ?? 0) > 10) {
      // rankList（长度最多为10）
      rankList = rankList.sublist(0, 10);
    }
    List<Widget> children = [];
    for (int i = 0; i < (rankList.length ?? 0); i++) {
      RankListModel model = rankList[i];
      Widget widget;
      // 本地rankImageUrls长度固定 防止数组越界
      if (model.rankType < 0 || model.rankType >= rankImageUrls.length) {
         widget = SizedBox.shrink();
      }else{
        if (model.topFlag == true) {
          // 如果登顶了，就显示登顶样式
          widget = buildGroupOfMyTop(model);
        } else {
          Widget groupContentWidget = buildGroupContent(model);
          widget = buildGroupStyle(groupContentWidget);
        }
      }
      children.add(widget);
    }

    return Container(
      margin: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
      padding: const EdgeInsets.only(top: 9),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.4),
        borderRadius: BorderRadius.circular(9),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: children,
      ),
    );
  }

  //我登顶的样式
  Widget buildGroupOfMyTop(RankListModel model) {
    int rankType = model.rankType;
    String imageUrl = rankImageUrls[rankType];

    Widget widget = Container(
      margin: EdgeInsets.only(left: 10, right: 10, bottom: 10),
      height: 160,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        // border: Border.all(
        //   color: Color(0xFFCC7200), // 边框的颜色
        //   width: 1.0, // 边框的宽度
        // ),
        gradient: const LinearGradient(
          colors: [Color(0xFFFFF9EC), Color(0xFFFEDEB6)], // 渐变的颜色数组
          begin: Alignment.topCenter, // 渐变的起点位置
          end: Alignment.bottomCenter, // 渐变的终点位置
          stops: [0.0, 1.0], // 渐变颜色的分布位置，范围是0.0到1.0
        ),
      ),
      child: buildTopImageWithText(imageUrl, model),
    );
    Widget stack = Stack(
      children: [
        widget,
        Align(
          alignment: Alignment.center,
          child: Image.asset(
            'assets/images/share/share_top.png',
            width: 132,
            height: 34,
          ),
        ),
      ],
    );
    return stack;
  }

  //登顶内容：图片、文字
  Widget buildTopImageWithText(String imageUrl, RankListModel model) {
    String amount = model.gmvAmount;
    String string = ''; //登顶的只返回一条数据
    if (model.userList?.isNotEmpty == true) {
      string = model.userList?.first.userDisplayName; //登顶的只返回一条数据
    }
    int index = string.indexOf("(");
    String name = index != -1
        ? string.substring(0, index)
        : string; // '花名'，如果没有 "("，将使用整个字符串作为名称
    String realName =
        index != -1 ? string.substring(index) : ""; // '(真名)'，如果没有 "("，描述为空字符串

    return Container(
      // color: Colors.green,
      child: Row(
        children: <Widget>[
          Container(
            child: Image.asset(imageUrl),
          ),
          const SizedBox(width: 7.5),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                buildItemFormatText(amount, isTop: true),
                // SizedBox(height: 2),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: name, //花名
                        style: TextStyle(
                          fontSize: 22,
                          color: Color(0xFFCF1004),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      TextSpan(
                        text: realName, //真名
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xFFCF1004),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 每个分组的样式
  Widget buildGroupStyle(Widget? child) {
    return Container(
      margin: const EdgeInsets.only(left: 9, right: 9, bottom: 9), //外边距
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
      ),
      child: child,
    );
  }

  // 每组数据 图片和文字，底部排名
  Widget buildGroupContent(RankListModel model) {
    int rankType = model.rankType;
    String imageUrl = rankImageUrls[rankType];

    List<Widget> children = [
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
           Container(
            width: 110, // 图片宽度
            height: 110, // 图片高度
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(imageUrl),
                fit: BoxFit.cover,
              ),
            ),
          ) ,
          const SizedBox(width: 16), // 图片和文字之间的间距
          Expanded(
            child: buildItemWidget(model),
          ),
        ],
      ),
    ];

    //如果排名rankLevel!= null && 不是M postType!=0，就添加widget
    if (model.rankLevel != null && postType != 0) {
      children.add(buildGroupBottomContent(model));
    }
    return Column(children: children);
  }

  //组内排名内容
  Widget buildGroupBottomContent(RankListModel model) {
    int rankLevel = model.rankLevel; // 等级
    int diffLevel = model.diffLevel ?? 0; // 等级变动
    String rankRateText = model.overPercent ?? ''; // 超过率文案

    String diffLevelText;
    Color color;
    if (diffLevel < 0) {
      diffLevelText = '下降${-diffLevel}名';
      color = Color(0xFF00B377);
    } else if (diffLevel > 0) {
      diffLevelText = "上升$diffLevel名";
      color = Color(0xFF00B377);
    } else {
      diffLevelText = "相同";
      color = Color(0xFF676773);
    }

    return Container(
      padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
      width: double.infinity,
      decoration: BoxDecoration(
        color: Color(0xFFF9F9F9),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(4),
          bottomRight: Radius.circular(4),
        ),
        border: Border.all(
          color: Color(0xFFDFDFDF), // 边框的颜色
          width: 0.5, // 边框的宽度
        ),
      ),
      child: RichText(
        textAlign: TextAlign.center,
        text: TextSpan(
          style: TextStyle(fontSize: 13, color: Color(0xFF676773)),
          children: [
            TextSpan(
              text: '我的排名',
            ),
            TextSpan(
              text: '$rankLevel名',
              style: TextStyle(
                  color: Color(0xFF00B377), fontWeight: FontWeight.bold),
            ),
            TextSpan(
              text: '，和昨天比',
            ),
            TextSpan(
              text: diffLevelText, //下降/上升/相同
              style: TextStyle(color: color, fontWeight: FontWeight.bold),
            ),
            TextSpan(
              text: '\n超过全国',
            ),
            TextSpan(
              text: rankRateText,
              style: TextStyle(
                  color: Color(0xFF00B377), fontWeight: FontWeight.bold),
            ),
            TextSpan(
              text: '的${selectedType == 1 ? 'BD' : (selectedType == 2 ? 'KA' : '电销')}',
            ),
          ],
        ),
      ),
    );
  }

  // 每组内容：
  // gmv金额 元/万+
  // List
  //    花名（姓名）
  //    区域/销售部门
  Widget buildItemWidget(RankListModel model) {
    List<UserListModel>? userList = model.userList;
    if (userList?.isEmpty == true) {
      // return Text('暂无数据');
      return Container(height: 110, alignment: Alignment.centerLeft, child: Text('暂无数据'));
    } else if (userList!.length > 10) {
      // userList（长度最多为10）
      userList = userList.sublist(0, 10);
    }
    return ListView.builder(
      padding: EdgeInsets.only(top: 10, bottom: 5),
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: userList.length + 1,
      itemBuilder: (context, index) {
        if (index == 0) {
          //55 万+
          return buildItemFormatText(model.gmvAmount);
        } else {
          final itemIndex = index - 1;
          String deptPath =
              userList![itemIndex].deptPath; //'湖北省区_武汉销售部_商务连锁部_KA一组';
          List<String> parts = deptPath.split('_');

          if (parts.isNotEmpty) {
            deptPath = parts.first + " ···· " + parts.last;
          } else {
            deptPath = '';
          }

          // 花名（姓名）
          // 区域/销售部门
          return Container(
            // color: Colors.red,
            padding: EdgeInsets.only(top: 8, bottom: 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  userList[itemIndex].userDisplayName,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  deptPath,
                  style: TextStyle(
                    fontSize: 12,
                    color: Color(0xFF676773),
                  ),
                ),
              ],
            ),
          );
        }
      },
    );
  }

  //每组顶部 gmv金额：xxx元/万+ "55万+"; // 或者是 "5500元"
  Widget buildItemFormatText(String amount, {bool isTop = false}) {
    String unit = "";
    if (amount.contains("万+")) {
      unit = "万+";
    } else if (amount.contains("元")) {
      unit = "元";
    }
    return RichText(
      text: TextSpan(
        text: amount.replaceAll(unit, ''), //金额
        style: TextStyle(
          fontSize: isTop ? 37 : 33,
          fontWeight: FontWeight.w600,
          color: isTop ? Color(0xFFCF1004) : Colors.red,
        ), // 设置初始字体大小
        children: [
          WidgetSpan(
            alignment: PlaceholderAlignment.middle,
            child: Text(
              unit, //单位
              style: TextStyle(
                color: isTop ? Color(0xFFCF1004) : Colors.red,
                fontSize: isTop ? 24 : 24,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  //底部激励文本
  Widget buildEncourageText() {
    return Container(
      color: Color(0xFFFB8702),
      padding: const EdgeInsets.only(left: 10, right: 10),
      child: Stack(
        children: <Widget>[
          Positioned(
            top: 0,
            left: 0,
            child: Image.asset(
              'assets/images/share/share_quotationMarks1.png',
              width: 20,
              height: 17,
            ),
          ),
          Positioned(
            bottom: 10,
            right: 0,
            child: Image.asset(
              'assets/images/share/share_quotationMarks2.png',
              width: 20,
              height: 17,
            ),
          ),
          Container(
            margin:
                const EdgeInsets.only(top: 20, left: 35, bottom: 20, right: 35),
            child: Text(
              '${widget.dataModel.encourageText}',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 14, color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  //尾部视图容器
  Widget buildFooterView() {
    return Stack(
      children: <Widget>[
        Image.asset('assets/images/share/share_bottom.png'),
        Positioned.fill(
          top: 6,
          left: 0,
          right: 0,
          child: Align(
            alignment: Alignment.center,
            child: buildBottomContentView(),
          ),
        ),
      ],
    );
  }

  //尾部个人信息，布局结构
  Widget buildBottomContentView() {
    return Container(
      // color: Colors.blue,
      padding: const EdgeInsets.only(left: 10, right: 10),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Expanded(
            flex: 4,
            child: Align(
              alignment: Alignment.center,
              child: buildBottomLeftColumn(),
            ),
          ),
          // const SizedBox(width: 2),
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerRight,
              child: buildBottomRightColumn(),
            ),
          ),
        ],
      ),
    );
  }

  //尾部左边：姓名、部门
  Widget buildBottomLeftColumn() {
    return Container(
      // color: Colors.green,
      child: Row(
        children: <Widget>[
          Container(
            child: Image.asset(
              'assets/images/share/share_logo.png',
              width: 43,
              height: 43,
            ),
          ),
          const SizedBox(width: 7.5),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  widget.dataModel.displayName ?? '--',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 2),
                Text(
                  '${widget.userInfo.department ?? '--'}',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: TextStyle(
                    fontSize: 13,
                    color: Color(0xFF676773),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  //尾部右边：日期、星期、天干地支
  Widget buildBottomRightColumn() {
    return Container(
      // color: Colors.blue,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            '${widget.dataModel.shortDate} ${widget.dataModel.weekDay}', //12月26日 星期六
            textAlign: TextAlign.center,
            style: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.bold,
                color: Color(0xFF00B377)),
          ),
          SizedBox(height: 5),
          Text(
            '${widget.dataModel.ganZhiDate}', //天干地支
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              color: Color(0xFF85858F),
            ),
          ),
        ],
      ),
    );
  }
}
