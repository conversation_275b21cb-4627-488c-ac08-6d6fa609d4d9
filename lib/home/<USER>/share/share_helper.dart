import 'package:flutter/material.dart';

import 'dart:io';
import 'dart:ui' as ui;
import 'dart:typed_data';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';

class ShareHelper {
  GlobalKey repaintWidgetKey = GlobalKey(); // 绘图key值

  /// 格式转换
  Future<File?> formUnit8ToFie(Uint8List? unit) async {
  if(unit == null) return null;
      Directory tempDir = await getTemporaryDirectory();
      String storagePath = tempDir.path;
      File file = File(
          '$storagePath/image_${DateTime.now().millisecondsSinceEpoch}.png');
      if (!file.existsSync()) {
        file.createSync();
      }
      file.writeAsBytesSync(unit);
      return file;
  }

  /// 分享图片
  Future<String?> onSharePlusShare(GlobalKey repaintWidgetKey) async {
    this.repaintWidgetKey = repaintWidgetKey;
    Uint8List? sourceBytes = await _capturePngToByteData();
    if (sourceBytes == null) {
      return null;
    }
    var file = await formUnit8ToFie(sourceBytes);
    // await XYYContainer.bridgeCall("open_share", parameters: {"path": file.path, "tag" : 0});
    return file?.path;
    // ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text("分享图片成功")));
  }

  /// 截屏图片生成图片，返回图片二进制
  Future<Uint8List?> _capturePngToByteData() async {
    try {
      RenderRepaintBoundary? boundary = repaintWidgetKey.currentContext
          ?.findRenderObject() as RenderRepaintBoundary?;
      if (boundary == null) {
        return null;
      }
      // 获取当前设备的像素比
      double dpr = ui.window.devicePixelRatio;
      ui.Image image = await boundary.toImage(pixelRatio: dpr);
      final sourceBytes = await image.toByteData(format: ui.ImageByteFormat.png);
      // final sourceBytes = await _roundImage(image: image, radius: dpr * 20);
      return sourceBytes?.buffer.asUint8List();
    } catch (e) {
      return null;
    }
  }

  /// 裁剪圆角
  Future<ByteData?> _roundImage({double radius = 13, required ui.Image image,}) async {
    // 创建一个记录仪
    final ui.PictureRecorder pictureRecorder = ui.PictureRecorder();
    final Canvas canvas = Canvas(pictureRecorder);

    // 获取当前设备的像素比，绘图是几倍，这里就是几倍
    double dpr = ui.window.devicePixelRatio;
    canvas.clipRRect(
      RRect.fromLTRBR(
        0,
        0,
        image.width.toDouble(),
        image.height.toDouble(),
        Radius.circular(radius),
      ),
    );
    canvas.drawImage(image, const Offset(0, 0), Paint());

    final resImg = await pictureRecorder.endRecording().toImage(image.width, image.height);
    return await resImg.toByteData(format: ui.ImageByteFormat.png);
  }
}
