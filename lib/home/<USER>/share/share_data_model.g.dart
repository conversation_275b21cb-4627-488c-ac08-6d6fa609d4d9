// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'share_data_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UserListModel _$UserListModelFromJson(Map<String, dynamic> json) {
  return UserListModel()
    ..userDisplayName = json['userDisplayName']
    ..deptPath = json['deptPath'];
}

Map<String, dynamic> _$UserListModelToJson(UserListModel instance) =>
    <String, dynamic>{
      'userDisplayName': instance.userDisplayName,
      'deptPath': instance.deptPath,
    };

RankListModel _$RankListModelFromJson(Map<String, dynamic> json) {
  return RankListModel()
    ..gmvAmount = json['gmvAmount']
    ..rankType = json['rankType']
    ..topFlag = json['topFlag'] as bool?
    ..rankLevel = json['rankLevel']
    ..diffLevel = json['diffLevel']
    ..overPercent = json['overPercent']
    ..userList = (json['userList'] as List<dynamic>?)
        ?.map((e) => UserListModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$RankListModelToJson(RankListModel instance) =>
    <String, dynamic>{
      'gmvAmount': instance.gmvAmount,
      'rankType': instance.rankType,
      'topFlag': instance.topFlag,
      'rankLevel': instance.rankLevel,
      'diffLevel': instance.diffLevel,
      'overPercent': instance.overPercent,
      'userList': instance.userList,
    };

RankPopupDataModel _$RankPopupDataModelFromJson(Map<String, dynamic> json) {
  return RankPopupDataModel()
    ..showPopup = json['showPopup'] as bool?
    ..postType = json['postType'] as int
    ..ganZhiDate = json['ganZhiDate']
    ..shortDate = json['shortDate']
    ..weekDay = json['weekDay']
    ..encourageText = json['encourageText']
    ..displayName = json['displayName']
    ..bdRankList = (json['BDRankList'] as List<dynamic>?)
        ?.map((e) => RankListModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..kaRankList = (json['KARankList'] as List<dynamic>?)
        ?.map((e) => RankListModel.fromJson(e as Map<String, dynamic>))
        .toList()
    ..telemarketingRankList = (json['telemarketingRankList'] as List<dynamic>?)
        ?.map((e) => RankListModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$RankPopupDataModelToJson(RankPopupDataModel instance) =>
    <String, dynamic>{
      'showPopup': instance.showPopup,
      'postType': instance.postType,
      'ganZhiDate': instance.ganZhiDate,
      'shortDate': instance.shortDate,
      'weekDay': instance.weekDay,
      'encourageText': instance.encourageText,
      'displayName': instance.displayName,
      'bdRankList': instance.bdRankList,
      'kaRankList': instance.kaRankList,
      'telemarketingRankList': instance.telemarketingRankList,
    };
