import 'dart:math';

import 'package:flutter/material.dart';

class BlockTabBasePainter extends CustomPainter {
  final double lineWidth = 1;

  final double radius;
  final double titleHeight;
  final int total;

  final ValueNotifier<int> factor;

  BlockTabBasePainter({
    required this.factor,
    this.total = 2,
    this.titleHeight = 34,
    this.radius = 6,
  }) : super(repaint: factor);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint();
    paint.color = Color(0xFFFFFFFF);
    paint.strokeWidth = this.lineWidth;
    paint.style = PaintingStyle.fill;

    int index = this.factor.value;

    int maxIndex = this.total - 1;
    double lineWidthHalf = this.lineWidth / 2;
    double itemWidth = size.width / this.total;
    double offset = 12;
    double startX = index * itemWidth;
    double edgeLine =
        sqrt(this.titleHeight * this.titleHeight + offset * offset);
    double offsetX = offset / edgeLine * radius;
    double offsetY = this.titleHeight / edgeLine * radius;

    // 路径
    var path = Path();
    // Tab左顶点
    if (index == 0) {
      path.moveTo(lineWidthHalf, radius + lineWidthHalf);
      path.quadraticBezierTo(
          lineWidthHalf, lineWidthHalf, radius + lineWidthHalf, lineWidthHalf);
    } else {
      path.moveTo(startX - offsetX - lineWidthHalf, offsetY + lineWidthHalf);
      path.quadraticBezierTo(startX - lineWidthHalf, lineWidthHalf,
          startX + radius - lineWidthHalf, lineWidthHalf);
    }

    // Tab右顶点
    double maxX = (index + 1) * itemWidth;
    path.lineTo(maxX - radius - lineWidthHalf, lineWidthHalf);
    if (index == maxIndex) {
      path.quadraticBezierTo(maxX - lineWidthHalf, lineWidthHalf,
          maxX - lineWidthHalf, radius + lineWidthHalf);
    } else {
      path.quadraticBezierTo(maxX - lineWidthHalf, lineWidthHalf,
          maxX - lineWidthHalf + offsetX, offsetY + lineWidthHalf);
    }

    if (index != maxIndex) {
      // Tab右下角
      path.lineTo(maxX + offset + lineWidthHalf - offsetX,
          this.titleHeight + lineWidthHalf - offsetY);
      path.quadraticBezierTo(
          maxX + offset + lineWidthHalf,
          this.titleHeight + lineWidthHalf,
          maxX + offset + lineWidthHalf + radius,
          this.titleHeight + lineWidthHalf);

      // 右顶点
      path.lineTo(size.width - lineWidthHalf - radius,
          this.titleHeight + lineWidthHalf);
      path.quadraticBezierTo(
          size.width - lineWidthHalf,
          this.titleHeight + lineWidthHalf,
          size.width - lineWidthHalf,
          this.titleHeight + lineWidthHalf + radius);
    }

    // 右底角
    path.lineTo(
        size.width - lineWidthHalf, size.height - lineWidthHalf - radius);
    path.quadraticBezierTo(
        size.width - lineWidthHalf,
        size.height - lineWidthHalf,
        size.width - lineWidthHalf - radius,
        size.height - lineWidthHalf);

    // 左底角
    path.lineTo(lineWidthHalf + radius, size.height - lineWidthHalf);
    path.quadraticBezierTo(lineWidthHalf, size.height - lineWidthHalf,
        lineWidthHalf, size.height - lineWidthHalf - radius);

    if (index != 0) {
      // 左顶角
      path.lineTo(lineWidthHalf, this.titleHeight + lineWidthHalf + radius);
      path.quadraticBezierTo(lineWidthHalf, this.titleHeight + lineWidthHalf,
          lineWidthHalf + radius, this.titleHeight + lineWidthHalf);

      // Tab左下角
      path.lineTo(startX - offset + lineWidthHalf - radius,
          this.titleHeight + lineWidthHalf);
      path.quadraticBezierTo(
          startX - offset + lineWidthHalf,
          this.titleHeight + lineWidthHalf,
          startX - offset + lineWidthHalf + offsetX,
          this.titleHeight + lineWidthHalf - offsetY);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
