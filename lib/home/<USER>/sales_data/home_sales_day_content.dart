import 'package:XyyBeanSproutsFlutter/home/<USER>/home_sales_item_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

class HomeSalesDayContent extends StatelessWidget {
  final HomeSalesItemModel model;
  bool todayFlag = true;
  HomeSalesDayContent(this.model);

  @override
  Widget build(BuildContext context) {
    todayFlag = Permission.isPermission('today');
    return Container(
      padding: EdgeInsets.fromLTRB(10, 15, 10, 15),
      child: Row(
        children: [
          Expanded(child: getTotalGMVWidget()),
          SizedBox(width: 30),
          Expanded(child: getCustomerNumberWidget()),
        ],
      ),
    );
  }

  Widget getTotalGMVWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '实付GMV(元）',
          style: TextStyle(
            color: Color(0xFF0D0E10),
            fontSize: 12,
          ),
        ),
        SizedBox(height: 5),
        Text(
          '${todayFlag ? (model.completionValue ?? '-') : '--'}',
          style: TextStyle(
            color: Color(0xFF0D0E10),
            fontSize: 19,
            fontWeight: FontWeight.w600,
          ),
        ),
        Spacer(),
        Row(
          children: [
            Text(
              '同比上周：',
              style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 12,
              ),
            ),
            Text(
              '${todayFlag ? model.getRatio() : '--'}',
              style: TextStyle(
                color:
                    model.isRatioUp() ? Color(0xFF00B377) : Color(0xFFFF4741),
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
            todayFlag ? Visibility(
              visible: model.isShowRatioToward(),
              child: Container(
                padding: EdgeInsets.only(bottom: 12),
                child: Transform.rotate(
                  angle: math.pi,
                  child: Image.asset(
                    model.isRatioUp()
                        ? 'assets/images/home/<USER>'
                        : 'assets/images/home/<USER>',
                    width: 6,
                    height: 7,
                  ),
                ),
              ),
            ) :SizedBox()
          ],
        )
      ],
    );
  }

  Widget getCustomerNumberWidget() {
    var children=[
      Text(
        '下单客户数',
        style: TextStyle(
          color: Color(0xFF0D0E10),
          fontSize: 12,
        ),
      ),
      SizedBox(height: 5),
      Text(
        '${todayFlag ? (model.customerNum ?? '-') : '--'}',
        style: TextStyle(
          color: Color(0xFF0D0E10),
          fontSize: 19,
          fontWeight: FontWeight.w600,
        ),
      ),

    ];
    if(model.isShowCustomerNumRatio()){
      children.addAll([
        Spacer(),
        Row(
          children: [
            Text(
              '同比上周：',
              style: TextStyle(
                color: Color(0xFF9494A6),
                fontSize: 12,
              ),
            ),
            Text(
              '${todayFlag ? model.getCustomerNumRatio() : '--'}',
              style: TextStyle(
                color:
                model.isCustomerNumRatioUp() ? Color(0xFF00B377) : Color(0xFFFF4741),
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
            todayFlag ? Visibility(
              visible: model.isShowCustomerNumRatioToward(),
              child: Container(
                padding: EdgeInsets.only(bottom: 12),
                child: Transform.rotate(
                  angle: math.pi,
                  child: Image.asset(
                    model.isCustomerNumRatioUp()
                        ? 'assets/images/home/<USER>'
                        : 'assets/images/home/<USER>',
                    width: 6,
                    height: 7,
                  ),
                ),
              ),
            ) : SizedBox()
          ],
        )
      ]);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }
}
