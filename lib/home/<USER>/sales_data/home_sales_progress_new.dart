import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';

class HomeSalesProgressNew extends StatelessWidget {
  final Color progressColor;
  final Color progressBackGroundColor;
  final Color percentTextColor;
  final double progress;
  final String progressText;
  final double w;
  final double h;
  final bool showNum;
  final List<Widget> textWidgets;

  HomeSalesProgressNew({
    this.progressColor = const Color(0xFFFED448),
    this.progressBackGroundColor = const Color(0xFF008649),
    this.percentTextColor = const Color(0xFF3F3F3F),
    this.progress = 0.5,
    this.w = 80,
    this.h = 55,
    this.textWidgets = const [

    ],
    this.progressText = '',
    this.showNum = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: this.w,
      height: this.h,
      child: CustomPaint(
        painter: HomeFinishedProgressPainer(
          bottomDistance: 8,
          progress: this.progress,
          progressColor: this.progressColor,
          backgroundColor: this.progressBackGroundColor,
        ),
        child: Container(
          width: this.w,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Positioned(
                bottom: 3,
                child: Column(
                  children: [
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: RichText(
                        text: TextSpan(
                            text: showNum ? progressText : '--',
                            style: TextStyle(
                              color: this.percentTextColor,
                              fontSize: 13,
                              fontWeight: FontWeight.w600,
                            ),
                            children: [
                              TextSpan(
                                text: showNum ? "%" : '',
                                style: TextStyle(
                                  color: this.percentTextColor,
                                  fontSize: 9,
                                  fontWeight: FontWeight.w500,
                                ),
                              )
                            ]),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    SizedBox(height: 3),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: this.textWidgets,
                          )
                        )
                      ],
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

class HomeFinishedProgressPainer extends CustomPainter {
  final Color progressColor;
  final Color backgroundColor;
  final double lineWidth;
  final double progress;
  final double bottomDistance;

  HomeFinishedProgressPainer({
    this.lineWidth = 5,
    this.progressColor = const Color(0xFFFED448),
    this.backgroundColor = const Color(0xFF008649),
    this.progress = 0.5,
    this.bottomDistance = 0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint();

    paint.strokeWidth = this.lineWidth;
    paint.style = PaintingStyle.stroke;
    paint.strokeCap = StrokeCap.round;

    Rect rect = Rect.fromCircle(
        center: Offset(size.width / 2, size.height - bottomDistance),
        radius: size.height - bottomDistance - this.lineWidth);

    // 背景
    paint.color = this.backgroundColor;
    canvas.drawArc(rect, pi, pi, false, paint);
    // 进度条
    paint.color = this.progressColor;
    canvas.drawArc(rect, pi, progress * pi, false, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
