import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_sales_item_model.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'home_sales_team_performance_rank_button.dart';

class HomeSalesDayOverview extends StatelessWidget {
  final HomeSalesItemModel model;
  bool todayFlag = true;
  HomeSalesDayOverview(this.model);

  @override
  Widget build(BuildContext context) {
    todayFlag = Permission.isPermission('today');
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  '总业绩',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ),
              Permission.isPermission('teamIndex') ? Visibility(
                visible: model.isShowTeamPerformanceEntry,
                child: HomeSalesTeamPerformanceRankButton(
                  content: '日·团队业绩',
                  callback: (){
                    var paramsModelMap = TeamPerformanceListParams()
                                  ..timeType = TIME_TYPE_TODAY;
                    String routerPath = "/team_performance_list_page?paramsModelJson=${json.encode(paramsModelMap)}&displayName=团队业绩";
                    routerPath = Uri.encodeFull(routerPath);
                    XYYContainer.open(routerPath);
                  },
                ),
              ) : SizedBox(),
            ],
          ),
          SizedBox(height: 14),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(child: this.getTotalGMVContent()),
              SizedBox(width: 30),
              Expanded(child: this.getCustomerNumberContent()),
            ],
          )
        ],
      ),
    );
  }

  Widget getTotalGMVContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '实付GMV(元)',
          style: TextStyle(
            fontSize: 12,
            color: Color(0xFFFFFFFF),
          ),
        ),
        SizedBox(height: 7),
        Text(
          '${todayFlag ? (model.completionValue ?? "-") : '--'}',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Color(0xFFFFFFFF),
          ),
        ),
        SizedBox(height: 14),
        Row(
          children: [
            Text(
              '同比上周：',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFFB3E9D7),
              ),
            ),
            Text(
              todayFlag ? model.getRatio() : '--',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFFFFFFFF),
              ),
            ),
            SizedBox(width: todayFlag ? 5 : 0),
            todayFlag ? Visibility(
              visible: model.isShowRatioToward(),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Color(0xFFFFFFFF),
                ),
                width: 12,
                height: 12,
                child: Center(
                  child: Transform.rotate(
                    angle: math.pi,
                    child: Image.asset(
                      model.isRatioUp()
                          ? 'assets/images/home/<USER>'
                          : 'assets/images/home/<USER>',
                      width: 6,
                      height: 7,
                    ),
                  ),
                ),
              ),
            ) : SizedBox()
          ],
        )
      ],
    );
  }

  Widget getCustomerNumberContent() {
    var children = [
      Text(
        '下单客户数',
        style: TextStyle(
          fontSize: 12,
          color: Color(0xFFFFFFFF),
        ),
      ),
      SizedBox(height: 7),
      Text(
        '${todayFlag ? (model.customerNum ?? "-") : '--'}',
        style: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Color(0xFFFFFFFF),
        ),
      ),
    ];
    if (model.isShowCustomerNumRatio()) {
      children.addAll([
        SizedBox(height: 14),
        Row(
          children: [
            Text(
              '同比上周：',
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFFB3E9D7),
              ),
            ),
            Text(
              todayFlag ? model.getCustomerNumRatio() : '--',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
                color: Color(0xFFFFFFFF),
              ),
            ),
            SizedBox(width: todayFlag ? 5 : 0),
            todayFlag ? Visibility(
              visible: model.isShowCustomerNumRatioToward(),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(6),
                  color: Color(0xFFFFFFFF),
                ),
                width: 12,
                height: 12,
                child: Center(
                  child: Transform.rotate(
                    angle: math.pi,
                    child: Image.asset(
                      model.isCustomerNumRatioUp()
                          ? 'assets/images/home/<USER>'
                          : 'assets/images/home/<USER>',
                      width: 6,
                      height: 7,
                    ),
                  ),
                ),
              ),
            ) : SizedBox()
          ],
        )
      ]);
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }
}
