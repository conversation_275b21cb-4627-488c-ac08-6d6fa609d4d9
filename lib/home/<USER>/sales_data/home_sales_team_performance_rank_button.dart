
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/cupertino.dart';

typedef HomeSalesRankCallback = void Function();

class HomeSalesTeamPerformanceRankButton extends StatelessWidget {

  final String? content;
  final HomeSalesRankCallback? callback;

  HomeSalesTeamPerformanceRankButton({required this.content, required this.callback});

  @override
  Widget build(BuildContext context) {
    if (!Permission.isPermission('teamIndex')) {
      return SizedBox();
    }
    return GestureDetector(
      onTap: callback,
      child: Opacity(
        opacity: 1,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: 8.5
          ),
          alignment: Alignment.centerLeft,
          height: 24,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            color: Color(0x33000000),
          ),
          child: Text.rich(
            TextSpan(
              style: TextStyle(
                fontSize: 12,
                color: Color(0xFFFFFFFF),
              ),
              children: [
                TextSpan(
                  text: content?? ''
                ),
                WidgetSpan(
                  child: Container(
                    padding: EdgeInsets.only(left: 5),
                    child: SizedBox(
                      width: 4.5,
                      height: 7,
                      child: Image.asset('assets/images/home/<USER>'),
                    ),
                  ),
                ),
              ],
            ),
            textAlign: TextAlign.center,
            strutStyle: StrutStyle(
              forceStrutHeight: true
            ),
          ),
        ),
      ),
    );
  }
}