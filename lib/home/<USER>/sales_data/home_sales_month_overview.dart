import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_sales_item_model.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/sales_data/home_sales_progress.dart';
import 'package:XyyBeanSproutsFlutter/performance/data/team_performance_list_params.dart';
import 'package:XyyBeanSproutsFlutter/utils/permission/permission_data.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

import 'home_sales_team_performance_rank_button.dart';

class HomeSalesMonthOverview extends StatelessWidget {
  final HomeSalesItemModel model;
  bool todayFlag = true;
  HomeSalesMonthOverview(this.model);

  @override
  Widget build(BuildContext context) {
    todayFlag = Permission.isPermission('today');
    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.fromLTRB(10, 0, 10, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Row(
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  '总业绩及目标',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
              ),
              Permission.isPermission('teamIndex') ? Visibility(
                visible: model.isShowTeamPerformanceEntry,
                child: HomeSalesTeamPerformanceRankButton(
                  content: '月·团队业绩',
                  callback: (){
                    var paramsModelMap = TeamPerformanceListParams()
                      ..timeType = TIME_TYPE_THIS_MOUTH;
                    String routerPath = "/team_performance_list_page?paramsModelJson=${json.encode(paramsModelMap)}&displayName=团队业绩";
                    routerPath = Uri.encodeFull(routerPath);
                    XYYContainer.open(routerPath);
                  },
                ),
              ) :SizedBox(),
            ],
          ),
          SizedBox(height: 14),
          Row(
            children: [
              Text(
                '实付GMV(元)',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFFFFFFFF),
                ),
              ),
              SizedBox(width: 14),
              Text(
                '目标(元)：${todayFlag ? (model.targetValue ?? '--') : '--'}',
                style: TextStyle(
                  fontSize: 12,
                  color: Color(0xFFB3E9D7),
                ),
              ),
            ],
          ),
          SizedBox(height: 7),
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${todayFlag ? (model.completionValue ?? '--') : '--'}',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w600,
                      color: Color(0xFFFFFFFF),
                    ),
                  ),
                  SizedBox(height: 14),
                  Row(
                    children: [
                      Text(
                        '环比：',
                        style: TextStyle(
                          fontSize: 12,
                          color: Color(0xFFB3E9D7),
                        ),
                      ),
                      Text(
                        todayFlag ? model.getRatio() : '--',
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFFFFFFFF),
                        ),
                      ),
                      SizedBox(width: todayFlag ? 5 : 0),
                      todayFlag ? Visibility(
                        visible: model.isShowRatioToward(),
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(6),
                            color: Color(0xFFFFFFFF),
                          ),
                          width: 12,
                          height: 12,
                          child: Center(
                            child: Transform.rotate(
                              angle: math.pi,
                              child: Image.asset(
                                model.isRatioUp()
                                    ? 'assets/images/home/<USER>'
                                    : 'assets/images/home/<USER>',
                                width: 6,
                                height: 7,
                              ),
                            ),
                          ),
                        ),
                      ) : SizedBox()
                    ],
                  ),
                ],
              ),
              Spacer(),
              HomeSalesProgress(
                progress: model.getFinishedProress(),
                progressText: '${model.progress ?? '--'}',
                showNum:todayFlag,
              ),
            ],
          )
        ],
      ),
    );
  }
}
