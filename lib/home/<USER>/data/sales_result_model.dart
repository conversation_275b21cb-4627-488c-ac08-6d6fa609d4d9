import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/home/<USER>/home_org_param_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:json_annotation/json_annotation.dart';

part 'sales_result_model.g.dart';

@JsonSerializable()
class SalesResultModel extends BaseModelV2<SalesResultModel> {
  List<SalesResultEntryModel>? dataStatisticsResult;

  @JsonKey(ignore: true)
  String title = '全部数据';

  @Json<PERSON>ey(ignore: true)
  String timeString = '昨天';

  @JsonKey(ignore: true)
  String rateTitle = '环比';

  @override
  SalesResultModel fromJsonMap(Map<String, dynamic> json) {
    return _$SalesResultModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$SalesResultModelToJson(this);
  }
}

@JsonSerializable()
class SalesResultEntryModel extends BaseModelV2<SalesResultEntryModel> {
  dynamic itemName;

  dynamic itemValue;

  dynamic itemRate;

  /// 6.6.5版本前 使用字段 （6.6.5 后 全年选项也使用此字段）
  dynamic index;

  /// 6.6.5 版本后标题
  dynamic get showTitleName {
    if (itemName == null) {
      return index;
    }
    return itemName;
  }

  /// 6.6.5版本前 使用字段 （6.6.5 后 全年选项也使用此字段）
  dynamic indexValue;

  /// 6.6.5 版本后内容
  dynamic get showContent {
    if (itemValue == null) {
      return indexValue;
    }
    return itemValue;
  }

  /// 6.6.5版本前 使用字段 （6.6.5 后 全年选项也使用此字段）
  dynamic rate;

  /// 6.6.5 版本后判断比例使用
  dynamic get _rate {
    if (itemRate == null) {
      return rate;
    }
    return itemRate;
  }

  dynamic type;

  dynamic targetId;

  dynamic gmvType;

  // false 不能跳转订单
  dynamic viewOrderFlag;

  @JsonKey(ignore: true)
  String rateTitle = '环比';

  /// 获取同比字符串
  String getRatio() {
    if (_rate != null &&
        _rate != '-' &&
        _rate != '--' &&
        _rate != 0 &&
        _rate != "0" &&
        _rate != "0.0" &&
        _rate != "0.00" &&
        _rate != "-0.00") {
      return (this.isRatioUp() ? "+" : "-") + "$_rate%".replaceAll('-', '');
    }
    return "-";
  }

  /// 判断是否是 同比上升
  bool isRatioUp() {
    double? ratio = double.tryParse("$_rate");
    if (ratio != null) {
      return ratio >= 0;
    }
    return false;
  }

  /// 判断是否展示 同比朝向
  bool isShowRatioToward() {
    double? ratio = double.tryParse("$_rate");
    return ratio != null && ratio != 0;
  }

  SalesResultEntryModel();

  factory SalesResultEntryModel.fromJson(Map<String, dynamic> json) {
    return _$SalesResultEntryModelFromJson(json);
  }

  @override
  SalesResultEntryModel fromJsonMap(Map<String, dynamic> json) {
    return _$SalesResultEntryModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$SalesResultEntryModelToJson(this);
  }
}

class SalesResultRequest {
  /// 请求首页销售数据
  /// period: 1.今日 2.本周 3.本月 4.全年 5.自定义 6.昨天 7.上周 8.上月
  /// queryType: 1:数据总览 2:控销 3:优选 4:自营 5:POP
  static Future<SalesResultModel?> requestSalesData({
    required HomeORGParamModel paramModel,
    required String period,
    required int queryType,
    required String drugstoreType,
  }) async {
    String requestPath = 'index/v2/indexAllStatistics';
    String title = '数据总览(元)';
    switch (queryType) {
      case 1:
        requestPath = 'index/v2/indexAllStatistics';
        title = '数据总览(元)';
        break;
      case 2:
        requestPath = 'index/v2/indexControlStatistics';
        title = '控销数据';
        break;
      case 3:
        requestPath = 'index/v2/indexHighGrossStatistics';
        title = '优选数据';
        break;
      case 4:
        requestPath = 'index/v2/indexSelfStatistics';
        title = '自营数据';
        break;
      case 5:
        requestPath = 'index/v2/indexPopStatistics';
        title = 'POP数据';
        break;
      case 6:
        requestPath = 'index/v2/indexSelectionHighGrossStatistics';
        title = '甄选数据';
        break;
      default:
    }
    var params = paramModel.toMap();
    params['period'] = period;
    if (drugstoreType != "-1") {
      params['drugstoreType'] = drugstoreType;
    }
    var result =
    await NetworkV2<SalesResultEntryModel>(SalesResultEntryModel())
        .requestDataV2(
      requestPath,
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var resultModel = SalesResultModel();
    resultModel.title = title;

    String timeString = '昨天';
    switch (period) {
      case '1':
        timeString = '今天';
        break;
      case '2':
        timeString = '本周';
        break;
      case '3':
        timeString = '本月';
        break;
      case '4':
        timeString = '全年';
        break;
      case '5':
        timeString = '自定义';
        break;
      case '6':
        timeString = '昨天';
        break;
      case '7':
        timeString = '上周';
        break;
      case '8':
        timeString = '上月';
        break;
      default:
    }

    resultModel.timeString = timeString;

    var data = result.getListData();
    if (period == "1" || period == "6") {
      resultModel.rateTitle = '同比上周';
      data?.forEach((element) {
        element.rateTitle = "同比上周";
      });
    }
    resultModel.dataStatisticsResult = data;
    return resultModel;
  }

  static Future<SalesResultModel?> requestOldSalesData({
    required HomeORGParamModel paramModel,
    required String period,
    required int queryType,
    required String drugstoreType,
  }) async {
    String requestPath = 'index/indexAllStatistics';
    String title = '数据总览';
    switch (queryType) {
      case 1:
        requestPath = 'index/indexAllStatistics';
        title = '数据总览';
        break;
      case 2:
        requestPath = 'index/indexControlStatistics';
        title = '控销数据';
        break;
      case 3:
        requestPath = 'index/indexHighGrossStatistics';
        title = '优选数据';
        break;
      case 4:
        requestPath = 'index/indexSelfStatistics';
        title = '自营数据';
        break;
      case 5:
        requestPath = 'index/indexPopStatistics';
        title = 'POP数据';
        break;
      default:
    }

    var params = paramModel.toMap();
    params['period'] = period;
    var result =
        await NetworkV2<SalesResultModel>(SalesResultModel()).requestDataV2(
      requestPath,
      parameters: params,
      method: RequestMethod.GET,
      contentType: RequestContentType.FORM,
    );
    var data = result.getData();
    if (data != null) {
      data.title = title;
    }
    String timeString = '昨天';
    switch (period) {
      case '1':
        timeString = '今天';
        break;
      case '2':
        timeString = '本周';
        break;
      case '3':
        timeString = '本月';
        break;
      case '4':
        timeString = '全年';
        break;
      case '5':
        timeString = '自定义';
        break;
      case '6':
        timeString = '昨天';
        break;
      case '7':
        timeString = '上周';
        break;
      case '8':
        timeString = '上月';
        break;
      default:
    }
    data?.timeString = timeString;

    if (period == "1" || period == "6") {
      data?.dataStatisticsResult?.forEach((element) {
        element.rateTitle = "同比上周";
      });
    }
    return data;
  }
}
