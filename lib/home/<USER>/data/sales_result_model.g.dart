// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'sales_result_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

SalesResultModel _$SalesResultModelFromJson(Map<String, dynamic> json) {
  return SalesResultModel()
    ..dataStatisticsResult = (json['dataStatisticsResult'] as List<dynamic>?)
        ?.map((e) => SalesResultEntryModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$SalesResultModelToJson(SalesResultModel instance) =>
    <String, dynamic>{
      'dataStatisticsResult': instance.dataStatisticsResult,
    };

SalesResultEntryModel _$SalesResultEntryModelFromJson(
    Map<String, dynamic> json) {
  return SalesResultEntryModel()
    ..itemName = json['itemName']
    ..itemValue = json['itemValue']
    ..itemRate = json['itemRate']
    ..index = json['index']
    ..indexValue = json['indexValue']
    ..rate = json['rate']
    ..type = json['type']
    ..targetId = json['targetId']
    ..gmvType = json['gmvType']
    ..viewOrderFlag = json['viewOrderFlag'];
}

Map<String, dynamic> _$SalesResultEntryModelToJson(
        SalesResultEntryModel instance) =>
    <String, dynamic>{
      'itemName': instance.itemName,
      'itemValue': instance.itemValue,
      'itemRate': instance.itemRate,
      'index': instance.index,
      'indexValue': instance.indexValue,
      'rate': instance.rate,
      'type': instance.type,
      'targetId': instance.targetId,
      'gmvType': instance.gmvType,
      'viewOrderFlag': instance.viewOrderFlag,
    };
