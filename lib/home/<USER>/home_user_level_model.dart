import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'home_user_level_model.g.dart';

@JsonSerializable()
class UserLevelModel extends BaseModelV2<UserLevelModel>{
  dynamic postType;

  UserLevelModel();

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$UserLevelModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$UserLevelModelToJson(this);
  }
}