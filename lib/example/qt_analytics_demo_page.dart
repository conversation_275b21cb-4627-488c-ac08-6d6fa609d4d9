import 'package:flutter/material.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page_with_analytics.dart';
import 'package:XyyBeanSproutsFlutter/utils/qt_analytics_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/qt_test_helper.dart';

/// QT埋点功能演示页面
class QTAnalyticsDemoPage extends BasePageWithAnalytics {
  const QTAnalyticsDemoPage({Key? key}) : super(key: key);
  
  @override
  String getPageName() => "qt_analytics_demo";
  
  @override
  Map<String, dynamic>? getPageProperties() => {
    'page_type': 'demo',
    'version': '1.0',
    'feature': 'qt_analytics',
  };
  
  @override
  _QTAnalyticsDemoPageState createState() => _QTAnalyticsDemoPageState();
}

class _QTAnalyticsDemoPageState extends BasePageWithAnalyticsState<QTAnalyticsDemoPage> {
  int _counter = 0;
  String _searchKeyword = '';
  final TextEditingController _searchController = TextEditingController();
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('QT埋点演示'),
        backgroundColor: Colors.blue,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // 页面说明
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'QT埋点功能演示',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 8),
                    Text(
                      '本页面演示了各种QT埋点功能的使用方法。页面进入和离开会自动埋点。',
                      style: TextStyle(color: Colors.grey[600]),
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 20),
            
            // 计数器演示
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    Text(
                      '计数器: $_counter',
                      style: TextStyle(fontSize: 24),
                    ),
                    SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _counter++;
                            });
                            // 按钮点击埋点
                            trackButtonClick('increment_button', extra: {
                              'current_count': _counter,
                              'action': 'increment',
                            });
                          },
                          child: Text('增加'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _counter--;
                            });
                            // 按钮点击埋点
                            trackButtonClick('decrement_button', extra: {
                              'current_count': _counter,
                              'action': 'decrement',
                            });
                          },
                          child: Text('减少'),
                        ),
                        ElevatedButton(
                          onPressed: () {
                            setState(() {
                              _counter = 0;
                            });
                            // 按钮点击埋点
                            trackButtonClick('reset_button', extra: {
                              'previous_count': _counter,
                              'action': 'reset',
                            });
                          },
                          child: Text('重置'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 20),
            
            // 搜索演示
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '搜索功能演示',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 12),
                    TextField(
                      controller: _searchController,
                      decoration: InputDecoration(
                        hintText: '请输入搜索关键词',
                        border: OutlineInputBorder(),
                        suffixIcon: IconButton(
                          icon: Icon(Icons.search),
                          onPressed: _performSearch,
                        ),
                      ),
                      onSubmitted: (value) => _performSearch(),
                    ),
                    SizedBox(height: 12),
                    if (_searchKeyword.isNotEmpty)
                      Text(
                        '搜索关键词: $_searchKeyword',
                        style: TextStyle(color: Colors.blue),
                      ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 20),
            
            // 业务操作演示
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '业务操作演示',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 12),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: [
                        ElevatedButton(
                          onPressed: () => _simulateProductView(),
                          child: Text('查看商品'),
                        ),
                        ElevatedButton(
                          onPressed: () => _simulateOrderCreate(),
                          child: Text('创建订单'),
                        ),
                        ElevatedButton(
                          onPressed: () => _simulateUserLogin(),
                          child: Text('模拟登录'),
                        ),
                        ElevatedButton(
                          onPressed: () => _simulateUserLogout(),
                          child: Text('模拟登出'),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 20),
            
            // 自定义事件演示
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '自定义事件演示',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              // 自定义事件埋点
                              trackCustomEvent('custom_demo_action', properties: {
                                'timestamp': DateTime.now().millisecondsSinceEpoch,
                                'user_action': 'demo_interaction',
                                'counter_value': _counter,
                                'search_keyword': _searchKeyword,
                              });

                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('自定义事件已发送')),
                              );
                            },
                            child: Text('发送自定义事件'),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              // 运行完整测试
                              QTTestHelper.runAllTests();
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('QT埋点测试已运行，请查看控制台日志')),
                              );
                            },
                            style: ElevatedButton.styleFrom(
                              primary: Colors.green,
                            ),
                            child: Text('运行测试'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            SizedBox(height: 20),
            
            // 全局属性演示
            Card(
              child: Padding(
                padding: EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '全局属性演示',
                      style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    SizedBox(height: 12),
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              QTAnalyticsUtil.registerGlobalProperties({
                                'app_version': '1.0.0',
                                'user_type': 'demo_user',
                                'session_id': 'demo_session_${DateTime.now().millisecondsSinceEpoch}',
                              });
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('全局属性已设置')),
                              );
                            },
                            child: Text('设置全局属性'),
                          ),
                        ),
                        SizedBox(width: 8),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              QTAnalyticsUtil.clearGlobalProperties();
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(content: Text('全局属性已清除')),
                              );
                            },
                            child: Text('清除全局属性'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  void _performSearch() {
    String keyword = _searchController.text.trim();
    if (keyword.isNotEmpty) {
      setState(() {
        _searchKeyword = keyword;
      });
      
      // 搜索埋点
      QTAnalyticsUtil.trackSearch(keyword, searchType: 'demo_search', resultCount: 10);
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('搜索: $keyword')),
      );
    }
  }
  
  void _simulateProductView() {
    QTAnalyticsUtil.trackProductView(
      'product_123',
      productName: '演示商品',
      category: '演示分类',
    );
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('商品查看埋点已发送')),
    );
  }
  
  void _simulateOrderCreate() {
    String orderId = 'order_${DateTime.now().millisecondsSinceEpoch}';
    QTAnalyticsUtil.trackOrder(orderId, 'create', amount: 99.99);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('订单创建埋点已发送: $orderId')),
    );
  }
  
  void _simulateUserLogin() {
    String userId = 'demo_user_${DateTime.now().millisecondsSinceEpoch}';
    QTAnalyticsUtil.trackUserLogin(userId);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('用户登录埋点已发送: $userId')),
    );
  }
  
  void _simulateUserLogout() {
    QTAnalyticsUtil.trackUserLogout();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('用户登出埋点已发送')),
    );
  }
  
  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
