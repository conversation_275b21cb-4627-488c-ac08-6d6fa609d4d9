import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_data.dart';
import 'package:flutter/material.dart';

class UserInfoUtil {
  /// 角色类型 0 :BD 1:BDM 2:跟进人 3:跟进人BDM
  static final int TYPE_BD = 0;
  static final int TYPE_BDM = 1;
  static final int TYPE_GJR = 2;
  static final int TYPE_GJR_BDM = 3;

  static Future<bool> isBDMOrGJRBDM() async {
    debugPrint("QuickTrackingSDK------ isBDMOrGJRBDM() 方法被调用");
    var userInfoData = await getUserInfo();
    if (userInfoData != null &&
        (userInfoData.roleType == TYPE_BDM ||
            userInfoData.roleType == TYPE_GJR_BDM)) {
      debugPrint("QuickTrackingSDK------ isBDMOrGJRBDM() 返回 true");
      return true;
    } else {
      debugPrint("QuickTrackingSDK------ isBDMOrGJRBDM() 返回 false");
      return false;
    }
  }

  /// 是否是跟进人权限
  static Future<bool> isFollowPeople() async {
    var userInfoData = await getUserInfo();
    if (userInfoData != null &&
        (userInfoData.roleType == TYPE_GJR ||
            userInfoData.roleType == TYPE_GJR_BDM)) {
      return true;
    } else {
      return false;
    }
  }

  static Future<String> sysUserId() async {
    var userInfoData = await getUserInfo();
    if (userInfoData != null && userInfoData.sysUserId != null) {
      return userInfoData.sysUserId!;
    }
    return "";
  }

  static Future<String> roleType() async {
    var userInfoData = await getUserInfo();
    if (userInfoData != null && userInfoData.roleType != null) {
      return "${userInfoData.roleType}";
    }
    return "";
  }

  static Future<UserInfoData?> getUserInfo() async {
    print("请求地址:getUserInfo----😄😄😄😄----请求参数:----😄😄😄😄----返回结果:----123123123 ");
    try {
      var userInfoJson = await XYYContainer.bridgeCall("user_info");
      print("guan ------ bridgeCall 返回结果: ${userInfoJson}");

      var decodeMap = json.decode(userInfoJson);
      print("guan ------ JSON 解析成功");

      var userInfoData = UserInfoData.fromJson(decodeMap);
      print("guan ------ UserInfoData 创建成功: ${userInfoData.toString()}");

      return userInfoData;
    } catch (e) {
      print("guan ------ 发生异常: $e");
      return null;
    }
  }
}
