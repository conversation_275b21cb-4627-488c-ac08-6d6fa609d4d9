import 'package:qt_common_sdk/qt_common_sdk.dart';

/// QT埋点工具类
/// 统一管理QT埋点事件上报
class QTAnalyticsUtil {
  
  /// 私有构造函数，防止实例化
  QTAnalyticsUtil._();
  
  /// 自定义事件埋点
  /// [event] 事件ID
  /// [properties] 自定义参数
  static void trackEvent(String event, {Map<String, dynamic>? properties}) {
    try {
      QTCommonSdk.onEvent(event, properties ?? {});
      print("QT Track Event: $event, Properties: $properties");
    } catch (e) {
      print("QT Track Event Error: $e");
    }
  }
  
  /// 带页面信息的事件埋点
  /// [event] 事件ID
  /// [pageName] 页面名称
  /// [properties] 自定义参数
  static void trackEventWithPage(String event, String pageName, {Map<String, dynamic>? properties}) {
    try {
      QTCommonSdk.onEventWithPage(event, pageName, properties ?? {});
      print("QT Track Event With Page: $event, Page: $pageName, Properties: $properties");
    } catch (e) {
      print("QT Track Event With Page Error: $e");
    }
  }
  
  /// 页面开始统计
  /// [pageName] 页面名称
  static void trackPageStart(String pageName) {
    try {
      QTCommonSdk.onPageStart(pageName);
      print("QT Page Start: $pageName");
    } catch (e) {
      print("QT Page Start Error: $e");
    }
  }
  
  /// 页面结束统计
  /// [pageName] 页面名称
  static void trackPageEnd(String pageName) {
    try {
      QTCommonSdk.onPageEnd(pageName);
      print("QT Page End: $pageName");
    } catch (e) {
      print("QT Page End Error: $e");
    }
  }
  
  /// 设置页面属性
  /// [pageName] 页面名称
  /// [properties] 页面属性
  static void setPageProperty(String pageName, Map<String, dynamic> properties) {
    try {
      QTCommonSdk.setPageProperty(pageName, properties);
      print("QT Set Page Property: $pageName, Properties: $properties");
    } catch (e) {
      print("QT Set Page Property Error: $e");
    }
  }
  
  /// 用户登录埋点
  /// [userId] 用户ID
  static void trackUserLogin(String userId) {
    try {
      QTCommonSdk.onProfileSignIn(userId);
      print("QT User Login: $userId");
    } catch (e) {
      print("QT User Login Error: $e");
    }
  }
  
  /// 用户登出埋点
  static void trackUserLogout() {
    try {
      QTCommonSdk.onProfileSignOff();
      print("QT User Logout");
    } catch (e) {
      print("QT User Logout Error: $e");
    }
  }
  
  /// 注册全局属性
  /// [properties] 全局属性
  static void registerGlobalProperties(Map<String, dynamic> properties) {
    try {
      QTCommonSdk.registerGlobalProperties(properties);
      print("QT Register Global Properties: $properties");
    } catch (e) {
      print("QT Register Global Properties Error: $e");
    }
  }
  
  /// 删除全局属性
  /// [propertyName] 属性名
  static void unregisterGlobalProperty(String propertyName) {
    try {
      QTCommonSdk.unregisterGlobalProperty(propertyName);
      print("QT Unregister Global Property: $propertyName");
    } catch (e) {
      print("QT Unregister Global Property Error: $e");
    }
  }
  
  /// 清除所有全局属性
  static void clearGlobalProperties() {
    try {
      QTCommonSdk.clearGlobalProperties();
      print("QT Clear Global Properties");
    } catch (e) {
      print("QT Clear Global Properties Error: $e");
    }
  }
  
  /// 跳过页面统计
  /// [pageName] 页面名称
  static void skipPageTracking(String pageName) {
    try {
      QTCommonSdk.skipMe(pageName);
      print("QT Skip Page Tracking: $pageName");
    } catch (e) {
      print("QT Skip Page Tracking Error: $e");
    }
  }
  
  // 常用的业务埋点事件
  
  /// 按钮点击埋点
  /// [buttonName] 按钮名称
  /// [pageName] 页面名称
  /// [extra] 额外参数
  static void trackButtonClick(String buttonName, String pageName, {Map<String, dynamic>? extra}) {
    Map<String, dynamic> properties = {
      'button_name': buttonName,
      'page_name': pageName,
    };
    if (extra != null) {
      properties.addAll(extra);
    }
    trackEvent('button_click', properties: properties);
  }
  
  /// 页面访问埋点
  /// [pageName] 页面名称
  /// [extra] 额外参数
  static void trackPageView(String pageName, {Map<String, dynamic>? extra}) {
    Map<String, dynamic> properties = {
      'page_name': pageName,
    };
    if (extra != null) {
      properties.addAll(extra);
    }
    trackEvent('page_view', properties: properties);
  }
  
  /// 搜索埋点
  /// [keyword] 搜索关键词
  /// [searchType] 搜索类型
  /// [resultCount] 搜索结果数量
  static void trackSearch(String keyword, {String? searchType, int? resultCount}) {
    Map<String, dynamic> properties = {
      'keyword': keyword,
    };
    if (searchType != null) {
      properties['search_type'] = searchType;
    }
    if (resultCount != null) {
      properties['result_count'] = resultCount;
    }
    trackEvent('search', properties: properties);
  }
  
  /// 商品查看埋点
  /// [productId] 商品ID
  /// [productName] 商品名称
  /// [category] 商品分类
  static void trackProductView(String productId, {String? productName, String? category}) {
    Map<String, dynamic> properties = {
      'product_id': productId,
    };
    if (productName != null) {
      properties['product_name'] = productName;
    }
    if (category != null) {
      properties['category'] = category;
    }
    trackEvent('product_view', properties: properties);
  }
  
  /// 订单相关埋点
  /// [orderId] 订单ID
  /// [action] 操作类型 (create, pay, cancel, complete等)
  /// [amount] 订单金额
  static void trackOrder(String orderId, String action, {double? amount}) {
    Map<String, dynamic> properties = {
      'order_id': orderId,
      'action': action,
    };
    if (amount != null) {
      properties['amount'] = amount;
    }
    trackEvent('order_$action', properties: properties);
  }
}
