class TimeUtils {
  String formatTime(int time) {
    String timeStr = DateTime.fromMillisecondsSinceEpoch(time).toString();
    if (timeStr.length > 4) {
      timeStr = timeStr.substring(0, timeStr.length - 4);
    }
    return timeStr;
  }

  String formatTime1(int time) {
    String timeStr = DateTime.fromMillisecondsSinceEpoch(time).toString();
    if (timeStr.length > 4) {
      timeStr = timeStr.substring(0, timeStr.length - 4);
    }
    return timeStr.replaceAll("-", ".");
  }

  String formatTime2(int time) {
    DateTime timeStr = DateTime.fromMillisecondsSinceEpoch(time);
    String timeResult = '${timeStr.year}年${timeStr.month}月${timeStr.day}日';
    return timeResult;
  }
}
