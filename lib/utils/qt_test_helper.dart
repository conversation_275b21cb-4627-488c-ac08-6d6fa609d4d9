import 'package:XyyBeanSproutsFlutter/utils/qt_analytics_util.dart';

/// QT埋点测试辅助类
class QTTestHelper {
  
  /// 测试基础埋点功能
  static void testBasicTracking() {
    print("=== QT埋点测试开始 ===");
    
    try {
      // 测试自定义事件埋点
      QTAnalyticsUtil.trackEvent('test_event', properties: {
        'test_type': 'basic_tracking',
        'timestamp': DateTime.now().millisecondsSinceEpoch,
      });
      print("✅ 自定义事件埋点测试通过");
      
      // 测试页面埋点
      QTAnalyticsUtil.trackPageView('test_page', extra: {
        'page_type': 'test',
      });
      print("✅ 页面埋点测试通过");
      
      // 测试按钮点击埋点
      QTAnalyticsUtil.trackButtonClick('test_button', 'test_page');
      print("✅ 按钮点击埋点测试通过");
      
      // 测试搜索埋点
      QTAnalyticsUtil.trackSearch('测试关键词', searchType: 'test_search');
      print("✅ 搜索埋点测试通过");
      
      // 测试用户行为埋点
      QTAnalyticsUtil.trackUserLogin('test_user_123');
      print("✅ 用户登录埋点测试通过");
      
      print("=== QT埋点测试完成 ===");
      
    } catch (e) {
      print("❌ QT埋点测试失败: $e");
    }
  }
  
  /// 测试页面生命周期埋点
  static void testPageLifecycle(String pageName) {
    try {
      QTAnalyticsUtil.trackPageStart(pageName);
      print("✅ 页面开始埋点: $pageName");
      
      // 模拟页面停留
      Future.delayed(Duration(seconds: 1), () {
        QTAnalyticsUtil.trackPageEnd(pageName);
        print("✅ 页面结束埋点: $pageName");
      });
      
    } catch (e) {
      print("❌ 页面生命周期埋点测试失败: $e");
    }
  }
  
  /// 测试全局属性功能
  static void testGlobalProperties() {
    try {
      // 设置全局属性
      QTAnalyticsUtil.registerGlobalProperties({
        'app_version': '1.0.0',
        'user_type': 'test_user',
        'device_type': 'mobile',
      });
      print("✅ 全局属性设置测试通过");
      
      // 发送带全局属性的事件
      QTAnalyticsUtil.trackEvent('test_with_global_props');
      print("✅ 带全局属性的事件埋点测试通过");
      
      // 删除单个全局属性
      QTAnalyticsUtil.unregisterGlobalProperty('device_type');
      print("✅ 删除单个全局属性测试通过");
      
      // 清除所有全局属性
      QTAnalyticsUtil.clearGlobalProperties();
      print("✅ 清除全局属性测试通过");
      
    } catch (e) {
      print("❌ 全局属性测试失败: $e");
    }
  }
  
  /// 测试业务场景埋点
  static void testBusinessScenarios() {
    try {
      // 商品相关埋点
      QTAnalyticsUtil.trackProductView('product_123', 
        productName: '测试商品', 
        category: '测试分类'
      );
      print("✅ 商品查看埋点测试通过");
      
      // 订单相关埋点
      QTAnalyticsUtil.trackOrder('order_123', 'create', amount: 99.99);
      print("✅ 订单创建埋点测试通过");
      
      QTAnalyticsUtil.trackOrder('order_123', 'pay', amount: 99.99);
      print("✅ 订单支付埋点测试通过");
      
      // 用户行为埋点
      QTAnalyticsUtil.trackUserLogout();
      print("✅ 用户登出埋点测试通过");
      
    } catch (e) {
      print("❌ 业务场景埋点测试失败: $e");
    }
  }
  
  /// 运行所有测试
  static void runAllTests() {
    print("\n🚀 开始运行QT埋点完整测试...\n");
    
    // 基础功能测试
    testBasicTracking();
    print("");
    
    // 页面生命周期测试
    testPageLifecycle('test_lifecycle_page');
    print("");
    
    // 全局属性测试
    testGlobalProperties();
    print("");
    
    // 业务场景测试
    testBusinessScenarios();
    print("");
    
    print("🎉 QT埋点测试全部完成！");
    print("请检查控制台日志和阿里云QT控制台数据。");
  }
}
