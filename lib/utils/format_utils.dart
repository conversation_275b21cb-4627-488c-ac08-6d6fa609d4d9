class FormatUtils {
  static String formatText(String? txt) {
    if (txt == null || txt.isEmpty) {
      return "—";
    }
    return txt;
  }

 static Map deepCopy(Map original) {
  Map copy = {};
  original.forEach((key, value) {
    if (value is Map) {
      // 如果值是一个 Map，则递归调用 deepCopy 函数
      copy[key] = deepCopy(value);
    } else if (value is List) {
      // 如果值是一个列表，递归遍历列表并拷贝其元素
      copy[key] = [];
      for (var item in value) {
        if (item is Map) {
          copy[key].add(deepCopy(item));
        } else {
          copy[key].add(item);
        }
      }
    } else {
      // 其他情况下，直接拷贝值
      copy[key] = value;
    }
  });
  return copy;
}
}
