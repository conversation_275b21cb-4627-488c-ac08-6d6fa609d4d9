import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

class CommonAsyncUtils {
  /// 自带pop
  static dialog({
    String? title,
    Widget? content,
    required BuildContext context,
    VoidCallback? onCancel,
    VoidCallback? onConfirm,
  }) {
    showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: Container(
              margin: EdgeInsets.all(40),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7), color: Colors.white),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                      margin: EdgeInsets.fromLTRB(25, 10, 25, 0),
                      child: Text(
                        title ?? "",
                        style: TextStyle(
                            color: Color(0xff333333),
                            fontSize: 17,
                            fontWeight: FontWeight.normal),
                      )),
                  Container(
                    padding: EdgeInsets.fromLTRB(25, 10, 25, 10),
                    child: content ?? const SizedBox(),
                  ),
                  Container(
                    height: 50,
                    child: Row(
                      children: [
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                            onCancel?.call();
                          },
                          child: Container(
                            height: 50,
                            alignment: Alignment.center,
                            child: Text("取消",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff999999),
                                    fontWeight: FontWeight.normal)),
                          ),
                        )),
                        Container(
                          color: Color(0xffe1e1e5),
                          width: 0.5,
                          child: Container(),
                        ),
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                            onConfirm?.call();
                          },
                          child: Container(
                            height: 50,
                            alignment: Alignment.center,
                            child: Text("确认",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff35c561),
                                    fontWeight: FontWeight.normal)),
                          ),
                        )),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }
}

/// 带loading异步按钮
class LoaingButton extends StatelessWidget {
  /// 文本
  final String? text;

  /// 文本-loading色
  final Color? textColor;

  /// 底色
  final Color? backgroundColor;

  /// 按钮内容
  final Widget? content;

  /// 大小
  final Size? size;

  /// click
  final AsyncCallback? onPressed;

  /// 是否需要loading
  final bool isLoading;

  LoaingButton({
    this.text,
    this.textColor,
    this.backgroundColor,
    this.content,
    this.size,
    this.onPressed,
    this.isLoading = true,
  });
  @override
  Widget build(BuildContext context) {
    ValueNotifier<bool> notifier = ValueNotifier<bool>(false);
    return ValueListenableBuilder(
        valueListenable: notifier,
        builder: (BuildContext context, bool loading, Widget? child) {
          return TextButton(
            onPressed: () async {
              if (isLoading) {
                if (notifier.value) {
                  return;
                }
                notifier.value = true;
                await onPressed?.call();
                notifier.value = false;
              } else {
                onPressed?.call();
              }
            },
            child: loading
                ? SizedBox(
                    width: 20,
                    height: 20,
                    child: Center(
                      child: CircularProgressIndicator(
                        strokeWidth: 4.0,
                        backgroundColor: Colors.transparent,
                        valueColor: AlwaysStoppedAnimation<Color>(
                            textColor ?? Colors.white),
                      ),
                    ),
                  )
                : (content ??
                    Text(
                      text ?? "确定",
                      style: TextStyle(
                        color: textColor ?? Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    )),
            style: ButtonStyle(
              // 按钮大小
              minimumSize: MaterialStateProperty.all(size ?? Size(109, 37)),
              // 水波纹颜色 不想使用 设置透明即可
              overlayColor: MaterialStateProperty.all(Colors.transparent),
              //背景颜色
              backgroundColor: MaterialStateProperty.all(
                  backgroundColor ?? Color(0xff00B377)),
              // 设置形状、圆角
              shape: MaterialStateProperty.all(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(5.0), // 设置圆角度数为5
                ),
              ),
            ),
          );
        });
  }
}

/// 通用输入框
class InputBox extends StatefulWidget {
  /// 默认渲染文本
  final String? value;
  /// 默认提示文本
  final String? hitText;
  /// 控制
  final TextEditingController? controller;
  final FocusNode? focusNode;
  /// 输入事件
  final ValueChanged<String>? onChanged;
  /// 键盘搜索
  final ValueChanged<String>? onSubmit;
  InputBox({
    this.value,
    this.hitText,
    this.onChanged,
    this.onSubmit,
    this.controller,
    this.focusNode,
  });

  @override
  State<StatefulWidget> createState() => InputBoxState();
}

class InputBoxState extends State<InputBox> {
  // 控制器
  FocusNode? inputFocusNode;
  TextEditingController? inputController;
  @override
  void initState() {
    super.initState();
    inputController = widget.controller ?? TextEditingController();
    inputController?.text = widget.value ?? '';
    inputFocusNode = widget.focusNode ?? FocusNode();
  }

  @override
  Widget build(BuildContext context) { 
    return TextField(
      autofocus: false,
      focusNode: inputFocusNode,
      controller: inputController,
      cursorColor: Colors.black,
      decoration: InputDecoration(
        prefixIcon: Icon(
          Icons.search_rounded,
          size: 20,
          color: Color(0xFFb3b3c2),
        ),
        border: InputBorder.none,
        isDense: true,
        hintText: widget.hitText,
        contentPadding: EdgeInsets.symmetric(vertical: 8),
        hintStyle: TextStyle(
          fontSize: 14,
          color: Color(0xFFb3b3c2),
        ),
      ),
      style: TextStyle(
        fontSize: 16,
        color: Color(0xFF292933),
      ),
      textInputAction: TextInputAction.search,
      onChanged: widget.onChanged,
      onSubmitted: widget.onSubmit,
      keyboardAppearance: Brightness.light,
    );
  }
}
