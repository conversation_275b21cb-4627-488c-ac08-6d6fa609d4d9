import 'package:json_annotation/json_annotation.dart';

part 'base_root_model.g.dart';

@JsonSerializable()
class BaseRootModel<T> {
  bool? isSuccess = false;
  String? status = "";
  String? msg = "";
  String? errorMsg;
  dynamic code;

  dynamic data;

  T? getData() {
    return data as T?;
  }

  List<T>? getListData() {
    return data as List<T>?;
  }

  BaseRootModel();

  factory BaseRootModel.fromJson(Map<String, dynamic> json) =>
      _$BaseRootModelFromJson<T>(json);
}
