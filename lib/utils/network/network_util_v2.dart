import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_root_model.dart';
import 'package:flutter/services.dart';

import 'base_model.dart';
import 'list_model.dart';

const SUCCESS_CODE = 'success';

class NetworkV2<T extends BaseModelV2> {
  T dataModel;
  ListModel<T>? dataListModel;

  bool showErrorToast = true;

  NetworkV2(this.dataModel);

  /// 网络请求封装
  /// ！！！！！！！！！！！！！！！注意！！！！！！！！！！！！！！！
  /// 请求成功时的dataModel是一个新的对象，与传入的dataModel不是同一个
  /// ！！！！！！！！！！！！！！！注意！！！！！！！！！！！！！！！
  Future<BaseRootModel<T>> requestDataV2(
    String path, {
    RequestMethod? method,
    RequestContentType? contentType = RequestContentType.FORM,
    Map<String, String>? additionalHeader,
    dynamic parameters,
    bool? showErrorToast,
  }) async {
    if (showErrorToast != null) {
      this.showErrorToast = showErrorToast;
    }
    additionalHeader = Map<String, String>();
    try {
      var result = await XYYContainer.requestChannel.request(path,
          method: method,
          contentType: contentType,
          additionalHeader: additionalHeader,
          parameters: parameters);
      print('请求地址:$path----😄😄😄😄----请求参数:$parameters----😄😄😄😄----返回结果:$result ');
      return handleSuccessV2(result);
    } on PlatformException catch (e) {
      handleError(e.code, e.message);
    } catch (e) {
      handleSystemError();
      print(e);
      return BaseRootModel<T>();
    }
    return BaseRootModel<T>();
  }

  BaseRootModel<T> handleSuccessV2(String result) {
    var decodeJsonMap = json.decode(result);
    var baseRootModel = BaseRootModel<T>.fromJson(decodeJsonMap);
    var dataFromJson;
    if (baseRootModel.data != null) {
      if (baseRootModel.data is String) {
        dataFromJson =
            dataModel.fromJson(baseRootModel.getData()?.toString() ?? "");
      } else if (baseRootModel.data is Map) {
        dataFromJson = dataModel.fromJsonMap(baseRootModel.data);
        print(dataFromJson);
      } else if (baseRootModel.data is List) {
        List<T> resultList = [];
        if (baseRootModel.data != null) {
          (baseRootModel.data as List).forEach((item) {
            resultList.add(dataModel.fromJsonMap(item));
          });
        }
        dataFromJson = resultList;
      }
    }

    if (baseRootModel.status == SUCCESS_CODE) {
      baseRootModel.data = dataFromJson;
      baseRootModel.isSuccess = true;
    }
    if (baseRootModel.isSuccess == null) {
      baseRootModel.isSuccess = false;
    }
    if (!baseRootModel.isSuccess!) {
      handleError(baseRootModel.status ?? "",
          baseRootModel.errorMsg ?? baseRootModel.msg);
    }
    return baseRootModel;
  }

  void handleError(String code, String? message) {
    if (this.showErrorToast) {
      var toastMsg =
          (message == null || message.isEmpty) ? "网络请求错误，请稍后重试!" : message;
      XYYContainer.toastChannel.toast(toastMsg);
    }
  }

  void handleSystemError() {}
}
