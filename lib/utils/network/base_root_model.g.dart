// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'base_root_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

BaseRootModel<T> _$BaseRootModelFromJson<T>(Map<String, dynamic> json) {
  return BaseRootModel<T>()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorMsg = json['errorMsg'] as String?
    ..code = json['code']
    ..data = json['data'];
}

Map<String, dynamic> _$BaseRootModelToJson<T>(BaseRootModel<T> instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorMsg': instance.errorMsg,
      'code': instance.code,
      'data': instance.data,
    };
