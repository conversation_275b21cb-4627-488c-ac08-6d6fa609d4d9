import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:flutter/services.dart';

import 'base_model.dart';
import 'list_model.dart';

const SUCCESS_CODE = 'success';
const FAILURE_CODE = 'failure';

class Network<T extends BaseModel> {
  T? dataModel;
  ListModel<T>? dataListModel;

  bool showErrorToast = true;

  Network(this.dataModel) : assert(dataModel != null);

  /// 网络请求封装
  /// ！！！！！！！！！！！！！！！注意！！！！！！！！！！！！！！！
  /// 请求成功时的dataModel是一个新的对象，与传入的dataModel不是同一个
  /// ！！！！！！！！！！！！！！！注意！！！！！！！！！！！！！！！
  Future<T> requestData(
    String path, {
    RequestMethod? method,
    RequestContentType? contentType,
    Map<String, String>? additionalHeader,
    dynamic parameters,
    bool? showErrorToast,
  }) async {
    if (showErrorToast != null) {
      this.showErrorToast = showErrorToast;
    }
    // additionalHeader = preProcessParams(additionalHeader);
    additionalHeader = Map<String, String>();
    try {
      var result = await XYYContainer.requestChannel.request(path,
          method: method,
          contentType: contentType,
          additionalHeader: additionalHeader,
          parameters: parameters);
      handleSuccess(result);
    } on PlatformException catch (e) {
      handleError(e.code, e.message);
    } catch (e) {
      handleSystemError();
      print(e);
    }
    if (dataModel?.isSuccess == null) {
      dataModel?.isSuccess = false;
    }
    return dataModel!;
  }

  Future<ListModel<T>> requestListData(
    String path, {
    RequestMethod? method,
    RequestContentType? contentType,
    Map<String, String>? additionalHeader,
    dynamic parameters,
    bool? showErrorToast,
  }) async {
    if (showErrorToast != null) {
      this.showErrorToast = showErrorToast;
    }
    additionalHeader = preProcessParams(additionalHeader);
    try {
      dataListModel = ListModel();
      var result = await XYYContainer.requestChannel.request(path,
          method: method,
          contentType: contentType,
          additionalHeader: additionalHeader,
          parameters: parameters);
      handleListSuccess(result);
    } on PlatformException catch (e) {
      handleError(e.code, e.message);
    } catch (e) {
      handleSystemError();
    }
    return dataListModel!;
  }

  void handleError(String? status, String? message) {
    //这里需要处理下一些异常，如超时、断网等
    if (this.showErrorToast) {
      var toastMsg =
          (message == null || message.isEmpty) ? "网络请求错误，请稍后重试!" : message;
      XYYContainer.toastChannel.toast(toastMsg);
    }
    setCommonData(false, status, message);
  }

  void setCommonData(bool isSuccess, String? status, String? message) {
    if (dataModel != null) {
      dataModel!.isSuccess = isSuccess;
      dataModel!.status = status;
      dataModel!.errorMsg = message;
    }
    if (dataListModel != null) {
      dataListModel!.isSuccess = isSuccess;
      dataListModel!.status = status;
      dataListModel!.errorMsg = message;
    }
  }

  void handleSystemError() {
    /*if (this.showErrorToast) {
      XYYContainer.toastChannel.toast('网络请求错误，请稍后重试');
    }
    setCommonData(false, -2, '网络请求错误，请稍后重试');*/
  }

  void handleSuccess(String result) {
    var fromJson = dataModel!.fromJson(result);
    print("guan:${fromJson.toString()}");
    if (fromJson != null && fromJson is BaseModel) {
      dataModel!.status = fromJson.status;
      dataModel!.errorMsg = fromJson.errorMsg;
    }
    switch (fromJson.status) {
      case SUCCESS_CODE:
        if (dataModel is NetworkBaseModel) {
          dataModel = fromJson;
        } else if (fromJson.data != null) {
          dataModel = fromJson.data;
        } else if (fromJson != null) {
          dataModel = fromJson;
        }
        dataModel!.isSuccess = true;
        break;
      default:
        handleError(dataModel!.status, dataModel!.errorMsg);
        break;
    }
  }

  void handleListSuccess(String result) {
    var decodeJson = json.decode(result);
    if (decodeJson is! Map) {
      throw Exception();
    }
    var jsonMap = decodeJson;

    var status = jsonMap['status'];
    var message = jsonMap['errorMsg'];
    var data = jsonMap['data'];
    dataListModel!.status = status;
    dataListModel!.errorMsg = message;

    switch (status) {
      case SUCCESS_CODE:
        List<T> resultList = [];
        if (data != null) {
          (data as List).forEach((item) {
            resultList.add(dataModel!.fromJsonMap(item));
          });
        }
        dataListModel!.data = resultList;
        dataListModel!.isSuccess = true;
        break;
      default:
        handleError(status, message);
        break;
    }
  }

  void _logout(String message) {
    /*XYYContainer.toastChannel.toast(message == null ? "退出登录" : message);
    var globalContext = BaseState.getLastGlobalContext();
    Navigator.of(globalContext).pushNamedAndRemoveUntil('/Login', (route) {
      return false;
    });*/
  }

  Map<String, String> preProcessParams(Map<String, String>? additionalHeader) {
    if (additionalHeader == null) {
      additionalHeader = Map<String, String>();
    }
    /*if (!additionalHeader.containsKey('honeycombId')) {
      if (HoneycombManager.instance.currentHoneycomb != null &&
          HoneycombManager.instance.currentHoneycomb.id != -1) {
        additionalHeader['honeycombId'] =
            HoneycombManager.instance.currentHoneycomb.id.toString();
      }
    }*/
    return additionalHeader;
  }
}
