import 'dart:convert';

abstract class BaseModel<T> {
  bool? isSuccess = false;
  String? status = "";
  String? msg = "";
  int? errorCode = -1;
  String? errorMsg = "";
  String? message;
  T? data;

  T fromJson(String jsonStr) {
    var decodeJsonMap = json.decode(jsonStr);
    return fromJsonMap(decodeJsonMap);
  }

  T fromJsonMap(Map<String, dynamic> json);

  Map<String, dynamic> toJson();
}

abstract class BaseModelV2<T> {
  T fromJson(String jsonStr) {
    var decodeJsonMap = json.decode(jsonStr);
    return fromJsonMap(decodeJsonMap);
  }

  T fromJsonMap(Map<String, dynamic> json);

  Map<String, dynamic> toJson();
}

class NetworkBaseModel extends BaseModel<dynamic> {
  NetworkBaseModel();

  @override
  NetworkBaseModel fromJsonMap(Map<String, dynamic> json) {
    return NetworkBaseModel()
      ..isSuccess = json['isSuccess'] as bool?
      ..status = json['status'] as String?
      ..msg = json['msg'] as String?
      ..errorCode = json['errorCode'] as int?
      ..errorMsg = json['errorMsg'] as String?
      ..message = json['message'] as String?
      ..data = json['data'];
  }

  @override
  Map<String, dynamic> toJson() {
    return <String, dynamic>{
      'isSuccess': this.isSuccess,
      'status': this.status,
      'msg': this.msg,
      'errorCode': this.errorCode,
      'errorMsg': this.errorMsg,
      'message': this.message,
      'data': this.data,
    };
  }
}

class NetworkBaseModelV2 extends BaseModelV2<NetworkBaseModelV2> {
  NetworkBaseModelV2();

  @override
  NetworkBaseModelV2 fromJsonMap(Map<String, dynamic> json) {
    return NetworkBaseModelV2();
  }

  @override
  Map<String, dynamic> toJson() {
    return <String, dynamic>{};
  }
}
