// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_count_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageCountData _$MessageCountDataFromJson(Map<String, dynamic> json) {
  return MessageCountData()
    ..taskUnReadCount = json['taskUnReadCount'] as int?
    ..licenseUnReadCount = json['licenseUnReadCount'] as int?
    ..dynamicUnReadCount = json['dynamicUnReadCount'] as int?
    ..imUnReadCount = json['imUnReadCount'] as int?
    ..newPoiUnReadCount = json['newPoiUnReadCount'] as int?
    ..skuCollectMessageUnReadCount =
        json['skuCollectMessageUnReadCount'] as int?;
}

Map<String, dynamic> _$MessageCountDataToJson(MessageCountData instance) =>
    <String, dynamic>{
      'taskUnReadCount': instance.taskUnReadCount,
      'licenseUnReadCount': instance.licenseUnReadCount,
      'dynamicUnReadCount': instance.dynamicUnReadCount,
      'imUnReadCount': instance.imUnReadCount,
      'newPoiUnReadCount': instance.newPoiUnReadCount,
      'skuCollectMessageUnReadCount': instance.skuCollectMessageUnReadCount,
    };
