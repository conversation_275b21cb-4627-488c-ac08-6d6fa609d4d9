import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'group_data.g.dart';

/// 组织结构数据，json示例：
/// {
///     "id": "P1486-1694",
///    "code": "P1486-1694",
///     "name": "用户运营岗",
///     "parentid": "D1486",
///     "sysUserList": [
///         {
///             "realName": "赵丽",
///             "groupId": "P1486-1694",
///             "id": 14709,
///             "jobNumber": "zhaoli1"
///         }
///     ],
///     "isChecked": 0
/// }
@JsonSerializable()
class GroupData extends BaseModelV2<GroupData> {
  dynamic id;

  /// 部门或岗位特有数据
  dynamic code;
  dynamic name;
  @JsonKey(name: "parentid")
  dynamic parentId;
  List<GroupData>? childGroup;
  List<GroupData>? sysUserList;

  /// 具体人特有数据
  dynamic realName;
  dynamic groupId;
  dynamic jobNumber;

  GroupData();

  /// 是否为部门
  bool isDepartment() {
    return code != null;
  }

  fromJsonMap(Map<String, dynamic> json) {
    return GroupData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$GroupDataToJson(this);
  }

  factory GroupData.fromJson(Map<String, dynamic> json) =>
      _$GroupDataFromJson(json);
}
