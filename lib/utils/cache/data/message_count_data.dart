import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'message_count_data.g.dart';

@JsonSerializable()
class MessageCountData extends BaseModelV2<MessageCountData> {
  //未读任务消息数量
  int? taskUnReadCount;
  //未读资质消息数量
  int? licenseUnReadCount;
  //未读通知/动态 消息数量
  int? dynamicUnReadCount;
  //未读im-客服 消息数量
  int? imUnReadCount;
  // 新门店提醒消息数量
  int? newPoiUnReadCount;
  // 商品集掉落预警唯独消息
  int? skuCollectMessageUnReadCount;

  MessageCountData();

  fromJsonMap(Map<String, dynamic> json) {
    return MessageCountData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$MessageCountDataToJson(this);
  }

  factory MessageCountData.fromJson(Map<String, dynamic> json) =>
      _$MessageCountDataFromJson(json);
}
