import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_level_data.g.dart';

@JsonSerializable()
class CustomerLevelData extends BaseModelV2<CustomerLevelData> {
  dynamic code;
  dynamic desc;

  CustomerLevelData();

  fromJsonMap(Map<String, dynamic> json) {
    return CustomerLevelData.fromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$CustomerLevelDataToJson(this);
  }

  factory CustomerLevelData.fromJson(Map<String, dynamic> json) =>
      _$CustomerLevelDataFromJson(json);
}