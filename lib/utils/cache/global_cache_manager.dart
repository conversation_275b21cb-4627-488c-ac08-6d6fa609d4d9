import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/message/tools/message_constant.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/customer_level_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/group_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/cache/data/message_count_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_root_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class GlobalCacheManager {
  GlobalCacheManager._privateConstructor();

  static final GlobalCacheManager _instance =
      GlobalCacheManager._privateConstructor();

  factory GlobalCacheManager() {
    return _instance;
  }

  static GlobalCacheManager get instance {
    return _instance;
  }

  /// 获取客户等级枚举，如果有缓存会直接用缓存
  Future<List<CustomerLevelData>?> get customerLevelList async {
    return await _requestCustomerLevelList();
  }

  /// 获取缓存的消息数量，不会刷新接口
  Future<Map<MessageType, int>?> get cacheMessageCountMap async {
    return await _requestMessageCountMap(true);
  }

  /// 获取最新的消息数量，会刷新接口
  Future<Map<MessageType, int>?> get messageCountMap async {
    return await _requestMessageCountMap(false);
  }

  /// 获取组织结构信息，仅限m级，不会刷新接口
  Future<List<GroupData>?> get cacheGroupDataList async {
    return await _requestGroupDataList(false);
  }

  /// 获取组织结构信息，仅限m级，会刷新接口
  Future<List<GroupData>?> get groupDataList async {
    return await _requestGroupDataList(true);
  }

  List<GroupData>? _cacheGroupDataList;

  Future<List<GroupData>?> _requestGroupDataList(bool useCache) async {
    if (useCache && _cacheGroupDataList?.isNotEmpty == true) {
      return _cacheGroupDataList;
    }
    var value = await NetworkV2<GroupData>(GroupData())
        .requestDataV2("order/queryGroups", method: RequestMethod.POST);
    if (value.isSuccess == true) {
      var rootGroupData = value.getData();
      return rootGroupData?.childGroup;
    }
    return null;
  }

  List<CustomerLevelData>? _customerLevelList; //客户等级枚举

  Future<List<CustomerLevelData>?> _requestCustomerLevelList() async {
    if (_customerLevelList?.isNotEmpty == true) {
      return _customerLevelList;
    }
    EasyLoading.show(maskType: EasyLoadingMaskType.clear, dismissOnTap: true);
    var value = await NetworkV2<CustomerLevelData>(CustomerLevelData())
        .requestDataV2("common/levelList", method: RequestMethod.GET);
    EasyLoading.dismiss();
    if (value.isSuccess == true) {
      _customerLevelList = value.getListData();
      return _customerLevelList;
    }
    return null;
  }

  Map<MessageType, int>? _cacheMessageCountMap;

  Future<Map<MessageType, int>?> _requestMessageCountMap(bool useCache) async {
    try {
      if (useCache && _cacheMessageCountMap?.isNotEmpty == true) {
        return _cacheMessageCountMap;
      }
      var result = await XYYContainer.requestChannel.request(
        "license/message/allUnReadCount",
        method: RequestMethod.GET,
      );
      var decodeJsonMap = json.decode(result);
      var value = BaseRootModel.fromJson(decodeJsonMap);
      if (value.status == "success") {
        var data = MessageCountData.fromJson(decodeJsonMap);
        if (_cacheMessageCountMap == null) {
          _cacheMessageCountMap = {};
        } else {
          _cacheMessageCountMap!.clear();
        }
        _cacheMessageCountMap![MessageType.Task] = data.taskUnReadCount ?? 0;
        _cacheMessageCountMap![MessageType.License] =
            data.licenseUnReadCount ?? 0;
        _cacheMessageCountMap![MessageType.DynamicEvent] =
            data.dynamicUnReadCount ?? 0;
        _cacheMessageCountMap![MessageType.CustomerService] =
            data.imUnReadCount ?? 0;
        _cacheMessageCountMap![MessageType.NewCustomer] =
            data.newPoiUnReadCount ?? 0;
        _cacheMessageCountMap![MessageType.Collect] =
            data.skuCollectMessageUnReadCount ?? 0;
        return _cacheMessageCountMap;
      }
    } on PlatformException catch (e) {
      handleError(e.code, e.message);
    }
    return null;
  }

  void handleError(String code, String? message) {
    var toastMsg =
        (message == null || message.isEmpty) ? "网络请求错误，请稍后重试!" : message;
    XYYContainer.toastChannel.toast(toastMsg);
  }
}
