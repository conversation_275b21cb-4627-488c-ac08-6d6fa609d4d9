import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

@JsonSerializable()
class PermissionData extends BaseModelV2<PermissionData> {
  String? hash;
  List<dynamic>? permissionList;

  PermissionData();

  @override
  PermissionData fromJsonMap(Map<String, dynamic> json) {
    return _$PermissionDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PermissionDataToJson(this);
  }
}


PermissionData _$PermissionDataFromJson(Map<String, dynamic> json) {
  return PermissionData()
    ..hash = json['hash'] as String?
    ..permissionList = json['permissionList'] as List<dynamic>?;
}

Map<String, dynamic> _$PermissionDataToJson(PermissionData instance) =>
    <String, dynamic>{
      'department': instance.hash,
      'permissionList': instance.permissionList,
};

class Permission  {
  static List<dynamic> list = [];

  static bool isPermission(String key) {
    String val = PermissionMenu.data[key];
    return list.contains(val);
  }
}

class PermissionMenu  {
  static Map<String, dynamic> data = {
    "app":"menu:crm:app", // app
    "performance":"menu:performance", // 业绩数据
    "todayIndex":"menu:performance:today:index", // 首页
    "today":"menu:performance:today", // 今日/本月数据
    "team":"menu:performance:team", // 团队业绩数据
    "teamIndex":"btn:performance:team:index", // 首页-团队业绩
    "teamMine":"btn:performance:team:mine", // 我的-团队业绩
    "more":"menu:performance:more", // 更多业绩数据
    "moreQuery":"btn:performance:more:query", // 首页-查看更多数据
    "order":"menu:order", // 订单数据
    "orderAll":"menu:order:all", // 所有订单数据
    "priceHealth":"menu:price:health", // 首页商品价格力
    "orderAllMould":"menu:order:all:mould", // 首页-订单模块
    "orderAllManage":"btn:order:all:manage", // 我的-订单管理
    "orderAllChargebacks":"btn:order:all:chargebacks", // 我的-退单管理
    "customer":"menu:customer", // 客户数据
    "customerActive":"menu:customer:active", // 活跃客户数据
    "customerActiveIndex":"menu:customer:active:index", // 首页-活跃用户
    "customerFunnel":"menu:customer:funnel", // 客户漏斗数据
    "customerFunnelIndex":"menu:customer:funnel:index", // 首页-客户漏斗
    "customerFunnelMine":"menu:customer:funnel:mine", // 我的-客户漏斗
    "product":"menu:product", // 商品数据
    "productHot":"menu:product:hot", // 热销商品已售数量
    "productHotSold":"data:menu:product:hot:sold", // 商品-热销榜单-销售额榜-已售盒数
    "productCustomer":"menu:product:customer", // 热销商品已购客户数量
    "productCustomerPurchased":"data:product:customer:purchased", // 商品-热销榜单-下单客户数榜-已购客户
    "productAgv":"menu:product:agv", // 热销商品平均到手价格
    "productAgvSale":"data:product:agv:sale", // 商品-热销榜单-平均到手价
    "productControl":"menu:product:control", // 控销品牌商品数量
    "productControlNumber":"data:product:control:number", // 商品-控销商品品牌列表-具体控销品牌的商品数量
    "productControlLeftNumber":"data:product:control:Left:number", // 商品-控销商品品牌列表-具体控销品牌的商品列表-左上角商品数量
    "productActual":"menu:product:actual", // 控销商品实付金额
    "productActualMonth":"data:product:actual:month", // 商品-控销商品品牌列表-具体控销品牌的本月实付金额
    "productAllocation":"menu:product:allocation", // 控销商品库存数量
    "productAllocationAmount":"data:product:allocation:amount", // 商品-控销商品品牌列表-具体控销品牌的商品列表-商品库存数量
    "visit":"menu:visit", // 拜访数据
    "visitToday":"menu:visit:today", // 今日/本月拜访数据
    "visitTodayIndex":"menu:visit:today:index", // 首页-拜访模块
    "visitManage":"menu:visit:manage", // 拜访管理数据
    "visitManageMine":"btn:visit:manage:mine", // 我的-拜访管理
    "superWorryApply":"btn:super:worry:apply", // 我的-超无忧
  };
}
