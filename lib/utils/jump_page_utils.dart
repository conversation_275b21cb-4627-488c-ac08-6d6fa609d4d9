import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XYYContainer/toast/toast_channel.dart';
import 'package:XyyBeanSproutsFlutter/customer/map/data/ybm_customer_poi_basic_model.dart';
import 'package:XyyBeanSproutsFlutter/order/bean/order_detail_merchant_check.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

/// 跳转客户详情，根据公私海不同跳转不同的页面
/// 这里要传merchantId，并且仅限已注册客户，未注册客户merchantId为-1
Future<void> jumpCustomerPageByMerchantId(dynamic merchantId,
    {bool canJumpPublic = true}) async {
  if (merchantId == null) {
    XYYContainer.toastChannel.toast("客户信息查询异常", type: ToastType.Normal);
    return;
  }
  Map<String, dynamic> param = {"merchantId": merchantId};

  EasyLoading.show(maskType: EasyLoadingMaskType.clear, dismissOnTap: true);

  var result = await NetworkV2(OrderDetailMerchantCheckModel()).requestDataV2(
    '/customer/private/checkPrivateCustomers',
    method: RequestMethod.POST,
    contentType: RequestContentType.FORM,
    parameters: param,
  );
  EasyLoading.dismiss();
  if (result.isSuccess == true) {
    OrderDetailMerchantCheckModel? checkModel = result.getData();
    if (checkModel?.isPrivateCustomer == true) {
      /// 在私海列表 跳客户详情
      String routerPath =
          '/customer_private_detail_page?customerId=${checkModel?.customerId}&distributable=${checkModel?.distributable}';
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath);
    } else if (canJumpPublic) {
      /// 不在私海 跳转公海详情
      String routerPath =
          '/customer_public_detail?openSeaId=${checkModel?.customerId}';
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath);
    } else {
      /// 不在私海 提示信息
      XYYContainer.toastChannel
          .toast(checkModel?.msg ?? "该客户已不在您的私海中，无法查看客户详情");
    }
  }
}

/// 跳转客户详情，根据公私海不同跳转不同的页面
/// 这里要传customerId
/// canJumpPublic:如果客户不在私海，是否跳转公海
/// canJumpChildPrivateForBDM：如果客户是子账号认领的，是否跳转私海
Future<void> jumpCustomerPageByCustomerId(dynamic customerId,
    {bool canJumpPublic = true, bool canJumpChildPrivateForBDM = false}) async {
  if (customerId == null) {
    XYYContainer.toastChannel.toast("客户信息查询异常", type: ToastType.Normal);
    return;
  }
  Map<String, dynamic> param = {"customerId": customerId};
  if (canJumpChildPrivateForBDM) {
    param["type"] = 1;
  }

  EasyLoading.show(maskType: EasyLoadingMaskType.clear, dismissOnTap: true);

  var result = await NetworkV2(OrderDetailMerchantCheckModel()).requestDataV2(
    '/openSea4POI/checkPoiIsClaimed',
    method: RequestMethod.GET,
    contentType: RequestContentType.FORM,
    parameters: param,
  );
  EasyLoading.dismiss();
  if (result.isSuccess == true) {
    OrderDetailMerchantCheckModel? checkModel = result.getData();
    if (checkModel?.isPrivateCustomer == true) {
      /// 在私海列表 跳客户详情
      String distributable = checkModel?.distributable == true ? "1" : "0";
      String routerPath =
          '/customer_private_detail_page?customerId=${checkModel?.customerId}&distributable=$distributable';
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath);
    } else if (canJumpPublic) {
      /// 不在私海 跳转公海详情
      String routerPath =
          '/customer_public_detail?openSeaId=${checkModel?.customerId}';
      routerPath = Uri.encodeFull(routerPath);
      XYYContainer.open(routerPath);
    } else {
      /// 不在私海 提示信息
      XYYContainer.toastChannel
          .toast(checkModel?.msg ?? "该客户已不在您的私海中，无法查看客户详情");
    }
  } else {
    XYYContainer.toastChannel.toast(result.errorMsg ?? "客户信息错误");
  }
}

/// 跳转客户导航或错误信息上报
Future<void> customerNavigationAndReportError(dynamic customerId,
    {bool isNavigation = true}) async {
  if (customerId == null) {
    XYYContainer.toastChannel.toast("客户信息查询异常", type: ToastType.Normal);
    return;
  }
  Map<String, dynamic> param = {"customerId": customerId};
  EasyLoading.show(maskType: EasyLoadingMaskType.clear, dismissOnTap: true);
  var result =
      await NetworkV2<YBMCustomerPoiBasicModel>(YBMCustomerPoiBasicModel())
          .requestDataV2("customerV2/poiBasic", parameters: param);
  EasyLoading.dismiss();
  if (result.isSuccess == true) {
    YBMCustomerPoiBasicModel? model = result.getData();
    if (model != null) {
      if (isNavigation) {
        // 导航
        if (model.poiLatitude == null || model.poiLongitude == null) {
          XYYContainer.toastChannel.toast("客户信息获取异常", type: ToastType.Normal);
          return;
        }
        double latitude = double.tryParse("${model.poiLatitude}") ?? 0;
        double longitude = double.tryParse("${model.poiLongitude}") ?? 0;
        XYYContainer.bridgeCall('map_navigation', parameters: {
          'latitude': latitude,
          'longitude': longitude,
          'address': model.address,
        });
      } else {
        if (model.poiAuditStatus == 1) {
          XYYContainer.toastChannel
              .toast("客户信息审核中，无法提交审核", type: ToastType.Normal);
          return;
        }
        if (model.poiAuditStatus == 3) {
          XYYContainer.toastChannel
              .toast("客户信息审核驳回，无法操作", type: ToastType.Normal);
          return;
        }
        // 跳转POI 错误上报页面
        var value = await XYYContainer.bridgeCall('app_host');
        String host = value['h5_host'];

        String pdcUrl = host + "/dataError?poiId=${model.poiId}&source=5";
        pdcUrl = Uri.encodeComponent(pdcUrl);
        String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
        XYYContainer.open(router);
      }
    } else {
      XYYContainer.toastChannel.toast("客户信息查询异常", type: ToastType.Normal);
      return;
    }
  } else {
    XYYContainer.toastChannel.toast("客户信息查询异常", type: ToastType.Normal);
    return;
  }
}
