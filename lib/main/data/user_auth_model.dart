import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_auth_model.g.dart';

@JsonSerializable()
class UserAuthModel extends BaseModelV2 {
  dynamic roleCode;
  dynamic roleName;

  UserAuthModel();

  @override
  UserAuthModel fromJsonMap(Map<String, dynamic> json) {
    return _$UserAuthModelFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$UserAuthModelToJson(this);
  }
}

const ROLE_TYPE_HY = 3;
const ROLE_TYPE_LZ = 2;
const ROLE_TYPE_YBM = 1;

class UserAuthManager {
  /// 获取所有身份code
  /// 1-药帮忙 2-灵芝 3-荷叶
  static Future<List<String>?> getRoleCode() async {
    String roleJSON =
        (await XYYContainer.storageChannel.getValue('roleJSON')) ?? "[]";
    try {
      List<dynamic> roleList = jsonDecode(roleJSON);
      return roleList.map((e) => "${e["roleCode"]}").toList();
    } catch (e) {
      return null;
    }
  }

  static Future<String> getRoleJSONString() async {
    String? roleJSON = await XYYContainer.storageChannel.getValue('roleJSON');
    return roleJSON ?? "";
  }

  static Future<bool> hasYBMRole() async {
    var roleCodes = await UserAuthManager.getRoleCode();
    return roleCodes?.contains("1") == true;
  }

  static Future<String> getSourceType() async {
    var roleCodes = await UserAuthManager.getRoleCode();
    String source = "";
    if (roleCodes?.contains("1") == true) {
      // 药帮忙
      source = "5";
    } else {
      // 荷叶
      source = "2";
    }
    return source;
  }
}
