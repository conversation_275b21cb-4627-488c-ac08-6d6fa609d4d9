import 'dart:math';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/main/data/user_auth_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:flutter/material.dart';

// 弹出快速入口
Future<void> showQuickAccess(BuildContext context) {
  XYYContainer.bridgeCall('event_track',
      parameters: {"action_type": "mc-homepage-create"});
  return Navigator.push(
    context,
    PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) {
        return CenterQuickAccessPage();
      },
      barrierColor: Colors.transparent,
      transitionDuration: Duration(milliseconds: 300),
      transitionsBuilder:
          (___, Animation<double> animation, ____, Widget child) {
        return FadeTransition(
          opacity: Tween(begin: 0.0, end: 1.0).animate(
            CurvedAnimation(
              parent: animation,
              curve: Curves.ease,
            ),
          ),
          child: child,
        );
      },
    ),
  );
}

class CenterQuickAccessPage extends StatefulWidget {
  @override
  State<StatefulWidget> createState() {
    return CenterQuickAccessState();
  }
}

class CenterQuickAccessState extends State<CenterQuickAccessPage> {
  List<QuickAccessConfig> configModels = [];

  Tween<double> _tween = Tween(begin: 0.0, end: 1.0);

  @override
  void initState() {
    UserInfoUtil.getUserInfo().then((value) {
      switch (value?.roleType) {
        case 0: // BD 拜访、客户、联系人
          configModels = [
            QuickAccessConfig.addVisit(addVisit),
            QuickAccessConfig.addClues(addClues),
            QuickAccessConfig.addVisitPlan(addVisitPlan),
            QuickAccessConfig.addContact(addContact),
          ];
          break;
        case 1: // BDM  拜访、客户、联系人、陪访
          configModels = [
            QuickAccessConfig.addVisit(addVisit),
            QuickAccessConfig.addClues(addClues),
            QuickAccessConfig.addVisitPlan(addVisitPlan),
            QuickAccessConfig.addContact(addContact),
            QuickAccessConfig.addAccompanied(addAccompanied),
          ];
          break;
        case 2: // 跟进人 拜访、联系人
          configModels = [
            QuickAccessConfig.addVisit(addVisit),
            QuickAccessConfig.addContact(addContact),
            QuickAccessConfig.addVisitPlan(addVisitPlan),
          ];
          break;
        case 3: // 跟进人BDM 拜访、联系人、陪访
          configModels = [
            QuickAccessConfig.addVisit(addVisit),
            QuickAccessConfig.addContact(addContact),
            QuickAccessConfig.addVisitPlan(addVisitPlan),
            QuickAccessConfig.addAccompanied(addAccompanied),
          ];
          break;
        default: // 更高权限  拜访、客户、联系人、陪访
          configModels = [
            QuickAccessConfig.addVisit(addVisit),
            QuickAccessConfig.addClues(addClues),
            QuickAccessConfig.addContact(addContact),
            QuickAccessConfig.addVisitPlan(addVisitPlan),
            QuickAccessConfig.addAccompanied(addAccompanied),
          ];
      }
      setState(() {});
    });
    super.initState();
  }

  // 新建拜访
  void addVisit() async {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-createVisit"});
    String roleJSON = await UserAuthManager.getRoleJSONString();
    var router = '/add_visit_page?rolesJSON=$roleJSON';
    router = Uri.encodeFull(router);
    Navigator.of(context).popAndPushNamed(router);
  }

  // 新建联系人
  void addContact() {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-createContact"});
    Navigator.of(context).pop();
    XYYContainer.open(
        'xyy://crm-app.ybm100.com/customer/add_contact_page?type=2&editable=true');
  }

  // 新建客户
  void addClues() async {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-createClient"});
    Navigator.of(context).pop();
    var result = await XYYContainer.bridgeCall('app_host');
    if (result is Map) {
      String pdcInterface = result['h5_host'] ?? "";
      var roleCodes = await UserAuthManager.getRoleCode();
      String source = "";
      if (roleCodes?.contains("1") == true) {
        // 药帮忙
        source = "5";
      } else {
        // 荷叶
        source = "2";
      }
      String pdcUrl = pdcInterface + "/newPoi?source=$source";
      pdcUrl = Uri.encodeComponent(pdcUrl);
      String router = "xyy://crm-app.ybm100.com/crm/web_view?url=$pdcUrl";
      XYYContainer.open(router);
    }
  }

  // 新建陪访
  void addAccompanied() async {
    XYYContainer.bridgeCall('event_track',
        parameters: {"action_type": "mc-homepage-createJointvisit"});
    String roleJSON = await UserAuthManager.getRoleJSONString();
    var router = '/add_accompany_visit_page?rolesJSON=$roleJSON';
    router = Uri.encodeFull(router);
    Navigator.of(context).popAndPushNamed(router);
  }

  //拜访计划
  void addVisitPlan() async {
    String roleJSON = await UserAuthManager.getRoleJSONString();
    //TODO 跳转拜访计划
    // var router = '/add_accompany_visit_page?rolesJSON=$roleJSON';
    var router = '/visit_plan_page';
    router = Uri.encodeFull(router);
    Navigator.of(context).popAndPushNamed(router);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pop();
      },
      behavior: HitTestBehavior.opaque,
      child: Container(
        color: Color(0xFFFFFFFF),
        child: Stack(
          alignment: Alignment.center,
          children: this.items(),
        ),
      ),
    );
  }

  List<Widget> items() {
    List<Widget> list = [];
    double bottomSpace = MediaQuery.of(context).padding.bottom + 80;
    double itemSpace = 30;
    double itemWidth = 90;
    double itemHeight = 90;

    if (this.configModels.length == 4) {
      double leftSpace =
          (MediaQuery.of(context).size.width - itemWidth * 2 - itemSpace) / 2;
      this.configModels.asMap().forEach((key, value) {
        list.add(
          TweenAnimationBuilder<double>(
            tween: _tween,
            duration: Duration(milliseconds: 300 + 3 * key),
            curve: Curves.easeInOut,
            builder: (context, animationValue, widget) {
              return Positioned(
                left: (key % 2) * (itemWidth + itemSpace) + leftSpace,
                bottom: ((key > 1 ? 0 : 1) * (itemHeight + itemSpace) +
                    bottomSpace) *
                    animationValue,
                child: Container(
                  width: itemWidth,
                  height: itemHeight,
                  child: CenterQuickAccessItem(model: value),
                ),
              );
            },
          ),
        );
      });
    } else if (this.configModels.length == 5) {
      this.configModels.asMap().forEach((key, value) {
        if (key <= 2) {
          double leftSpace =
              (MediaQuery.of(context).size.width - itemWidth * 3 - 2 *itemSpace) / 2;
          list.add(
            TweenAnimationBuilder<double>(
              tween: _tween,
              duration: Duration(milliseconds: 300 + 3 * key),
              curve: Curves.easeInOut,
              builder: (context, animationValue, widget) {
                return Positioned(
                  left: key * (itemWidth + itemSpace) + leftSpace,
                  bottom: ((itemHeight + itemSpace) + bottomSpace) * animationValue,
                  child: Container(
                    width: itemWidth,
                    height: itemHeight,
                    child: CenterQuickAccessItem(model: value),
                  ),
                );
              },
            ),
          );
        } else {
          double leftSpace =
              (MediaQuery.of(context).size.width - itemWidth * 2 - itemSpace) / 2;
          list.add(
            TweenAnimationBuilder<double>(
              tween: _tween,
              duration: Duration(milliseconds: 300 + 3 * key),
              curve: Curves.easeInOut,
              builder: (context, animationValue, widget) {
                return Positioned(
                  left: (key - 3) * (itemWidth + itemSpace) + leftSpace,
                  bottom: bottomSpace * animationValue,
                  child: Container(
                    width: itemWidth,
                    height: itemHeight,
                    child: CenterQuickAccessItem(model: value),
                  ),
                );
              },
            ),
          );
        }
      });
    } else {
      this.configModels.asMap().forEach((key, value) {
        double leftSpace = (MediaQuery.of(context).size.width -
            itemWidth * 3 -
            itemSpace * 2) /
            2;
        list.add(
          TweenAnimationBuilder<double>(
            tween: _tween,
            duration: Duration(milliseconds: 300 + 3 * key),
            curve: Curves.easeInOut,
            builder: (context, animationValue, widget) {
              return Positioned(
                left: key * (itemWidth + itemSpace) + leftSpace,
                bottom: bottomSpace * animationValue,
                child: Container(
                  width: itemWidth,
                  height: itemHeight,
                  child: CenterQuickAccessItem(model: value),
                ),
              );
            },
          ),
        );
      });
    }

    list.add(TweenAnimationBuilder<double>(
      tween: _tween,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      builder: (context, animationValue, widget) {
        return Positioned(
          bottom: MediaQuery.of(context).padding.bottom + 50,
          child: Transform.rotate(
            angle: (pi / 4) * animationValue,
            child: Image.asset(
              'assets/images/quick/quick_close_icon.png',
              width: 24,
              height: 24,
            ),
          ),
        );
      },
    ));
    return list;
  }
}

class QuickAccessConfig {
  String title;
  String imagePath;
  VoidCallback? action;

  QuickAccessConfig({
    required this.title,
    required this.imagePath,
    this.action,
  });

  factory QuickAccessConfig.addVisit(VoidCallback action) {
    return QuickAccessConfig(
        title: "添加拜访",
        imagePath: "assets/images/quick/quick_add_visit.png",
        action: action);
  }

  factory QuickAccessConfig.addClues(VoidCallback action) {
    return QuickAccessConfig(
        title: "新建门店",
        imagePath: "assets/images/quick/quick_add_clues.png",
        action: action);
  }

  factory QuickAccessConfig.addContact(VoidCallback action) {
    return QuickAccessConfig(
        title: "新建联系人",
        imagePath: "assets/images/quick/quick_add_contact.png",
        action: action);
  }

  factory QuickAccessConfig.addAccompanied(VoidCallback action) {
    return QuickAccessConfig(
        title: "添加陪访",
        imagePath: "assets/images/quick/quikc_add_accompanied.png",
        action: action);
  }

  factory QuickAccessConfig.addVisitPlan(VoidCallback action) {
    return QuickAccessConfig(
        title: "拜访计划",
        imagePath: "assets/images/quick/quick_add_visit_plan.png",
        action: action);
  }
}

class CenterQuickAccessItem extends StatelessWidget {
  final QuickAccessConfig model;

  CenterQuickAccessItem({required this.model});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: model.action,
      behavior: HitTestBehavior.opaque,
      child: Column(
        children: [
          Image.asset(
            model.imagePath,
            width: 60,
            height: 60,
          ),
          SizedBox(height: 5),
          Text(
            model.title,
            style: TextStyle(color: Color(0xFF333333), fontSize: 13),
          ),
        ],
      ),
    );
  }
}
