import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/event_bus/event_bus.dart';
import 'package:flutter/services.dart';

class OpenMainBridgeHandler extends BridgeBaseHandler {
  @override
  Future<dynamic> handleMethodCall(MethodCall call) async {
    EventBus()
        .sendMessage(MainTabBarEventBusName.OPEN_MAIN, arg: call.arguments);
    return null;
  }
}
