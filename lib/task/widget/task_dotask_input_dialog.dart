import 'package:XyyBeanSproutsFlutter/common/ensure_visible/ensure_visible_when_focused.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

Future<String?> showDoTaskInputView(
  BuildContext context, {
  String? title,
  String? itemKey,
  String? placehold,
  String? content,
  int? maxLenght,
  VoidCallback? cancel,
  ValueChanged? pressed,
}) {
  return showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    barrierColor: Color(0x80000000),
    isScrollControlled: true,
    builder: (BuildContext context) {
      return TaskDoTaskInputDialog(
        title: title,
        itemKey: itemKey,
        placehold: placehold,
        content: content,
        maxLenght: maxLenght,
        cancel: cancel,
        pressed: pressed,
      );
    },
  );
}

// ignore: must_be_immutable
class TaskDoTaskInputDialog extends StatefulWidget {
  final String? title;
  final String? itemKey;
  final String? placehold;
  String? content;
  final int? maxLenght;
  final VoidCallback? cancel;
  final ValueChanged? pressed;

  TaskDoTaskInputDialog({
    this.title,
    this.itemKey,
    this.placehold,
    this.content,
    this.maxLenght,
    this.cancel,
    this.pressed,
  });

  @override
  State<StatefulWidget> createState() {
    return TaskDoTaskInputDialogState();
  }
}

class TaskDoTaskInputDialogState extends State<TaskDoTaskInputDialog> {
  // 输入框的焦点实例
  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.transparent,
      body: Container(
        margin: EdgeInsets.only(top: 65),
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(4)),
        ),
        child: Container(
          child: Column(
            children: [
              _InputTopBar(
                title: widget.title,
                barCancelPressed: () {
                  if (widget.cancel != null) {
                    widget.cancel!();
                  }
                  Navigator.of(context).pop();
                },
                barDeterminePressed: () {
                  Navigator.of(context).pop(widget.content);
                },
              ),
              Container(
                padding: EdgeInsets.fromLTRB(15, 0, 15, 15),
                child: EnsureVisibleWhenFocused(
                  focusNode: _focusNode,
                  child: TextFormField(
                    maxLengthEnforcement: MaxLengthEnforcement.enforced,
                    focusNode: _focusNode,
                    autofocus: true,
                    controller: TextEditingController.fromValue(
                      TextEditingValue(
                        text: widget.content!,
                        selection: TextSelection.fromPosition(
                          ///用来设置文本的位置
                          TextPosition(
                            affinity: TextAffinity.downstream,
                            // 光标向后移动的长度
                            offset: widget.content!.length,
                          ),
                        ),
                      ),
                    ),
                    onChanged: (text) {
                      widget.content = text;
                    },
                    style: TextStyle(
                      fontSize: 15,
                      color: Color(0xFF292933),
                    ),
                    scrollPadding: EdgeInsets.zero,
                    decoration: InputDecoration(
                      counterStyle: TextStyle(
                        fontSize: 12,
                        color: Color(0xFF9494A6),
                      ),
                      hintText: widget.placehold ?? "",
                      hintStyle: TextStyle(
                        fontSize: 15,
                        color: Color(0xFF9494A6),
                      ),
                      hintMaxLines: 3,
                      border: OutlineInputBorder(
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                    ),
                    maxLength: widget.maxLenght,
                    // 达到最大长度不允许输入
                    maxLines: 15,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _focusNode.dispose();
  }
}

class _InputTopBar extends StatelessWidget {
  final String? title;
  final VoidCallback barCancelPressed;
  final VoidCallback barDeterminePressed;

  _InputTopBar({
    Key? key,
    required this.title,
    required this.barCancelPressed,
    required this.barDeterminePressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                minimumSize: Size.fromWidth(62)),
            onPressed: this.barCancelPressed,
            child: Text(
              '取消',
              style: TextStyle(
                inherit: false,
                fontSize: 16,
                color: Color(0xFF292933),
              ),
            ),
          ),
          Text(
            this.title!,
            style: TextStyle(
              inherit: false,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF292933),
            ),
          ),
          TextButton(
            style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                minimumSize: Size.fromWidth(62)),
            onPressed: this.barDeterminePressed,
            child: Text(
              '确定',
              style: TextStyle(
                  inherit: false,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF00B377)),
            ),
          ),
        ],
      ),
    );
  }
}
