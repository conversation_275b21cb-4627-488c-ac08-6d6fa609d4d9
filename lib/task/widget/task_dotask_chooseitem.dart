import 'package:flutter/material.dart';

typedef DoTaskValueChange = void Function(String? title, String content);

// ignore: must_be_immutable
class TaskDoTaskChooseItem extends StatefulWidget {
  final String? itemTitle;
  int selectIndex;

  final DoTaskValueChange? changeSelectValue;

  TaskDoTaskChooseItem({
    this.itemTitle,
    this.selectIndex = 0,
    this.changeSelectValue,
  });

  @override
  State<StatefulWidget> createState() {
    return TaskDoTaskChooseItemState();
  }
}

class TaskDoTaskChooseItemState extends State<TaskDoTaskChooseItem> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 15, 15, 0),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 90, // 固定宽度
                child: Text(
                  widget.itemTitle ?? "",
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF292933),
                  ),
                  maxLines: 2,
                ),
              ),
              SizedBox(
                width: 15,
              ),
              TaskRadioButton(
                itemTitle: "是",
                value: 0,
                isSelected: widget.selectIndex == 0,
                onChange: onChangeSelected,
              ),
              SizedBox(
                width: 40,
              ),
              TaskRadioButton(
                itemTitle: "否",
                value: 1,
                isSelected: widget.selectIndex == 1,
                onChange: onChangeSelected,
              ),
            ],
          ),
          SizedBox(
            height: 15,
          ),
          Container(
            color: Color(0xFFF6F6F6),
            height: 1,
          ),
        ],
      ),
    );
  }

  void onChangeSelected(dynamic value) {
    setState(() {
      widget.selectIndex = value;
    });
    if (widget.changeSelectValue != null) {
      widget.changeSelectValue!(widget.itemTitle, value == 0 ? '是' : '否');
    }
  }
}

class TaskRadioButton extends StatelessWidget {
  final String? itemTitle;

  final int? value;

  final bool? isSelected;

  final ValueChanged? onChange;

  TaskRadioButton({
    this.itemTitle,
    this.value,
    this.isSelected,
    this.onChange,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        if (this.isSelected == false) {
          this.onChange!(this.value);
        }
      },
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Stack(
              alignment: Alignment.center,
              children: [
                Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: Color(this.isSelected! ? 0xFF00B377 : 0xFFD1D1D6),
                      width: 1,
                    ),
                    borderRadius: BorderRadius.circular(9),
                    color: Color(0xFFFFFFFF),
                  ),
                  width: 18,
                  height: 18,
                ),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Color(this.isSelected! ? 0xFF00B377 : 0xFFFFFFFF),
                  ),
                  width: 10,
                  height: 10,
                ),
              ],
            ),
            SizedBox(
              width: 10,
            ),
            Text(
              this.itemTitle ?? '',
              style: TextStyle(
                fontSize: 15,
                color: Color(this.isSelected! ? 0xFF00B377 : 0xFF292933),
              ),
            )
          ],
        ),
      ),
    );
  }
}
