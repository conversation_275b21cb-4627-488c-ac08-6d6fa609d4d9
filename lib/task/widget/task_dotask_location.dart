import 'package:XYYContainer/XYYContainer.dart';
import 'package:flutter/widgets.dart';

class TaskDoTaskLocationItem extends StatefulWidget {
  final void Function(String? address, String? lat, String? lng)? changeLocation;

  TaskDoTaskLocationItem({this.changeLocation});

  @override
  State<StatefulWidget> createState() {
    return TaskDoTaskLocationItemState();
  }
}

class TaskDoTaskLocationItemState extends State<TaskDoTaskLocationItem> {
  String? address;
  String? lat;
  String? lng;

  @override
  void initState() {
    super.initState();
    this.getLocation();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      child: Row(
        children: [
          Image.asset(
            'assets/images/task/dotask_location_icon.png',
            width: 22,
            height: 22,
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: 10, right: 20),
              child: Text(
                this.address ?? '',
                style: TextStyle(
                  fontSize: 15,
                  color: Color(0xFF292933),
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          GestureDetector(
            onTap: getLocation,
            child: Text(
              '重新定位',
              style: TextStyle(
                fontSize: 14,
                color: Color(0xFF00B377),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void getLocation() async {
    var data = await XYYContainer.locationChannel.locate();
    if (data.isSuccess==true) {
      this.lat = data.latitude;
      this.lng = data.longitude;
      if (mounted) {
        setState(() {
          this.address = data.address;
        });
      }
      widget.changeLocation!(address, lat, lng);
    } else {
      XYYContainer.toastChannel.toast('获取位置信息失败${data.isSuccess}');
    }
  }
}
