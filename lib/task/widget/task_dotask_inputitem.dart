import 'package:XyyBeanSproutsFlutter/common/ensure_visible/ensure_visible_when_focused.dart';
import 'package:XyyBeanSproutsFlutter/task/notifier/task_dotask_notifier.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_chooseitem.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// ignore: must_be_immutable
class TaskDoTaskInputItem extends StatefulWidget {
  final String? itemTitle;
  final String? placehold;
  String? content;
  final int? maxLenght;
  final DoTaskValueChange? valueChange;

  final TaskDoTaskInputChangeNotifier contentNotifier =
      TaskDoTaskInputChangeNotifier();

  TaskDoTaskInputItem({
    this.itemTitle,
    this.placehold,
    this.content,
    this.maxLenght,
    this.valueChange,
  });

  @override
  State<StatefulWidget> createState() {
    return TaskDoTaskInputItemState();
  }
}

class TaskDoTaskInputItemState extends State<TaskDoTaskInputItem> {
  FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    widget.contentNotifier.content = widget.content;
  }

  @override
  void dispose() {
    super.dispose();
    this._focusNode.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 15, 15, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widget.itemTitle ?? "",
            style: TextStyle(
              fontSize: 15,
              color: Color(0xFF292933),
            ),
          ),
          Container(
            padding: EdgeInsets.all(10),
            margin: EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: Color(0xFFF7F7F8),
            ),
            child: Container(
              height: 160,
              child: EnsureVisibleWhenFocused(
                focusNode: _focusNode,
                child: TextFormField(
                  focusNode: _focusNode,
                  controller: TextEditingController.fromValue(
                    TextEditingValue(
                      text: widget.content ?? "",
                      selection: TextSelection.fromPosition(
                        ///用来设置文本的位置
                        TextPosition(
                          affinity: TextAffinity.downstream,
                          // 光标向后移动的长度
                          offset: widget.content?.length ?? 0,
                        ),
                      ),
                    ),
                  ),
                  onChanged: (text) {
                    widget.contentNotifier.content = text;
                    widget.content = text;
                    widget.valueChange!(widget.itemTitle, text);
                  },
                  style: TextStyle(
                    fontSize: 15,
                    color: Color(0xFF292933),
                  ),
                  scrollPadding: EdgeInsets.zero,
                  decoration: InputDecoration(
                    // counterText: "",
                    counterStyle: TextStyle(
                      fontSize: 12,
                      color: Color(0xFF9494A6),
                    ),
                    hintText: widget.placehold ?? "",
                    hintStyle: TextStyle(
                      fontSize: 15,
                      color: Color(0xFF9494A6),
                    ),
                    hintMaxLines: 3,
                    border: OutlineInputBorder(
                      borderSide: BorderSide.none,
                    ),
                    contentPadding: EdgeInsets.zero,
                    isDense: true,
                  ),
                  maxLength: widget.maxLenght, // 最大长度
                  maxLengthEnforcement: MaxLengthEnforcement
                      .truncateAfterCompositionEnds, // 达到最大长度不允许输入
                  maxLines: 8,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }
}
