import 'package:flutter/material.dart';

class TaskDoTaskTitleItem extends StatelessWidget {
  /// 任务名称
  final String? taskTitle;

  /// 任务类型 1:信息收集 2：系统/软件售卖
  final int? taskType;

  /// 任务类型文本
  final String? taskTypeStr;

  TaskDoTaskTitleItem({
    this.taskTitle,
    this.taskType,
    this.taskTypeStr,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(15),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Flexible(
            flex: 1,
            child: Text(
              this.taskTitle ?? "",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: Color(0xFF292933),
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Container(
            margin: EdgeInsets.only(left: 10, top: 3.5),
            padding: EdgeInsets.only(left: 4, right: 4, top: 1),
            decoration: BoxDecoration(
              border: Border.all(
                color:
                    this.taskType == 1 ? Color(0x80FF7200) : Color(0x8000B377),
                width: 0.5,
              ),
              borderRadius: BorderRadius.circular(1),
              color: this.taskType == 1 ? Color(0x0DF78D00) : Color(0x0D00B377),
            ),
            alignment: Alignment.center,
            child: Text(
              this.taskTypeStr ?? "-",
              style: TextStyle(
                fontSize: 14,
                color:
                    this.taskType == 1 ? Color(0xFFFF7200) : Color(0xFF00B377),
              ),
              strutStyle: StrutStyle(forceStrutHeight: true),
            ),
          ),
        ],
      ),
    );
  }
}
