import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/common/image/ImageWidget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

// ignore: must_be_immutable
class TaskDoTaskImageItem extends StatefulWidget {
  List<String>? imageArray;
  final int? limitCount;

  final ValueChanged<List<String>?>? imageChange;

  TaskDoTaskImageItem({
    this.imageArray,
    this.limitCount,
    this.imageChange,
  });

  @override
  State<StatefulWidget> createState() {
    return TaskDoTaskImageItemState();
  }
}

class TaskDoTaskImageItemState extends State<TaskDoTaskImageItem> {
  @override
  void initState() {
    super.initState();
    if (widget.imageArray == null) {
      widget.imageArray = [];
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(15, 10, 15, 0),
      height: 116,
      child: Column(
        children: [
          Flexible(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: getItemCount(),
              itemBuilder: getItemForIndex,
            ),
          ),
          SizedBox(height: 15),
          Container(
            height: 1,
            color: Color(0xFFF6F6F6),
          ),
        ],
      ),
    );
  }

  int? getItemCount() {
    int totalCount = widget.imageArray?.length ?? 1;
    return totalCount >= widget.limitCount! ? widget.limitCount : totalCount + 1;
  }

  Widget getItemForIndex(BuildContext context, int index) {
    int totalCount = widget.imageArray?.length ?? 1;
    if (totalCount != widget.limitCount && index == totalCount) {
      return GestureDetector(
        onTap: () {
          this.selectImageAction();
        },
        child: Container(
          height: 90,
          width: 90,
          margin: EdgeInsets.only(left: totalCount >= 1 ? 10 : 0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
          ),
          child: Image.asset(
            'assets/images/task/dotask_select_image.png',
          ),
        ),
      );
    } else {
      var imageUrl = widget.imageArray![index];
      return Container(
        height: 90,
        width: 90,
        margin: EdgeInsets.only(left: index >= 1 ? 10 : 0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(2),
        ),
        child: Stack(
          alignment: Alignment.topRight,
          children: [
            ImageWidget(
              url: imageUrl,
              w: 90,
              h: 90,
              fit: BoxFit.cover,
            ),
            GestureDetector(
              onTap: () {
                this.deleteImageActionForIndex(index);
              },
              child: Image.asset(
                'assets/images/task/dotask_delete_image.png',
                width: 18,
                height: 18,
              ),
            ),
          ],
        ),
      );
    }
  }

  /// 选择图片
  void selectImageAction() async {
    try {
      EasyLoading.show(maskType: EasyLoadingMaskType.clear);
      var result = await XYYContainer.photoForCameraWithUpload(
          'schedule/uploadLicenseAuditImg');
      EasyLoading.dismiss();
      if (mounted) {
        if (widget.imageArray!.length < widget.limitCount!) {
          setState(() {
            if (result is List<String> && result.length > 0) {
              widget.imageArray!.add(result.first);
              widget.imageChange!(widget.imageArray);
            }
          });
        }
      }
    } on PlatformException catch (e) {
      EasyLoading.dismiss();
      XYYContainer.toastChannel.toast(e.message!);
    }
  }

  /// 删除图片
  void deleteImageActionForIndex(int index) {
    setState(() {
      widget.imageArray!.removeAt(index);
      widget.imageChange!(widget.imageArray);
    });
  }
}
