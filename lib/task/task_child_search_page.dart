import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_appbar_search.dart';
import 'package:XyyBeanSproutsFlutter/task/data/task_child_list_data.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/tab_custom_indicator.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/time_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

class TaskChildSearchPage extends BasePage {
  final String? mainTaskId;

  TaskChildSearchPage(this.mainTaskId);

  @override
  BaseState<StatefulWidget> initState() {
    return TaskChildSearchPageState();
  }
}

const CHILD_TASK_STATUS = <ChildTaskStatus>[
  ChildTaskStatus("全部", -1),
  ChildTaskStatus("已执行", 1),
  ChildTaskStatus("未执行", 0),
];

class ChildTaskStatus {
  final String text;
  final int id;

  const ChildTaskStatus(this.text, this.id);
}

class TaskChildSearchPageState extends BaseState<TaskChildSearchPage>
    with SingleTickerProviderStateMixin {
  List<EasyRefreshController> _refreshControllerList =
      <EasyRefreshController>[];
  List<ScrollController> _scrollControllerList = <ScrollController>[];
  List<dynamic> _childTaskModelList = <dynamic>[];

  TabController? _tabController;

  var providerList = <ChangeNotifierProvider<dynamic>>[];

  bool hasInit = false;

  @override
  void onCreate() {
    _tabController =
        TabController(length: CHILD_TASK_STATUS.length, vsync: this);
    _tabController!.addListener(() {
      print(
          "guan:index:${_tabController!.index.toDouble()},animIndex：${_tabController!.animation!.value}");
      if (_tabController!.index.toDouble() ==
          _tabController!.animation!.value) {
        // tab更新
        print("guan: request item ${_tabController!.index.toDouble()}");
        _childTaskModelList[_tabController!.index]?.requestTaskList(true);
      }
    });

    for (int i = 0; i < CHILD_TASK_STATUS.length; i++) {
      var easyRefreshController = EasyRefreshController();
      var scrollController = ScrollController();
      _refreshControllerList.add(easyRefreshController);
      _scrollControllerList.add(scrollController);
      switch (i) {
        case 0:
          var childTaskListModel1 = ChildTaskListModel1(
              widget.mainTaskId,
              CHILD_TASK_STATUS[i],
              easyRefreshController,
              scrollController,
              this);
          _childTaskModelList.add(childTaskListModel1);
          providerList.add(ChangeNotifierProvider<ChildTaskListModel1>(
              create: (context) => childTaskListModel1));
          break;
        case 1:
          var childTaskListModel2 = ChildTaskListModel2(
              widget.mainTaskId,
              CHILD_TASK_STATUS[i],
              easyRefreshController,
              scrollController,
              this);
          _childTaskModelList.add(childTaskListModel2);
          providerList.add(ChangeNotifierProvider<ChildTaskListModel2>(
              create: (context) => childTaskListModel2));
          break;
        case 2:
          var childTaskListModel3 = ChildTaskListModel3(
              widget.mainTaskId,
              CHILD_TASK_STATUS[i],
              easyRefreshController,
              scrollController,
              this);
          _childTaskModelList.add(childTaskListModel3);
          providerList.add(ChangeNotifierProvider<ChildTaskListModel3>(
              create: (context) => childTaskListModel3));
          break;
      }
    }

    super.onCreate();
  }

  @override
  List<SingleChildWidget> getProvider() {
    return providerList;
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: const Color(0xFFF7F7F8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildFilterBarWidget(),
          buildListWidget(),
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return SAppBarSearch(
      hintText: "输入客户名称搜索",
      showLeading: true,
      leading: GestureDetector(
        behavior: HitTestBehavior.opaque,
        onTap: () {
          if (mounted) {
            Navigator.of(context).pop();
          }
        },
        child: Container(
          height: 44,
          width: 47,
          alignment: Alignment.center,
          child: Image.asset(
            "assets/images/titlebar/icon_back.png",
            width: 22,
            height: 22,
          ),
        ),
      ),
      hideCancel: true,
      autoFocus: true,
      onSearch: (value) {
        _childTaskModelList.forEach((element) {
          (element as ChildTaskSearchModel).customerName = value;
        });
        (_childTaskModelList[_tabController!.index] as ChildTaskSearchModel)
            .requestTaskList(true);
      },
    );
  }

  @override
  String getTitleName() {
    return "子任务列表";
  }

  Widget buildFilterBarWidget() {
    return Container(
      height: 44,
      color: Colors.white,
      child: Row(
        children: [Expanded(child: buildTabBarWidget())],
      ),
    );
  }

  Widget buildListWidget() {
    return Expanded(
      child: Container(
        child: TabBarView(
          controller: _tabController,
          children: List.generate(CHILD_TASK_STATUS.length,
              (index) => buildTabViewItemWidget(index)),
        ),
      ),
    );
  }

  Widget buildTabItemWidget(ChildTaskStatus status) {
    return Container(
      height: 44,
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: Tab(
        text: status.text,
      ),
    );
  }

  TextStyle getStatusTextStyle(bool isSelected) {
    if (isSelected) {
      // 选中
      return TextStyle(
          color: Color(0xFF292933), fontSize: 14, fontWeight: FontWeight.w500);
    } else {
      // 未选中
      return TextStyle(
          color: Color(0xFF676773),
          fontSize: 14,
          fontWeight: FontWeight.normal);
    }
  }

  Widget buildTabBarWidget() {
    return TabBar(
        controller: _tabController,
        labelColor: Color(0xFF292933),
        labelStyle: getStatusTextStyle(true),
        unselectedLabelStyle: getStatusTextStyle(false),
        unselectedLabelColor: Color(0xFF676773),
        isScrollable: false,
        // indicatorSize: TabBarIndicatorSize.tab,
        indicator: TabCustomIndicator(
            wantWidth: 30,
            borderSide: const BorderSide(width: 3.0, color: Color(0xFF00B377))),
        tabs: List.generate(CHILD_TASK_STATUS.length,
            (index) => buildTabItemWidget(CHILD_TASK_STATUS[index])));
  }

  Widget buildTabViewItemWidget(int index) {
    var listModel = _childTaskModelList[index];
    switch (index) {
      case 0:
        return Consumer<ChildTaskListModel1>(builder: (context, model, child) {
          print("guan: build item1 $index");
          return buildListItemWidget(index, listModel);
        });
      case 1:
        return Consumer<ChildTaskListModel2>(builder: (context, model, child) {
          print("guan: build item2 $index");
          return buildListItemWidget(index, listModel);
        });
      case 2:
        return Consumer<ChildTaskListModel3>(builder: (context, model, child) {
          print("guan: build item3 $index");
          return buildListItemWidget(index, listModel);
        });
    }
    return Consumer<ChildTaskSearchModel>(builder: (context, model, child) {
      print("guan: build item $index");
      return buildListItemWidget(index, listModel);
    });
  }

  EasyRefresh buildListItemWidget(int index, ChildTaskSearchModel listModel) {
    return EasyRefresh.custom(
        controller: _refreshControllerList[index],
        scrollController: _scrollControllerList[index],
        enableControlFinishRefresh: true,
        enableControlFinishLoad: true,
        onRefresh: () async {
          listModel.requestTaskList(true);
        },
        onLoad: !listModel.isLastPage!
            ? () async {
                listModel.requestTaskList(false);
              }
            : null,
        slivers: [
          SliverPadding(padding: EdgeInsets.only(top: 10)),
          SliverList(
            delegate:
                SliverChildBuilderDelegate((BuildContext context, int index) {
              //创建列表项
              return buildTaskItem(listModel.list![index], listModel);
            }, childCount: listModel.list?.length ?? 0),
          )
        ],
        emptyWidget: getEmptyWidget(listModel));
  }

  Widget buildTaskItem(
      TaskChildItemData itemData, ChildTaskSearchModel listModel) {
    if (itemData == null) {
      return Container();
    } else {
      var hasExecute = itemData.status == 1;
      return Container(
        margin: EdgeInsets.fromLTRB(15, 0, 15, 10),
        padding: EdgeInsets.fromLTRB(10, 10, 10, 0),
        decoration: BoxDecoration(
            color: Colors.white, borderRadius: BorderRadius.circular(2)),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 标题
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: Text(
                  itemData.customerName ?? "",
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  style: TextStyle(
                      color: const Color(0xFF292933),
                      fontWeight: FontWeight.w500,
                      fontSize: 16),
                )),
                SizedBox(
                  width: 37,
                ),
                Text(
                  itemData.taskStatus!,
                  style: TextStyle(
                      color: hasExecute ? Color(0xFF9494A6) : Color(0xFFFF7200),
                      fontWeight: FontWeight.normal,
                      fontSize: 14),
                )
              ],
            ),
            buildInfoRowWidget("执行人", itemData.executor ?? "", null),
            Visibility(
              visible: hasExecute && itemData.scheduleId != null,
              child: Divider(
                height: 1,
                color: Color(0xFFF0F0F2),
              ),
            ),
            Visibility(
                visible: hasExecute && itemData.scheduleId != null,
                child: buildInfoRowWidget(
                    itemData.visitorType ?? "", itemData.visitorTime ?? "", () {
                  XYYContainer.open(
                      "xyy://crm-app.ybm100.com/schedule/detail?scheduleId=${itemData.scheduleId}");
                })),
            Visibility(
              visible: hasExecute,
              child: Divider(
                height: 1,
                color: Color(0xFFF0F0F2),
              ),
            ),
            Visibility(
              visible: hasExecute,
              child: buildInfoRowWidget("执行结果", "", () {
                Navigator.of(context).pushNamed("/task_child_result_page",
                    arguments: {"subTaskId": itemData.taskId?.toString()});
              }),
            ),
            Visibility(
                visible: itemData.doTask == 1,
                child: GestureDetector(
                  onTap: () {
                    Navigator.of(context).pushNamed("/task_dotask",
                        arguments: {"taskId": itemData.taskId}).then((value) {
                      if (value is Map) {
                        listModel.requestTaskList(true);
                      }
                    });
                  },
                  child: Container(
                    width: double.infinity,
                    alignment: Alignment.center,
                    margin: EdgeInsets.only(bottom: 10),
                    height: 32,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        border: Border.all(color: Color(0xFF00B377), width: 1)),
                    child: Text(
                      "做任务",
                      style: TextStyle(
                          color: Color(0xFF00B377),
                          fontSize: 14,
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                ))
          ],
        ),
      );
    }
  }

  Widget? getEmptyWidget(ChildTaskSearchModel listModel) {
    if (listModel.isSuccess == null) {
      return null;
    }
    if (listModel.isSuccess == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        listModel.requestTaskList(true);
      });
    }

    if ((listModel.list?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }

  buildInfoRowWidget(String label, String content, VoidCallback? onPress) {
    return GestureDetector(
      onTap: () {
        if (onPress != null) {
          onPress();
        }
      },
      child: Container(
          height: 44,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 66,
                alignment: Alignment.centerLeft,
                child: Text(
                  label,
                  style: TextStyle(
                      color: Color(0xFF9494A6),
                      fontSize: 14,
                      fontWeight: FontWeight.normal),
                ),
              ),
              Expanded(
                  child: Text(
                content,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                    color: Color(0xFF676773),
                    fontSize: 14,
                    fontWeight: FontWeight.normal),
              )),
              Visibility(
                  visible: onPress != null,
                  child: Image.asset(
                    "assets/images/task/task_child_item_arrow.png",
                    width: 15,
                    height: 15,
                  ))
            ],
          )),
    );
  }

  String formatTime(int visitorTime) {
    try {
      var dateTime = DateTime.fromMillisecondsSinceEpoch(visitorTime);
      return TimeUtil.formatTime2(dateTime);
    } catch (e) {
      return "";
    }
  }
}

class ChildTaskListModel1 extends ChildTaskSearchModel {
  ChildTaskListModel1(
      String? taskId,
      ChildTaskStatus status,
      EasyRefreshController easyRefreshController,
      ScrollController scrollController,
      BaseState state)
      : super(taskId, status, easyRefreshController, scrollController, state);
}

class ChildTaskListModel2 extends ChildTaskSearchModel {
  ChildTaskListModel2(
      String? taskId,
      ChildTaskStatus status,
      EasyRefreshController easyRefreshController,
      ScrollController scrollController,
      BaseState state)
      : super(taskId, status, easyRefreshController, scrollController, state);
}

class ChildTaskListModel3 extends ChildTaskSearchModel {
  ChildTaskListModel3(
      String? taskId,
      ChildTaskStatus status,
      EasyRefreshController easyRefreshController,
      ScrollController scrollController,
      BaseState state)
      : super(taskId, status, easyRefreshController, scrollController, state);
}

class ChildTaskSearchModel extends ChangeNotifier {
  List<TaskChildItemData>? list;
  ChildTaskStatus status;
  String? taskId;
  bool? isLastPage = true;
  bool? isSuccess;
  bool isDisposed = false;
  int? offset = 0;
  String? customerName;
  EasyRefreshController easyRefreshController;
  ScrollController scrollController;
  BaseState state;

  ChildTaskSearchModel(this.taskId, this.status, this.easyRefreshController,
      this.scrollController, this.state);

  void requestTaskList(bool isRefresh) {
    var params = Map<String, dynamic>();
    if (isRefresh) {
      params["offset"] = 0;
    } else {
      params["offset"] = offset! + 1;
    }
    params["limit"] = 10;
    if (status.id != -1) {
      params["taskStatus"] = status.id;
    }
    params["id"] = taskId;
    requestBDTaskList(isRefresh, params);
  }

  void handleResult(bool? isSuccess, TaskChildListData? value,
      Map<String, dynamic> params, bool isRefresh) {
    if (!isDisposed) {
      EasyLoading.dismiss();
      this.isSuccess = isSuccess;
      easyRefreshController.finishLoad();
      easyRefreshController.finishRefresh();
      if (isSuccess==true) {
        offset = params["offset"] as int?;
        if (isRefresh) {
          scrollController.jumpTo(0.0);
          list = value!.rows;
        } else {
          list!.addAll(value!.rows!);
        }
        isLastPage = value.lastPage;
      } else if (isRefresh) {
        offset = 0;
        list = null;
        isLastPage = true;
      }
      state.setState(() {});
    }
  }

  void requestBDTaskList(bool isRefresh, Map<String, dynamic> params) {
    if (isRefresh) {
      EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    }
    if (customerName != null && customerName!.isNotEmpty) {
      params["customerName"] = customerName;
    }
    NetworkV2<TaskChildListData>(TaskChildListData())
        .requestDataV2("task/bd/childTask",
            method: RequestMethod.GET, parameters: params)
        .then((value) {
      handleResult(value.isSuccess, value.getData(), params, isRefresh);
    });
  }

  @override
  void dispose() {
    isDisposed = true;
    print("guan:ChildTaskListModel dispose $status");
    super.dispose();
  }
}
