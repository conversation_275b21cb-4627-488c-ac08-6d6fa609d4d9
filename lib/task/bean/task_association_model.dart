import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'task_association_model.g.dart';

@JsonSerializable()
class TaskAssociationModel extends BaseModel<TaskAssociationModel> {
  bool? lastPage;

  List<TaskAssociationListModel>? rows;

  TaskAssociationModel();

  factory TaskAssociationModel.fromJson(Map<String, dynamic> json) =>
      _$TaskAssociationModelFromJson(json);

  @override
  TaskAssociationModel fromJsonMap(Map<String, dynamic>? json) {
    return _$TaskAssociationModelFromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskAssociationModelToJson(this);
  }
}

@JsonSerializable()
class TaskAssociationListModel extends BaseModel<TaskAssociationListModel> {
  /// 主键
  int? id;

  String? scheduleTheme;

  int? startTime;
  int? endTime;
  int? createTime;
  int? type;
  int? visitId;
  int? userId;
  int? oaId;
  String? remark;
  String? userName;
  bool? effective;

  TaskAssociationListModel();

  factory TaskAssociationListModel.fromJson(Map<String, dynamic> json) =>
      _$TaskAssociationListModelFromJson(json);

  @override
  TaskAssociationListModel fromJsonMap(Map<String, dynamic>? json) {
    return _$TaskAssociationListModelFromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskAssociationListModelToJson(this);
  }
}
