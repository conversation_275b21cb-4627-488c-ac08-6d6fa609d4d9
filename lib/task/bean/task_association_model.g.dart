// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_association_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskAssociationModel _$TaskAssociationModelFromJson(Map<String, dynamic> json) {
  return TaskAssociationModel()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : TaskAssociationModel.fromJson(json['data'] as Map<String, dynamic>)
    ..lastPage = json['lastPage'] as bool?
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map(
            (e) => TaskAssociationListModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$TaskAssociationModelToJson(
        TaskAssociationModel instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'lastPage': instance.lastPage,
      'rows': instance.rows,
    };

TaskAssociationListModel _$TaskAssociationListModelFromJson(
    Map<String, dynamic> json) {
  return TaskAssociationListModel()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : TaskAssociationListModel.fromJson(
            json['data'] as Map<String, dynamic>)
    ..id = json['id'] as int?
    ..scheduleTheme = json['scheduleTheme'] as String?
    ..startTime = json['startTime'] as int?
    ..endTime = json['endTime'] as int?
    ..createTime = json['createTime'] as int?
    ..type = json['type'] as int?
    ..visitId = json['visitId'] as int?
    ..userId = json['userId'] as int?
    ..oaId = json['oaId'] as int?
    ..remark = json['remark'] as String?
    ..userName = json['userName'] as String?
    ..effective = json['effective'] as bool?;
}

Map<String, dynamic> _$TaskAssociationListModelToJson(
        TaskAssociationListModel instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'id': instance.id,
      'scheduleTheme': instance.scheduleTheme,
      'startTime': instance.startTime,
      'endTime': instance.endTime,
      'createTime': instance.createTime,
      'type': instance.type,
      'visitId': instance.visitId,
      'userId': instance.userId,
      'oaId': instance.oaId,
      'remark': instance.remark,
      'userName': instance.userName,
      'effective': instance.effective,
    };
