import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'dotask_config_model.g.dart';

@JsonSerializable()
class DoTaskConfigModel extends BaseModel<DoTaskConfigModel> {
  /// 任务类型 1:信息收集 2：系统/软件售卖
  int? taskType;

  /// 任务类型描述
  String? taskTypeStr;

  /// 任务名称/主题
  String? theme;

  /// 客户名称
  String? customerName;

  /// 客户地址
  String? customerAddress;

  /// 配置信息
  List<DoTaskConfigItemModel>? showList;

  DoTaskConfigModel();

  factory DoTaskConfigModel.fromJson(Map<String, dynamic> json) =>
      _$DoTaskConfigModelFromJson(json);

  @override
  DoTaskConfigModel fromJsonMap(Map<String, dynamic>? json) {
    return _$DoTaskConfigModelFromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$DoTaskConfigModelToJson(this);
  }
}

@JsonSerializable()
class DoTaskConfigItemModel extends BaseModel<DoTaskConfigItemModel> {
  /// 填写时需要展示的名称
  String? name;

  /// item类型 1 手填 2 单选  3 灰色分割条
  int? type;

  /// 输入类型 1 弹窗输入  2 本页输入（此字段后端没有下发）
  int? inputType;

  /// 输入框限制的最大输入长度 后端没有下发（本地处理）
  int? limitLenght;

  /// 提示文本信息 后端没有下发 （本地处理）
  String? placehold;

  /// 标识当前name为（客户是否购买）0否 1是
  int? isBuy;

  DoTaskConfigItemModel();

  factory DoTaskConfigItemModel.fromJson(Map<String, dynamic> json) =>
      _$DoTaskConfigItemModelFromJson(json);

  @override
  DoTaskConfigItemModel fromJsonMap(Map<String, dynamic>? json) {
    return _$DoTaskConfigItemModelFromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$DoTaskConfigItemModelToJson(this);
  }
}
