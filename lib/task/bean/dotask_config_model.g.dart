// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'dotask_config_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DoTaskConfigModel _$DoTaskConfigModelFromJson(Map<String, dynamic> json) {
  return DoTaskConfigModel()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : DoTaskConfigModel.fromJson(json['data'] as Map<String, dynamic>)
    ..taskType = json['taskType'] as int?
    ..taskTypeStr = json['taskTypeStr'] as String?
    ..theme = json['theme'] as String?
    ..customerName = json['customerName'] as String?
    ..customerAddress = json['customerAddress'] as String?
    ..showList = (json['showList'] as List<dynamic>?)
        ?.map((e) => DoTaskConfigItemModel.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$DoTaskConfigModelToJson(DoTaskConfigModel instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'taskType': instance.taskType,
      'taskTypeStr': instance.taskTypeStr,
      'theme': instance.theme,
      'customerName': instance.customerName,
      'customerAddress': instance.customerAddress,
      'showList': instance.showList,
    };

DoTaskConfigItemModel _$DoTaskConfigItemModelFromJson(
    Map<String, dynamic> json) {
  return DoTaskConfigItemModel()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : DoTaskConfigItemModel.fromJson(json['data'] as Map<String, dynamic>)
    ..name = json['name'] as String?
    ..type = json['type'] as int?
    ..inputType = json['inputType'] as int?
    ..limitLenght = json['limitLenght'] as int?
    ..placehold = json['placehold'] as String?
    ..isBuy = json['isBuy'] as int?;
}

Map<String, dynamic> _$DoTaskConfigItemModelToJson(
        DoTaskConfigItemModel instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'name': instance.name,
      'type': instance.type,
      'inputType': instance.inputType,
      'limitLenght': instance.limitLenght,
      'placehold': instance.placehold,
      'isBuy': instance.isBuy,
    };
