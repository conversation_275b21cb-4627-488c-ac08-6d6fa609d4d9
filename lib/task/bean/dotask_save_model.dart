import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'dotask_save_model.g.dart';

@JsonSerializable()
class DoTaskSaveModel extends BaseModel<DoTaskSaveModel> {
  DoTaskSaveModel();

  factory DoTaskSaveModel.fromJson(Map<String, dynamic> json) =>
      _$DoTaskSaveModelFromJson(json);

  @override
  DoTaskSaveModel fromJsonMap(Map<String, dynamic>? json) {
    return _$DoTaskSaveModelFromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$DoTaskSaveModelToJson(this);
  }
}
