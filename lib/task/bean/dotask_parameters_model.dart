import 'package:json_annotation/json_annotation.dart';

part 'dotask_parameters_model.g.dart';

@JsonSerializable()
class DoTaskParametersModel {
  /// 子任务id
  String? id;

  /// 备注
  String? remark;

  /// 图片URL 多图逗号分开 aa,bb
  // ignore: non_constant_identifier_names
  String? imageUrl;

  /// k：v形式填写的内容
  Map<String, String>? result = {};

  /// 关联的拜访id
  int? scheduleId;

  /// 拜访主题 - 仅用回显到页面上
  @JsonKey(ignore: true)
  String? visitTheme;

  /// 任务类型 1:信息收集 2：系统/软件售卖
  int? taskType;

  /// 经度
  double? longitude;

  /// 纬度
  double? latitude;

  /// 地址信息
  String? collectAddress;

  DoTaskParametersModel();

  DoTaskParametersModel fromJsonMap(Map<String, dynamic> json) {
    return _$DoTaskParametersModelFromJson(json);
  }

  Map<String, dynamic> toJson() {
    return _$DoTaskParametersModelToJson(this);
  }
}
