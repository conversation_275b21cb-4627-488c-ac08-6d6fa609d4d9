import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/task/data/task_child_result_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class TaskChildResultPage extends BasePage {
  final String taskId;

  TaskChildResultPage(this.taskId);

  @override
  BaseState<StatefulWidget> initState() {
    return TaskChildResultPageState();
  }
}

class TaskChildResultPageState extends BaseState<TaskChildResultPage> {
  List<TaskChildResultItem>? list = <TaskChildResultItem>[];

  @override
  void onCreate() {
    requestResultList();
    super.onCreate();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 15),
      child: list == null || list!.isEmpty
          ? buildEmptyWidget()
          : ListView.builder(
              itemBuilder: (BuildContext context, int index) {
                return buildContentWidget(index);
              },
              itemCount: list!.length,
            ),
    );
  }

  @override
  String getTitleName() {
    return "执行结果";
  }

  Widget buildEmptyWidget() {
    if (list == null) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        requestResultList();
      });
    }
    if (list!.isEmpty) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return Container();
  }

  Widget buildContentWidget(int index) {
    var itemData = list![index];
    if (itemData == null) {
      return Container();
    }
    return Container(
        padding: EdgeInsets.only(top: 15),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(
              width: 77,
              child: Text(
                itemData.key!,
                style: TextStyle(
                    color: const Color(0xFF676773),
                    fontSize: 15,
                    fontWeight: FontWeight.normal),
              ),
            ),
            SizedBox(
              width: 20,
            ),
            Expanded(
                child: Text(itemData.value!,
                    style: TextStyle(
                        color: Color(0xFF292933),
                        fontSize: 15,
                        fontWeight: FontWeight.normal)))
          ],
        ));
  }

  void requestResultList() {
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    Network<TaskChildResultItem>(TaskChildResultItem()).requestListData(
        "task/bd/taskResult",
        method: RequestMethod.GET,
        parameters: {"subTaskId": widget.taskId}).then((value) {
      EasyLoading.dismiss();
      if (mounted) {
        if (value.isSuccess) {
          list = value.data;
        } else {
          list = null;
        }
        setState(() {});
      }
    });
  }

  @override
  void onDestroy() {
    EasyLoading.dismiss();
    super.onDestroy();
  }
}
