// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_child_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskChildListData _$TaskChildListDataFromJson(Map<String, dynamic> json) {
  return TaskChildListData()
    ..lastPage = json['lastPage'] as bool?
    ..rows = (json['rows'] as List<dynamic>?)
        ?.map((e) => TaskChildItemData.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$TaskChildListDataToJson(TaskChildListData instance) =>
    <String, dynamic>{
      'lastPage': instance.lastPage,
      'rows': instance.rows,
    };

TaskChildItemData _$TaskChildItemDataFromJson(Map<String, dynamic> json) {
  return TaskChildItemData()
    ..taskId = json['taskId'] as int?
    ..taskStatus = json['taskStatus'] as String?
    ..status = json['status'] as int?
    ..remark = json['remark'] as String?
    ..executor = json['executor'] as String?
    ..customerName = json['customerName'] as String?
    ..doTask = json['doTask'] as int?
    ..visitorType = json['visitorType'] as String?
    ..visitorTime = json['visitorTime'] as String?
    ..visitorId = json['visitorId'] as int?
    ..scheduleId = json['scheduleId'] as int?;
}

Map<String, dynamic> _$TaskChildItemDataToJson(TaskChildItemData instance) =>
    <String, dynamic>{
      'taskId': instance.taskId,
      'taskStatus': instance.taskStatus,
      'status': instance.status,
      'remark': instance.remark,
      'executor': instance.executor,
      'customerName': instance.customerName,
      'doTask': instance.doTask,
      'visitorType': instance.visitorType,
      'visitorTime': instance.visitorTime,
      'visitorId': instance.visitorId,
      'scheduleId': instance.scheduleId,
    };
