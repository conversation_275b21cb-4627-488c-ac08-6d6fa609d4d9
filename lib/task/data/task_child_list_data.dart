import 'package:json_annotation/json_annotation.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

part 'task_child_list_data.g.dart';

@JsonSerializable()
class TaskChildListData extends BaseModelV2<TaskChildListData> {
  bool? lastPage;
  List<TaskChildItemData>? rows;

  TaskChildListData();

  @override
  TaskChildListData fromJsonMap(Map<String, dynamic>? json) {
    return TaskChildListData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskChildListDataToJson(this);
  }

  factory TaskChildListData.fromJson(Map<String, dynamic> json) =>
      _$TaskChildListDataFromJson(json);
}

@JsonSerializable()
class TaskChildItemData extends BaseModelV2<TaskChildItemData> {
  int? taskId; //子任务主键ID
  String? taskStatus; //已执行 、未执行
  int? status; //已执行1 、未执行0
  String? remark; //执行备注
  String? executor; //执行人
  String? customerName; //药店名称
  int? doTask; //0 无做任务按钮 1 有做任务按钮
  String? visitorType; //1:上门拜访 2:电话拜访
  String? visitorTime; //拜访时间
  int? visitorId; //拜访任务ID,没有用
  int? scheduleId; // 日程id，跳转拜访详情需要用这个id

  TaskChildItemData();

  @override
  TaskChildItemData fromJsonMap(Map<String, dynamic>? json) {
    return TaskChildItemData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskChildItemDataToJson(this);
  }

  factory TaskChildItemData.fromJson(Map<String, dynamic> json) =>
      _$TaskChildItemDataFromJson(json);
}
