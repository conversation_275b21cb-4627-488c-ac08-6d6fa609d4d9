// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'task_child_result_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

TaskChildResultData _$TaskChildResultDataFromJson(Map<String, dynamic> json) {
  return TaskChildResultData()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : TaskChildResultData.fromJson(json['data'] as Map<String, dynamic>)
    ..resultMap = (json['resultMap'] as List<dynamic>?)
        ?.map((e) => TaskChildResultItem.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$TaskChildResultDataToJson(
        TaskChildResultData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'resultMap': instance.resultMap,
    };

TaskChildResultItem _$TaskChildResultItemFromJson(Map<String, dynamic> json) {
  return TaskChildResultItem()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : TaskChildResultItem.fromJson(json['data'] as Map<String, dynamic>)
    ..key = json['key'] as String?
    ..value = json['value'] as String?;
}

Map<String, dynamic> _$TaskChildResultItemToJson(
        TaskChildResultItem instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'key': instance.key,
      'value': instance.value,
    };
