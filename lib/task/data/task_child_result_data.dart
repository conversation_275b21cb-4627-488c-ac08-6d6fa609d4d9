import 'package:json_annotation/json_annotation.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';

part 'task_child_result_data.g.dart';

@JsonSerializable()
class TaskChildResultData extends BaseModel<TaskChildResultData> {
  List<TaskChildResultItem>? resultMap;

  TaskChildResultData();

  @override
  TaskChildResultData fromJsonMap(Map<String, dynamic>? json) {
    return TaskChildResultData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskChildResultDataToJson(this);
  }

  factory TaskChildResultData.fromJson(Map<String, dynamic> json) =>
      _$TaskChildResultDataFromJson(json);
}

@JsonSerializable()
class TaskChildResultItem extends BaseModel<TaskChildResultItem> {

  String? key;
  String? value;

  TaskChildResultItem();

  @override
  TaskChildResultItem fromJsonMap(Map<String, dynamic>? json) {
    return TaskChildResultItem.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$TaskChildResultItemToJson(this);
  }

  factory TaskChildResultItem.fromJson(Map<String, dynamic> json) =>
      _$TaskChildResultItemFromJson(json);
}
