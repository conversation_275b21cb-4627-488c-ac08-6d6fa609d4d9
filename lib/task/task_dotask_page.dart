import 'dart:convert';

import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XYYContainer/toast/toast_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/task/bean/dotask_config_model.dart';
import 'package:XyyBeanSproutsFlutter/task/bean/dotask_parameters_model.dart';
import 'package:XyyBeanSproutsFlutter/task/bean/dotask_save_model.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_chooseitem.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_imageitem.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_input_dialog.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_inputitem.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_location.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_selectitem.dart';
import 'package:XyyBeanSproutsFlutter/task/widget/task_dotask_title.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class TaskDoTaskPage extends BasePage {
  /// 子任务id
  final String taskId;

  /// 拜访来的做任务页面，则保存信息时不走网络，直接把信息返回
  final bool isFromVisit;

  TaskDoTaskPage({required this.taskId, this.isFromVisit = false});

  @override
  BaseState<StatefulWidget> initState() {
    return TaskDoTaskPageState();
  }
}

class TaskDoTaskPageState extends BaseState<TaskDoTaskPage> {
  DoTaskParametersModel paramsModel = DoTaskParametersModel();

  DoTaskConfigModel? configModel;

  @override
  void initState() {
    super.initState();
    this.paramsModel.id = widget.taskId;

    this.getTaskConfig();
  }

  @override
  bool needKeepAlive() => true;

  @override
  Widget buildWidget(BuildContext context) {
    return SafeArea(
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Container(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                color: Color(0xFFF0F0F2),
                height: 1,
              ),
              Expanded(
                child: ListView.custom(
                  childrenDelegate: SliverChildBuilderDelegate(
                    getItemForIndex,
                    childCount: getItemCont(),
                  ),
                ),
              ),
              GestureDetector(
                onTap: submitAction,
                child: Container(
                  margin: EdgeInsets.fromLTRB(15, 9, 15, 5),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(2),
                    color: Color(0xFF00B377),
                  ),
                  height: 44,
                  child: Text(
                    "提交",
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFFFFFFFF),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  int getItemCont() {
    /// 额外的四个item是本地判断写死的。后端不配合做成全部动态的
    return (this.configModel?.showList?.length ?? 0) + 5;
  }

  Widget getItemForIndex(BuildContext context, int index) {
    int configCount = this.configModel?.showList?.length ?? 0;
    if (index == 0) {
      return TaskDoTaskTitleItem(
        taskTitle: this.configModel?.theme ?? "-",
        taskType: this.configModel?.taskType ?? 1,
        taskTypeStr: this.configModel?.taskTypeStr ?? "信息收集",
      );
    } else if (index == 1) {
      if (widget.isFromVisit) {
        return Container();
      }
      return TaskDoTaskSelectItem(
        itemTitle: "关联拜访",
        itemKey: 'association',
        placehold: "请选择关联拜访",
        selectChange: (itemKey, controller) {
          this.selectItemAction(context, itemKey ?? "", null, controller, null);
        },
        content: this.paramsModel.visitTheme,
      );
    } else if (index > 1 && index <= 1 + configCount) {
      DoTaskConfigItemModel itemModel = this.configModel!.showList![index - 2];
      switch (itemModel.type) {
        case 1: // 手动填写
          {
            if (itemModel.inputType == 1) {
              return TaskDoTaskSelectItem(
                itemTitle: itemModel.name,
                placehold: itemModel.placehold,
                itemKey: itemModel.name,
                content: this.paramsModel.result![itemModel.name],
                selectChange: (itemKey, controller) {
                  this.selectItemAction(
                    context,
                    itemKey ?? "",
                    itemModel.placehold,
                    controller,
                    itemModel.limitLenght,
                  );
                },
              );
            }
            return TaskDoTaskInputItem(
              itemTitle: itemModel.name,
              placehold: itemModel.placehold,
              maxLenght: itemModel.limitLenght,
              valueChange: (title, content) {
                this.paramsModel.result![title ?? ""] = content;
              },
            );
          }
        case 2: // 单选
          {
            return TaskDoTaskChooseItem(
              itemTitle: itemModel.name,
              selectIndex: this.paramsModel.result![itemModel.name] == '是' ||
                      this.paramsModel.result![itemModel.name] == null
                  ? 0
                  : 1,
              changeSelectValue: (title, content) {
                // 挺恶心的 用标题做判断  目前后端不支持模版类型匹配
                if (title == '客户是否购买') {
                  if (content == '是') {
                    // 动态添加选项内容 也挺恶心的
                    var showList = this.configModel!.showList!;
                    // 移除不需要的字段
                    showList.removeWhere((element) => [
                          '门店是否在售',
                          '其他品牌和进价',
                          '其他原因',
                          '推荐方法'
                        ].contains(element.name));
                    // 添加新增的字段
                    showList.add(DoTaskConfigItemModel()
                      ..name = '推荐方法'
                      ..type = 1
                      ..inputType = 2
                      ..limitLenght = 200
                      ..placehold = '请填写推荐方法(必填)');
                    setState(() {
                      this.configModel!.showList = showList;
                    });
                  } else {
                    // 动态添加选项内容 也挺恶心的
                    var showList = this.configModel!.showList!;
                    // 移除不需要的字段
                    showList.removeWhere((element) => [
                          '门店是否在售',
                          '其他品牌和进价',
                          '其他原因',
                          '推荐方法'
                        ].contains(element.name));
                    // 添加新增的字段
                    showList.add(DoTaskConfigItemModel()
                      ..name = '门店是否在售'
                      ..type = 2);
                    showList.add(DoTaskConfigItemModel()
                      ..name = '其他品牌和进价'
                      ..type = 1
                      ..inputType = 2
                      ..limitLenght = 200
                      ..placehold = '请填写其他品牌和进价');
                    showList.add(DoTaskConfigItemModel()
                      ..name = '其他原因'
                      ..type = 1
                      ..inputType = 2
                      ..limitLenght = 200
                      ..placehold = '请填写其他原因');
                    // 添加默认参数
                    this.paramsModel.result!['门店是否在售'] = '是';
                    this.paramsModel.result!['其他品牌和进价'] = '是';
                    setState(() {
                      this.configModel!.showList = showList;
                    });
                  }
                }
                this.paramsModel.result![title ?? ""] = content;
              },
            );
          }
        default:
          {
            return Container(
              height: 10,
              color: Color(0xFFF6F6F6),
            );
          }
      }
    } else if (index == 2 + configCount) {
      return TaskDoTaskInputItem(
        itemTitle: "任务备注",
        placehold: "请填写任务备注，完成或未完成的经验，障碍等信息(必填)",
        content: this.paramsModel.remark ?? "",
        maxLenght: 200,
        valueChange: (title, content) {
          this.paramsModel.remark = content;
        },
      );
    } else if (index == 3 + configCount) {
      // 选择图片
      return TaskDoTaskImageItem(
        limitCount: 5,
        imageArray: this.paramsModel.imageUrl?.split(',') ?? [],
        imageChange: (value) {
          if (value?.isNotEmpty ?? false) {
            this.paramsModel.imageUrl = value?.join(',');
          } else {
            this.paramsModel.imageUrl = null;
          }
        },
      );
    } else if (index == 4 + configCount) {
      return TaskDoTaskLocationItem(
        changeLocation: (address, lat, lng) {
          this.paramsModel.collectAddress = address;
          // upgrade_2.0 guanchong 这里如果没有获取到定位返回的是 0，0
          this.paramsModel.latitude = double.parse(lat ?? "0");
          this.paramsModel.longitude = double.parse(lng ?? "0");
        },
      );
    } else {
      return Container();
    }
  }

  /// 获取页面配置
  Future getTaskConfig() async {
    try {
      showLoadingDialog();
      var result = await Network<DoTaskConfigModel>(DoTaskConfigModel())
          .requestData('task/getSubTask', parameters: {'id': widget.taskId});
      dismissLoadingDialog();
      if (result.isSuccess == true && result.showList != null) {
        if (mounted) {
          this.paramsModel.taskType = result.taskType;
          // 信息收集类的 手填项目都设置为弹窗填写，取本地预置的数据， 如果服务端传递了 则不处理
          // 目前服务端不支持模版的配置 做本地判断
          if (result.taskType == 1) {
            result.showList?.forEach((element) {
              if (element.type == 2) {
                // 单选类型填默认参数
                this.paramsModel.result![element.name ?? ""] = '是';
              }
              if (element.type == 1 && element.inputType == null) {
                element.inputType = 1;
              }
              if (element.limitLenght == null) {
                element.limitLenght = 50;
              }
              if (element.placehold == null) {
                element.placehold = '请填写${element.name}(必填)';
              }
            });
            // 没有办法区分自定义或者预置的字段，所以根据后台可配置的字段进行判断
            List<String> prepareTitles = [
              "是否完成",
              "面积",
              "人数",
              "周边环境",
              "消费人群",
              "客流量",
              "月销售额",
              "SKU数",
              "TOP商品"
            ];
            var firstCustomIndex = result.showList!
                .indexWhere((element) => !prepareTitles.contains(element.name));
            if (firstCustomIndex != -1 &&
                firstCustomIndex != result.showList!.length - 1) {
              result.showList!
                  .insert(firstCustomIndex, DoTaskConfigItemModel()..type = 3);
            }
          } else {
            result.showList?.forEach((element) {
              if (element.type == 2) {
                // 单选类型填默认参数
                this.paramsModel.result![element.name ?? ""] = '是';
              }
              if (element.type == 1 && element.inputType == null) {
                element.inputType = 2;
              }
              if (element.limitLenght == null) {
                element.limitLenght = 200;
              }
              if (element.placehold == null) {
                element.placehold = '请填写${element.name}(必填)';
              }
            });
            // 信息收集类的内容在本地配置
            result.showList?.add(DoTaskConfigItemModel()
              ..name = '是否价格优势'
              ..type = 2);
            this.paramsModel.result!['是否价格优势'] = '是';
            // 添加新增的字段
            result.showList?.add(DoTaskConfigItemModel()
              ..name = '推荐方法'
              ..type = 1
              ..inputType = 2
              ..limitLenght = 200
              ..placehold = '请填写推荐方法(必填)');
          }

          this.paramsModel.result!['是否完成'] = '是';

          setState(() {
            this.configModel = result;
          });
        }
      } else {
        showToast(result.errorMsg??"错误！");
      }
    } on PlatformException catch (e) {
      showToast(e.message!);
      dismissLoadingDialog();
    }
  }

  void selectItemAction(
    BuildContext context,
    String itemKey,
    String? placehold,
    ValueNotifier<String?>? controller,
    int? limitLenght,
  ) async {
    if (itemKey == 'association') {
      var result = await Navigator.of(context)
          .pushNamed('/task_association?taskId=${widget.taskId}');
      if (result is Map<String, dynamic> &&
          result.keys.contains('visitTheme')) {
        controller?.value = result['visitTheme'] ?? "";
        // 记录拜访的主题 - 接口上传时忽略次字段
        this.paramsModel.visitTheme = result['visitTheme'] ?? "";
        // 记录拜访的id
        this.paramsModel.scheduleId = result['visitId'];
      }
    } else {
      String currentContent = this.paramsModel.result![itemKey] ?? "";
      var result = await showDoTaskInputView(
        context,
        title: itemKey,
        placehold: placehold,
        itemKey: itemKey,
        content: currentContent,
        maxLenght: limitLenght,
      );
      if (result is String) {
        controller?.value = result;
        this.paramsModel.result![itemKey] = result;
      }
    }
  }

  /// 提交事件
  void submitAction() {
    if (checkInput()) {
      Map<String, dynamic> resultParam = this.paramsModel.toJson();
      // 去掉空参数
      resultParam.removeWhere((key, value) => value == null);
      //处理result入参格式
      var titleList =
          this.configModel!.showList!.map((e) => e.name).toSet().toList();
      titleList.removeWhere((element) => element == null || element.isEmpty);
      resultParam['result'] = jsonEncode(
          titleList.map((e) => {e: this.paramsModel.result![e]}).toList());
      // 如果是拜访里面关联任务进来的 则添加任务主题参数 并回传参数到拜访中处理
      if (widget.isFromVisit) {
        resultParam['themeTitle'] = this.configModel!.theme;
        Navigator.of(context).pop(resultParam);
      } else {
        requestSaveTask(resultParam);
      }
    }
  }

  void requestSaveTask(Map<String, dynamic> resultParam) async {
    showLoadingDialog();
    var result = await Network<DoTaskSaveModel>(DoTaskSaveModel()).requestData(
      'task/v2/doTask',
      method: RequestMethod.POST,
      contentType: RequestContentType.FORM,
      parameters: resultParam,
    );
    dismissLoadingDialog();
    if (mounted) {
      if (result.isSuccess==true) {
        showToast('提交成功', type: ToastType.Success);
        Navigator.of(context).pop({'SaveSuccess': true});
      } else {
        showToast(result.errorMsg!);
      }
    }
  }

  bool checkInput() {
    if (this.configModel?.showList == null) {
      showToast("showList为空，不能提交");
      return false;
    }
    bool flag = true;
    this.configModel!.showList!.forEach((element) {
      if (element.name != null && flag != false) {
        if ((this.paramsModel.result!.keys.contains(element.name) &&
                this.paramsModel.result![element.name]!.isEmpty) ||
            !this.paramsModel.result!.keys.contains(element.name)) {
          showToast('请填写${element.name}');
          flag = false;
        }
      }
    });
    if (flag == false) {
      return flag;
    }
    if (this.paramsModel.remark?.isEmpty ?? true) {
      showToast('请填写任务备注');
      return false;
    }
    return true;
  }

  @override
  String getTitleName() {
    return "做任务";
  }
}
