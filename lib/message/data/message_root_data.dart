import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'message_list_data.dart';
import 'package:json_annotation/json_annotation.dart';
part 'message_root_data.g.dart';

@JsonSerializable()
class MessageRootData extends BaseModel<MessageRootData?> {
  List<MessageListData>? rows;
  bool? lastPage = false;

  MessageRootData();

  factory MessageRootData.fromJson(Map<String, dynamic> json) =>
      _$MessageRootDataFromJson(json);

  @override
  fromJsonMap(Map<String, dynamic> json) {
    return _$MessageRootDataFromJson(json);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$MessageRootDataToJson(this);
  }
}
