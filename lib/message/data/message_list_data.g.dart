// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'message_list_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

MessageListData _$MessageListDataFromJson(Map<String, dynamic> json) {
  return MessageListData(
    id: json['id'] as int?,
    title: json['title'] as String?,
    oaId: json['oaId'] as int?,
    content: json['content'] as String?,
    createTime: json['createTime'] as int?,
    state: json['state'] as int?,
    taskId: json['taskId'] as int?,
  )
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : MessageListData.fromJson(json['data'] as Map<String, dynamic>)
    ..customType = json['customType'] as String?
    ..messageStatus = json['messageStatus'] as int?
    ..messageType = json['messageType'] as int?
    ..licenseType = json['licenseType'] as int?
    ..merchantId = json['merchantId'] as int?
    ..customerId = json['customerId'] as int?
    ..applicationNumber = json['applicationNumber'] as String?
    ..messageTitle = json['messageTitle'] as String?
    ..time = json['time'] as String?
    ..dateTime = json['dateTime'] as String?
    ..typeName = json['typeName'] as String?
    ..canRedirect = json['canRedirect'] as bool?
    ..remark = json['remark'] as String?
    ..readStatus = json['readStatus'] as int?;
}

Map<String, dynamic> _$MessageListDataToJson(MessageListData instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'id': instance.id,
      'customType': instance.customType,
      'title': instance.title,
      'oaId': instance.oaId,
      'content': instance.content,
      'createTime': instance.createTime,
      'state': instance.state,
      'taskId': instance.taskId,
      'messageStatus': instance.messageStatus,
      'messageType': instance.messageType,
      'licenseType': instance.licenseType,
      'merchantId': instance.merchantId,
      'customerId': instance.customerId,
      'applicationNumber': instance.applicationNumber,
      'messageTitle': instance.messageTitle,
      'time': instance.time,
      'dateTime': instance.dateTime,
      'typeName': instance.typeName,
      'canRedirect': instance.canRedirect,
      'remark': instance.remark,
      'readStatus': instance.readStatus,
    };
