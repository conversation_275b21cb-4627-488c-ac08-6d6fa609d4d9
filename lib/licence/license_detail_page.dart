import 'dart:convert';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_detail_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_have_type.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_init_data.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

/// 资质详情
class LicenseDetailPage extends BasePage {
  final String? licenseAuditId;
  final String? type;

  LicenseDetailPage(this.licenseAuditId, this.type);

  @override
  BaseState<StatefulWidget> initState() {
    return LicenseDetailPageState();
  }
}

class LicenseDetailPageState extends BaseState<LicenseDetailPage> {
  LicenseDetailData? detailData;

  @override
  void onCreate() {
    requestLicenseDetail();
    super.onCreate();
  }

  @override
  Widget buildWidget(BuildContext context) {
    var isEdit = detailData?.licenseAudit?.isEdit ?? -1;
    return Container(
      color: Color(0xFFF4F5F9),
      child: Column(
        children: [
          Expanded(
            child: ListView(
              children: [
                buildCommonHeaderWidget(),
                Visibility(
                    visible:
                        detailData?.necessaryLicenceList?.isNotEmpty ?? false,
                    child: buildListHeaderWidget("必填项：", Color(0xffff3737))),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(
                      detailData?.necessaryLicenceList?.length ?? 0, (index) {
                    return buildListItem(
                        detailData?.necessaryLicenceList?.elementAt(index),
                        true);
                  }),
                ),
                Visibility(
                    visible:
                        detailData?.optionalLicenceList?.isNotEmpty ?? false,
                    child: buildListHeaderWidget("非必填项：", Color(0xfff717173))),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: List.generate(
                      detailData?.optionalLicenceList?.length ?? 0, (index) {
                    return buildListItem(
                        detailData?.optionalLicenceList?.elementAt(index),
                        false);
                  }),
                )
              ],
            ),
          ),
          Visibility(
            visible: isEdit == 0 || isEdit == 2,
            child: GestureDetector(
              onTap: () {
                requestHaveType();
              },
              child: Container(
                decoration: BoxDecoration(
                    color: Color(0xff35C561),
                    borderRadius: BorderRadius.circular(5),
                    border: Border.all(color: Color(0xffd3d3d3), width: 1)),
                margin: EdgeInsets.all(10),
                alignment: Alignment.center,
                height: 45,
                child: Text(
                  "编辑信息",
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      fontWeight: FontWeight.normal),
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  PreferredSizeWidget getTitleBar(BuildContext context) {
    return CommonTitleBar(
      getTitleName(),
      rightButtons: [
        GestureDetector(
          behavior: HitTestBehavior.opaque,
          onTap: () {
            Navigator.of(context).pushNamed("/licence_examine_log", arguments: {
              "licenseAuditId": widget.licenseAuditId,
              "type": widget.type
            });
          },
          child: Container(
            height: 44,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(horizontal: 15),
            child: Text(
              "审批日志",
              style: TextStyle(
                  color: Color(0xff35C561),
                  fontSize: 14,
                  fontWeight: FontWeight.normal),
            ),
          ),
        )
      ],
    );
  }

  @override
  String getTitleName() {
    return "资质详情";
  }

  Widget buildListItem(LicenseDetailItem? itemData, bool isNecessary) {
    if (itemData == null) {
      return Container();
    }
    return Column(
      children: [
        Divider(
          height: 1,
          color: Color(0xffE1E1E5),
        ),
        Container(
          padding: EdgeInsets.only(left: 15, right: 10),
          color: Colors.white,
          height: 90,
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                width: 144,
                child: Row(
                  children: [
                    Visibility(
                        visible: isNecessary,
                        child: Image.asset(
                          "assets/images/licence/icon_need_checked.png",
                          width: 7,
                          height: 6,
                        )),
                    Expanded(
                      child: Container(
                        alignment: Alignment.centerLeft,
                        child: Text(
                          itemData.licenseName ?? "-",
                          style: TextStyle(
                              color: Color(0xff333333),
                              fontSize: 14,
                              fontWeight: FontWeight.normal),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                color: Color(0xffE1E1E5),
                width: 1,
                child: Container(),
              ),
              Expanded(
                child: Container(
                  child: buildListItemRight(itemData),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget buildCommonHeaderWidget() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.all(15),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(
                  text: detailData?.licenseAudit?.applicationNumber));
              XYYContainer.toastChannel.toast("单据编号复制成功");
            },
            child: buildHeaderRow(
                "单据编号:",
                detailData?.licenseAudit?.applicationNumber ?? "-",
                Color(0xFF333333)),
          ),
          SizedBox(
            height: 3,
          ),
          buildHeaderRow(
              "状        态:",
              detailData?.licenseAudit?.auditStatusName ?? "-",
              Color(0xffFE3D3D)),
          SizedBox(
            height: 3,
          ),
          buildHeaderRow("单据类型:", detailData?.licenseAudit?.typeName ?? "-",
              Color(0xFF333333)),
          SizedBox(
            height: 3,
          ),
          buildHeaderRow("审批机构:", detailData?.licenseAudit?.oaBranchName ?? "-",
              Color(0xFF333333))
        ],
      ),
    );
  }

  Widget buildHeaderRow(String label, String content, Color contentColor) {
    return Row(
      children: [
        Text(
          label,
          overflow: TextOverflow.ellipsis,
          maxLines: 1,
          style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.normal,
              color: Color(0xff333333)),
        ),
        SizedBox(
          width: 12,
        ),
        Expanded(
            child: Text(content,
                overflow: TextOverflow.ellipsis,
                maxLines: 1,
                style: TextStyle(
                    color: contentColor,
                    fontWeight: FontWeight.normal,
                    fontSize: 14)))
      ],
    );
  }

  Widget buildListHeaderWidget(String label, Color labelColor) {
    return Container(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            alignment: Alignment.centerLeft,
            height: 40,
            padding: EdgeInsets.only(left: 15),
            child: Text(
              label,
              style: TextStyle(
                  color: labelColor,
                  fontSize: 13,
                  fontWeight: FontWeight.normal),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 15),
            height: 40,
            color: Colors.white,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 144,
                  child: Text("资质",
                      style: TextStyle(
                          fontSize: 15,
                          color: Color(0xff333333),
                          fontWeight: FontWeight.normal)),
                  alignment: Alignment.centerLeft,
                ),
                Container(
                  width: 1,
                  color: Color(0xFFE4E5EA),
                  child: Container(),
                ),
                Expanded(
                  child: Container(
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.only(left: 12),
                    child: Text(
                      "上传照片",
                      style: TextStyle(
                          fontSize: 15,
                          color: Color(0xff333333),
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget buildListItemRight(LicenseDetailItem itemData) {
    if (itemData.enclosureList == null ||
        itemData.enclosureList!.isEmpty) {
      return Container();
    }
    var imageInfos = parseImageInfo(itemData.enclosureList);
    return Container(
        child: ListView.builder(
      scrollDirection: Axis.horizontal,
      itemCount: imageInfos.length,
      itemBuilder: (context, index) {
        var imageInfo = imageInfos[index];
        return GestureDetector(
          child: Container(
            padding: EdgeInsets.fromLTRB(10, 15, 0, 15),
            child: ExtendedImage.network(
              imageInfo.url ?? "",
              width: 60,
              height: 60,
              fit: BoxFit.cover,
              cache: true,
            ),
            // child: ImageCatchWidget(
            //   url: imageInfo.url,
            //   w: 60,
            //   h: 60,
            //   fit: BoxFit.cover,
            // ),
          ),
          onTap: () {
            viewPhoto(imageInfo.url);
          },
        );
      },
    ));
  }

  List<RightImageInfo> parseImageInfo(List<String>? enclosureList) {
    var list = <RightImageInfo>[];
    if (enclosureList != null && enclosureList.isNotEmpty) {
      enclosureList.forEach((element) {
        if (element.isNotEmpty) {
          try {
            var rightImageInfo = RightImageInfo();
            Map<String, dynamic> decode = json.decode(element);
            // rightImageInfo.enclosureName = decode["enclosureName"];
            rightImageInfo.url = decode["url"];
            list.add(rightImageInfo);
          } catch (e) {
            print(e);
          }
        }
      });
    }
    return list;
  }

  void requestLicenseDetail() {
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    NetworkV2<LicenseDetailData>(LicenseDetailData()).requestDataV2(
        "licenseOptimize/queryLicenseAuditDetail",
        method: RequestMethod.GET,
        contentType: RequestContentType.FORM,
        parameters: {
          "licenseAuditId": widget.licenseAuditId,
          "type": widget.type
        }).then((value) {
      EasyLoading.dismiss();
      if (mounted) {
        if (value.isSuccess==true) {
          setPageState(PageState.Normal);
          setState(() {
            detailData = value.getData();
          });
        } else {
          setPageState(PageState.Error, errorClick: () {
            requestLicenseDetail();
          });
        }
      }
    });
  }

  void requestHaveType() {
    if (detailData?.licenseAudit?.merchantId == null) {
      return;
    }
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    NetworkV2<LicenseHaveType>(LicenseHaveType()).requestDataV2(
        "invoice/isHaveType",
        method: RequestMethod.GET,
        parameters: {
          "merchantId": detailData?.licenseAudit?.merchantId,
        }).then((value) {
      EasyLoading.dismiss();
      if (mounted) {
        if (value.isSuccess==true) {
          if (value.getData()!.exit == 0) {
            // 提示发票信息
            showReceiptDialog(
                "设置发票类型", "此客户还未设置发票类型，需先设置发票类型再提交资质信息", "是否前往设置？", () {
              // 跳转发票设置
              XYYContainer.open(
                  "xyy://crm-app.ybm100.com/drugstore/detail/item?merchantId=${detailData?.licenseAudit?.merchantId}&position=${4}");
            });
          } else {
            // 请求资质初始化数据
            requestLicenseAuditDetail();
          }
        }
      }
    });
  }

  ///预览图片
  void viewPhoto(String? url) {
    Navigator.of(context).pushNamed("/photo_view_page",
        arguments: {"urlPath": url, "delete": false, "callback": () {}});
  }

  /// 跳转资质编辑
  void jumpEditPage(LicenseInitData data) {
    // 1是首营，2是变更
    String isAddType = (detailData?.licenseAudit?.type == 1) ? "true" : "false";
    Navigator.of(context).pushNamed('/licence_edit_base_page', arguments: {
      "qualificationNo": data.customer!.qualificationNo,
      "mIsAddType": isAddType,
      "merchantId": detailData?.licenseAudit?.merchantId.toString(),
      "type": detailData?.licenseAudit?.type?.toString(),
      "ecOrgCode": detailData?.licenseAudit?.ecOrgCode,
      "isDraft": "true",
      "from": "",
      "licenseInitDataJson": json.encode(data)
    }).then((value) {
      print("guan :$value");
      if (value != null) {
        requestLicenseDetail();
      }
    });
  }

  void showReceiptDialog(
      String title, String content, String tips, VoidCallback onConfirm) {
    showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: Container(
              margin: EdgeInsets.all(40),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(7), color: Colors.white),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                      margin: EdgeInsets.fromLTRB(25, 25, 25, 0),
                      child: Text(
                        title,
                        style: TextStyle(
                            color: Color(0xff333333),
                            fontSize: 17,
                            fontWeight: FontWeight.normal),
                      )),
                  Container(
                    padding: EdgeInsets.fromLTRB(25, 4, 25, 0),
                    child: Text(
                      content,
                      style: TextStyle(
                          fontSize: 14,
                          color: Color(0xff666666),
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.fromLTRB(0, 10, 0, 18),
                    padding: EdgeInsets.symmetric(horizontal: 25),
                    child: Text(
                      tips,
                      style: TextStyle(
                          fontSize: 14,
                          color: Color(0xff666666),
                          fontWeight: FontWeight.normal),
                    ),
                  ),
                  Divider(
                    color: Color(0xffe1e1e5),
                    height: 0.5,
                  ),
                  Container(
                    height: 50,
                    child: Row(
                      children: [
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            height: 50,
                            alignment: Alignment.center,
                            child: Text("取消",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff999999),
                                    fontWeight: FontWeight.normal)),
                          ),
                        )),
                        Container(
                          color: Color(0xffe1e1e5),
                          width: 0.5,
                          child: Container(),
                        ),
                        Expanded(
                            child: GestureDetector(
                          behavior: HitTestBehavior.opaque,
                          onTap: () {
                            Navigator.of(context).pop();
                            onConfirm();
                          },
                          child: Container(
                            height: 50,
                            alignment: Alignment.center,
                            child: Text("确认",
                                style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xff35c561),
                                    fontWeight: FontWeight.normal)),
                          ),
                        )),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        });
  }

  void requestLicenseAuditDetail() {
    if (detailData?.licenseAudit?.merchantId == null) {
      return;
    }
    EasyLoading.show(status: "加载中...", maskType: EasyLoadingMaskType.clear);
    NetworkV2<LicenseInitData>(LicenseInitData()).requestDataV2(
        "licenseOptimize/initLicenseAuditDetail",
        method: RequestMethod.POST,
        contentType: RequestContentType.FORM,
        parameters: {
          "merchantId": detailData?.licenseAudit?.merchantId,
          "applicationNumber": detailData?.licenseAudit?.applicationNumber,
          "type": detailData?.licenseAudit?.type,
          "ecOrgCode": detailData?.licenseAudit?.ecOrgCode
        }).then((value) {
      EasyLoading.dismiss();
      if (mounted) {
        if (value.isSuccess==true && value.data != null) {
          jumpEditPage(value.getData()!);
        } else {
          showToast("资质状态获取失败，请先重新获取资质状态");
        }
      }
    });
  }
}

class RightImageInfo {
  String? enclosureName;
  String? url;
}
