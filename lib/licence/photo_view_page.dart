import 'dart:io';
import 'dart:ui' as ui;
import 'dart:math' as math;

import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/utils/user/user_info_util.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class PhotoViewPage extends BasePage {
  final String? urlPath;
  final bool isWaterMark;
  final bool delete;
  final VoidCallback? callback;

  PhotoViewPage(this.urlPath,
      {this.delete = false, this.callback, this.isWaterMark = true});

  @override
  BaseState<StatefulWidget> initState() {
    return PhotoViewPageState();
  }
}

class PhotoViewPageState extends BaseState<PhotoViewPage> {
  String waterMarkStr = "";

  GlobalKey imageKey = GlobalKey();

  ui.Image? loadedImage;

  @override
  Widget buildWidget(BuildContext context) {
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          child:CustomPaint(
            child: GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: widget.urlPath == null || widget.urlPath!.isEmpty
                  ? Container()
                  : widget.urlPath!.startsWith("http") ||
                          widget.urlPath!.startsWith("Http")
                      ? ExtendedImage.network(widget.urlPath ?? "",
                          key: imageKey,
                          mode: ExtendedImageMode.gesture,
                          cache: true,
                          width: MediaQuery.of(context).size.width,
                          enableSlideOutPage: true,
                          fit: BoxFit.contain,
                          afterPaintImage: (canvas, rect, image, paint) {
                          loadedImage = image;
                          paintWaterMarkImage(
                              canvas,
                              MediaQuery.of(context).size.width,
                              MediaQuery.of(context).size.height);
                        })
                      : ExtendedImage.asset(widget.urlPath ?? "",
                          key: imageKey,
                          mode: ExtendedImageMode.gesture,
                          width: MediaQuery.of(context).size.width,
                          fit: BoxFit.contain,
                          height: MediaQuery.of(context).size.height,
                          afterPaintImage: (canvas, rect, image, paint) {
                          loadedImage = image;
                          paintWaterMarkImage(
                              canvas,
                              MediaQuery.of(context).size.width,
                              MediaQuery.of(context).size.height);
                        }),
              onTap: () {
                Navigator.of(context).pop();
              },
            ),
          ),
        ),
        Positioned(
          bottom: 0,
          child: Visibility(
            visible: widget.delete,
            child: GestureDetector(
                child: Container(
                  alignment: Alignment.center,
                  width: MediaQuery.of(context).size.width,
                  height: 40,
                  color: Color(0x77000000),
                  child: Text("删除照片",
                      style: TextStyle(fontSize: 14, color: Colors.white)),
                ),
                onTap: () {
                  if (widget.callback != null) {
                    widget.callback!();
                  }
                }),
          ),
        )
      ],
    );
  }

  void paintWaterMarkImage(Canvas canvas, double width, double height) {
    if (waterMarkStr.isEmpty) {
      return;
    }
    print("guan w:${width},h:${height}");
    // 计算四边形的对角线长度（宽度）
    double dimensionWidth = math.sqrt(math.pow(width, 2) + math.pow(height, 2));
    var fontSize = 30;
    // 完整覆盖下的矩形高度
    var dimensionHeight = ((width * height) / dimensionWidth) * 2;
    // 完整覆盖下的矩形面积
    var rectSize = dimensionHeight * dimensionWidth;


    var titleTextStyle = TextStyle(
        fontSize: fontSize.toDouble(),
        fontWeight: FontWeight.w900,
        color: Color.fromRGBO(0, 0, 0, .2),
        height: 8);
    var initTotalLength =
        boundingTextSize(waterMarkStr, titleTextStyle).width.toInt() + 30;

    // 根据面积与字符大小计算文本重复次数
    int textRepeating =
    (rectSize / (fontSize * initTotalLength*8))
        .round(); // text.length + padding 是因为要添加个空格字符

    print("guan textRepeating:${textRepeating.toString()}");

    math.Point pivotPoint = math.Point(dimensionWidth / 2, dimensionHeight / 2);
    canvas.save();
    canvas.translate(pivotPoint.x.toDouble(), pivotPoint.y.toDouble());
    canvas.rotate(-25 * math.pi / 180);
    canvas.translate(
        -pivotPoint.distanceTo(math.Point(0, height)),
        -pivotPoint.distanceTo(
            math.Point(0, 0))); // 计算文本区域起始坐标分别到图片左侧顶部与底部的距离，作为文本区域移动的距离。

    var titleTextPainter = TextPainter(
      text: TextSpan(
          text:
              getFixedWidthText(initTotalLength, waterMarkStr, titleTextStyle) *
                  textRepeating,
          style: titleTextStyle),
      maxLines: null,
      textDirection: ui.TextDirection.ltr,
      textAlign: TextAlign.center,
    );
    titleTextPainter.layout(maxWidth: dimensionWidth);
    titleTextPainter.paint(canvas, Offset.zero);

    canvas.restore();
  }

  Size boundingTextSize(String? text, TextStyle style) {
    if (text == null || text.isEmpty) {
      return Size.zero;
    }
    final TextPainter textPainter = TextPainter(
        textDirection: TextDirection.ltr,
        text: TextSpan(text: text, style: style),
        maxLines: 1)
      ..layout(maxWidth: double.infinity);
    return textPainter.size;
  }

  String getFixedWidthText(int totalLength, String keyWord, TextStyle style) {
    return keyWord
        .padLeft(totalLength - boundingTextSize(keyWord, style).width.toInt());
  }


  @override
  PreferredSizeWidget? getTitleBar(BuildContext context) {
    return null;
  }

  @override
  String getTitleName() {
    return "";
  }

  @override
  void onCreate() {
    super.onCreate();
    if (widget.isWaterMark) {
      UserInfoUtil.getUserInfo().then((value) {
        waterMarkStr = "药帮忙平台专用${value?.sysUserId ?? "--"}";
        setState(() {});
      });
    }
  }

  String getJobStr(int? roleType) {
    var jobStrMap = {
      UserInfoUtil.TYPE_BD: "BD",
      UserInfoUtil.TYPE_BDM: "BDM",
      UserInfoUtil.TYPE_GJR: "跟进人",
      UserInfoUtil.TYPE_GJR_BDM: "跟进人BDM"
    };
    return jobStrMap[roleType] ?? "--";
  }
}
