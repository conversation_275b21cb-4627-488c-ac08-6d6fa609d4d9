import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

void showCustomPickerView(
  BuildContext context, {
  required String title,
  VoidCallback? cancelOnPressed,
  required CustomPickerCallBack surePressed,
  required List<String?> values,
  String? selectValue,
}) {
  showModalBottomSheet(
    context: context,
    backgroundColor: Colors.transparent,
    barrierColor: Color(0x80000000),
    builder: (BuildContext context) {
      return Container(
        child: CustomPickerView(
          title: title,
          surePressed: (index, value) {
            Navigator.pop(context);
            surePressed(index, value);
          },
          cancelOnPressed: () {
            Navigator.pop(context);
            if (cancelOnPressed != null) {
              cancelOnPressed();
            }
          },
          values: values,
          selectValue: selectValue,
        ),
      );
    },
  );
}

typedef CustomPickerCallBack = void Function(int index, String? value);

class CustomPickerView extends StatefulWidget {
  final String title;
  final VoidCallback? cancelOnPressed;
  final CustomPickerCallBack surePressed;
  final List<String?> values;
  final String? selectValue;

  CustomPickerView({
    Key? key,
    required this.title,
    required this.surePressed,
    required this.values,
    this.selectValue,
    this.cancelOnPressed,
  }) : super(key: key);

  @override
  _CustomPickerState createState() {
    return _CustomPickerState();
  }
}

class _CustomPickerState extends State<CustomPickerView> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final MediaQueryData data = MediaQuery.of(context);
    double safeBottom = data.padding.bottom;
    // Bottom padding has been consumed - i.e. by the keyboard
    if (data.padding.bottom == 0.0 && data.viewInsets.bottom != 0.0)
      safeBottom = data.viewPadding.bottom;

    if (widget.selectValue != null) {
      _currentIndex = widget.values.indexOf(widget.selectValue);
    }

    return Container(
      height: 266 + safeBottom,
      padding: EdgeInsets.fromLTRB(0, 0, 0, safeBottom),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
      ),
      child: Column(
        children: [
          _PickerTopBar(
            title: widget.title,
            barCancelPressed: () {
              // 取消方法
              widget.cancelOnPressed!();
              Navigator.pop(context);
            },
            barDeterminePressed: () {
              // 确认方法
              widget.surePressed(
                this._currentIndex,
                widget.values[this._currentIndex],
              );
            },
          ),
          Container(
            height: 216,
            child: CupertinoPicker(
              itemExtent: 40,
              backgroundColor: Colors.white,
              // 设置默认选中项
              scrollController: FixedExtentScrollController(
                  initialItem: widget.values.indexOf(widget.selectValue)),
              onSelectedItemChanged: (value) {
                this._currentIndex = value;
              },
              children: widget.values.map((itemTitle) {
                return Padding(
                    padding: EdgeInsets.fromLTRB(15, 0, 15, 0),
                    child: Center(
                      child: Text(
                        itemTitle!,
                        style: TextStyle(
                          fontSize: 17,
                          color: Color(0xFF292933),
                        ),
                        textAlign: TextAlign.center,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ));
              }).toList(),
            ),
          )
        ],
      ),
    );
  }
}

class _PickerTopBar extends StatelessWidget {
  final String title;
  final VoidCallback barCancelPressed;
  final VoidCallback barDeterminePressed;

  _PickerTopBar({
    Key? key,
    required this.title,
    required this.barCancelPressed,
    required this.barDeterminePressed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          TextButton(
            style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                minimumSize: Size.fromWidth(62)),
            onPressed: this.barCancelPressed,
            child: Text(
              '取消',
              style: TextStyle(
                inherit: false,
                fontSize: 16,
                color: Color(0xFF292933),
              ),
            ),
          ),
          Text(
            this.title,
            style: TextStyle(
              inherit: false,
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF292933),
            ),
          ),
          TextButton(
            style: TextButton.styleFrom(
                padding: EdgeInsets.zero,
                tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                minimumSize: Size.fromWidth(62)),
            onPressed: this.barDeterminePressed,
            child: Text(
              '确定',
              style: TextStyle(
                  inherit: false,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color: Color(0xFF35c561)),
            ),
          ),
        ],
      ),
    );
  }
}
