import 'package:XyyBeanSproutsFlutter/licence/bean/licence_examine_log_list_bean.dart';
import 'package:XyyBeanSproutsFlutter/utils/time/time_utils.dart';
import 'package:date_format/date_format.dart';
import 'package:flutter/material.dart';
import 'package:collection/collection.dart';

class LicenseDetailExamineLogWidget extends StatelessWidget {
  final List<LicenceExamineLogListBean>? examineLogList;

  LicenseDetailExamineLogWidget(this.examineLogList);

  @override
  Widget build(BuildContext context) {
    List<Widget> widgetList = [];
    widgetList.add(Text("审批日志",
        style: TextStyle(
            color: const Color(0xff292933),
            fontSize: 14,
            fontWeight: FontWeight.w500)));
    widgetList.add(SizedBox(
      height: 15,
    ));

    examineLogList?.forEachIndexed((index, element) {
      widgetList
          .add(buildLogItemWidget(index >= examineLogList!.length - 1, element));
    });

    return Container(
      margin: EdgeInsets.only(top: 10, left: 10, right: 10, bottom: 10),
      padding: EdgeInsets.only(left: 12, right: 12, top: 15, bottom: 15),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: widgetList,
      ),
    );
  }

  Widget buildLogItemWidget(bool isLast, LicenceExamineLogListBean examineLog) {
    return Container(
      padding: EdgeInsets.only(left: 4, right: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          buildStepWidget(isLast),
          SizedBox(
            width: 10,
          ),
          Expanded(child: buildLogContentWidget(examineLog))
        ],
      ),
    );
  }

  Widget buildStepWidget(bool isLast) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          height: 21,
          alignment: Alignment.center,
          child: Image.asset(
            "assets/images/licence/icon_examine_log_step.png",
            width: 9,
            height: 9,
          ),
        ),
        SizedBox(
          height: 1.5,
        ),
        Visibility(
          visible: !isLast,
          child: Container(
            height: 80,
            width: 0.5,
            color: const Color(0xff00b377),
          ),
        )
      ],
    );
  }

  Widget buildLogContentWidget(LicenceExamineLogListBean examineLog) {
    var commonStyle = TextStyle(
        fontSize: 13,
        fontWeight: FontWeight.normal,
        color: const Color(0xff292933));
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 21,
          alignment: Alignment.centerLeft,
          child: Text(
            examineLog.typeName ?? "--",
            style: commonStyle,
          ),
        ),
        SizedBox(
          height: 6.5,
        ),
        Text(
          "审核时间：${TimeUtils().formatTime(examineLog.createTime ?? 0)}",
          style: commonStyle,
        ),
        SizedBox(
          height: 10,
        ),
        Row(
          children: [
            Text(
              "操作人：${examineLog.creator ?? "--"}     状态：",
              style: commonStyle,
            ),
            Text(
              examineLog.statusName ?? "—-",
              style: TextStyle(
                  fontSize: 13,
                  fontWeight: FontWeight.normal,
                  color: examineLog.statusName == "审核通过"
                      ? const Color(0xFF35C561)
                      : const Color(0xFFFE3D3D)),
            ),
          ],
        ),
        SizedBox(
          height: 10,
        ),
        Text(
          "${examineLog.status == 0 ? "驳回原因" : "备注"}：${examineLog.remark ?? "--"}",
          style: commonStyle,
        ),
        SizedBox(height: 12.5,)
      ],
    );
  }
}
