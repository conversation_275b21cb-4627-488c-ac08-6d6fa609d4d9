import 'package:XyyBeanSproutsFlutter/licence/bean/licence_filter_bean.dart';
import 'package:flutter/material.dart';

import '../licence_manager_page.dart';
import 'pop_menus.dart';
import 'tab_custom_indicator.dart';

/// 资质管理自定义tab
// ignore: must_be_immutable
class LicenceTabBar extends StatefulWidget implements PreferredSizeWidget {
  TabController? _tabController;
  BuildContext _context;
  ValueChanged<int>? onTap;
  StatusModel? _statusModel;
  @override
  final Size preferredSize;

  @override
  State<StatefulWidget> createState() {
    return TabBarStates(_tabController, _statusModel, _context, onTap: onTap);
  }

  LicenceTabBar(
      this._tabController, this._statusModel, this.preferredSize, this._context,
      {this.onTap});
}

class TabBarStates extends State<LicenceTabBar> {
  TabController? _tabController;
  StatusModel? _statusModel;
  BuildContext _context;
  ValueChanged<int>? onTap;
  int type = 0; //0、1、2 时间、状态、类型

  TabBarStates(this._tabController, this._statusModel, this._context,
      {this.onTap});

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      children: [
        Divider(
          height: 1,
          color: Color(0xFFE1E1E5),
        ),
        tabBar(),
        Divider(
          height: 1,
          color: Color(0xFFE1E1E5),
        ),
        filter()
      ],
    ));
  }

  //tab
  Widget tabBar() {
    return Row(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: PreferredSize(
            child: Stack(
              children: [
                TabBar(
                  controller: _tabController,
                  isScrollable: false,
                  labelColor: Color(0xFF292933),
                  indicator: TabCustomIndicator(wantWidth: 16),
                  labelStyle:
                      TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
                  unselectedLabelColor: Color(0xFF575766),
                  unselectedLabelStyle:
                      TextStyle(fontSize: 15, fontWeight: FontWeight.normal),
                  tabs: _statusModel!.tabs
                      .map(
                        (e) => Padding(
                          padding: EdgeInsets.fromLTRB(0, 10, 0, 6),
                          child: Text(
                            e.label!,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      )
                      .toList(),
                  onTap: onTap,
                )
              ],
            ),
            preferredSize: Size.fromHeight(30),
          ),
        )
      ],
    );
  }

  //顶部筛选
  Widget filter() {
    return Container(
        height: 40,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Expanded(
                child: filterBottom(_statusModel!.timeStr!,
                    _statusModel!.dateType != 0, getTimeData(), 130, 16, 0)),
            Expanded(
                child: filterBottom(
                    _statusModel!.statusStr!,
                    _statusModel!.statusType != -1,
                    _statusModel!.statueList,
                    130,
                    MediaQuery.of(_context).size.width / 2 - 42,
                    1)),
            Expanded(
                child: filterBottom(
                    _statusModel!.typeStr!,
                    _statusModel!.licenseType != 0,
                    getTypeData(),
                    130,
                    MediaQuery.of(_context).size.width - 100,
                    2))
          ],
        ));
  }

  List<LicenceFilterBean> getTimeData() {
    List<String> strList = ["全部", "今天", "本周", "本月", "上周", "上月"];
    List<int> codeList = [0, 1, 5, 2, 3, 4];
    List<LicenceFilterBean> list = [];
    for (int i = 0; i < strList.length; i++) {
      LicenceFilterBean filterBean = LicenceFilterBean();
      filterBean.name = strList[i];
      filterBean.code = codeList[i];
      list.add(filterBean);
    }
    return list;
  }

  List<LicenceFilterBean> getTypeData() {
    List<String> strList = ["全部", "首营资质", "资质变更"];
    List<int> codeList = [0, 1, 2];
    List<LicenceFilterBean> list = [];
    for (int i = 0; i < strList.length; i++) {
      LicenceFilterBean filterBean = LicenceFilterBean();
      filterBean.name = strList[i];
      filterBean.code = codeList[i];
      list.add(filterBean);
    }
    return list;
  }

  //tab下面的筛选
  Widget filterBottom(String text, bool select,
      List<LicenceFilterBean>? listData, double topP, double leftP, int type) {
    return GestureDetector(
      child: Container(
        margin: EdgeInsets.only(left: 15, right: 15, top: 6, bottom: 6),
        height: 27,
        constraints: BoxConstraints(minWidth: 20),
        decoration: BoxDecoration(
            color: select ? Color(0xFFFFFFFF) : Color(0xFFF0F0F0),
            borderRadius: BorderRadius.all(Radius.circular(13)),
            border: Border.all(
                width: 1,
                color: select ? Color(0xFF35C561) : Color(0xFFF0F0F0))),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Text(
              text,
              style: TextStyle(
                  color: select ? Color(0xFF35C561) : Color(0xFF666666),
                  fontSize: 12.0),
            ),
            SizedBox(width: 3),
            Image.asset(
                select
                    ? 'assets/images/licence/icon_up_green.png'
                    : 'assets/images/licence/icon_down_gray.png',
                width: 6,
                height: 4)
          ],
        ),
      ),
      onTap: () {
        showPop(listData, topP, leftP, type);
      },
    );
  }

  void showPop(
      List<LicenceFilterBean>? listData, double topP, double leftP, int type) {
    PopMenus.showPop(
        context: _context,
        listData: listData,
        selText: type == 0
            ? _statusModel!.timeStr
            : type == 1
                ? _statusModel!.statusStr
                : type == 2
                    ? _statusModel!.typeStr
                    : "",
        leftP: leftP,
        topP: topP,
        clickCallback: (int index) {
          setState(() {
            switch (type) {
              case 0:
                if (index != 0) {
                  _statusModel!.dateType = listData![index].code;
                  _statusModel!.timeStr = listData[index].name;
                } else {
                  _statusModel!.timeStr = "创建时间";
                  _statusModel!.dateType = listData![index].code;
                }
                break;
              case 1:
                if (index != 0) {
                  _statusModel!.statusType = listData![index].code;
                  _statusModel!.statusStr = listData[index].name;
                } else {
                  _statusModel!.statusType = listData![index].code;
                  _statusModel!.statusStr = "单据状态";
                }
                break;
              case 2:
                if (index != 0) {
                  _statusModel!.licenseType = listData![index].code;
                  _statusModel!.typeStr = listData[index].name;
                } else {
                  _statusModel!.licenseType = listData![index].code;
                  _statusModel!.typeStr = "资质类型";
                }
                break;
            }
          });
          _statusModel!.refreshPage(
              _statusModel!.dateType,
              _statusModel!.statusType,
              _statusModel!.code,
              _statusModel!.licenseType,
              false);
        });
  }
}
