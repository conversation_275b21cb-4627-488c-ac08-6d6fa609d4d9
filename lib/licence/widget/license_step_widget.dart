import 'package:XyyBeanSproutsFlutter/licence/license_edit_base_page.dart';
import 'package:flutter/material.dart';

class LicenseStepWidget extends StatelessWidget {
  final EditStep step;

  LicenseStepWidget(this.step);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(top: 15, bottom: 25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: const Color(0xff00B377),
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                ),
              ),
              Container(
                  height: 2,
                  width: MediaQuery.of(context).size.width / 3,
                  color: step.index > 0
                      ? const Color(0xff00B377)
                      : const Color(0xffdddddd)),
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: step.index > 0
                      ? const Color(0xff00B377)
                      : const Color(0xffdddddd),
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                ),
              ),
              Container(
                  height: 2,
                  width: MediaQuery.of(context).size.width / 3,
                  color: step.index > 1
                      ? const Color(0xff00B377)
                      : const Color(0xffdddddd)),
              Container(
                width: 10,
                height: 10,
                decoration: BoxDecoration(
                  color: step.index > 1
                      ? const Color(0xff00B377)
                      : const Color(0xffdddddd),
                  borderRadius: BorderRadius.all(Radius.circular(5)),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 12,
          ),
          Row(
            mainAxisSize: MainAxisSize.max,
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                  child: Container(
                alignment: Alignment.center,
                child: Text(
                  "信息认证",
                  style: TextStyle(fontSize: 14, color: Color(0xFF292933)),
                ),
              )),
              Expanded(
                  child: Container(
                alignment: Alignment.center,
                child: Text(
                  "信息确认",
                  style: TextStyle(
                      fontSize: 14,
                      color: step.index > 0
                          ? Color(0xFF282833)
                          : Color(0xFF9494A6)),
                ),
              )),
              Expanded(
                  child: Container(
                alignment: Alignment.center,
                child: Text(
                  "上传资质照片",
                  style: TextStyle(
                      fontSize: 14,
                      color: step.index > 1
                          ? Color(0xFF282833)
                          : Color(0xFF9494A6)),
                ),
              ))
            ],
          )
        ],
      ),
    );
  }
}
