import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/message_dialog.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/customer_type_bean.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_base_info_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/custom_picker_view.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_edit_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class LicenseBaseInfoWidget extends StatefulWidget {
  final bool isAddType; // 资质类型：1.添加首营、2.资质变更
  final int? customerTypeCode; // 客户类型
  final String? qualificationNo; // 证书编号
  final BaseInfoCallback callback; // 点击下一步回调

  final String? from;
  final String? merchantId;
  final String? ecOrgCode;
  final String? applicationNumber;
  final String? type;

  LicenseBaseInfoWidget(
      this.isAddType,
      this.customerTypeCode,
      this.qualificationNo,
      this.from,
      this.merchantId,
      this.ecOrgCode,
      this.applicationNumber,
      this.type,
      this.callback);

  @override
  State<StatefulWidget> createState() {
    return LicenseBaseInfoState();
  }
}

class LicenseBaseInfoState extends State<LicenseBaseInfoWidget> {
  late int? customerTypeCode;
  String? customerTypeName = "";
  late String qualificationNo;

  List<CustomerTypeBean>? customerTypeList;

  FocusNode focusNode = new FocusNode();

  @override
  void initState() {
    super.initState();
    customerTypeCode = widget.customerTypeCode;
    qualificationNo = widget.qualificationNo ?? "";
    getCustomerType();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(left: 10, right: 10),
        child: Column(
          children: [
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Text(
                    "企业信息",
                    style: TextStyle(
                        fontSize: 15,
                        color: const Color(0xff292933),
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 15),
                  LicenseEditItemWidget(
                    "企业类型",
                    customerTypeName ?? "",
                    ItemAction.Click,
                    showPointer: true,
                    clickCallback: () {
                      handleCustomerTypeClick();
                    },
                  ),
                  Divider(height: 1, color: const Color(0xfff5f5f5)),
                  LicenseEditItemWidget(
                    getQualificationLabel(),
                    qualificationNo,
                    ItemAction.Edit,
                    showPointer: true,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp("[-a-zA-Z0-9]*"))
                    ],
                    hintText: "请输入",
                    showArrow: false,
                    focusNode: focusNode,
                    editCallback: (text) {
                      qualificationNo = text;
                    },
                  ),
                  Divider(height: 1, color: const Color(0xfff5f5f5)),
                  SizedBox(height: 10),
                  Text(
                    "提示：为保证结果准确性，请保证企业类型和证照编码与实际相符",
                    style: TextStyle(
                        fontSize: 12,
                        color: const Color(0xFF9494A6),
                        fontWeight: FontWeight.normal),
                  ),
                ],
              ),
            ),
            Expanded(child: Container()),
            GestureDetector(
              behavior: HitTestBehavior.opaque,
              child: Container(
                  color: Colors.white,
                  child: Container(
                      margin: EdgeInsets.fromLTRB(10, 15, 10, 15),
                      width: double.infinity,
                      height: 44,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(2)),
                          color: Color(0xFF00B377)),
                      child: Text(
                        "信息认证",
                        style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.bold),
                      ))),
              onTap: () {
                if (customerTypeCode == -1 ||
                    customerTypeCode == null ||
                    customerTypeName == null ||
                    customerTypeName!.isEmpty) {
                  XYYContainer.toastChannel.toast("请选择企业类型");
                } else if (qualificationNo.isEmpty) {
                  XYYContainer.toastChannel
                      .toast("请输入${getQualificationLabel()}");
                } else {
                  getBaseInfo();
                }
              },
            )
          ],
        ),
      ),
    );
  }

  ///获取选中的企业类型name
  void getCustomerType() async {
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    Network<CustomerTypeBean>(CustomerTypeBean())
        .requestListData("ui/common/getAllCustomerTypes",
            method: RequestMethod.GET)
        .then((value) {
      EasyLoading.dismiss();
      if (value.isSuccess && mounted) {
        customerTypeList = value.data;
        if (customerTypeList != null && customerTypeList!.isNotEmpty) {
          if (customerTypeCode == null || customerTypeCode == -1) {
            customerTypeCode = customerTypeList!.first.type;
            customerTypeName = customerTypeList!.first.text ?? "";
          } else {
            customerTypeList!.forEach((element) {
              if (element.type == customerTypeCode) {
                customerTypeName = element.text ?? "";
              }
            });
          }
        }
        setState(() {});
      }
    });
  }

  String getQualificationLabel() {
    /*
     * 设置证书类型文案,按如下规则展示：
     * 字段与客户类型对应关系
     *
     *   a. 当选择以下客户类型时，展示“营业执照编码”字段
     *
     *   单体药店(1)、连锁总部(21)、连锁直营(3)、连锁加盟(2)、药品批发(5)、非药类经营(19)
     *   非药类生产(17)、药品生产(16)、其他企业(20)、境外企业(18)
     *
     *   b. 当选择以下客户类型时，展示“医疗机构执业许可证编码”字段
     *
     *   诊所(4)、门诊部(14)、卫生室(10)、社区卫生服务站(9)、卫生院(8)、其它医疗机构(15)、民营医院(6)、公立医院(7)
     */
    switch (customerTypeCode) {
      case 1:
      case 21:
      case 3:
      case 2:
      case 5:
      case 19:
      case 17:
      case 16:
      case 20:
      case 18:
        return "营业执照编码";
      case 4:
      case 14:
      case 10:
      case 9:
      case 8:
      case 15:
      case 6:
      case 7:
        return "医疗机构执业许可证编码";
      default:
        return "未知执照编码";
    }
  }

  void handleCustomerTypeClick() {
    if (customerTypeList == null || customerTypeList!.length == 0) {
      XYYContainer.toastChannel.toast("数据加载中，请稍后");
      return;
    }
    showCustomPickerView(
      context,
      title: "企业类型",
      selectValue: customerTypeName,
      surePressed: (index, selectValue) {
        showMessageDialog2(
            title: "",
            message: "变更客户类型将覆盖原对应资质，\n是否替换？",
            negativeText: "取消",
            positiveText: "替换",
            callBack: () {
              if (customerTypeList != null &&
                  customerTypeList![index].type != null &&
                  customerTypeCode != customerTypeList![index].type) {
                setState(() {
                  customerTypeCode = customerTypeList![index].type!;
                  customerTypeName = selectValue!;
                  qualificationNo = "";
                });
              }
            });
      },
      values: customerTypeList!.map((e) => e.text).toList(),
    );
  }

  ///弹对话框
  void showMessageDialog2(
      {String title = "提示",
      String? message,
      String negativeText = "确定",
      String positiveText = "取消",
      Function? callBack,
      Function? cancelCallBack}) {
    if (message != null && message.isNotEmpty) {
      showDialog<Null>(
          context: context, //BuildContext对象
          barrierDismissible: false,
          builder: (BuildContext context) {
            return MessageDialog(
              title: title,
              negativeText: negativeText,
              positiveText: positiveText,
              message: message,
              onPositivePressEvent: () {
                Navigator.pop(context);
                if (callBack != null) {
                  callBack();
                }
              },
              onCloseEvent: () {
                Navigator.pop(context);
                if (cancelCallBack != null) {
                  cancelCallBack();
                }
              },
            ); //调用对话框);
          });
    }
  }

  void getBaseInfo() {
    var map = Map();
    map["merchantId"] = widget.merchantId;
    map["customerType"] = customerTypeCode;
    map["licenseCode"] = qualificationNo;
    if (widget.from != null) {
      map["from"] = widget.from;
    }
    if (widget.ecOrgCode != null) {
      map["ecOrgCode"] = widget.ecOrgCode;
      map["applicationNumber"] = widget.applicationNumber;
      map["type"] = widget.type;
    }
    EasyLoading.show(status: "加载中", maskType: EasyLoadingMaskType.clear);
    NetworkV2<LicenseBaseInfoData>(LicenseBaseInfoData())
        .requestDataV2("licenseOptimize/customerCode",
            method: RequestMethod.GET, parameters: map)
        .then((value) {
      EasyLoading.dismiss();
      if (mounted && (value.isSuccess ?? false)) {
        if (value.getData() != null) {
          widget.callback(customerTypeCode!, value.getData()!);
          if (focusNode.hasFocus) {
            focusNode.unfocus();
          }
        } else {
          XYYContainer.toastChannel.toast("请求失败！");
        }
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    focusNode.unfocus();
  }
}

typedef BaseInfoCallback = void Function(
    int customerTypeCode, LicenseBaseInfoData baseInfo);
