import 'dart:convert';
import 'dart:io';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_base_info_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_init_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/license_edit_item_widget.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

class LicenseBaseInfoConfirmWidget extends StatefulWidget {
  final String? merchantId; // 客户id
  final String? from;
  final String? type;
  final String? ecOrgCode;
  final String? applicationNumber;
  final int customerTypeCode; // 客户类型

  final LicenseBaseInfoData? baseInfoData;

  final BaseInfoCallback callback; // 点击下一步回调

  LicenseBaseInfoConfirmWidget(
      this.baseInfoData,
      this.merchantId,
      this.from,
      this.type,
      this.ecOrgCode,
      this.applicationNumber,
      this.customerTypeCode,
      this.callback);

  @override
  State<StatefulWidget> createState() {
    return LicenseBaseInfoConfirmState();
  }
}

class LicenseBaseInfoConfirmState extends State<LicenseBaseInfoConfirmWidget> {
  FocusNode focusNode = new FocusNode();

  String? detailAddress;
  String? mProvinceCode;
  String? mCityCode;
  String? mAreaCode;
  String? mStreetCode;
  String? mProvinceName;
  String? mCityName;
  String? mAreaName;
  String? mStreetName;

  @override
  void initState() {
    super.initState();
    detailAddress = widget.baseInfoData?.address;
    mProvinceCode = widget.baseInfoData?.provinceCode?.toString();
    mCityCode = widget.baseInfoData?.cityCode?.toString();
    mAreaCode = widget.baseInfoData?.areaCode?.toString();
    mStreetCode = widget.baseInfoData?.streetCode?.toString();
    mProvinceName = widget.baseInfoData?.provinceName;
    mCityName = widget.baseInfoData?.cityName;
    mAreaName = widget.baseInfoData?.areaName;
    mStreetName = widget.baseInfoData?.street;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(left: 10, right: 10),
        child: Column(
          children: [
            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                mainAxisSize: MainAxisSize.max,
                children: [
                  Text(
                    "企业信息",
                    style: TextStyle(
                        fontSize: 15,
                        color: const Color(0xff292933),
                        fontWeight: FontWeight.bold),
                  ),
                  SizedBox(height: 15),
                  Divider(height: 1, color: const Color(0xfff5f5f5)),
                  LicenseEditItemWidget(
                    "客户名称",
                    widget.baseInfoData?.customerName ?? "",
                    ItemAction.NonEdit,
                    showArrow: false,
                  ),
                  Divider(height: 1, color: const Color(0xfff5f5f5)),
                  LicenseEditItemWidget(
                    "所在区域",
                    getAreaText(),
                    ItemAction.Click,
                    clickCallback: () {
                      FocusScope.of(context).requestFocus(FocusNode());
                      handleAreaClick();
                    },
                  ),
                  Divider(height: 1, color: const Color(0xfff5f5f5)),
                  LicenseEditItemWidget(
                    "详细地址",
                    detailAddress ?? "",
                    ItemAction.Edit,
                    hintText: "请输入",
                    showArrow: false,
                    focusNode: focusNode,
                    editCallback: (text) {
                      detailAddress = text;
                    },
                  )
                ],
              ),
            ),
            Expanded(child: Container()),
            GestureDetector(
              child: Container(
                  color: Colors.white,
                  child: Container(
                      margin: EdgeInsets.fromLTRB(10, 15, 10, 15),
                      width: double.infinity,
                      height: 44,
                      alignment: Alignment.center,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(2)),
                          color: Color(0xFF00B377)),
                      child: Text(
                        "信息认证",
                        style: TextStyle(
                            fontSize: 16,
                            color: Colors.white,
                            fontWeight: FontWeight.bold),
                      ))),
              onTap: () {
                checkAddressField().then((value) {
                  if (value) {
                    initLicenseList();
                  }
                });
              },
            )
          ],
        ),
      ),
    );
  }

  Future<bool> checkAddressField() async {
    // 地址信息不全
    if (mProvinceName?.isEmpty != false ||
        mAreaName?.isEmpty != false ||
        mCityName?.isEmpty != false) {
      XYYContainer.toastChannel.toast("请补全地址信息！！");
      return false;
    }
    if (mStreetName?.isEmpty != false) {
      // 四级地址为空，需要补全
      if (widget.baseInfoData?.localCheck == null) {
        // 后端判断
        XYYContainer.toastChannel.toast("请补四级地址信息！！");
        return false;
      } else {
        // 本地校验
        return await checkStreetValid(mAreaCode);
      }
    }
    return true;
  }

  /// 请求地址信息
  Future<bool> checkStreetValid(String? areaCode) async {
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    var value = await NetworkV2<NetworkBaseModelV2>(NetworkBaseModelV2())
        .requestDataV2("ui/common/getAddressV2",
            method: RequestMethod.GET, parameters: {"areaCode": areaCode});
    EasyLoading.dismiss();
    if (mounted && value.isSuccess != null && value.isSuccess!) {
      var listData = value.getListData();
      if (listData == null) {
        XYYContainer.toastChannel.toast("校验地址信息失败！！");
      } else if (listData.isNotEmpty) {
        // 可选四级地址，但是没有选
        XYYContainer.toastChannel.toast("请补全四级地址！！");
      } else {
        // 不可选四级地址，且三级地址不为空，复制
        mStreetCode = mAreaCode;
        mStreetName = mAreaName;
        return true;
      }
    }
    return false;
  }

  ///下一步后初始化接口
  void initLicenseList() {
    var map = Map();
    map["merchantId"] = widget.merchantId;
    map["customerType"] = widget.customerTypeCode;
    if (widget.from != null) {
      map["from"] = widget.from;
    }
    if (widget.ecOrgCode != null) {
      map["ecOrgCode"] = widget.ecOrgCode;
      map["applicationNumber"] = widget.applicationNumber;
      map["type"] = widget.type;
    }
    EasyLoading.show(maskType: EasyLoadingMaskType.clear);
    NetworkV2<LicenseInitData>(LicenseInitData())
        .requestDataV2("licenseOptimize/initLicenseAuditDetail",
            method: RequestMethod.POST,
            contentType: RequestContentType.FORM,
            parameters: map)
        .then((value) {
      EasyLoading.dismiss();
      if (mounted && value.isSuccess != null && value.isSuccess!) {
        var licenseInitData = value.getData();
        if (licenseInitData != null) {
          if (focusNode.hasFocus) {
            focusNode.unfocus();
          }
          widget.callback(licenseInitData, mProvinceCode, mCityCode, mAreaCode,
              mStreetCode, detailAddress);
        } else {
          XYYContainer.toastChannel.toast("获取模板失败！");
        }
      }
    });
  }

  /// 获取拼接后的区域名
  String getAreaText() {
    return "$mProvinceName$mCityName$mAreaName$mStreetName";
  }

  /// 点击跳转区域筛选
  void handleAreaClick() {
    if (Platform.isIOS) {
      XYYContainer.bridgeCall('areaSelect').then((value) {
        if (value != null) {
          Map<String, dynamic>? map = json.decode(value);
          if (map != null) {
            updateArea(
                map["province"].toString(),
                map["provinceCode"].toString(),
                map["city"].toString(),
                map["cityCode"].toString(),
                map["area"].toString(),
                map["areaCode"].toString(),
                map.containsKey("street") ? map["street"].toString() : "",
                map.containsKey("streetCode")
                    ? map["streetCode"].toString()
                    : "");
          }
        }
      });
    } else {
      XYYContainer.open("xyy://crm-app.ybm100.com/select_area",
          callback: (resultData) {
        if (resultData != null &&
            resultData is Map &&
            resultData.containsKey("provinceBean")) {
          updateArea(
              resultData["provinceBean"]["areaName"],
              resultData["provinceBean"]["areaCode"],
              resultData["cityBean"]["areaName"],
              resultData["cityBean"]["areaCode"],
              resultData["areaBean"]["areaName"],
              resultData["areaBean"]["areaCode"],
              resultData.containsKey("streetBean")
                  ? resultData["streetBean"]["areaName"]
                  : "",
              resultData.containsKey("streetBean")
                  ? resultData["streetBean"]["areaCode"]
                  : "");
        }
      });
    }
  }

  ///更新地址
  void updateArea(
      String? provinceName,
      String? provinceCode,
      String? cityName,
      String? cityCode,
      String? areaName,
      String? areaCode,
      String? streetName,
      String? streetCode) {
    setState(() {
      mProvinceCode = provinceCode;
      mCityCode = cityCode;
      mAreaCode = areaCode;
      mProvinceName = provinceName ?? "";
      mCityName = cityName ?? "";
      mAreaName = areaName ?? "";

      if (streetCode?.isNotEmpty == true || streetName?.isNotEmpty == true) {
        // 四级地址不为空
        mStreetCode = streetCode;
        mStreetName = streetName ?? "";
      } else {
        // 四级地址为空，将三级地址补全到四级地址
        mStreetCode = areaCode;
        mStreetName = areaName ?? "";
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
    focusNode.unfocus();
  }
}

typedef BaseInfoCallback = void Function(
    LicenseInitData licenseInitData,
    String? provinceCode,
    String? cityCode,
    String? areaCode,
    String? streetCode,
    String? detailAddress);
