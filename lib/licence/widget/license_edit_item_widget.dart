import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LicenseEditItemWidget extends StatelessWidget {
  final String leftText;
  final String rightText;
  final ItemAction itemAction;

  final bool? showArrow;

  final String? hintText;
  final VoidCallback? clickCallback;
  final ValueChanged<String>? editCallback;
  final FocusNode? focusNode;
  final bool? showPointer;
  final List<TextInputFormatter>? inputFormatters;

  LicenseEditItemWidget(this.leftText, this.rightText, this.itemAction,
      {this.clickCallback,
      this.hintText,
      this.showArrow,
      this.focusNode,
      this.showPointer,
      this.inputFormatters,
      this.editCallback});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Container(
          padding: EdgeInsets.only(top: 12, bottom: 12),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                child: Text(
                  "*",
                  style: TextStyle(
                      fontSize: 15,
                      fontWeight: FontWeight.normal,
                      color: Color(0xFFFE3D3D)),
                ),
                visible: showPointer ?? false,
              ),
              SizedBox(
                width: 84,
                child: Text(
                  leftText,
                  style:
                      TextStyle(fontSize: 14, color: const Color(0xff292933)),
                ),
              ),
              SizedBox(width: 20),
              Expanded(
                child: getRightView(),
              ),
              Visibility(
                visible: showArrow ?? true,
                child: Image.asset("assets/images/order/order_filter_arrow.png",
                    width: 15, height: 15),
              )
            ],
          )),
    );
  }

  Widget getRightView() {
    switch (itemAction) {
      case ItemAction.Click:
      case ItemAction.NonEdit:
        return GestureDetector(
            child: Text(
              rightText,
              style: TextStyle(fontSize: 14, color: const Color(0xff292933)),
            ),
            onTap: () {
              if (itemAction == ItemAction.Click && clickCallback != null) {
                clickCallback!();
              }
            });
      case ItemAction.Edit:
        return editView(rightText, editCallback);
    }
  }

  ///详细地址编辑
  Widget editView(String text, ValueChanged<String>? changed) {
    return TextFormField(
      maxLengthEnforcement: MaxLengthEnforcement.enforced,
      focusNode: focusNode,
      inputFormatters: inputFormatters,
      autofocus: false,
      controller: TextEditingController.fromValue(
        TextEditingValue(
          text: text,
          selection: TextSelection.fromPosition(
            ///用来设置文本的位置
            TextPosition(
              affinity: TextAffinity.downstream,
              // 光标向后移动的长度
              offset: text.length,
            ),
          ),
        ),
      ),
      onChanged: changed,
      style: TextStyle(
        fontSize: 14,
        color: const Color(0xff292933),
      ),
      scrollPadding: EdgeInsets.zero,
      decoration: InputDecoration(
        counterText: "",
        hintStyle: TextStyle(
          fontSize: 14,
          color: const Color(0xff9494A6),
        ),
        hintText: hintText ?? "",
        hintMaxLines: 1,
        border: OutlineInputBorder(
          borderSide: BorderSide.none,
        ),
        contentPadding: EdgeInsets.zero,
        isDense: true,
      ),
      maxLength: 50,
      minLines: 1,
      maxLines: 10,
      // 达到最大长度不允许输入
    );
  }
}

enum ItemAction { Click, Edit, NonEdit }
