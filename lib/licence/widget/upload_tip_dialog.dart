import 'package:flutter/material.dart';

class UploadTipSheet extends StatefulWidget {
  final VoidCallback onAlbum;
  final String code;
  final String? remark;
  final String? imgUrl;



  const UploadTipSheet({Key? key, required this.onAlbum, required this.code, this.remark, this.imgUrl}) : super(key: key);

  @override
  _UploadTipSheetState createState() => _UploadTipSheetState();
}

class _UploadTipSheetState extends State<UploadTipSheet> {
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _preloadImage();
  }

  void _preloadImage() async {
    final String imagePath = widget.imgUrl!;
    if (imagePath.startsWith('http') || imagePath.startsWith('https')) {
      // 网络图片
      final image = Image.network(imagePath);
      final ImageStream stream = image.image.resolve(ImageConfiguration());
      stream.addListener(ImageStreamListener((_, __) {
        if (mounted) setState(() => _isLoading = false);
      }, onError: (dynamic _, __) {
        if (mounted) setState(() => _isLoading = false);
      }));
    } else {
      // 本地图片
      await precacheImage(AssetImage(imagePath), context).catchError((_) {});
      if (mounted) setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    // 2. 根据code选择图片和规则文字
    //final String imagePath = codeToImage[code] ?? 'assets/images/licence/yyzz_example.png';
    final String imagePath = widget.imgUrl!;
    final String rulesText = (widget.remark ?? '').replaceAll('\\n', '\n').replaceAll('\n', '\n');
    print('$imagePath,llwq');
    //final String rulesText = codeToRules[code] ?? codeToRules['YYZZ']!;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
      ),
      child: _isLoading
          ? SizedBox(
              height: 300,
              child: Center(child: CircularProgressIndicator()),
            )
          : Column(
        children: [
          // Header
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 8, 8, 0),
            child: Stack(
              alignment: Alignment.center,
              children: [
                Text(
                  "上传说明",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Color(0xff292933)),
                ),
                Align(
                  alignment: Alignment.centerRight,
                  child: IconButton(
                    icon: Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ),
              ],
            ),
          ),
          // 顶部黄色提示条（固定）
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Container(
              width: double.infinity,
              color: Color(0xFFFDF6EC),
              padding: EdgeInsets.all(8),
              child: Text(
                "请保持证件照位于首图（附件置于首图后），证件质量影响审核结果和发货时效，请按审核标准上传证件件。",
                style: TextStyle(fontSize: 13, color: Color(0xFFB26A00)),
              ),
            ),
          ),
          // 图片+标签+规则文字（一起滚动）
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  (imagePath.startsWith('http') || imagePath.startsWith('https'))
                      ? Image.network(
                          imagePath,
                          width: double.infinity,
                          fit: BoxFit.contain,
                          errorBuilder: (context, error, stackTrace) => Text('图片加载失败'),
                        )
                      : Image.asset(
                          imagePath,
                          width: double.infinity,
                          fit: BoxFit.contain,
                        ),
                  SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildTag("企业公章"),
                      SizedBox(width: 8),
                      _buildTag("清晰完整"),
                    ],
                  ),
                  SizedBox(height: 12),
                  Align(
                    alignment: Alignment.centerLeft,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: rulesText
                          .split('\n')
                          .where((line) => line.trim().isNotEmpty)
                          .map((line) => Padding(
                                padding: const EdgeInsets.only(bottom: 20),
                                child: Text(
                                  line,
                                  style: TextStyle(fontSize: 14, color: Color(0xFF292933)),
                                  textAlign: TextAlign.left,
                                ),
                              ))
                          .toList(),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // Footer Button
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: SizedBox(
              width: double.infinity,
              height: 44,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  primary: Colors.white,
                  onPrimary: Color(0xFF00B377),
                  side: BorderSide(color: Color(0xFF00B377)),
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
                  elevation: 0,
                ),
                onPressed: widget.onAlbum,
                child: Text("相册"),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String text) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      decoration: BoxDecoration(
          color: Color(0xFFFFF1E6),
          borderRadius: BorderRadius.circular(2)),
      child: Text(text, style: TextStyle(color: Color(0xFFFF5B20))),
    );
  }
}