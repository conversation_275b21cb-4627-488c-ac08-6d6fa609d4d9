import 'dart:convert';
import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/dialog/message_dialog.dart';
import 'package:XyyBeanSproutsFlutter/common/image/image_catch_widget.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_image_upload_info.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_init_data.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_upload_info.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/list_info.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/post_model.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'upload_tip_dialog.dart'; 

class LicenseUpdatePhotoWidget extends StatefulWidget {
  final String? applicationNumber;
  final bool mIsAddType;
  final String? merchantId; // 客户id
  final int customerTypeCode; // 客户类型

  final String? mProvinceCode;
  final String? mCityCode;
  final String? mAreaCode;
  final String? mStreetCode;
  final String? detailAddress;
  final bool isDraft;

  final String? from;

  final LicenseInitData? licenseInitData;

  final UpdateCallback? callback;

  LicenseUpdatePhotoWidget(
      this.isDraft,
      this.merchantId,
      this.from,
      this.applicationNumber,
      this.customerTypeCode,
      this.mIsAddType,
      this.mProvinceCode,
      this.mCityCode,
      this.mAreaCode,
      this.mStreetCode,
      this.detailAddress,
      this.licenseInitData,
      this.callback);

  @override
  State<StatefulWidget> createState() {
    return LicenseUpdatePhotoState();
  }
}

class LicenseUpdatePhotoState extends State<LicenseUpdatePhotoWidget> {
  /// 资质更新
  // ignore: non_constant_identifier_names
  final int RX_BUS_UPDATE_LICENCEDTAIL = 10021;

  /// 资质变更完成
  // ignore: non_constant_identifier_names
  final int RX_BUS_UPDATE_LICENCEDTAIL_CHANGE = 10024;

  List<LicenseItem>? necessaryLicenceList;
  List<LicenseItem>? optionalLicenceList;
  List<LicenseQualificationInfoDTOs>? qualificationInfoDTOS;
  List<LicenseImageUpLoadInfo> licenseListUploadInfo = [];

  String imageHost = "";
  ValueNotifier<bool> photoViewController = ValueNotifier(true);
  bool _hasPrecached = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_hasPrecached) {
      _hasPrecached = true;
      XYYContainer.bridgeCall("app_host").then((value) {
        if (value is Map) {
          this.imageHost = value['license_image_host'] ?? "";
        }
      });
      necessaryLicenceList = widget.licenseInitData?.necessaryLicenceList;
      optionalLicenceList = widget.licenseInitData?.optionalLicenceList;
      qualificationInfoDTOS = widget.licenseInitData?.qualificationInfoDTOS;
      print('zmtaa,${qualificationInfoDTOS}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 10, right: 10),
      child: Column(
        children: [
          ValueListenableBuilder(
          valueListenable: photoViewController,
          builder: (BuildContext context, bool value,
              Widget? child) {
            return Expanded(
            child: SingleChildScrollView(
                child: Column(
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Column(
                    children: List.generate(necessaryLicenceList?.length ?? 0,
                        (index) {
                  return buildListItem(
                      index, necessaryLicenceList ?? List.empty(), true);
                })),
                Column(
                    children: List.generate(optionalLicenceList?.length ?? 0,
                        (index) {
                  return buildListItem(
                      index, optionalLicenceList ?? List.empty(), false);
                })),
              ],
            )),
          );
          },
        )
          ,
          operationView()
        ],
      ),
    );
  }

  ///保存至草稿&提交
  Widget operationView() {
    return Container(
      color: Colors.white,
      height: 74,
      child: Row(
        children: [
          Expanded(
            flex: widget.isDraft ? 1 : 0,
            child: Visibility(
              visible: widget.isDraft,
              child: GestureDetector(
                onTap: () {
                  addOrUpdate(false);
                },
                child: Container(
                  margin: EdgeInsets.fromLTRB(10, 15, 10, 15),
                  width: double.infinity,
                  height: 44,
                  alignment: Alignment.center,
                  decoration: ShapeDecoration(
                    color: Color(0xffffffff),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.all(
                        Radius.circular(2.0),
                      ),
                      side: BorderSide(color: Color(0xff9494A6), width: 1),
                    ),
                  ),
                  child: Text(
                    "保存至草稿",
                    style: TextStyle(
                        fontSize: 16,
                        color: Color(0xff292933),
                        fontWeight: FontWeight.w500),
                  ),
                ),
              ),
            ),
          ),
          Visibility(
            child: SizedBox(width: 8),
            visible: widget.isDraft,
          ),
          Expanded(
            child: GestureDetector(
              child: Container(
                  margin: EdgeInsets.fromLTRB(10, 15, 10, 15),
                  width: double.infinity,
                  height: 44,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(2)),
                      color: Color(0xFF00B377)),
                  child: Text(
                    "保存并提交",
                    style: TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500),
                  )),
              onTap: () {
                addOrUpdate(true);
              },
            ),
          )
        ],
      ),
    );
  }

  ///上传资质照片列表
  Widget buildListItem(
      int index, List<LicenseItem> licenseItem, bool necessary) {
    var nameColor = 0xff292933;
    switch(licenseItem[index].validateFlag) {
      case 0:
        //正常
        nameColor = 0xff292933;
        break;
      case 1:
        //临期
        nameColor = 0xffff5b20;
        break;
      case 2:
        //过期
        nameColor = 0xffff071b;
        break;
    }
    return Container(
        padding: EdgeInsets.only(left: 10, right: 10),
        child: Column(
            mainAxisSize: MainAxisSize.max,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(height: 15),
              Row(
                  mainAxisSize: MainAxisSize.max,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    1 == licenseItem[index].isRequired
                        ? Image.asset(
                            "assets/images/licence/icon_need_checked.png",
                            width: 10,
                            height: 10,
                          )
                        : Container(),
                    Expanded(
                        child: Text(
                      licenseItem[index].name ?? "",
                      style: TextStyle(fontSize: 15, color: Color(nameColor)),
                    ))
                  ]),
              SizedBox(height: 5),
              buildListItemBottom(
                  licenseItem[index].enclosureList,
                  false,
                  "其他" == licenseItem[index].name ? 10 : 3,
                  isKPXX(licenseItem[index].code),
                  necessary,
                  index,
                  licenseItem[index].code!),
              SizedBox(height: 15)
            ]));
  }

  ///判断是否是开票信息
  bool isKPXX(String? code) {
    return code == "KPXX";
  }

  ///加号列表
  Widget buildListItemBottom(List<String>? enclosureList, bool showExample,
      int maxSize, bool isKpxx, bool necessary, int parentIndex, String code) {
    String addImage = "assets/images/licence/icon_license_add_image.png";
    String exampleImage = "assets/images/licence/icon_invoice_example.png";
    if (enclosureList == null || enclosureList.isEmpty) {
      enclosureList = [];
      //添加开票信息
      if (isKpxx) {
        maxSize++;
        enclosureList.add(exampleImage);
      }
      enclosureList.add(addImage);
    } else {
      //添加开票信息
      if (isKpxx && !enclosureList[0].contains(exampleImage)) {
        maxSize++;
        List<String> temp = [];
        temp.add(exampleImage);
        temp.addAll(enclosureList);
        enclosureList = temp;
      }
      if (enclosureList.length < maxSize && !enclosureList.contains(addImage)) {
        List<String> temp = [];
        temp.addAll(enclosureList);
        temp.add(addImage);
        enclosureList = temp;
      }
    }
    return Column(
      mainAxisSize: MainAxisSize.max,
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          height: 85,
          width: double.infinity,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount:
                enclosureList.length > maxSize ? maxSize : enclosureList.length,
            separatorBuilder: (BuildContext context, int index) =>
                VerticalDivider(width: 13, color: Colors.white),
            itemBuilder: (context, index) {
              return GestureDetector(
                child: Stack(
                  clipBehavior: Clip.none, alignment: Alignment.bottomLeft,
                  children: [
                    localPath(enclosureList![index])
                        ? Image.asset(
                            enclosureList[index],
                            width: 80,
                            height: 80,
                          )
                        : SizedBox(
                      width: 80,
                      height: 80,
                      child: FadeInImage(
                      image:ResizeImage(
                                 NetworkImage(enclosureList[index]),
                                 width: window.physicalSize.width~/3
                            ),
                      placeholder: AssetImage("assets/images/base/icon_default_image.png"),
                      fit: BoxFit.cover,
                      imageErrorBuilder: (context, error, stackTrace) {
                          return Image.asset("assets/images/base/icon_default_image.png");
                      }
                    ),
                    ),
                    Positioned(
                      right: -5,
                      top: 0,
                      child: GestureDetector(
                          child: Visibility(
                            child: Image.asset(
                                "assets/images/licence/icon_license_close.png",
                                width: 15,
                                height: 15),
                            visible: !localPath(enclosureList[index]),
                          ),
                          onTap: () {
                            showMessageDialog2(
                                title: "",
                                message: "确认删除照片?",
                                negativeText: "取消",
                                positiveText: "确认",
                                callBack: () {
                                  deletePhoto(parentIndex, necessary,
                                      enclosureList![index]);
                                });
                          }),
                    ),
                    Positioned(
                      child: Visibility(
                        child: Container(
                          color: const Color(0xfff5a623),
                          child: Text(
                            "示例图片",
                            style: TextStyle(color: Colors.white, fontSize: 12),
                          ),
                          alignment: Alignment.center,
                          width: 80,
                          height: 20,
                        ),
                        visible: isKpxx && index == 0,
                      ),
                      bottom: 0,
                    )
                  ],
                ),
                onTap: () {
                  if (localPath(enclosureList![index])) {
                    if (enclosureList[index] == addImage) {
                      selectAlbum(parentIndex, necessary, maxSize,code);
                    } else {
                      viewPhoto(
                          enclosureList[index], parentIndex, necessary, false);
                    }
                  } else {
                    viewPhoto(
                        enclosureList[index], parentIndex, necessary, true);
                  }
                },
              );
            },
          ),
        ),
        Visibility(child: SizedBox(height: 10), visible: isKpxx),
        Visibility(
            child: Text(
              "开具增值税专用发票，请完整、准确提供以下信息并加盖公章：名称、纳税人识别号、地址、电话、开户行、账号。",
              style: TextStyle(fontSize: 12, color: const Color(0xff9494A6)),
            ),
            visible: isKpxx)
      ],
    );
  }

  ///打开相册
  void photoForAlbumWithUpload(
      String imagePath, bool necessary, int index, int maxCount) async {
    if (necessary) {
      var length = necessaryLicenceList![index] == null
          ? 0
          : necessaryLicenceList![index].enclosureList == null
              ? 0
              : necessaryLicenceList![index].enclosureList!.length;
      maxCount = maxCount - length;
    } else {
      var length = optionalLicenceList![index] == null
          ? 0
          : optionalLicenceList![index].enclosureList == null
              ? 0
              : optionalLicenceList![index].enclosureList!.length;
      maxCount = maxCount - length;
    }
    try {
      List<String>? ids = await XYYContainer.photoForAlbumWithUpload(imagePath,
          imageCount: maxCount,
          extraParams: {"merchantId": widget.merchantId, "isUploadOrigin": "true"},
          limitWidth: 1280,
          limitHeight: 1280);
      ids = ids?.map((e) => this.imageHost + e).toList();
      if (necessary) {
        if (necessaryLicenceList![index].enclosureList == null) {
          necessaryLicenceList![index].enclosureList = ids;
        } else {
          List<String> temp = [];
          temp.addAll(necessaryLicenceList![index].enclosureList!);
          temp.addAll(ids!);
          necessaryLicenceList![index].enclosureList = temp;
        }
      } else {
        if (optionalLicenceList![index].enclosureList == null) {
          optionalLicenceList![index].enclosureList = ids;
        } else {
          List<String> temp = [];
          temp.addAll(optionalLicenceList![index].enclosureList!);
          temp.addAll(ids!);
          optionalLicenceList![index].enclosureList = temp;
        }
      }
    } catch (e) {
    }
    if (mounted) {
      photoViewController.value = !photoViewController.value;
    }
  }

  // 允许上传的资质类型
  static const Set<String> allowedLicenseTypeCodes = {
    'YYZZ', // 营业执照
    'YPJY', // 药品经营许可证
    'FRSQ', // 授权委托书
    'KPXX', // 开票信息和开户许可证
    'WTRZ', // 被委托人身份证复印件（正反两面)
    'ZYXK', // 医疗机构执业许可证"
  };

  /// 先择相册
  void selectAlbum(int index, bool necessary, int maxSize , String code) {
    if (allowedLicenseTypeCodes.contains(code)) {
      LicenseQualificationInfoDTOs? match;
      try {
        match = qualificationInfoDTOS?.firstWhere((e) => e.name == code);
      } catch (e) {
        match = null;
      }
      print('qqqqq2025,${match},${qualificationInfoDTOS}');
      if (match != null) {
        // 弹窗逻辑
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder: (BuildContext context) {
            return SizedBox(
              height: MediaQuery.of(context).size.height * 0.8,
              child: UploadTipSheet(
                onAlbum: () {
                  Navigator.pop(context);
                  photoForAlbumWithUpload("license/uploadLicenseAuditImg", necessary, index, maxSize);
                },
                code: code,
                remark: match!.remark,
                imgUrl: (match.imgUrl != null && match.imgUrl!.isNotEmpty) ? match.imgUrl!.first : null,
              ),
            );
          },
        );
        print('匹配到的name: ${match.name}');
        print('匹配到的remark: ${match.remark}');
        print('匹配到的imgUrl: ${match.imgUrl}');
      } else {
        // 不弹窗，直接走后续逻辑
        photoForAlbumWithUpload("license/uploadLicenseAuditImg", necessary, index, maxSize);
      }
    } else {
      // 不弹窗，直接走后续逻辑
      photoForAlbumWithUpload("license/uploadLicenseAuditImg", necessary, index, maxSize);
    }
  }

  ///预览图片
  void viewPhoto(String url, int parentIndex, bool necessary, bool delete) {
    Navigator.of(context).pushNamed("/photo_view_page", arguments: {
      "urlPath": url,
      "delete": delete,
      "callback": () {
        if (delete) {
          showMessageDialog2(
              title: "",
              message: "确认删除照片?",
              negativeText: "取消",
              positiveText: "确认",
              callBack: () {
                Navigator.of(context).pop();
                deletePhoto(parentIndex, necessary, url);
              });
        }
      }
    });
  }

  ///弹对话框
  void showMessageDialog2(
      {String title = "提示",
      String? message,
      String negativeText = "确定",
      String positiveText = "取消",
      Function? callBack,
      Function? cancelCallBack}) {
    if (message != null && message.isNotEmpty) {
      showDialog<Null>(
          context: context, //BuildContext对象
          barrierDismissible: false,
          builder: (BuildContext context) {
            return MessageDialog(
              title: title,
              negativeText: negativeText,
              positiveText: positiveText,
              message: message,
              onPositivePressEvent: () {
                Navigator.pop(context);
                if (callBack != null) {
                  callBack();
                }
              },
              onCloseEvent: () {
                Navigator.pop(context);
                if (cancelCallBack != null) {
                  cancelCallBack();
                }
              },
            ); //调用对话框);
          });
    }
  }

  ///判断是否是本地图片展示
  bool localPath(String path) {
    if (path.startsWith("assets")) {
      return true;
    }
    return false;
  }

  ///检查必填资质是否完成
  bool checkPostInfo(bool isCommit) {
    licenseListUploadInfo.clear();
    if (necessaryLicenceList != null) {
      for (int i = 0; i < necessaryLicenceList!.length; i++) {
        if (necessaryLicenceList![i].enclosureList == null) {
          if (isCommit) {
            String? name = (necessaryLicenceList![i].name == null ||
                    necessaryLicenceList![i].name!.isEmpty)
                ? "必须的资质均"
                : necessaryLicenceList![i].name;
            XYYContainer.toastChannel.toast("$name需上传");
            return false;
          } else {
            continue;
          }
        }
        LicenseImageUpLoadInfo upLoadInfo = LicenseImageUpLoadInfo();
        List<ListInfo> enclosureList = [];
        for (int index = 0;
            necessaryLicenceList![i].enclosureList != null &&
                index < necessaryLicenceList![i].enclosureList!.length;
            index++) {
          ListInfo info = ListInfo();
          info.url = necessaryLicenceList![i].enclosureList![index];
          info.enclosureName = necessaryLicenceList![i].name;
          enclosureList.add(info);
        }
        if (enclosureList.length == 0) {
          continue;
        }
        upLoadInfo.enclosureList = enclosureList;
        upLoadInfo.credentialCode = necessaryLicenceList![i].code;
        licenseListUploadInfo.add(upLoadInfo);
      }
    }
    if (optionalLicenceList != null) {
      for (int i = 0; i < optionalLicenceList!.length; i++) {
        LicenseImageUpLoadInfo upLoadInfo = LicenseImageUpLoadInfo();
        List<ListInfo> enclosureList = [];
        for (int index = 0;
            optionalLicenceList![i].enclosureList != null &&
                index < optionalLicenceList![i].enclosureList!.length;
            index++) {
          ListInfo info = ListInfo();
          info.url = optionalLicenceList![i].enclosureList![index];
          info.enclosureName = optionalLicenceList![i].name;
          enclosureList.add(info);
        }
        if (enclosureList.length == 0) {
          continue;
        }
        upLoadInfo.enclosureList = enclosureList;
        upLoadInfo.credentialCode = optionalLicenceList![i].code;
        licenseListUploadInfo.add(upLoadInfo);
      }
    }
    if (licenseListUploadInfo.length == 0) {
      XYYContainer.toastChannel.toast("请至少提交一张资质");
      return false;
    }
    if (necessaryLicenceList != null) {
      bool has = false;
      necessaryLicenceList!.forEach((element) {
        if (isKPXX(element.code) && element.enclosureList!.length == 1) {
          has = true;
        }
      });
      if (has && !widget.mIsAddType && "1" == widget.from) {
        XYYContainer.toastChannel.toast("请上传开户信息和开户许可证");
        return false;
      }
    }
    return true;
  }

  ///保存并提交
  void addOrUpdate(bool isCommit) async {
    if (checkPostInfo(isCommit)) {
      LicenseUploadInfo uploadInfo = LicenseUploadInfo();
      //新增时为空，修改时必传 merchantId
      uploadInfo.applicationNumber = widget.applicationNumber;
      //1首营2资质变更
      uploadInfo.type = widget.mIsAddType ? "1" : "2";
      //客户ID
      uploadInfo.merchantId = widget.merchantId;
      //1保存,2保存并提交审核
      uploadInfo.operateType = isCommit ? "2" : "1";
      //客户类型
      uploadInfo.customerType = widget.customerTypeCode.toString();
      //收货地址省份ID
      uploadInfo.deliveryProvinceId = widget.mProvinceCode;
      //收获地址市级ID
      uploadInfo.deliveryCityId = widget.mCityCode;
      //收获地址区ID
      uploadInfo.deliveryDistrictId = widget.mAreaCode;
      //收获地址街道ID
      uploadInfo.deliveryStreetId = widget.mStreetCode;
      //详细地址
      uploadInfo.deliveryAddress = widget.detailAddress;
      if (widget.from != null && widget.from!.isNotEmpty) {
        uploadInfo.from = widget.from;
      }
      uploadInfo.credentialList = licenseListUploadInfo;
      // print("json${json.decode(json.encode(uploadInfo))}");
      EasyLoading.show(maskType: EasyLoadingMaskType.clear);
      Network<PostModel>(PostModel())
          .requestData("licenseOptimize/addOrUpdateLicense",
              contentType: RequestContentType.JSON,
              method: RequestMethod.POST,
              parameters: json.decode(json.encode(uploadInfo)))
          .then((value) {
        EasyLoading.dismiss();
        if (value.isSuccess == true) {
          XYYContainer.toastChannel.toast("提交成功");
          Map<String, dynamic> map = Map();
          map["isCommit"] = isCommit;
          XYYContainer.bridgeCall("event_bus",
              parameters: {'code': RX_BUS_UPDATE_LICENCEDTAIL});
          XYYContainer.bridgeCall("event_bus", parameters: {
            'code': RX_BUS_UPDATE_LICENCEDTAIL_CHANGE,
            'data': map
          });
          if (widget.callback != null) {
            widget.callback!(true);
          }
        }
      });
    }
  }

  void deletePhoto(int parentIndex, bool necessary, String photoPath) {
    if (necessary) {
      if (necessaryLicenceList!.length > parentIndex) {
        necessaryLicenceList![parentIndex].enclosureList!.remove(photoPath);
      }
    } else {
      if (optionalLicenceList!.length > parentIndex) {
        optionalLicenceList![parentIndex].enclosureList!.remove(photoPath);
      }
    }
    setState(() {});
  }
}

typedef UpdateCallback = void Function(bool success);
