import 'package:XyyBeanSproutsFlutter/common/titlebar/common_title_bar.dart';
import 'package:XyyBeanSproutsFlutter/licence/widget/licence_tabbar.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../licence_manager_page.dart';

///资质列表头部 标题、tab、筛选
// ignore: must_be_immutable
class LicenceAppBar extends StatelessWidget implements PreferredSizeWidget {
  VoidCallback? callback;
  TabController? _tabController;
  ValueChanged<int> onTap;
  StatusModel? _statusModel;

  LicenceAppBar(this._statusModel, this._tabController, this.onTap);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      leading: LeftButton(
        leftBtnType: LeftButtonType.back,
        onPressed: callback,
      ),
      backgroundColor: Colors.white,
      elevation: 0.5,
      shadowColor: Color(0xFFF6F6F6),
      centerTitle: true,
      title: Text("资质列表", style: TextStyle(color: Colors.black, fontSize: 17)),
      actions: [
        IconButton(
          icon: Image.asset(
            'assets/images/titlebar/icon_search.png',
            width: 22,
            height: 22,
          ),
          iconSize: 22,
          onPressed: () {
            search(context);
          },
        )
      ],
      bottom: LicenceTabBar(
        _tabController,
        _statusModel,
        Size.fromHeight(30),
        context,
        onTap: onTap,
      ), toolbarTextStyle: TextTheme(
        headline6: TextStyle(
          color: Colors.black,
        ),
      ).bodyText2, titleTextStyle: TextTheme(
        headline6: TextStyle(
          color: Colors.black,
        ),
      ).headline6, systemOverlayStyle: SystemUiOverlayStyle.dark,
    );
  }

  void search(BuildContext context) {
    Navigator.of(context).pushNamed('/licence_search');
  }

  @override
  Size get preferredSize => Size.fromHeight(130);
}
