import 'dart:convert';
import 'dart:ui';

import 'package:XYYContainer/XYYContainer.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/license_detail_data.dart';
import 'package:extended_image/extended_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class LicenseDetailPhotoWidget extends StatelessWidget {
  final List<LicenseDetailItem>? licencePhotoList;

  LicenseDetailPhotoWidget(this.licencePhotoList);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 10, left: 10, right: 10),
      padding: EdgeInsets.symmetric(vertical: 15, horizontal: 12),
      decoration: BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(8)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(licencePhotoList?.length ?? 0, (index) {
          return buildListItem(
              context, licencePhotoList?.elementAt(index), index);
        }),
      ),
    );
  }

  Widget buildListItem(
      BuildContext context, LicenseDetailItem? itemData, int index) {
    if (itemData == null) {
      return Container();
    }
    return Column(
      children: [
        Container(
          color: Colors.white,
          height: 90,
          alignment: Alignment.centerLeft,
          child: Row(
            children: [
              Container(
                width: 144,
                child: Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "${itemData.licenseName ?? "-"}:",
                    style: TextStyle(
                        color: Color(0xff676773),
                        fontSize: 14,
                        fontWeight: FontWeight.normal),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  child: buildListItemRight(context, itemData, index),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget buildListItemRight(
      BuildContext context, LicenseDetailItem itemData, int index) {
    if (itemData.enclosureList == null || itemData.enclosureList!.isEmpty) {
      return Container();
    }
    var imageInfos = parseImageInfo(itemData.enclosureList);
    return Container(
        padding: EdgeInsets.only(top: index == 0 ? 0 : 15),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          itemCount: imageInfos.length,
          itemBuilder: (context, index) {
            var imageInfo = imageInfos[index];
            return GestureDetector(
              child: Container(
                padding: EdgeInsets.only(left: 10),
                width: 70,
                height: 60,
                child: FadeInImage(
                      image:ResizeImage(
                                 NetworkImage(imageInfo.url ?? ""),
                                 width: window.physicalSize.width~/3
                            ),
                      placeholder: AssetImage("assets/images/base/icon_default_image.png"),
                      fit: BoxFit.cover,
                      imageErrorBuilder: (context, error, stackTrace) {
                          return Image.asset("assets/images/base/icon_default_image.png");
                      }
                    ),
              ),
              onTap: () {
                Navigator.of(context).pushNamed("/photo_view_page", arguments: {
                  "urlPath": imageInfo.url,
                  "delete": false,
                  "callback": () {}
                });
              },
            );
          },
        ));
  }

  List<RightImageInfo> parseImageInfo(List<String>? enclosureList) {
    var list = <RightImageInfo>[];
    if (enclosureList != null && enclosureList.isNotEmpty) {
      enclosureList.forEach((element) {
        if (element.isNotEmpty) {
          try {
            var rightImageInfo = RightImageInfo();
            Map<String, dynamic> decode = json.decode(element);
            // rightImageInfo.enclosureName = decode["enclosureName"];
            rightImageInfo.url = decode["url"];
            list.add(rightImageInfo);
          } catch (e) {
            print(e);
          }
        }
      });
    }
    return list;
  }
}

class RightImageInfo {
  String? enclosureName;
  String? url;
}
