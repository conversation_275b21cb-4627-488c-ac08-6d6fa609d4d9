import 'dart:math';

import 'package:XyyBeanSproutsFlutter/licence/bean/licence_filter_bean.dart';
import 'package:flutter/material.dart';

import 'triangle_up_widget.dart';

Color _bgColor = Colors.white;
double cellHeight = 34;
double cellWidth = 100;

typedef ClickCallBack = void Function(int selectIndex);

///自定义选择弹窗
class PopMenus {
  static void showPop(
      {required BuildContext context,
      required List<LicenceFilterBean>? listData,
      required double topP,
      required double leftP,
      required String? selText,
      ClickCallBack? clickCallback}) {
    Widget _buildMenuLineCell(List<LicenceFilterBean> dataArr) {
      return ListView.separated(
        itemCount: dataArr.length,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (BuildContext context, int index) {
          return GestureDetector(
              onTap: () {
                Navigator.pop(context);
                if (clickCallback != null) {
                  clickCallback(index);
                }
              },
              child: Container(
                height: cellHeight,
                constraints: BoxConstraints(minWidth: 20),
                color: Colors.white,
                padding: EdgeInsets.only(left: 15, right: 15),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    selText == dataArr[index].name
                        ? Text(dataArr[index].name!,
                            style: TextStyle(
                                fontSize: 14, color: Color(0xFF35C561)),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)
                        : Text(dataArr[index].name!,
                            style: TextStyle(
                                fontSize: 14, color: Color(0xFF666666)),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis)
                  ],
                ),
              ));
        },
        separatorBuilder: (context, index) {
          return Divider(
            height: 0.1,
            color: Color(0xFFE6E6E6),
          );
        },
      );
    }

    _buildMenusView(dataArr, double leftP, double topP) {
      var cellH = dataArr.length * cellHeight;
      return Positioned(
        left: leftP,
        top: topP,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Container(
              child: TriangleUpWidget(height: 10, width: 14),
            ),
            ClipRRect(
                borderRadius: BorderRadius.circular(2),
                child: Container(
                    color: _bgColor,
                    width:
                        max(MediaQuery.of(context).size.width / 3, cellWidth),
                    height: cellH,
                    child: _buildMenuLineCell(dataArr)))
          ],
        ),
      );
    }

    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return BasePopMenus(child: _buildMenusView(listData, leftP, topP));
        });
  }
}

class BasePopMenus extends Dialog {
  BasePopMenus({
    Key? key,
    this.child,
  }) : super(key: key);

  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Material(
      type: MaterialType.transparency,
      child: Stack(
        fit: StackFit.expand,
        children: <Widget>[
          GestureDetector(onTap: () => Navigator.pop(context)),
          child!
        ],
      ),
    );
  }
}
