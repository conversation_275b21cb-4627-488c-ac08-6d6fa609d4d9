import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'licence_filter_bean.g.dart';

@JsonSerializable()
class LicenceFilterBean extends BaseModel<LicenceFilterBean> {
  int? code;
  String? name;
  List<LicenceFilterBean>? licenseStatusList;

  LicenceFilterBean();

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return LicenceFilterBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenceFilterBeanToJson(this);
  }

  factory LicenceFilterBean.fromJson(Map<String, dynamic> json) =>
      _$LicenceFilterBeanFromJson(json);
}
