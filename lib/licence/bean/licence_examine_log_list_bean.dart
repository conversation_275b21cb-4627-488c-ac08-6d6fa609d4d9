import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'licence_examine_log_list_bean.g.dart';

@JsonSerializable()
class LicenceExamineLogListBean extends BaseModelV2<LicenceExamineLogListBean> {
  String? typeName; //资质审核 一审
  int? createTime; //时间
  String? creator; //操作人姓名
  String? creatorJob; //操作人
  String? statusName; //操作
  String? remark; //驳回原因
  int? status;
  LicenceExamineLogListBean();

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return LicenceExamineLogListBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenceExamineLogListBeanToJson(this);
  }

  factory LicenceExamineLogListBean.fromJson(Map<String, dynamic> json) =>
      _$LicenceExamineLogListBeanFromJson(json);
}
