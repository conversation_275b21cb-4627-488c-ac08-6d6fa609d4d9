
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'license_detail_data.g.dart';

@JsonSerializable()
class LicenseDetailData extends BaseModelV2<LicenseDetailData>{
  List<LicenseDetailItem>?  necessaryLicenceList;
  List<LicenseDetailItem>?  optionalLicenceList;
  LicenseAudit? licenseAudit;



  LicenseDetailData();

  @override
  LicenseDetailData fromJsonMap(Map<String, dynamic>? json) {
    return LicenseDetailData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseDetailDataToJson(this);
  }

  factory LicenseDetailData.fromJson(Map<String, dynamic> json) =>
      _$LicenseDetailDataFromJson(json);
}

@JsonSerializable()
class LicenseDetailItem extends BaseModelV2<LicenseDetailItem>{

  int? credentialTypeId;
  String? licenseCode;
  String? licenseName;
  String? credentialName;
  List<String>? enclosureList;
  int? changeStatus;
  int? validateFlag;



  LicenseDetailItem();

  @override
  LicenseDetailItem fromJsonMap(Map<String, dynamic>? json) {
    return LicenseDetailItem.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseDetailItemToJson(this);
  }

  factory LicenseDetailItem.fromJson(Map<String, dynamic> json) =>
      _$LicenseDetailItemFromJson(json);

}

@JsonSerializable()
class LicenseAudit extends BaseModelV2<LicenseAudit>{
  String? applicationNumber;
  String? customerCode;
  int? auditStatus;
  String? auditStatusName;
  int? merchantId;
  int? isEdit;
  String? typeName;
  int? type;
  String? ecOrgCode;
  String? oaBranchName;


  LicenseAudit();

  @override
  LicenseAudit fromJsonMap(Map<String, dynamic>? json) {
    return LicenseAudit.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseAuditToJson(this);
  }

  factory LicenseAudit.fromJson(Map<String, dynamic> json) =>
      _$LicenseAuditFromJson(json);

}


