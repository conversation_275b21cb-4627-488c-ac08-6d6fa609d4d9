import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';
part 'license_customer_bean.g.dart';
@JsonSerializable()
class LicenseCustomerBean extends BaseModelV2<LicenseCustomerBean> {
  var customerType;
  var provinceCode;
  var provinceName;
  var cityCode;
  var cityName;
  var areaCode;
  var area;
  var street;
  var streetCode;
  var address;
  var realName;
  var applicationNumber;

  LicenseCustomerBean();

  @override
  LicenseCustomerBean fromJsonMap(Map<String, dynamic>? json) {
    return LicenseCustomerBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseCustomerBeanToJson(this);
  }

  factory LicenseCustomerBean.fromJson(Map<String, dynamic> json) =>
      _$LicenseCustomerBeanFromJson(json);
}