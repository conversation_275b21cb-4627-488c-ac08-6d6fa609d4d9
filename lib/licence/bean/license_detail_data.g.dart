// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_detail_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenseDetailData _$LicenseDetailDataFromJson(Map<String, dynamic> json) {
  return LicenseDetailData()
    ..necessaryLicenceList = (json['necessaryLicenceList'] as List<dynamic>?)
        ?.map((e) => LicenseDetailItem.fromJson(e as Map<String, dynamic>))
        .toList()
    ..optionalLicenceList = (json['optionalLicenceList'] as List<dynamic>?)
        ?.map((e) => LicenseDetailItem.fromJson(e as Map<String, dynamic>))
        .toList()
    ..licenseAudit = json['licenseAudit'] == null
        ? null
        : LicenseAudit.fromJson(json['licenseAudit'] as Map<String, dynamic>);
}

Map<String, dynamic> _$LicenseDetailDataToJson(LicenseDetailData instance) =>
    <String, dynamic>{
      'necessaryLicenceList': instance.necessaryLicenceList,
      'optionalLicenceList': instance.optionalLicenceList,
      'licenseAudit': instance.licenseAudit,
    };

LicenseDetailItem _$LicenseDetailItemFromJson(Map<String, dynamic> json) {
  return LicenseDetailItem()
    ..credentialTypeId = json['credentialTypeId'] as int?
    ..licenseCode = json['licenseCode'] as String?
    ..licenseName = json['licenseName'] as String?
    ..credentialName = json['credentialName'] as String?
    ..enclosureList = (json['enclosureList'] as List<dynamic>?)
        ?.map((e) => e as String)
        .toList()
    ..changeStatus = json['changeStatus'] as int?
    ..validateFlag = json['validateFlag'] as int?;
}

Map<String, dynamic> _$LicenseDetailItemToJson(LicenseDetailItem instance) =>
    <String, dynamic>{
      'credentialTypeId': instance.credentialTypeId,
      'licenseCode': instance.licenseCode,
      'licenseName': instance.licenseName,
      'credentialName': instance.credentialName,
      'enclosureList': instance.enclosureList,
      'changeStatus': instance.changeStatus,
      'validateFlag': instance.validateFlag,
    };

LicenseAudit _$LicenseAuditFromJson(Map<String, dynamic> json) {
  return LicenseAudit()
    ..applicationNumber = json['applicationNumber'] as String?
    ..customerCode = json['customerCode'] as String?
    ..auditStatus = json['auditStatus'] as int?
    ..auditStatusName = json['auditStatusName'] as String?
    ..merchantId = json['merchantId'] as int?
    ..isEdit = json['isEdit'] as int?
    ..typeName = json['typeName'] as String?
    ..type = json['type'] as int?
    ..ecOrgCode = json['ecOrgCode'] as String?
    ..oaBranchName = json['oaBranchName'] as String?;
}

Map<String, dynamic> _$LicenseAuditToJson(LicenseAudit instance) =>
    <String, dynamic>{
      'applicationNumber': instance.applicationNumber,
      'customerCode': instance.customerCode,
      'auditStatus': instance.auditStatus,
      'auditStatusName': instance.auditStatusName,
      'merchantId': instance.merchantId,
      'isEdit': instance.isEdit,
      'typeName': instance.typeName,
      'type': instance.type,
      'ecOrgCode': instance.ecOrgCode,
      'oaBranchName': instance.oaBranchName,
    };
