// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'licence_examine_log_list_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenceExamineLogListBean _$LicenceExamineLogListBeanFromJson(
    Map<String, dynamic> json) {
  return LicenceExamineLogListBean()
    ..typeName = json['typeName'] as String?
    ..createTime = json['createTime'] as int?
    ..creator = json['creator'] as String?
    ..creatorJob = json['creatorJob'] as String?
    ..statusName = json['statusName'] as String?
    ..remark = json['remark'] as String?
    ..status = json['status'] as int?;
}

Map<String, dynamic> _$LicenceExamineLogListBeanToJson(
        LicenceExamineLogListBean instance) =>
    <String, dynamic>{
      'typeName': instance.typeName,
      'createTime': instance.createTime,
      'creator': instance.creator,
      'creatorJob': instance.creatorJob,
      'statusName': instance.statusName,
      'remark': instance.remark,
      'status': instance.status,
    };
