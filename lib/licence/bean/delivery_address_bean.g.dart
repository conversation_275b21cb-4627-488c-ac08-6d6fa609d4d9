// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'delivery_address_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

DeliveryAddressBean _$DeliveryAddressBeanFromJson(Map<String, dynamic> json) {
  return DeliveryAddressBean()
    ..areaCode = json['areaCode'] as int?
    ..areaName = json['areaName'] as String?
    ..cityCode = json['cityCode'] as String?
    ..id = json['id'] as int?
    ..initials = json['initials'] as String?
    ..lat = json['lat'] as String?
    ..level = json['level'] as int?
    ..lng = json['lng'] as String?
    ..parentCode = json['parentCode'] as int?
    ..pinyin = json['pinyin'] as String?
    ..shortName = json['shortName'] as String?
    ..sort = json['sort'] as int?
    ..status = json['status'] as int?
    ..zipCode = json['zipCode'] as int?
    ..selected = json['selected'] as bool?;
}

Map<String, dynamic> _$DeliveryAddressBeanToJson(
        DeliveryAddressBean instance) =>
    <String, dynamic>{
      'areaCode': instance.areaCode,
      'areaName': instance.areaName,
      'cityCode': instance.cityCode,
      'id': instance.id,
      'initials': instance.initials,
      'lat': instance.lat,
      'level': instance.level,
      'lng': instance.lng,
      'parentCode': instance.parentCode,
      'pinyin': instance.pinyin,
      'shortName': instance.shortName,
      'sort': instance.sort,
      'status': instance.status,
      'zipCode': instance.zipCode,
      'selected': instance.selected,
    };
