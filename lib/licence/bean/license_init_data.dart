import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'license_init_data.g.dart';

@JsonSerializable()
class LicenseInitData extends BaseModelV2<LicenseInitData> {
  List<LicenseItem>? necessaryLicenceList;
  List<LicenseItem>? optionalLicenceList;
  LicenseCustomer? customer;
  List<LicenseQualificationInfoDTOs>? qualificationInfoDTOS;

  LicenseInitData();

  @override
  LicenseInitData fromJsonMap(Map<String, dynamic>? json) {
    return LicenseInitData.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseInitDataToJson(this);
  }

  factory LicenseInitData.fromJson(Map<String, dynamic> json) =>
      _$LicenseInitDataFromJson(json);
}

@JsonSerializable()
class LicenseItem extends BaseModelV2<LicenseItem> {
  String? name;
  String? code;
  int? isRequired;
  int? validateFlag; //0:正常,1临期,2过期
  List<String>? enclosureList;

  LicenseItem();

  @override
  LicenseItem fromJsonMap(Map<String, dynamic>? json) {
    return LicenseItem.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseItemToJson(this);
  }

  factory LicenseItem.fromJson(Map<String, dynamic> json) =>
      _$LicenseItemFromJson(json);
}

@JsonSerializable()
class LicenseCustomer extends BaseModelV2<LicenseCustomer> {
  int? customerType;
  int? provinceCode;
  String? provinceName;
  int? cityCode;
  String? cityName;
  int? areaCode;
  String? area;
  String? street;
  int? streetCode;
  String? address;
  String? realName;
  String? applicationNumber;
  String? qualificationNo;

  LicenseCustomer();

  @override
  LicenseCustomer fromJsonMap(Map<String, dynamic>? json) {
    return LicenseCustomer.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseCustomerToJson(this);
  }

  factory LicenseCustomer.fromJson(Map<String, dynamic> json) =>
      _$LicenseCustomerFromJson(json);
}

@JsonSerializable()
class LicenseQualificationInfoDTOs extends BaseModelV2<LicenseQualificationInfoDTOs> {
  String? name;
  String? remark;
  List<String>? imgUrl;

  LicenseQualificationInfoDTOs();

  @override
  LicenseQualificationInfoDTOs fromJsonMap(Map<String, dynamic>? json) {
    return LicenseQualificationInfoDTOs.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseQualificationInfoDTOsToJson(this);
  }

  factory LicenseQualificationInfoDTOs.fromJson(Map<String, dynamic> json) =>
      _$LicenseQualificationInfoDTOsFromJson(json);
}
