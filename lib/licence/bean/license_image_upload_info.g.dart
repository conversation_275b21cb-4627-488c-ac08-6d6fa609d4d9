// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_image_upload_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenseImageUpLoadInfo _$LicenseImageUpLoadInfoFromJson(
    Map<String, dynamic> json) {
  return LicenseImageUpLoadInfo()
    ..credentialCode = json['credentialCode'] as String?
    ..enclosureList = (json['enclosureList'] as List<dynamic>?)
        ?.map((e) => ListInfo.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$LicenseImageUpLoadInfoToJson(
        LicenseImageUpLoadInfo instance) =>
    <String, dynamic>{
      'credentialCode': instance.credentialCode,
      'enclosureList': instance.enclosureList,
    };
