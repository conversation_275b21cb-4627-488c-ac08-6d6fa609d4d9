// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_have_type.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenseHaveType _$LicenseHaveTypeFromJson(Map<String, dynamic> json) {
  return LicenseHaveType()..exit = json['exit'] as int?;
}

Map<String, dynamic> _$LicenseHaveTypeToJson(LicenseHaveType instance) =>
    <String, dynamic>{
      'exit': instance.exit,
    };
