// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_upload_info.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenseUploadInfo _$LicenseUploadInfoFromJson(Map<String, dynamic> json) {
  return LicenseUploadInfo()
    ..credentialList = (json['credentialList'] as List<dynamic>?)
        ?.map((e) => LicenseImageUpLoadInfo.fromJson(e as Map<String, dynamic>))
        .toList()
    ..customerType = json['customerType'] as String?
    ..deliveryAddress = json['deliveryAddress'] as String?
    ..deliveryCityId = json['deliveryCityId'] as String?
    ..deliveryDistrictId = json['deliveryDistrictId'] as String?
    ..deliveryProvinceId = json['deliveryProvinceId'] as String?
    ..deliveryStreetId = json['deliveryStreetId'] as String?
    ..merchantId = json['merchantId'] as String?
    ..operateType = json['operateType'] as String?
    ..applicationNumber = json['applicationNumber'] as String?
    ..type = json['type'] as String?
    ..from = json['from'] as String?;
}

Map<String, dynamic> _$LicenseUploadInfoToJson(LicenseUploadInfo instance) =>
    <String, dynamic>{
      'credentialList': instance.credentialList,
      'customerType': instance.customerType,
      'deliveryAddress': instance.deliveryAddress,
      'deliveryCityId': instance.deliveryCityId,
      'deliveryDistrictId': instance.deliveryDistrictId,
      'deliveryProvinceId': instance.deliveryProvinceId,
      'deliveryStreetId': instance.deliveryStreetId,
      'merchantId': instance.merchantId,
      'operateType': instance.operateType,
      'applicationNumber': instance.applicationNumber,
      'type': instance.type,
      'from': instance.from,
    };
