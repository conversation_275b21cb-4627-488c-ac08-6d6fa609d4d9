import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'licence_item_bean.g.dart';

@JsonSerializable()
class LicenceItemBean extends BaseModel<LicenceItemBean> {
  String? branchName; //单据来源
  String? code; //单据编号
  String? merchantName; //客户名称
  int? type; //1首营 2资质变更
  int? createTime;
  int? auditStatus;//资质对应code
  String? licenseSourceStr;
  int? offset;
  String? sysUserName;
  int? sysUserId;
  String? typeName; //	资质类型名称
  String? audit1StatusName; //资质状态名称
  String? holder; //持有人 不为空的时候表示已认领
  String? oaBranchName; //审批机构

  LicenceItemBean(); //单据过期

  @override
  LicenceItemBean fromJsonMap(Map<String, dynamic>? json) {
    return LicenceItemBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenceItemBeanToJson(this);
  }

  factory LicenceItemBean.fromJson(Map<String, dynamic> json) =>
      _$LicenceItemBeanFromJson(json);
}
