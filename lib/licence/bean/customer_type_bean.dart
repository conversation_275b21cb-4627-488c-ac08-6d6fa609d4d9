import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'customer_type_bean.g.dart';

@JsonSerializable()
class CustomerTypeBean extends BaseModel<CustomerTypeBean> {
  String? text;
  int? type;

  CustomerTypeBean();

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return CustomerTypeBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$CustomerTypeBeanToJson(this);
  }

  factory CustomerTypeBean.fromJson(Map<String, dynamic> json) =>
      _$CustomerTypeBeanFromJson(json);
}
