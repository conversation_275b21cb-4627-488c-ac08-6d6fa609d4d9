// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_init_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenseInitData _$LicenseInitDataFromJson(Map<String, dynamic> json) {
  return LicenseInitData()
    ..necessaryLicenceList = (json['necessaryLicenceList'] as List<dynamic>?)
        ?.map((e) => LicenseItem.fromJson(e as Map<String, dynamic>))
        .toList()
    ..optionalLicenceList = (json['optionalLicenceList'] as List<dynamic>?)
        ?.map((e) => LicenseItem.fromJson(e as Map<String, dynamic>))
        .toList()
    ..customer = json['customer'] == null
        ? null
        : LicenseCustomer.fromJson(json['customer'] as Map<String, dynamic>)
    ..qualificationInfoDTOS = (json['qualificationInfoDTOS'] as List<dynamic>?)
        ?.map((e) => LicenseQualificationInfoDTOs.fromJson(e as Map<String, dynamic>))
        .toList();
}

Map<String, dynamic> _$LicenseInitDataToJson(LicenseInitData instance) =>
    <String, dynamic>{
      'necessaryLicenceList': instance.necessaryLicenceList,
      'optionalLicenceList': instance.optionalLicenceList,
      'customer': instance.customer,
      'qualificationInfoDTOS': instance.qualificationInfoDTOS,
    };

LicenseItem _$LicenseItemFromJson(Map<String, dynamic> json) {
  return LicenseItem()
    ..name = json['name'] as String?
    ..code = json['code'] as String?
    ..isRequired = json['isRequired'] as int?
    ..validateFlag = json['validateFlag'] as int?
    ..enclosureList = (json['enclosureList'] as List<dynamic>?)
        ?.map((e) => e as String)
        .toList();
}

Map<String, dynamic> _$LicenseItemToJson(LicenseItem instance) =>
    <String, dynamic>{
      'name': instance.name,
      'code': instance.code,
      'isRequired': instance.isRequired,
      'validateFlag': instance.validateFlag,
      'enclosureList': instance.enclosureList,
    };

LicenseCustomer _$LicenseCustomerFromJson(Map<String, dynamic> json) {
  return LicenseCustomer()
    ..customerType = json['customerType'] as int?
    ..provinceCode = json['provinceCode'] as int?
    ..provinceName = json['provinceName'] as String?
    ..cityCode = json['cityCode'] as int?
    ..cityName = json['cityName'] as String?
    ..areaCode = json['areaCode'] as int?
    ..area = json['area'] as String?
    ..street = json['street'] as String?
    ..streetCode = json['streetCode'] as int?
    ..address = json['address'] as String?
    ..realName = json['realName'] as String?
    ..applicationNumber = json['applicationNumber'] as String?
    ..qualificationNo = json['qualificationNo'] as String?;
}

Map<String, dynamic> _$LicenseCustomerToJson(LicenseCustomer instance) =>
    <String, dynamic>{
      'customerType': instance.customerType,
      'provinceCode': instance.provinceCode,
      'provinceName': instance.provinceName,
      'cityCode': instance.cityCode,
      'cityName': instance.cityName,
      'areaCode': instance.areaCode,
      'area': instance.area,
      'street': instance.street,
      'streetCode': instance.streetCode,
      'address': instance.address,
      'realName': instance.realName,
      'applicationNumber': instance.applicationNumber,
      'qualificationNo': instance.qualificationNo,
    };

LicenseQualificationInfoDTOs _$LicenseQualificationInfoDTOsFromJson(Map<String, dynamic> json) {
  return LicenseQualificationInfoDTOs()
    ..name = json['name'] as String?
    ..remark = json['remark'] as String?
    ..imgUrl = (json['imgUrl'] as List<dynamic>?)?.map((e) => e as String).toList();
}

Map<String, dynamic> _$LicenseQualificationInfoDTOsToJson(LicenseQualificationInfoDTOs instance) =>
    <String, dynamic>{
      'name': instance.name,
      'remark': instance.remark,
      'imgUrl': instance.imgUrl,
    };
