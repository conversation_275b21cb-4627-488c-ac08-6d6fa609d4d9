// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_base_info_data.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenseBaseInfoData _$LicenseBaseInfoDataFromJson(Map<String, dynamic> json) {
  return LicenseBaseInfoData()
    ..customerName = json['customerName'] as String?
    ..provinceCode = json['provinceCode'] as int?
    ..provinceName = json['provinceName'] as String?
    ..cityCode = json['cityCode'] as int?
    ..cityName = json['cityName'] as String?
    ..areaCode = json['areaCode'] as int?
    ..areaName = json['areaName'] as String?
    ..address = json['address'] as String?
    ..street = json['street'] as String?
    ..streetCode = json['streetCode'] as int?
    ..localCheck = json['localCheck'];
}

Map<String, dynamic> _$LicenseBaseInfoDataToJson(
        LicenseBaseInfoData instance) =>
    <String, dynamic>{
      'customerName': instance.customerName,
      'provinceCode': instance.provinceCode,
      'provinceName': instance.provinceName,
      'cityCode': instance.cityCode,
      'cityName': instance.cityName,
      'areaCode': instance.areaCode,
      'areaName': instance.areaName,
      'address': instance.address,
      'street': instance.street,
      'streetCode': instance.streetCode,
      'localCheck': instance.localCheck,
    };
