
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'license_have_type.g.dart';

@JsonSerializable()
class LicenseHaveType extends BaseModelV2<LicenseHaveType>{

  int? exit;


  LicenseHaveType();

  @override
  LicenseHaveType fromJsonMap(Map<String, dynamic>? json) {
    return LicenseHaveType.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseHaveTypeToJson(this);
  }

  factory LicenseHaveType.fromJson(Map<String, dynamic> json) =>
      _$LicenseHaveTypeFromJson(json);
}
