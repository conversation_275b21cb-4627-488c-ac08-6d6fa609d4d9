// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'license_customer_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenseCustomerBean _$LicenseCustomerBeanFromJson(Map<String, dynamic> json) {
  return LicenseCustomerBean()
    ..customerType = json['customerType']
    ..provinceCode = json['provinceCode']
    ..provinceName = json['provinceName']
    ..cityCode = json['cityCode']
    ..cityName = json['cityName']
    ..areaCode = json['areaCode']
    ..area = json['area']
    ..street = json['street']
    ..streetCode = json['streetCode']
    ..address = json['address']
    ..realName = json['realName']
    ..applicationNumber = json['applicationNumber'];
}

Map<String, dynamic> _$LicenseCustomerBeanToJson(
        LicenseCustomerBean instance) =>
    <String, dynamic>{
      'customerType': instance.customerType,
      'provinceCode': instance.provinceCode,
      'provinceName': instance.provinceName,
      'cityCode': instance.cityCode,
      'cityName': instance.cityName,
      'areaCode': instance.areaCode,
      'area': instance.area,
      'street': instance.street,
      'streetCode': instance.streetCode,
      'address': instance.address,
      'realName': instance.realName,
      'applicationNumber': instance.applicationNumber,
    };
