import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'delivery_address_bean.g.dart';

@JsonSerializable()
class DeliveryAddressBean extends BaseModelV2<DeliveryAddressBean> {
  //	行政编码，使用该字段传参获取下一级地区列表	number	@mock=$order(110101,110102,110105,110106,110107,110108,110109,110111,110112,110113,110114,110115,110116,110117,110118,110119)
  int? areaCode;

  //	行政名称	string	@mock=$order('东城区','西城区','朝阳区','丰台区','石景山区','海淀区','门头沟区','房山区','通州区','顺义区','昌平区','大兴区','怀柔区','平谷区','密云区','延庆区')
  String? areaName;

  //	父级行政code	string	@mock=$order('010','010','010','010','010','010','010','010','010','010','010','010','010','010','undefined','undefined')
  String? cityCode;

  //	id	number	@mock=$order(20066,20266,20541,21204,21628,21788,22487,22799,23449,24062,24646,25208,26004,26342,26673,27130)
  int? id;

  //行政区划的拼音首字母	string	@mock=$order('dcq','xcq','cyq','ftq','sjsq','hdq','mtgq','fsq','tzq','syq','cpq','dxq','hrq','pgq','myq','yqq')
  String? initials;

  //	维度	string	@mock=$order('39.917545','39.91531','39.92149','39.863644','39.9146','39.956074','39.937183','39.735535','39.902485','40.128937','40.218086','39.728909','40.324272','40.144783','undefined','undefined')
  String? lat;

  //	级别，第一级为1	number	@mock=$order(3,3,3,3,3,3,3,3,3,3,3,3,3,3,3,3)
  int? level;

  //	经度	string	@mock=$order('116.418755','116.366791','116.486412','116.286964','116.195442','116.310318','116.105377','116.13916','116.6586','116.653526','116.235909','116.338036','116.637123','117.112335','undefined','undefined')
  String? lng;

  //	父级行政编码	number	@mock=$order(110100,110100,110100,110100,110100,110100,110100,110100,110100,110100,110100,110100,110100,110100,110100,110100)
  int? parentCode;

  //	拼音	string	@mock=$order('Dongcheng','Xicheng','Chaoyang','Fengtai','Shijingshan','Haidian','Mentougou','Fangshan','Tongzhou','Shunyi','Changping','Daxing','Huairou','Pinggu','Miyun','Yanqing')
  String? pinyin;

  //	行政区划的缩写，一般是去掉市区等字样	string	@mock=$order('东城','西城','朝阳','丰台','石景山','海淀','门头沟','房山','通州','顺义','昌平','大兴','怀柔','平谷','密云区','延庆区')
  String? shortName;

  //排序	number	@mock=$order(1,2,5,6,7,8,9,10,12,13,14,15,16,17,18,19)
  int? sort;

  //	1有效，0无效	number	@mock=$order(1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1)
  int? status;

  //	邮编	number	@mock=$order(100010,100032,100020,100071,100043,100089,102300,102488,101149,101300,102200,102600,101400,101200,,)
  int? zipCode;
  bool? selected;

  DeliveryAddressBean();

  @override
  DeliveryAddressBean fromJsonMap(Map<String, dynamic>? json) {
    return DeliveryAddressBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$DeliveryAddressBeanToJson(this);
  }

  factory DeliveryAddressBean.fromJson(Map<String, dynamic> json) =>
      _$DeliveryAddressBeanFromJson(json);
}
