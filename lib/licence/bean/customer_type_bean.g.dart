// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'customer_type_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

CustomerTypeBean _$CustomerTypeBeanFromJson(Map<String, dynamic> json) {
  return CustomerTypeBean()
    ..isSuccess = json['isSuccess'] as bool?
    ..status = json['status'] as String?
    ..msg = json['msg'] as String?
    ..errorCode = json['errorCode'] as int?
    ..errorMsg = json['errorMsg'] as String?
    ..message = json['message'] as String?
    ..data = json['data'] == null
        ? null
        : CustomerTypeBean.fromJson(json['data'] as Map<String, dynamic>)
    ..text = json['text'] as String?
    ..type = json['type'] as int?;
}

Map<String, dynamic> _$CustomerTypeBeanToJson(CustomerTypeBean instance) =>
    <String, dynamic>{
      'isSuccess': instance.isSuccess,
      'status': instance.status,
      'msg': instance.msg,
      'errorCode': instance.errorCode,
      'errorMsg': instance.errorMsg,
      'message': instance.message,
      'data': instance.data,
      'text': instance.text,
      'type': instance.type,
    };
