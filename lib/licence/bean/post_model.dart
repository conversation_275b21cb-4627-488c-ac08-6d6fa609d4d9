import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'post_model.g.dart';

@JsonSerializable()
class PostModel extends BaseModel<PostModel> {
  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return PostModel.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$PostModelToJson(this);
  }

  factory PostModel.fromJson(Map<String, dynamic> json) =>
      _$PostModelFromJson(json);

  PostModel();
}
