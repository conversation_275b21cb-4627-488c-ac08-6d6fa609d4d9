import 'package:XyyBeanSproutsFlutter/licence/bean/list_info.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'license_image_upload_info.g.dart';

@JsonSerializable()
class LicenseImageUpLoadInfo extends BaseModelV2<LicenseImageUpLoadInfo> {
  ///资质分类编码
  String? credentialCode;
  List<ListInfo>? enclosureList;

  LicenseImageUpLoadInfo();

  @override
  LicenseImageUpLoadInfo fromJsonMap(Map<String, dynamic>? json) {
    return LicenseImageUpLoadInfo.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenseImageUpLoadInfoToJson(this);
  }

  factory LicenseImageUpLoadInfo.fromJson(Map<String, dynamic> json) =>
      _$LicenseImageUpLoadInfoFromJson(json);
}
