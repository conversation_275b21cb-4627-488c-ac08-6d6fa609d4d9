import 'package:XyyBeanSproutsFlutter/licence/bean/licence_audit_bean.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_examine_log_list_bean.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'licence_examine_log_bean.g.dart';

@JsonSerializable()
class LicenceExamineLogBean extends BaseModelV2<LicenceExamineLogBean> {
  List<LicenceExamineLogListBean>? list;
  LicenceAuditBean? licenseAudit;

  LicenceExamineLogBean();

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return LicenceExamineLogBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenceExamineLogBeanToJson(this);
  }

  factory LicenceExamineLogBean.fromJson(Map<String, dynamic> json) =>
      _$LicenceExamineLogBeanFromJson(json);
}
