// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'licence_examine_log_bean.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

LicenceExamineLogBean _$LicenceExamineLogBeanFromJson(
    Map<String, dynamic> json) {
  return LicenceExamineLogBean()
    ..list = (json['list'] as List<dynamic>?)
        ?.map((e) =>
            LicenceExamineLogListBean.fromJson(e as Map<String, dynamic>))
        .toList()
    ..licenseAudit = json['licenseAudit'] == null
        ? null
        : LicenceAuditBean.fromJson(
            json['licenseAudit'] as Map<String, dynamic>);
}

Map<String, dynamic> _$LicenceExamineLogBeanToJson(
        LicenceExamineLogBean instance) =>
    <String, dynamic>{
      'list': instance.list,
      'licenseAudit': instance.licenseAudit,
    };
