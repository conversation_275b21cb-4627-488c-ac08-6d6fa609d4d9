import 'package:XyyBeanSproutsFlutter/utils/network/base_model.dart';
import 'package:json_annotation/json_annotation.dart';

part 'licence_audit_bean.g.dart';

@JsonSerializable()
class LicenceAuditBean extends BaseModelV2<LicenceAuditBean> {
  String? applicationNumber; //单据编号
  String? auditStatusName;

  LicenceAuditBean(); //状态

  @override
  fromJsonMap(Map<String, dynamic>? json) {
    return LicenceAuditBean.fromJson(json!);
  }

  @override
  Map<String, dynamic> toJson() {
    return _$LicenceAuditBeanToJson(this);
  }

  factory LicenceAuditBean.fromJson(Map<String, dynamic> json) =>
      _$LicenceAuditBeanFromJson(json);
}
