import 'package:XYYContainer/request_channel/request_channel.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_error_widget.dart';
import 'package:XyyBeanSproutsFlutter/common/base/base_page.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_audit_bean.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_examine_log_bean.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_examine_log_list_bean.dart';
import 'package:XyyBeanSproutsFlutter/utils/network/network_util_v2.dart';
import 'package:XyyBeanSproutsFlutter/utils/time/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_easyrefresh/easy_refresh.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

class LicenceExamineLogPage extends BasePage {
  final String? licenseAuditId;
  final String? type;

  LicenceExamineLogPage(this.licenseAuditId, this.type);

  @override
  BaseState<StatefulWidget> initState() {
    return LicenceExamineLogState(licenseAuditId, type);
  }
}

class LicenceExamineLogState extends BaseState<LicenceExamineLogPage> {
  late ExamineLogListModel _examineLogListModel;
  PageStateWidget? pageStateWidget;
  String? licenseAuditId;
  String? type;
  var _refreshController = EasyRefreshController();
  var _scrollController = ScrollController();

  LicenceExamineLogState(this.licenseAuditId, this.type);

  @override
  List<SingleChildWidget> getProvider() {
    return [
      ChangeNotifierProvider<ExamineLogListModel>(
          create: (context) => _examineLogListModel)
    ];
  }

  @override
  void onCreate() {
    pageStateWidget = new PageStateWidget();
    _examineLogListModel = ExamineLogListModel();
    _examineLogListModel.requestListData(licenseAuditId, type);
    super.onCreate();
  }

  @override
  Widget buildWidget(BuildContext context) {
    return Consumer<ExamineLogListModel>(builder: (context, model, child) {
      return Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Divider(
              height: 1,
              color: Color(0xFFE1E1E5),
            ),
            buildHeadView(),
            Divider(
              height: 1,
              color: Color(0xFFF5F5F5),
            ),
            Expanded(child: buildListView())
          ],
        ),
      );
    });
  }

  @override
  String getTitleName() {
    return "审批日志";
  }

  Widget buildHeadView() {
    return Container(
        color: Colors.white,
        alignment: Alignment.centerLeft,
        padding: EdgeInsets.fromLTRB(15, 10, 15, 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.max,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.only(right: 10),
                  width: 80,
                  child: Text(
                    "单据编号:",
                    style: TextStyle(
                        color: Color(0xFF333333), fontSize: 14, height: 1),
                  ),
                ),
                Text(
                  "${_examineLogListModel.auditBean?.applicationNumber ?? "-"}",
                  style: TextStyle(
                      color: Color(0xFF333333), fontSize: 14, height: 1),
                )
              ],
            ),
            SizedBox(height: 3),
            Row(
              children: [
                Container(
                    padding: EdgeInsets.only(right: 10),
                    width: 80,
                    child: Text(
                      "状态:",
                      style: TextStyle(color: Color(0xFF333333), fontSize: 14),
                    )),
                Text(
                  "${_examineLogListModel.auditBean?.auditStatusName ?? "-"}",
                  style: TextStyle(
                      color: _examineLogListModel.auditBean?.auditStatusName ==
                              "审核通过"
                          ? Color(0xFF35C561)
                          : Color(0xFFFE3D3D),
                      fontSize: 14),
                )
              ],
            )
          ],
        ));
  }

  Widget buildListView() {
    return EasyRefresh.custom(
        enableControlFinishRefresh: false,
        enableControlFinishLoad: false,
        scrollController: _scrollController,
        controller: _refreshController,
        slivers: [
          SliverList(
            delegate:
                SliverChildBuilderDelegate((BuildContext context, int index) {
              //创建列表项
              return _ExamineLogInfoItem(
                listBean: _examineLogListModel.listBean![index],
                first: index == 0,
                last: index + 1 == (_examineLogListModel.listBean?.length ?? 0),
              );
            }, childCount: _examineLogListModel.listBean?.length ?? 0),
          )
        ],
        emptyWidget: getEmptyWidget());
  }

  Widget? getEmptyWidget() {
    if (_examineLogListModel.success == null) {
      return null;
    }
    if (_examineLogListModel.success == false) {
      return PageStateWidget.pageEmpty(PageState.Error, errorClick: () {
        _examineLogListModel.requestListData(licenseAuditId, type);
      });
    }

    if ((_examineLogListModel.listBean?.length ?? 0) == 0) {
      return PageStateWidget.pageEmpty(PageState.Empty);
    }
    return null;
  }
}

class ExamineLogListModel extends ChangeNotifier {
  LicenceAuditBean? auditBean = LicenceAuditBean();
  List<LicenceExamineLogListBean>? listBean = [];

  bool _isDisposed = false;
  bool? success = true;

  void requestListData(String? licenseAuditId, String? type) async {
    EasyLoading.show(status: "加载中");
    NetworkV2<LicenceExamineLogBean>(LicenceExamineLogBean()).requestDataV2(
        "licenseOptimize/queryLicenseAuditLogList",
        contentType: RequestContentType.FORM,
        method: RequestMethod.POST,
        parameters: {
          "licenseAuditId": licenseAuditId,
          "type": type
        }).then((value) {
      EasyLoading.dismiss();
      handlerResult(value.isSuccess, value.data);
    });
  }

  void handlerResult(bool? success, LicenceExamineLogBean? logBean) {
    if (!_isDisposed && success!) {
      this.success = success;
      this.auditBean = logBean!.licenseAudit;
      this.listBean = logBean.list;
      notifyListeners();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }
}

class _ExamineLogInfoItem extends StatelessWidget {
  final LicenceExamineLogListBean? listBean;
  final bool? first;
  final bool? last;

  _ExamineLogInfoItem({this.listBean, this.first, this.last});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      constraints: BoxConstraints(minHeight: 50),
      padding: EdgeInsets.only(left: 10, right: 15),
      child: CustomPaint(
        painter: _LicenceLinePainter(
          isTop: this.first == true,
          isBootom: this.last == true,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Container(
              width: (this.first == true) ? 14 : 12,
              height: (this.first == true) ? 14 : 12,
              margin: EdgeInsets.only(
                  top: 14, bottom: 5, left: (this.first == true) ? 0 : 1),
              decoration: BoxDecoration(
                color: first! ? Color(0xFF35C561) : Color(0xFFB4B8C2),
                borderRadius: BorderRadius.all(
                  Radius.circular((this.first == true) ? 7 : 6),
                ),
              ),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(top: 10, bottom: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    _ExamineLogRightInfoItem(
                      title: listBean?.typeName,
                      content: listBean?.createTime != null
                          ? TimeUtils().formatTime(listBean?.createTime ?? 0)
                          : "-",
                      first: first,
                      no: true,
                      bold: first! ? true : false,
                    ),
                    _ExamineLogRightInfoItem(
                        title: "操作人：",
                        content: listBean?.creator ?? "—",
                        first: false,
                        no: false,
                        bold: false),
                    _ExamineLogRightInfoItem(
                        title: "状态",
                        content: listBean?.statusName ?? "—",
                        first: false,
                        no: false,
                        color: listBean!.statusName == "审核通过"
                            ? 0xFF35C561
                            : 0xFFFE3D3D,
                        bold: false),
                    listBean?.remark == null
                        ? Container()
                        : _ExamineLogRightInfoItem(
                            title: listBean!.status == 0 ? "驳回原因" : "备注",
                            content: listBean?.remark ?? "—",
                            first: false,
                            no: false,
                            bold: false),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ExamineLogRightInfoItem extends StatelessWidget {
  final String? title;
  final String content;
  final bool? first;
  final bool bold;
  final bool no; //是否是第一个
  final int? color;

  _ExamineLogRightInfoItem({
    Key? key,
    required this.title,
    required this.content,
    required this.first,
    required this.bold,
    required this.no,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        padding: EdgeInsets.only(top: 5, bottom: 3),
        child: Row(
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              alignment: Alignment.centerLeft,
              constraints: BoxConstraints(minWidth: 80),
              padding: EdgeInsets.only(left: 10),
              child: Text(
                this.title ?? "",
                style: TextStyle(
                  fontSize: first! ? 16 : 14,
                  height: 1,
                  fontWeight: bold ? FontWeight.bold : FontWeight.normal,
                  color: no ? Color(0xFF333333) : Color(0xFF999999),
                ),
              ),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(left: 15),
                alignment: Alignment.centerLeft,
                child: Text(
                  this.content,
                  style: TextStyle(
                    fontSize: first! ? 16 : 14,
                    height: 1,
                    fontWeight: bold ? FontWeight.bold : FontWeight.normal,
                    color: color == null ? Color(0xFF333333) : Color(color!),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _LicenceLinePainter extends CustomPainter {
  final bool isTop;
  final bool isBootom;

  _LicenceLinePainter({this.isTop = false, this.isBootom = false});

  @override
  void paint(Canvas canvas, Size size) {
    var paint = Paint();
    paint.color = Color(0xFFE4E4E6);
    paint.strokeWidth = 1;

    canvas.drawLine(
      Offset(7, this.isTop ? 14 : 0),
      Offset(7, this.isBootom ? 14 : size.height),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
