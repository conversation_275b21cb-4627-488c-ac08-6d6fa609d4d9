import 'package:XyyBeanSproutsFlutter/constant/licence_code.dart';
import 'package:XyyBeanSproutsFlutter/licence/bean/licence_item_bean.dart';
import 'package:XyyBeanSproutsFlutter/utils/time/time_utils.dart';
import 'package:flutter/material.dart';

class LicenceListItem extends StatelessWidget {
  LicenceItemBean itemBean;

  //单据编号
  Widget getCodeView(String? code, String? audit1StatusName, int? auditStatus) {
    return Container(
        height: 40,
        padding: EdgeInsets.only(left: 10, right: 10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.max,
          children: [
            Expanded(
                child: Text(
              '单据编号：${code ?? "—"}',
              style: TextStyle(color: Color(0xFF666666), fontSize: 12),
            )),
            Text('${audit1StatusName ?? "—"}',
                style: TextStyle(
                    color: Color(LicenceCode.setStatusColor(auditStatus)),
                    fontSize: 12))
          ],
        ));
  }

  //资质信息
  Widget getContentView(String? merchantName, String? sysUserName,
      String? branchName, int createTime, String? oaBranchName) {
    return Container(
        alignment: Alignment.centerLeft,
        constraints: BoxConstraints(minHeight: 106),
        color: Colors.white,
        padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 15),
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text('${merchantName ?? "—"}',
                  style: TextStyle(
                      color: Color(0xFF333333),
                      fontSize: 15,
                      fontWeight: FontWeight.bold)),
              SizedBox(height: 4),
              Text('提交人 : ${sysUserName ?? "—"}',
                  style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12)),
              SizedBox(height: 4),
              Text('单据来源 : ${branchName ?? "—"}',
                  style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12)),
              SizedBox(height: 4),
              Text('提交时间 : ${TimeUtils().formatTime(createTime)}',
                  style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12)),
              SizedBox(height: 4),
              Text('审批机构 : $oaBranchName',
                  style: TextStyle(color: Color(0xFF8E8E93), fontSize: 12)),
            ]));
  }

  @override
  Widget build(BuildContext context) {
    if (itemBean != null) {
      return GestureDetector(
        child: Container(
          constraints: BoxConstraints(minHeight: 146),
          width: double.infinity,
          margin: EdgeInsets.fromLTRB(10, 5, 10, 5),
          decoration: BoxDecoration(
              color: Colors.white, borderRadius: BorderRadius.circular(7)),
          child: Column(
            mainAxisSize: MainAxisSize.max,
            children: [
              getCodeView(itemBean.code, itemBean.audit1StatusName,
                  itemBean.auditStatus),
              Divider(height: 1, color: Color(0xFFE1E1E5)),
              getContentView(
                  itemBean.merchantName,
                  itemBean.sysUserName,
                  itemBean.branchName,
                  itemBean.createTime ?? 0,
                  itemBean.oaBranchName)
            ],
          ),
        ),
        onTap: () {
          Navigator.of(context).pushNamed('/license_detail_page', arguments: {
            "licenseAuditId": itemBean.code,
            "type": itemBean.type.toString()
          });
        },
      );
    } else {
      return Container();
    }
  }

  LicenceListItem(this.itemBean);
}
